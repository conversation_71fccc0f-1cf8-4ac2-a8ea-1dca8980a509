{"ast": null, "code": "import { isConstantNode, isFunctionNode, isOperatorNode, isParenthesisNode } from '../../../utils/is.js';\nexport { isConstantNode, isSymbolNode as isVariableNode } from '../../../utils/is.js';\nexport function isNumericNode(x) {\n  return isConstantNode(x) || isOperatorNode(x) && x.isUnary() && isConstantNode(x.args[0]);\n}\nexport function isConstantExpression(x) {\n  if (isConstantNode(x)) {\n    // Basic Constant types\n    return true;\n  }\n  if ((isFunctionNode(x) || isOperatorNode(x)) && x.args.every(isConstantExpression)) {\n    // Can be constant depending on arguments\n    return true;\n  }\n  if (isParenthesisNode(x) && isConstantExpression(x.content)) {\n    // Parenthesis are transparent\n    return true;\n  }\n  return false; // Probably missing some edge cases\n}", "map": {"version": 3, "names": ["isConstantNode", "isFunctionNode", "isOperatorNode", "isParenthesisNode", "isSymbolNode", "isVariableNode", "isNumericNode", "x", "isUnary", "args", "isConstantExpression", "every", "content"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/simplify/wildcards.js"], "sourcesContent": ["import { isConstantNode, isFunctionNode, isOperatorNode, isParenthesisNode } from '../../../utils/is.js';\nexport { isConstantNode, isSymbolNode as isVariableNode } from '../../../utils/is.js';\nexport function isNumericNode(x) {\n  return isConstantNode(x) || isOperatorNode(x) && x.isUnary() && isConstantNode(x.args[0]);\n}\nexport function isConstantExpression(x) {\n  if (isConstantNode(x)) {\n    // Basic Constant types\n    return true;\n  }\n  if ((isFunctionNode(x) || isOperatorNode(x)) && x.args.every(isConstantExpression)) {\n    // Can be constant depending on arguments\n    return true;\n  }\n  if (isParenthesisNode(x) && isConstantExpression(x.content)) {\n    // Parenthesis are transparent\n    return true;\n  }\n  return false; // Probably missing some edge cases\n}"], "mappings": "AAAA,SAASA,cAAc,EAAEC,cAAc,EAAEC,cAAc,EAAEC,iBAAiB,QAAQ,sBAAsB;AACxG,SAASH,cAAc,EAAEI,YAAY,IAAIC,cAAc,QAAQ,sBAAsB;AACrF,OAAO,SAASC,aAAaA,CAACC,CAAC,EAAE;EAC/B,OAAOP,cAAc,CAACO,CAAC,CAAC,IAAIL,cAAc,CAACK,CAAC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,CAAC,IAAIR,cAAc,CAACO,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3F;AACA,OAAO,SAASC,oBAAoBA,CAACH,CAAC,EAAE;EACtC,IAAIP,cAAc,CAACO,CAAC,CAAC,EAAE;IACrB;IACA,OAAO,IAAI;EACb;EACA,IAAI,CAACN,cAAc,CAACM,CAAC,CAAC,IAAIL,cAAc,CAACK,CAAC,CAAC,KAAKA,CAAC,CAACE,IAAI,CAACE,KAAK,CAACD,oBAAoB,CAAC,EAAE;IAClF;IACA,OAAO,IAAI;EACb;EACA,IAAIP,iBAAiB,CAACI,CAAC,CAAC,IAAIG,oBAAoB,CAACH,CAAC,CAACK,OAAO,CAAC,EAAE;IAC3D;IACA,OAAO,IAAI;EACb;EACA,OAAO,KAAK,CAAC,CAAC;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}