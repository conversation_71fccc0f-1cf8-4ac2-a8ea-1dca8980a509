{"ast": null, "code": "export var pickRandomDocs = {\n  name: 'pickRandom',\n  category: 'Probability',\n  syntax: ['pickRandom(array)', 'pickRandom(array, number)', 'pickRandom(array, weights)', 'pickRandom(array, number, weights)', 'pickRandom(array, weights, number)'],\n  description: 'Pick a random entry from a given array.',\n  examples: ['pickRandom(0:10)', 'pickRandom([1, 3, 1, 6])', 'pickRandom([1, 3, 1, 6], 2)', 'pickRandom([1, 3, 1, 6], [2, 3, 2, 1])', 'pickRandom([1, 3, 1, 6], 2, [2, 3, 2, 1])', 'pickRandom([1, 3, 1, 6], [2, 3, 2, 1], 2)'],\n  seealso: ['random', 'randomInt']\n};", "map": {"version": 3, "names": ["pickRandomDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/probability/pickRandom.js"], "sourcesContent": ["export var pickRandomDocs = {\n  name: 'pickRandom',\n  category: 'Probability',\n  syntax: ['pickRandom(array)', 'pickRandom(array, number)', 'pickRandom(array, weights)', 'pickRandom(array, number, weights)', 'pickRandom(array, weights, number)'],\n  description: 'Pick a random entry from a given array.',\n  examples: ['pickRandom(0:10)', 'pickRandom([1, 3, 1, 6])', 'pickRandom([1, 3, 1, 6], 2)', 'pickRandom([1, 3, 1, 6], [2, 3, 2, 1])', 'pickRandom([1, 3, 1, 6], 2, [2, 3, 2, 1])', 'pickRandom([1, 3, 1, 6], [2, 3, 2, 1], 2)'],\n  seealso: ['random', 'randomInt']\n};"], "mappings": "AAAA,OAAO,IAAIA,cAAc,GAAG;EAC1BC,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CAAC,mBAAmB,EAAE,2BAA2B,EAAE,4BAA4B,EAAE,oCAAoC,EAAE,oCAAoC,CAAC;EACpKC,WAAW,EAAE,yCAAyC;EACtDC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,0BAA0B,EAAE,6BAA6B,EAAE,wCAAwC,EAAE,2CAA2C,EAAE,2CAA2C,CAAC;EAC7NC,OAAO,EAAE,CAAC,QAAQ,EAAE,WAAW;AACjC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}