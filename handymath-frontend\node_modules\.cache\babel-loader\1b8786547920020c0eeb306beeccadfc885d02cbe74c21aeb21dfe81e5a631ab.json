{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { number } from '../../../value/types/numbers/index.mjs';\nconst int = _objectSpread(_objectSpread({}, number), {}, {\n  transform: Math.round\n});\nexport { int };", "map": {"version": 3, "names": ["number", "int", "_objectSpread", "transform", "Math", "round"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/framer-motion/dist/es/render/dom/value-types/type-int.mjs"], "sourcesContent": ["import { number } from '../../../value/types/numbers/index.mjs';\n\nconst int = {\n    ...number,\n    transform: Math.round,\n};\n\nexport { int };\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,wCAAwC;AAE/D,MAAMC,GAAG,GAAAC,aAAA,CAAAA,aAAA,KACFF,MAAM;EACTG,SAAS,EAAEC,IAAI,CAACC;AAAK,EACxB;AAED,SAASJ,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}