{"ast": null, "code": "/*\r\n * Natural Sort algorithm for Javascript - Version 0.7 - Released under MIT license\r\n * Author: <PERSON> (based on chunking idea from <PERSON>)\r\n */\n/*jshint unused:false */\nmodule.exports = function naturalSort(a, b) {\n  \"use strict\";\n\n  var re = /(^([+\\-]?(?:0|[1-9]\\d*)(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?)?$|^0x[0-9a-f]+$|\\d+)/gi,\n    sre = /(^[ ]*|[ ]*$)/g,\n    dre = /(^([\\w ]+,?[\\w ]+)?[\\w ]+,?[\\w ]+\\d+:\\d+(:\\d+)?[\\w ]?|^\\d{1,4}[\\/\\-]\\d{1,4}[\\/\\-]\\d{1,4}|^\\w+, \\w+ \\d+, \\d{4})/,\n    hre = /^0x[0-9a-f]+$/i,\n    ore = /^0/,\n    i = function (s) {\n      return naturalSort.insensitive && ('' + s).toLowerCase() || '' + s;\n    },\n    // convert all to strings strip whitespace\n    x = i(a).replace(sre, '') || '',\n    y = i(b).replace(sre, '') || '',\n    // chunk/tokenize\n    xN = x.replace(re, '\\0$1\\0').replace(/\\0$/, '').replace(/^\\0/, '').split('\\0'),\n    yN = y.replace(re, '\\0$1\\0').replace(/\\0$/, '').replace(/^\\0/, '').split('\\0'),\n    // numeric, hex or date detection\n    xD = parseInt(x.match(hre), 16) || xN.length !== 1 && x.match(dre) && Date.parse(x),\n    yD = parseInt(y.match(hre), 16) || xD && y.match(dre) && Date.parse(y) || null,\n    oFxNcL,\n    oFyNcL;\n  // first try and sort Hex codes or Dates\n  if (yD) {\n    if (xD < yD) {\n      return -1;\n    } else if (xD > yD) {\n      return 1;\n    }\n  }\n  // natural sorting through split numeric strings and default strings\n  for (var cLoc = 0, numS = Math.max(xN.length, yN.length); cLoc < numS; cLoc++) {\n    // find floats not starting with '0', string or 0 if not defined (Clint Priest)\n    oFxNcL = !(xN[cLoc] || '').match(ore) && parseFloat(xN[cLoc]) || xN[cLoc] || 0;\n    oFyNcL = !(yN[cLoc] || '').match(ore) && parseFloat(yN[cLoc]) || yN[cLoc] || 0;\n    // handle numeric vs string comparison - number < string - (Kyle Adams)\n    if (isNaN(oFxNcL) !== isNaN(oFyNcL)) {\n      return isNaN(oFxNcL) ? 1 : -1;\n    }\n    // rely on string comparison if different types - i.e. '02' < 2 != '02' < '2'\n    else if (typeof oFxNcL !== typeof oFyNcL) {\n      oFxNcL += '';\n      oFyNcL += '';\n    }\n    if (oFxNcL < oFyNcL) {\n      return -1;\n    }\n    if (oFxNcL > oFyNcL) {\n      return 1;\n    }\n  }\n  return 0;\n};", "map": {"version": 3, "names": ["module", "exports", "naturalSort", "a", "b", "re", "sre", "dre", "hre", "ore", "i", "s", "insensitive", "toLowerCase", "x", "replace", "y", "xN", "split", "yN", "xD", "parseInt", "match", "length", "Date", "parse", "yD", "oFxNcL", "oFyNcL", "cLoc", "numS", "Math", "max", "parseFloat", "isNaN"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/javascript-natural-sort/naturalSort.js"], "sourcesContent": ["/*\r\n * Natural Sort algorithm for Javascript - Version 0.7 - Released under MIT license\r\n * Author: <PERSON> (based on chunking idea from <PERSON>)\r\n */\r\n/*jshint unused:false */\r\nmodule.exports = function naturalSort (a, b) {\r\n\t\"use strict\";\r\n\tvar re = /(^([+\\-]?(?:0|[1-9]\\d*)(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?)?$|^0x[0-9a-f]+$|\\d+)/gi,\r\n\t\tsre = /(^[ ]*|[ ]*$)/g,\r\n\t\tdre = /(^([\\w ]+,?[\\w ]+)?[\\w ]+,?[\\w ]+\\d+:\\d+(:\\d+)?[\\w ]?|^\\d{1,4}[\\/\\-]\\d{1,4}[\\/\\-]\\d{1,4}|^\\w+, \\w+ \\d+, \\d{4})/,\r\n\t\thre = /^0x[0-9a-f]+$/i,\r\n\t\tore = /^0/,\r\n\t\ti = function(s) { return naturalSort.insensitive && ('' + s).toLowerCase() || '' + s; },\r\n\t\t// convert all to strings strip whitespace\r\n\t\tx = i(a).replace(sre, '') || '',\r\n\t\ty = i(b).replace(sre, '') || '',\r\n\t\t// chunk/tokenize\r\n\t\txN = x.replace(re, '\\0$1\\0').replace(/\\0$/,'').replace(/^\\0/,'').split('\\0'),\r\n\t\tyN = y.replace(re, '\\0$1\\0').replace(/\\0$/,'').replace(/^\\0/,'').split('\\0'),\r\n\t\t// numeric, hex or date detection\r\n\t\txD = parseInt(x.match(hre), 16) || (xN.length !== 1 && x.match(dre) && Date.parse(x)),\r\n\t\tyD = parseInt(y.match(hre), 16) || xD && y.match(dre) && Date.parse(y) || null,\r\n\t\toFxNcL, oFyNcL;\r\n\t// first try and sort Hex codes or Dates\r\n\tif (yD) {\r\n\t\tif ( xD < yD ) { return -1; }\r\n\t\telse if ( xD > yD ) { return 1; }\r\n\t}\r\n\t// natural sorting through split numeric strings and default strings\r\n\tfor(var cLoc=0, numS=Math.max(xN.length, yN.length); cLoc < numS; cLoc++) {\r\n\t\t// find floats not starting with '0', string or 0 if not defined (Clint Priest)\r\n\t\toFxNcL = !(xN[cLoc] || '').match(ore) && parseFloat(xN[cLoc]) || xN[cLoc] || 0;\r\n\t\toFyNcL = !(yN[cLoc] || '').match(ore) && parseFloat(yN[cLoc]) || yN[cLoc] || 0;\r\n\t\t// handle numeric vs string comparison - number < string - (Kyle Adams)\r\n\t\tif (isNaN(oFxNcL) !== isNaN(oFyNcL)) { return (isNaN(oFxNcL)) ? 1 : -1; }\r\n\t\t// rely on string comparison if different types - i.e. '02' < 2 != '02' < '2'\r\n\t\telse if (typeof oFxNcL !== typeof oFyNcL) {\r\n\t\t\toFxNcL += '';\r\n\t\t\toFyNcL += '';\r\n\t\t}\r\n\t\tif (oFxNcL < oFyNcL) { return -1; }\r\n\t\tif (oFxNcL > oFyNcL) { return 1; }\r\n\t}\r\n\treturn 0;\r\n};\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAAEC,CAAC,EAAEC,CAAC,EAAE;EAC5C,YAAY;;EACZ,IAAIC,EAAE,GAAG,6EAA6E;IACrFC,GAAG,GAAG,gBAAgB;IACtBC,GAAG,GAAG,gHAAgH;IACtHC,GAAG,GAAG,gBAAgB;IACtBC,GAAG,GAAG,IAAI;IACVC,CAAC,GAAG,SAAAA,CAASC,CAAC,EAAE;MAAE,OAAOT,WAAW,CAACU,WAAW,IAAI,CAAC,EAAE,GAAGD,CAAC,EAAEE,WAAW,CAAC,CAAC,IAAI,EAAE,GAAGF,CAAC;IAAE,CAAC;IACvF;IACAG,CAAC,GAAGJ,CAAC,CAACP,CAAC,CAAC,CAACY,OAAO,CAACT,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE;IAC/BU,CAAC,GAAGN,CAAC,CAACN,CAAC,CAAC,CAACW,OAAO,CAACT,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE;IAC/B;IACAW,EAAE,GAAGH,CAAC,CAACC,OAAO,CAACV,EAAE,EAAE,QAAQ,CAAC,CAACU,OAAO,CAAC,KAAK,EAAC,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAC,EAAE,CAAC,CAACG,KAAK,CAAC,IAAI,CAAC;IAC5EC,EAAE,GAAGH,CAAC,CAACD,OAAO,CAACV,EAAE,EAAE,QAAQ,CAAC,CAACU,OAAO,CAAC,KAAK,EAAC,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAC,EAAE,CAAC,CAACG,KAAK,CAAC,IAAI,CAAC;IAC5E;IACAE,EAAE,GAAGC,QAAQ,CAACP,CAAC,CAACQ,KAAK,CAACd,GAAG,CAAC,EAAE,EAAE,CAAC,IAAKS,EAAE,CAACM,MAAM,KAAK,CAAC,IAAIT,CAAC,CAACQ,KAAK,CAACf,GAAG,CAAC,IAAIiB,IAAI,CAACC,KAAK,CAACX,CAAC,CAAE;IACrFY,EAAE,GAAGL,QAAQ,CAACL,CAAC,CAACM,KAAK,CAACd,GAAG,CAAC,EAAE,EAAE,CAAC,IAAIY,EAAE,IAAIJ,CAAC,CAACM,KAAK,CAACf,GAAG,CAAC,IAAIiB,IAAI,CAACC,KAAK,CAACT,CAAC,CAAC,IAAI,IAAI;IAC9EW,MAAM;IAAEC,MAAM;EACf;EACA,IAAIF,EAAE,EAAE;IACP,IAAKN,EAAE,GAAGM,EAAE,EAAG;MAAE,OAAO,CAAC,CAAC;IAAE,CAAC,MACxB,IAAKN,EAAE,GAAGM,EAAE,EAAG;MAAE,OAAO,CAAC;IAAE;EACjC;EACA;EACA,KAAI,IAAIG,IAAI,GAAC,CAAC,EAAEC,IAAI,GAACC,IAAI,CAACC,GAAG,CAACf,EAAE,CAACM,MAAM,EAAEJ,EAAE,CAACI,MAAM,CAAC,EAAEM,IAAI,GAAGC,IAAI,EAAED,IAAI,EAAE,EAAE;IACzE;IACAF,MAAM,GAAG,CAAC,CAACV,EAAE,CAACY,IAAI,CAAC,IAAI,EAAE,EAAEP,KAAK,CAACb,GAAG,CAAC,IAAIwB,UAAU,CAAChB,EAAE,CAACY,IAAI,CAAC,CAAC,IAAIZ,EAAE,CAACY,IAAI,CAAC,IAAI,CAAC;IAC9ED,MAAM,GAAG,CAAC,CAACT,EAAE,CAACU,IAAI,CAAC,IAAI,EAAE,EAAEP,KAAK,CAACb,GAAG,CAAC,IAAIwB,UAAU,CAACd,EAAE,CAACU,IAAI,CAAC,CAAC,IAAIV,EAAE,CAACU,IAAI,CAAC,IAAI,CAAC;IAC9E;IACA,IAAIK,KAAK,CAACP,MAAM,CAAC,KAAKO,KAAK,CAACN,MAAM,CAAC,EAAE;MAAE,OAAQM,KAAK,CAACP,MAAM,CAAC,GAAI,CAAC,GAAG,CAAC,CAAC;IAAE;IACxE;IAAA,KACK,IAAI,OAAOA,MAAM,KAAK,OAAOC,MAAM,EAAE;MACzCD,MAAM,IAAI,EAAE;MACZC,MAAM,IAAI,EAAE;IACb;IACA,IAAID,MAAM,GAAGC,MAAM,EAAE;MAAE,OAAO,CAAC,CAAC;IAAE;IAClC,IAAID,MAAM,GAAGC,MAAM,EAAE;MAAE,OAAO,CAAC;IAAE;EAClC;EACA,OAAO,CAAC;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}