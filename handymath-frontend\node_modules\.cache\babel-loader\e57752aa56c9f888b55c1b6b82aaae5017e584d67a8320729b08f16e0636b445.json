{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { FibonacciHeapDependencies } from './dependenciesFibonacciHeapClass.generated.js';\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { createSpaClass } from '../../factoriesAny.js';\nexport var SpaDependencies = {\n  FibonacciHeapDependencies,\n  addScalarDependencies,\n  equalScalarDependencies,\n  createSpaClass\n};", "map": {"version": 3, "names": ["FibonacciHeapDependencies", "addScalarDependencies", "equalScalarDependencies", "createSpaClass", "SpaDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSpaClass.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { FibonacciHeapDependencies } from './dependenciesFibonacciHeapClass.generated.js';\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { createSpaClass } from '../../factoriesAny.js';\nexport var SpaDependencies = {\n  FibonacciHeapDependencies,\n  addScalarDependencies,\n  equalScalarDependencies,\n  createSpaClass\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,yBAAyB,QAAQ,+CAA+C;AACzF,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,SAASC,cAAc,QAAQ,uBAAuB;AACtD,OAAO,IAAIC,eAAe,GAAG;EAC3BJ,yBAAyB;EACzBC,qBAAqB;EACrBC,uBAAuB;EACvBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}