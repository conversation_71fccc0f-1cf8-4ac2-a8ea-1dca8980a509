{"ast": null, "code": "export var hypotDocs = {\n  name: 'hypot',\n  category: 'Arithmetic',\n  syntax: ['hypot(a, b, c, ...)', 'hypot([a, b, c, ...])'],\n  description: 'Calculate the hypotenuse of a list with values.',\n  examples: ['hypot(3, 4)', 'sqrt(3^2 + 4^2)', 'hypot(-2)', 'hypot([3, 4, 5])'],\n  seealso: ['abs', 'norm']\n};", "map": {"version": 3, "names": ["hypotDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/hypot.js"], "sourcesContent": ["export var hypotDocs = {\n  name: 'hypot',\n  category: 'Arithmetic',\n  syntax: ['hypot(a, b, c, ...)', 'hypot([a, b, c, ...])'],\n  description: 'Calculate the hypotenuse of a list with values.',\n  examples: ['hypot(3, 4)', 'sqrt(3^2 + 4^2)', 'hypot(-2)', 'hypot([3, 4, 5])'],\n  seealso: ['abs', 'norm']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,qBAAqB,EAAE,uBAAuB,CAAC;EACxDC,WAAW,EAAE,iDAAiD;EAC9DC,QAAQ,EAAE,CAAC,aAAa,EAAE,iBAAiB,EAAE,WAAW,EAAE,kBAAkB,CAAC;EAC7EC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}