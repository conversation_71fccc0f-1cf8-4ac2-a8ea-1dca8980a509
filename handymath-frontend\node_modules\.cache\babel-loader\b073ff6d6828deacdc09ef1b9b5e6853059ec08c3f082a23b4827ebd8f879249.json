{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nexport var createCompareUnits = /* #__PURE__ */factory('compareUnits', ['typed'], _ref => {\n  var {\n    typed\n  } = _ref;\n  return {\n    'Unit, Unit': typed.referToSelf(self => (x, y) => {\n      if (!x.equalBase(y)) {\n        throw new Error('Cannot compare units with different base');\n      }\n      return typed.find(self, [x.valueType(), y.valueType()])(x.value, y.value);\n    })\n  };\n});", "map": {"version": 3, "names": ["factory", "createCompareUnits", "_ref", "typed", "referToSelf", "self", "x", "y", "equalBase", "Error", "find", "valueType", "value"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/relational/compareUnits.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nexport var createCompareUnits = /* #__PURE__ */factory('compareUnits', ['typed'], _ref => {\n  var {\n    typed\n  } = _ref;\n  return {\n    'Unit, Unit': typed.referToSelf(self => (x, y) => {\n      if (!x.equalBase(y)) {\n        throw new Error('Cannot compare units with different base');\n      }\n      return typed.find(self, [x.valueType(), y.valueType()])(x.value, y.value);\n    })\n  };\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,OAAO,IAAIC,kBAAkB,GAAG,eAAeD,OAAO,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,EAAEE,IAAI,IAAI;EACxF,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR,OAAO;IACL,YAAY,EAAEC,KAAK,CAACC,WAAW,CAACC,IAAI,IAAI,CAACC,CAAC,EAAEC,CAAC,KAAK;MAChD,IAAI,CAACD,CAAC,CAACE,SAAS,CAACD,CAAC,CAAC,EAAE;QACnB,MAAM,IAAIE,KAAK,CAAC,0CAA0C,CAAC;MAC7D;MACA,OAAON,KAAK,CAACO,IAAI,CAACL,IAAI,EAAE,CAACC,CAAC,CAACK,SAAS,CAAC,CAAC,EAAEJ,CAAC,CAACI,SAAS,CAAC,CAAC,CAAC,CAAC,CAACL,CAAC,CAACM,KAAK,EAAEL,CAAC,CAACK,KAAK,CAAC;IAC3E,CAAC;EACH,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}