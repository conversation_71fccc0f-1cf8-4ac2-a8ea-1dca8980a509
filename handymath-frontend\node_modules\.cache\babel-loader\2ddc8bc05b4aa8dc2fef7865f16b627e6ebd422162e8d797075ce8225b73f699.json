{"ast": null, "code": "/* eslint no-template-curly-in-string: \"off\" */\n\nimport escapeLatexLib from 'escape-latex';\nimport { hasOwnProperty } from './object.js';\nexport var latexSymbols = {\n  // GREEK LETTERS\n  Alpha: 'A',\n  alpha: '\\\\alpha',\n  Beta: 'B',\n  beta: '\\\\beta',\n  Gamma: '\\\\Gamma',\n  gamma: '\\\\gamma',\n  Delta: '\\\\Delta',\n  delta: '\\\\delta',\n  Epsilon: 'E',\n  epsilon: '\\\\epsilon',\n  varepsilon: '\\\\varepsilon',\n  Zeta: 'Z',\n  zeta: '\\\\zeta',\n  Eta: 'H',\n  eta: '\\\\eta',\n  Theta: '\\\\Theta',\n  theta: '\\\\theta',\n  vartheta: '\\\\vartheta',\n  Iota: 'I',\n  iota: '\\\\iota',\n  Kappa: 'K',\n  kappa: '\\\\kappa',\n  varkappa: '\\\\varkappa',\n  Lambda: '\\\\Lambda',\n  lambda: '\\\\lambda',\n  Mu: 'M',\n  mu: '\\\\mu',\n  Nu: 'N',\n  nu: '\\\\nu',\n  Xi: '\\\\Xi',\n  xi: '\\\\xi',\n  Omicron: 'O',\n  omicron: 'o',\n  Pi: '\\\\Pi',\n  pi: '\\\\pi',\n  varpi: '\\\\varpi',\n  Rho: 'P',\n  rho: '\\\\rho',\n  varrho: '\\\\varrho',\n  Sigma: '\\\\Sigma',\n  sigma: '\\\\sigma',\n  varsigma: '\\\\varsigma',\n  Tau: 'T',\n  tau: '\\\\tau',\n  Upsilon: '\\\\Upsilon',\n  upsilon: '\\\\upsilon',\n  Phi: '\\\\Phi',\n  phi: '\\\\phi',\n  varphi: '\\\\varphi',\n  Chi: 'X',\n  chi: '\\\\chi',\n  Psi: '\\\\Psi',\n  psi: '\\\\psi',\n  Omega: '\\\\Omega',\n  omega: '\\\\omega',\n  // logic\n  true: '\\\\mathrm{True}',\n  false: '\\\\mathrm{False}',\n  // other\n  i: 'i',\n  // TODO use \\i ??\n  inf: '\\\\infty',\n  Inf: '\\\\infty',\n  infinity: '\\\\infty',\n  Infinity: '\\\\infty',\n  oo: '\\\\infty',\n  lim: '\\\\lim',\n  undefined: '\\\\mathbf{?}'\n};\nexport var latexOperators = {\n  transpose: '^\\\\top',\n  ctranspose: '^H',\n  factorial: '!',\n  pow: '^',\n  dotPow: '.^\\\\wedge',\n  // TODO find ideal solution\n  unaryPlus: '+',\n  unaryMinus: '-',\n  bitNot: '\\\\~',\n  // TODO find ideal solution\n  not: '\\\\neg',\n  multiply: '\\\\cdot',\n  divide: '\\\\frac',\n  // TODO how to handle that properly?\n  dotMultiply: '.\\\\cdot',\n  // TODO find ideal solution\n  dotDivide: '.:',\n  // TODO find ideal solution\n  mod: '\\\\mod',\n  add: '+',\n  subtract: '-',\n  to: '\\\\rightarrow',\n  leftShift: '<<',\n  rightArithShift: '>>',\n  rightLogShift: '>>>',\n  equal: '=',\n  unequal: '\\\\neq',\n  smaller: '<',\n  larger: '>',\n  smallerEq: '\\\\leq',\n  largerEq: '\\\\geq',\n  bitAnd: '\\\\&',\n  bitXor: '\\\\underline{|}',\n  bitOr: '|',\n  and: '\\\\wedge',\n  xor: '\\\\veebar',\n  or: '\\\\vee'\n};\nexport var latexFunctions = {\n  // arithmetic\n  abs: {\n    1: '\\\\left|${args[0]}\\\\right|'\n  },\n  add: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.add, \"${args[1]}\\\\right)\")\n  },\n  cbrt: {\n    1: '\\\\sqrt[3]{${args[0]}}'\n  },\n  ceil: {\n    1: '\\\\left\\\\lceil${args[0]}\\\\right\\\\rceil'\n  },\n  cube: {\n    1: '\\\\left(${args[0]}\\\\right)^3'\n  },\n  divide: {\n    2: '\\\\frac{${args[0]}}{${args[1]}}'\n  },\n  dotDivide: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.dotDivide, \"${args[1]}\\\\right)\")\n  },\n  dotMultiply: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.dotMultiply, \"${args[1]}\\\\right)\")\n  },\n  dotPow: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.dotPow, \"${args[1]}\\\\right)\")\n  },\n  exp: {\n    1: '\\\\exp\\\\left(${args[0]}\\\\right)'\n  },\n  expm1: \"\\\\left(e\".concat(latexOperators.pow, \"{${args[0]}}-1\\\\right)\"),\n  fix: {\n    1: '\\\\mathrm{${name}}\\\\left(${args[0]}\\\\right)'\n  },\n  floor: {\n    1: '\\\\left\\\\lfloor${args[0]}\\\\right\\\\rfloor'\n  },\n  fraction: {\n    2: '\\\\frac{${args[0]}}{${args[1]}}'\n  },\n  gcd: '\\\\gcd\\\\left(${args}\\\\right)',\n  hypot: '\\\\hypot\\\\left(${args}\\\\right)',\n  log: {\n    1: '\\\\ln\\\\left(${args[0]}\\\\right)',\n    2: '\\\\log_{${args[1]}}\\\\left(${args[0]}\\\\right)'\n  },\n  log10: {\n    1: '\\\\log_{10}\\\\left(${args[0]}\\\\right)'\n  },\n  log1p: {\n    1: '\\\\ln\\\\left(${args[0]}+1\\\\right)',\n    2: '\\\\log_{${args[1]}}\\\\left(${args[0]}+1\\\\right)'\n  },\n  log2: '\\\\log_{2}\\\\left(${args[0]}\\\\right)',\n  mod: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.mod, \"${args[1]}\\\\right)\")\n  },\n  multiply: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.multiply, \"${args[1]}\\\\right)\")\n  },\n  norm: {\n    1: '\\\\left\\\\|${args[0]}\\\\right\\\\|',\n    2: undefined // use default template\n  },\n  nthRoot: {\n    2: '\\\\sqrt[${args[1]}]{${args[0]}}'\n  },\n  nthRoots: {\n    2: '\\\\{y : y^${args[1]} = {${args[0]}}\\\\}'\n  },\n  pow: {\n    2: \"\\\\left(${args[0]}\\\\right)\".concat(latexOperators.pow, \"{${args[1]}}\")\n  },\n  round: {\n    1: '\\\\left\\\\lfloor${args[0]}\\\\right\\\\rceil',\n    2: undefined // use default template\n  },\n  sign: {\n    1: '\\\\mathrm{${name}}\\\\left(${args[0]}\\\\right)'\n  },\n  sqrt: {\n    1: '\\\\sqrt{${args[0]}}'\n  },\n  square: {\n    1: '\\\\left(${args[0]}\\\\right)^2'\n  },\n  subtract: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.subtract, \"${args[1]}\\\\right)\")\n  },\n  unaryMinus: {\n    1: \"\".concat(latexOperators.unaryMinus, \"\\\\left(${args[0]}\\\\right)\")\n  },\n  unaryPlus: {\n    1: \"\".concat(latexOperators.unaryPlus, \"\\\\left(${args[0]}\\\\right)\")\n  },\n  // bitwise\n  bitAnd: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.bitAnd, \"${args[1]}\\\\right)\")\n  },\n  bitNot: {\n    1: latexOperators.bitNot + '\\\\left(${args[0]}\\\\right)'\n  },\n  bitOr: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.bitOr, \"${args[1]}\\\\right)\")\n  },\n  bitXor: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.bitXor, \"${args[1]}\\\\right)\")\n  },\n  leftShift: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.leftShift, \"${args[1]}\\\\right)\")\n  },\n  rightArithShift: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.rightArithShift, \"${args[1]}\\\\right)\")\n  },\n  rightLogShift: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.rightLogShift, \"${args[1]}\\\\right)\")\n  },\n  // combinatorics\n  bellNumbers: {\n    1: '\\\\mathrm{B}_{${args[0]}}'\n  },\n  catalan: {\n    1: '\\\\mathrm{C}_{${args[0]}}'\n  },\n  stirlingS2: {\n    2: '\\\\mathrm{S}\\\\left(${args}\\\\right)'\n  },\n  // complex\n  arg: {\n    1: '\\\\arg\\\\left(${args[0]}\\\\right)'\n  },\n  conj: {\n    1: '\\\\left(${args[0]}\\\\right)^*'\n  },\n  im: {\n    1: '\\\\Im\\\\left\\\\lbrace${args[0]}\\\\right\\\\rbrace'\n  },\n  re: {\n    1: '\\\\Re\\\\left\\\\lbrace${args[0]}\\\\right\\\\rbrace'\n  },\n  // logical\n  and: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.and, \"${args[1]}\\\\right)\")\n  },\n  not: {\n    1: latexOperators.not + '\\\\left(${args[0]}\\\\right)'\n  },\n  or: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.or, \"${args[1]}\\\\right)\")\n  },\n  xor: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.xor, \"${args[1]}\\\\right)\")\n  },\n  // matrix\n  cross: {\n    2: '\\\\left(${args[0]}\\\\right)\\\\times\\\\left(${args[1]}\\\\right)'\n  },\n  ctranspose: {\n    1: \"\\\\left(${args[0]}\\\\right)\".concat(latexOperators.ctranspose)\n  },\n  det: {\n    1: '\\\\det\\\\left(${args[0]}\\\\right)'\n  },\n  dot: {\n    2: '\\\\left(${args[0]}\\\\cdot${args[1]}\\\\right)'\n  },\n  expm: {\n    1: '\\\\exp\\\\left(${args[0]}\\\\right)'\n  },\n  inv: {\n    1: '\\\\left(${args[0]}\\\\right)^{-1}'\n  },\n  pinv: {\n    1: '\\\\left(${args[0]}\\\\right)^{+}'\n  },\n  sqrtm: {\n    1: \"{${args[0]}}\".concat(latexOperators.pow, \"{\\\\frac{1}{2}}\")\n  },\n  trace: {\n    1: '\\\\mathrm{tr}\\\\left(${args[0]}\\\\right)'\n  },\n  transpose: {\n    1: \"\\\\left(${args[0]}\\\\right)\".concat(latexOperators.transpose)\n  },\n  // probability\n  combinations: {\n    2: '\\\\binom{${args[0]}}{${args[1]}}'\n  },\n  combinationsWithRep: {\n    2: '\\\\left(\\\\!\\\\!{\\\\binom{${args[0]}}{${args[1]}}}\\\\!\\\\!\\\\right)'\n  },\n  factorial: {\n    1: \"\\\\left(${args[0]}\\\\right)\".concat(latexOperators.factorial)\n  },\n  gamma: {\n    1: '\\\\Gamma\\\\left(${args[0]}\\\\right)'\n  },\n  lgamma: {\n    1: '\\\\ln\\\\Gamma\\\\left(${args[0]}\\\\right)'\n  },\n  // relational\n  equal: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.equal, \"${args[1]}\\\\right)\")\n  },\n  larger: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.larger, \"${args[1]}\\\\right)\")\n  },\n  largerEq: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.largerEq, \"${args[1]}\\\\right)\")\n  },\n  smaller: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.smaller, \"${args[1]}\\\\right)\")\n  },\n  smallerEq: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.smallerEq, \"${args[1]}\\\\right)\")\n  },\n  unequal: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.unequal, \"${args[1]}\\\\right)\")\n  },\n  // special\n  erf: {\n    1: 'erf\\\\left(${args[0]}\\\\right)'\n  },\n  // statistics\n  max: '\\\\max\\\\left(${args}\\\\right)',\n  min: '\\\\min\\\\left(${args}\\\\right)',\n  variance: '\\\\mathrm{Var}\\\\left(${args}\\\\right)',\n  // trigonometry\n  acos: {\n    1: '\\\\cos^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  acosh: {\n    1: '\\\\cosh^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  acot: {\n    1: '\\\\cot^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  acoth: {\n    1: '\\\\coth^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  acsc: {\n    1: '\\\\csc^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  acsch: {\n    1: '\\\\mathrm{csch}^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  asec: {\n    1: '\\\\sec^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  asech: {\n    1: '\\\\mathrm{sech}^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  asin: {\n    1: '\\\\sin^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  asinh: {\n    1: '\\\\sinh^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  atan: {\n    1: '\\\\tan^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  atan2: {\n    2: '\\\\mathrm{atan2}\\\\left(${args}\\\\right)'\n  },\n  atanh: {\n    1: '\\\\tanh^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  cos: {\n    1: '\\\\cos\\\\left(${args[0]}\\\\right)'\n  },\n  cosh: {\n    1: '\\\\cosh\\\\left(${args[0]}\\\\right)'\n  },\n  cot: {\n    1: '\\\\cot\\\\left(${args[0]}\\\\right)'\n  },\n  coth: {\n    1: '\\\\coth\\\\left(${args[0]}\\\\right)'\n  },\n  csc: {\n    1: '\\\\csc\\\\left(${args[0]}\\\\right)'\n  },\n  csch: {\n    1: '\\\\mathrm{csch}\\\\left(${args[0]}\\\\right)'\n  },\n  sec: {\n    1: '\\\\sec\\\\left(${args[0]}\\\\right)'\n  },\n  sech: {\n    1: '\\\\mathrm{sech}\\\\left(${args[0]}\\\\right)'\n  },\n  sin: {\n    1: '\\\\sin\\\\left(${args[0]}\\\\right)'\n  },\n  sinh: {\n    1: '\\\\sinh\\\\left(${args[0]}\\\\right)'\n  },\n  tan: {\n    1: '\\\\tan\\\\left(${args[0]}\\\\right)'\n  },\n  tanh: {\n    1: '\\\\tanh\\\\left(${args[0]}\\\\right)'\n  },\n  // unit\n  to: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.to, \"${args[1]}\\\\right)\")\n  },\n  // utils\n  numeric: function numeric(node, options) {\n    // Not sure if this is strictly right but should work correctly for the vast majority of use cases.\n    return node.args[0].toTex();\n  },\n  // type\n  number: {\n    0: '0',\n    1: '\\\\left(${args[0]}\\\\right)',\n    2: '\\\\left(\\\\left(${args[0]}\\\\right)${args[1]}\\\\right)'\n  },\n  string: {\n    0: '\\\\mathtt{\"\"}',\n    1: '\\\\mathrm{string}\\\\left(${args[0]}\\\\right)'\n  },\n  bignumber: {\n    0: '0',\n    1: '\\\\left(${args[0]}\\\\right)'\n  },\n  bigint: {\n    0: '0',\n    1: '\\\\left(${args[0]}\\\\right)'\n  },\n  complex: {\n    0: '0',\n    1: '\\\\left(${args[0]}\\\\right)',\n    2: \"\\\\left(\\\\left(${args[0]}\\\\right)+\".concat(latexSymbols.i, \"\\\\cdot\\\\left(${args[1]}\\\\right)\\\\right)\")\n  },\n  matrix: {\n    0: '\\\\begin{bmatrix}\\\\end{bmatrix}',\n    1: '\\\\left(${args[0]}\\\\right)',\n    2: '\\\\left(${args[0]}\\\\right)'\n  },\n  sparse: {\n    0: '\\\\begin{bsparse}\\\\end{bsparse}',\n    1: '\\\\left(${args[0]}\\\\right)'\n  },\n  unit: {\n    1: '\\\\left(${args[0]}\\\\right)',\n    2: '\\\\left(\\\\left(${args[0]}\\\\right)${args[1]}\\\\right)'\n  }\n};\nexport var defaultTemplate = '\\\\mathrm{${name}}\\\\left(${args}\\\\right)';\nvar latexUnits = {\n  deg: '^\\\\circ'\n};\nexport function escapeLatex(string) {\n  return escapeLatexLib(string, {\n    preserveFormatting: true\n  });\n}\n\n// @param {string} name\n// @param {boolean} isUnit\nexport function toSymbol(name, isUnit) {\n  isUnit = typeof isUnit === 'undefined' ? false : isUnit;\n  if (isUnit) {\n    if (hasOwnProperty(latexUnits, name)) {\n      return latexUnits[name];\n    }\n    return '\\\\mathrm{' + escapeLatex(name) + '}';\n  }\n  if (hasOwnProperty(latexSymbols, name)) {\n    return latexSymbols[name];\n  }\n  return escapeLatex(name);\n}", "map": {"version": 3, "names": ["escapeLatexLib", "hasOwnProperty", "latexSymbols", "Alpha", "alpha", "Beta", "beta", "Gamma", "gamma", "Delta", "delta", "Epsilon", "epsilon", "varepsilon", "Zeta", "zeta", "Eta", "eta", "Theta", "theta", "var<PERSON>ta", "Iota", "iota", "Kappa", "kappa", "<PERSON><PERSON><PERSON>", "Lambda", "lambda", "Mu", "mu", "<PERSON>u", "nu", "Xi", "xi", "Omicron", "omicron", "Pi", "pi", "var<PERSON>", "Rho", "rho", "varrho", "Sigma", "sigma", "varsigma", "Tau", "tau", "Upsilon", "upsilon", "Phi", "phi", "var<PERSON>", "<PERSON>", "chi", "Psi", "psi", "Omega", "omega", "true", "false", "i", "inf", "Inf", "infinity", "Infinity", "oo", "lim", "undefined", "latexOperators", "transpose", "ctranspose", "factorial", "pow", "dotPow", "unaryPlus", "unaryMinus", "bitNot", "not", "multiply", "divide", "dotMultiply", "dotDivide", "mod", "add", "subtract", "to", "leftShift", "rightArithShift", "rightLogShift", "equal", "unequal", "smaller", "larger", "smallerEq", "largerEq", "bitAnd", "bitXor", "bitOr", "and", "xor", "or", "latexFunctions", "abs", "concat", "cbrt", "ceil", "cube", "exp", "expm1", "fix", "floor", "fraction", "gcd", "hypot", "log", "log10", "log1p", "log2", "norm", "nthRoot", "nthRoots", "round", "sign", "sqrt", "square", "bellNumbers", "catalan", "stirlingS2", "arg", "conj", "im", "re", "cross", "det", "dot", "expm", "inv", "pinv", "sqrtm", "trace", "combinations", "combinationsWithRep", "lgamma", "erf", "max", "min", "variance", "acos", "acosh", "acot", "acoth", "acsc", "acsch", "asec", "asech", "asin", "asinh", "atan", "atan2", "atanh", "cos", "cosh", "cot", "coth", "csc", "csch", "sec", "sech", "sin", "sinh", "tan", "tanh", "numeric", "node", "options", "args", "toTex", "number", "string", "bignumber", "bigint", "complex", "matrix", "sparse", "unit", "defaultTemplate", "latexUnits", "deg", "escapeLatex", "preserveFormatting", "toSymbol", "name", "isUnit"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/latex.js"], "sourcesContent": ["/* eslint no-template-curly-in-string: \"off\" */\n\nimport escapeLatexLib from 'escape-latex';\nimport { hasOwnProperty } from './object.js';\nexport var latexSymbols = {\n  // GREEK LETTERS\n  Alpha: 'A',\n  alpha: '\\\\alpha',\n  Beta: 'B',\n  beta: '\\\\beta',\n  Gamma: '\\\\Gamma',\n  gamma: '\\\\gamma',\n  Delta: '\\\\Delta',\n  delta: '\\\\delta',\n  Epsilon: 'E',\n  epsilon: '\\\\epsilon',\n  varepsilon: '\\\\varepsilon',\n  Zeta: 'Z',\n  zeta: '\\\\zeta',\n  Eta: 'H',\n  eta: '\\\\eta',\n  Theta: '\\\\Theta',\n  theta: '\\\\theta',\n  vartheta: '\\\\vartheta',\n  Iota: 'I',\n  iota: '\\\\iota',\n  Kappa: 'K',\n  kappa: '\\\\kappa',\n  varkappa: '\\\\varkappa',\n  Lambda: '\\\\Lambda',\n  lambda: '\\\\lambda',\n  Mu: 'M',\n  mu: '\\\\mu',\n  Nu: 'N',\n  nu: '\\\\nu',\n  Xi: '\\\\Xi',\n  xi: '\\\\xi',\n  Omicron: 'O',\n  omicron: 'o',\n  Pi: '\\\\Pi',\n  pi: '\\\\pi',\n  varpi: '\\\\varpi',\n  Rho: 'P',\n  rho: '\\\\rho',\n  varrho: '\\\\varrho',\n  Sigma: '\\\\Sigma',\n  sigma: '\\\\sigma',\n  varsigma: '\\\\varsigma',\n  Tau: 'T',\n  tau: '\\\\tau',\n  Upsilon: '\\\\Upsilon',\n  upsilon: '\\\\upsilon',\n  Phi: '\\\\Phi',\n  phi: '\\\\phi',\n  varphi: '\\\\varphi',\n  Chi: 'X',\n  chi: '\\\\chi',\n  Psi: '\\\\Psi',\n  psi: '\\\\psi',\n  Omega: '\\\\Omega',\n  omega: '\\\\omega',\n  // logic\n  true: '\\\\mathrm{True}',\n  false: '\\\\mathrm{False}',\n  // other\n  i: 'i',\n  // TODO use \\i ??\n  inf: '\\\\infty',\n  Inf: '\\\\infty',\n  infinity: '\\\\infty',\n  Infinity: '\\\\infty',\n  oo: '\\\\infty',\n  lim: '\\\\lim',\n  undefined: '\\\\mathbf{?}'\n};\nexport var latexOperators = {\n  transpose: '^\\\\top',\n  ctranspose: '^H',\n  factorial: '!',\n  pow: '^',\n  dotPow: '.^\\\\wedge',\n  // TODO find ideal solution\n  unaryPlus: '+',\n  unaryMinus: '-',\n  bitNot: '\\\\~',\n  // TODO find ideal solution\n  not: '\\\\neg',\n  multiply: '\\\\cdot',\n  divide: '\\\\frac',\n  // TODO how to handle that properly?\n  dotMultiply: '.\\\\cdot',\n  // TODO find ideal solution\n  dotDivide: '.:',\n  // TODO find ideal solution\n  mod: '\\\\mod',\n  add: '+',\n  subtract: '-',\n  to: '\\\\rightarrow',\n  leftShift: '<<',\n  rightArithShift: '>>',\n  rightLogShift: '>>>',\n  equal: '=',\n  unequal: '\\\\neq',\n  smaller: '<',\n  larger: '>',\n  smallerEq: '\\\\leq',\n  largerEq: '\\\\geq',\n  bitAnd: '\\\\&',\n  bitXor: '\\\\underline{|}',\n  bitOr: '|',\n  and: '\\\\wedge',\n  xor: '\\\\veebar',\n  or: '\\\\vee'\n};\nexport var latexFunctions = {\n  // arithmetic\n  abs: {\n    1: '\\\\left|${args[0]}\\\\right|'\n  },\n  add: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.add, \"${args[1]}\\\\right)\")\n  },\n  cbrt: {\n    1: '\\\\sqrt[3]{${args[0]}}'\n  },\n  ceil: {\n    1: '\\\\left\\\\lceil${args[0]}\\\\right\\\\rceil'\n  },\n  cube: {\n    1: '\\\\left(${args[0]}\\\\right)^3'\n  },\n  divide: {\n    2: '\\\\frac{${args[0]}}{${args[1]}}'\n  },\n  dotDivide: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.dotDivide, \"${args[1]}\\\\right)\")\n  },\n  dotMultiply: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.dotMultiply, \"${args[1]}\\\\right)\")\n  },\n  dotPow: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.dotPow, \"${args[1]}\\\\right)\")\n  },\n  exp: {\n    1: '\\\\exp\\\\left(${args[0]}\\\\right)'\n  },\n  expm1: \"\\\\left(e\".concat(latexOperators.pow, \"{${args[0]}}-1\\\\right)\"),\n  fix: {\n    1: '\\\\mathrm{${name}}\\\\left(${args[0]}\\\\right)'\n  },\n  floor: {\n    1: '\\\\left\\\\lfloor${args[0]}\\\\right\\\\rfloor'\n  },\n  fraction: {\n    2: '\\\\frac{${args[0]}}{${args[1]}}'\n  },\n  gcd: '\\\\gcd\\\\left(${args}\\\\right)',\n  hypot: '\\\\hypot\\\\left(${args}\\\\right)',\n  log: {\n    1: '\\\\ln\\\\left(${args[0]}\\\\right)',\n    2: '\\\\log_{${args[1]}}\\\\left(${args[0]}\\\\right)'\n  },\n  log10: {\n    1: '\\\\log_{10}\\\\left(${args[0]}\\\\right)'\n  },\n  log1p: {\n    1: '\\\\ln\\\\left(${args[0]}+1\\\\right)',\n    2: '\\\\log_{${args[1]}}\\\\left(${args[0]}+1\\\\right)'\n  },\n  log2: '\\\\log_{2}\\\\left(${args[0]}\\\\right)',\n  mod: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.mod, \"${args[1]}\\\\right)\")\n  },\n  multiply: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.multiply, \"${args[1]}\\\\right)\")\n  },\n  norm: {\n    1: '\\\\left\\\\|${args[0]}\\\\right\\\\|',\n    2: undefined // use default template\n  },\n  nthRoot: {\n    2: '\\\\sqrt[${args[1]}]{${args[0]}}'\n  },\n  nthRoots: {\n    2: '\\\\{y : y^${args[1]} = {${args[0]}}\\\\}'\n  },\n  pow: {\n    2: \"\\\\left(${args[0]}\\\\right)\".concat(latexOperators.pow, \"{${args[1]}}\")\n  },\n  round: {\n    1: '\\\\left\\\\lfloor${args[0]}\\\\right\\\\rceil',\n    2: undefined // use default template\n  },\n  sign: {\n    1: '\\\\mathrm{${name}}\\\\left(${args[0]}\\\\right)'\n  },\n  sqrt: {\n    1: '\\\\sqrt{${args[0]}}'\n  },\n  square: {\n    1: '\\\\left(${args[0]}\\\\right)^2'\n  },\n  subtract: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.subtract, \"${args[1]}\\\\right)\")\n  },\n  unaryMinus: {\n    1: \"\".concat(latexOperators.unaryMinus, \"\\\\left(${args[0]}\\\\right)\")\n  },\n  unaryPlus: {\n    1: \"\".concat(latexOperators.unaryPlus, \"\\\\left(${args[0]}\\\\right)\")\n  },\n  // bitwise\n  bitAnd: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.bitAnd, \"${args[1]}\\\\right)\")\n  },\n  bitNot: {\n    1: latexOperators.bitNot + '\\\\left(${args[0]}\\\\right)'\n  },\n  bitOr: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.bitOr, \"${args[1]}\\\\right)\")\n  },\n  bitXor: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.bitXor, \"${args[1]}\\\\right)\")\n  },\n  leftShift: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.leftShift, \"${args[1]}\\\\right)\")\n  },\n  rightArithShift: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.rightArithShift, \"${args[1]}\\\\right)\")\n  },\n  rightLogShift: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.rightLogShift, \"${args[1]}\\\\right)\")\n  },\n  // combinatorics\n  bellNumbers: {\n    1: '\\\\mathrm{B}_{${args[0]}}'\n  },\n  catalan: {\n    1: '\\\\mathrm{C}_{${args[0]}}'\n  },\n  stirlingS2: {\n    2: '\\\\mathrm{S}\\\\left(${args}\\\\right)'\n  },\n  // complex\n  arg: {\n    1: '\\\\arg\\\\left(${args[0]}\\\\right)'\n  },\n  conj: {\n    1: '\\\\left(${args[0]}\\\\right)^*'\n  },\n  im: {\n    1: '\\\\Im\\\\left\\\\lbrace${args[0]}\\\\right\\\\rbrace'\n  },\n  re: {\n    1: '\\\\Re\\\\left\\\\lbrace${args[0]}\\\\right\\\\rbrace'\n  },\n  // logical\n  and: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.and, \"${args[1]}\\\\right)\")\n  },\n  not: {\n    1: latexOperators.not + '\\\\left(${args[0]}\\\\right)'\n  },\n  or: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.or, \"${args[1]}\\\\right)\")\n  },\n  xor: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.xor, \"${args[1]}\\\\right)\")\n  },\n  // matrix\n  cross: {\n    2: '\\\\left(${args[0]}\\\\right)\\\\times\\\\left(${args[1]}\\\\right)'\n  },\n  ctranspose: {\n    1: \"\\\\left(${args[0]}\\\\right)\".concat(latexOperators.ctranspose)\n  },\n  det: {\n    1: '\\\\det\\\\left(${args[0]}\\\\right)'\n  },\n  dot: {\n    2: '\\\\left(${args[0]}\\\\cdot${args[1]}\\\\right)'\n  },\n  expm: {\n    1: '\\\\exp\\\\left(${args[0]}\\\\right)'\n  },\n  inv: {\n    1: '\\\\left(${args[0]}\\\\right)^{-1}'\n  },\n  pinv: {\n    1: '\\\\left(${args[0]}\\\\right)^{+}'\n  },\n  sqrtm: {\n    1: \"{${args[0]}}\".concat(latexOperators.pow, \"{\\\\frac{1}{2}}\")\n  },\n  trace: {\n    1: '\\\\mathrm{tr}\\\\left(${args[0]}\\\\right)'\n  },\n  transpose: {\n    1: \"\\\\left(${args[0]}\\\\right)\".concat(latexOperators.transpose)\n  },\n  // probability\n  combinations: {\n    2: '\\\\binom{${args[0]}}{${args[1]}}'\n  },\n  combinationsWithRep: {\n    2: '\\\\left(\\\\!\\\\!{\\\\binom{${args[0]}}{${args[1]}}}\\\\!\\\\!\\\\right)'\n  },\n  factorial: {\n    1: \"\\\\left(${args[0]}\\\\right)\".concat(latexOperators.factorial)\n  },\n  gamma: {\n    1: '\\\\Gamma\\\\left(${args[0]}\\\\right)'\n  },\n  lgamma: {\n    1: '\\\\ln\\\\Gamma\\\\left(${args[0]}\\\\right)'\n  },\n  // relational\n  equal: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.equal, \"${args[1]}\\\\right)\")\n  },\n  larger: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.larger, \"${args[1]}\\\\right)\")\n  },\n  largerEq: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.largerEq, \"${args[1]}\\\\right)\")\n  },\n  smaller: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.smaller, \"${args[1]}\\\\right)\")\n  },\n  smallerEq: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.smallerEq, \"${args[1]}\\\\right)\")\n  },\n  unequal: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.unequal, \"${args[1]}\\\\right)\")\n  },\n  // special\n  erf: {\n    1: 'erf\\\\left(${args[0]}\\\\right)'\n  },\n  // statistics\n  max: '\\\\max\\\\left(${args}\\\\right)',\n  min: '\\\\min\\\\left(${args}\\\\right)',\n  variance: '\\\\mathrm{Var}\\\\left(${args}\\\\right)',\n  // trigonometry\n  acos: {\n    1: '\\\\cos^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  acosh: {\n    1: '\\\\cosh^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  acot: {\n    1: '\\\\cot^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  acoth: {\n    1: '\\\\coth^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  acsc: {\n    1: '\\\\csc^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  acsch: {\n    1: '\\\\mathrm{csch}^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  asec: {\n    1: '\\\\sec^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  asech: {\n    1: '\\\\mathrm{sech}^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  asin: {\n    1: '\\\\sin^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  asinh: {\n    1: '\\\\sinh^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  atan: {\n    1: '\\\\tan^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  atan2: {\n    2: '\\\\mathrm{atan2}\\\\left(${args}\\\\right)'\n  },\n  atanh: {\n    1: '\\\\tanh^{-1}\\\\left(${args[0]}\\\\right)'\n  },\n  cos: {\n    1: '\\\\cos\\\\left(${args[0]}\\\\right)'\n  },\n  cosh: {\n    1: '\\\\cosh\\\\left(${args[0]}\\\\right)'\n  },\n  cot: {\n    1: '\\\\cot\\\\left(${args[0]}\\\\right)'\n  },\n  coth: {\n    1: '\\\\coth\\\\left(${args[0]}\\\\right)'\n  },\n  csc: {\n    1: '\\\\csc\\\\left(${args[0]}\\\\right)'\n  },\n  csch: {\n    1: '\\\\mathrm{csch}\\\\left(${args[0]}\\\\right)'\n  },\n  sec: {\n    1: '\\\\sec\\\\left(${args[0]}\\\\right)'\n  },\n  sech: {\n    1: '\\\\mathrm{sech}\\\\left(${args[0]}\\\\right)'\n  },\n  sin: {\n    1: '\\\\sin\\\\left(${args[0]}\\\\right)'\n  },\n  sinh: {\n    1: '\\\\sinh\\\\left(${args[0]}\\\\right)'\n  },\n  tan: {\n    1: '\\\\tan\\\\left(${args[0]}\\\\right)'\n  },\n  tanh: {\n    1: '\\\\tanh\\\\left(${args[0]}\\\\right)'\n  },\n  // unit\n  to: {\n    2: \"\\\\left(${args[0]}\".concat(latexOperators.to, \"${args[1]}\\\\right)\")\n  },\n  // utils\n  numeric: function numeric(node, options) {\n    // Not sure if this is strictly right but should work correctly for the vast majority of use cases.\n    return node.args[0].toTex();\n  },\n  // type\n  number: {\n    0: '0',\n    1: '\\\\left(${args[0]}\\\\right)',\n    2: '\\\\left(\\\\left(${args[0]}\\\\right)${args[1]}\\\\right)'\n  },\n  string: {\n    0: '\\\\mathtt{\"\"}',\n    1: '\\\\mathrm{string}\\\\left(${args[0]}\\\\right)'\n  },\n  bignumber: {\n    0: '0',\n    1: '\\\\left(${args[0]}\\\\right)'\n  },\n  bigint: {\n    0: '0',\n    1: '\\\\left(${args[0]}\\\\right)'\n  },\n  complex: {\n    0: '0',\n    1: '\\\\left(${args[0]}\\\\right)',\n    2: \"\\\\left(\\\\left(${args[0]}\\\\right)+\".concat(latexSymbols.i, \"\\\\cdot\\\\left(${args[1]}\\\\right)\\\\right)\")\n  },\n  matrix: {\n    0: '\\\\begin{bmatrix}\\\\end{bmatrix}',\n    1: '\\\\left(${args[0]}\\\\right)',\n    2: '\\\\left(${args[0]}\\\\right)'\n  },\n  sparse: {\n    0: '\\\\begin{bsparse}\\\\end{bsparse}',\n    1: '\\\\left(${args[0]}\\\\right)'\n  },\n  unit: {\n    1: '\\\\left(${args[0]}\\\\right)',\n    2: '\\\\left(\\\\left(${args[0]}\\\\right)${args[1]}\\\\right)'\n  }\n};\nexport var defaultTemplate = '\\\\mathrm{${name}}\\\\left(${args}\\\\right)';\nvar latexUnits = {\n  deg: '^\\\\circ'\n};\nexport function escapeLatex(string) {\n  return escapeLatexLib(string, {\n    preserveFormatting: true\n  });\n}\n\n// @param {string} name\n// @param {boolean} isUnit\nexport function toSymbol(name, isUnit) {\n  isUnit = typeof isUnit === 'undefined' ? false : isUnit;\n  if (isUnit) {\n    if (hasOwnProperty(latexUnits, name)) {\n      return latexUnits[name];\n    }\n    return '\\\\mathrm{' + escapeLatex(name) + '}';\n  }\n  if (hasOwnProperty(latexSymbols, name)) {\n    return latexSymbols[name];\n  }\n  return escapeLatex(name);\n}"], "mappings": "AAAA;;AAEA,OAAOA,cAAc,MAAM,cAAc;AACzC,SAASC,cAAc,QAAQ,aAAa;AAC5C,OAAO,IAAIC,YAAY,GAAG;EACxB;EACAC,KAAK,EAAE,GAAG;EACVC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChBC,OAAO,EAAE,GAAG;EACZC,OAAO,EAAE,WAAW;EACpBC,UAAU,EAAE,cAAc;EAC1BC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,QAAQ;EACdC,GAAG,EAAE,GAAG;EACRC,GAAG,EAAE,OAAO;EACZC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChBC,QAAQ,EAAE,YAAY;EACtBC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,GAAG;EACVC,KAAK,EAAE,SAAS;EAChBC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,UAAU;EAClBC,MAAM,EAAE,UAAU;EAClBC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,OAAO,EAAE,GAAG;EACZC,OAAO,EAAE,GAAG;EACZC,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,KAAK,EAAE,SAAS;EAChBC,GAAG,EAAE,GAAG;EACRC,GAAG,EAAE,OAAO;EACZC,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChBC,QAAQ,EAAE,YAAY;EACtBC,GAAG,EAAE,GAAG;EACRC,GAAG,EAAE,OAAO;EACZC,OAAO,EAAE,WAAW;EACpBC,OAAO,EAAE,WAAW;EACpBC,GAAG,EAAE,OAAO;EACZC,GAAG,EAAE,OAAO;EACZC,MAAM,EAAE,UAAU;EAClBC,GAAG,EAAE,GAAG;EACRC,GAAG,EAAE,OAAO;EACZC,GAAG,EAAE,OAAO;EACZC,GAAG,EAAE,OAAO;EACZC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChB;EACAC,IAAI,EAAE,gBAAgB;EACtBC,KAAK,EAAE,iBAAiB;EACxB;EACAC,CAAC,EAAE,GAAG;EACN;EACAC,GAAG,EAAE,SAAS;EACdC,GAAG,EAAE,SAAS;EACdC,QAAQ,EAAE,SAAS;EACnBC,QAAQ,EAAE,SAAS;EACnBC,EAAE,EAAE,SAAS;EACbC,GAAG,EAAE,OAAO;EACZC,SAAS,EAAE;AACb,CAAC;AACD,OAAO,IAAIC,cAAc,GAAG;EAC1BC,SAAS,EAAE,QAAQ;EACnBC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,GAAG;EACdC,GAAG,EAAE,GAAG;EACRC,MAAM,EAAE,WAAW;EACnB;EACAC,SAAS,EAAE,GAAG;EACdC,UAAU,EAAE,GAAG;EACfC,MAAM,EAAE,KAAK;EACb;EACAC,GAAG,EAAE,OAAO;EACZC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,QAAQ;EAChB;EACAC,WAAW,EAAE,SAAS;EACtB;EACAC,SAAS,EAAE,IAAI;EACf;EACAC,GAAG,EAAE,OAAO;EACZC,GAAG,EAAE,GAAG;EACRC,QAAQ,EAAE,GAAG;EACbC,EAAE,EAAE,cAAc;EAClBC,SAAS,EAAE,IAAI;EACfC,eAAe,EAAE,IAAI;EACrBC,aAAa,EAAE,KAAK;EACpBC,KAAK,EAAE,GAAG;EACVC,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,GAAG;EACZC,MAAM,EAAE,GAAG;EACXC,SAAS,EAAE,OAAO;EAClBC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,KAAK;EACbC,MAAM,EAAE,gBAAgB;EACxBC,KAAK,EAAE,GAAG;EACVC,GAAG,EAAE,SAAS;EACdC,GAAG,EAAE,UAAU;EACfC,EAAE,EAAE;AACN,CAAC;AACD,OAAO,IAAIC,cAAc,GAAG;EAC1B;EACAC,GAAG,EAAE;IACH,CAAC,EAAE;EACL,CAAC;EACDnB,GAAG,EAAE;IACH,CAAC,EAAE,mBAAmB,CAACoB,MAAM,CAACnC,cAAc,CAACe,GAAG,EAAE,oBAAoB;EACxE,CAAC;EACDqB,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACD3B,MAAM,EAAE;IACN,CAAC,EAAE;EACL,CAAC;EACDE,SAAS,EAAE;IACT,CAAC,EAAE,mBAAmB,CAACsB,MAAM,CAACnC,cAAc,CAACa,SAAS,EAAE,oBAAoB;EAC9E,CAAC;EACDD,WAAW,EAAE;IACX,CAAC,EAAE,mBAAmB,CAACuB,MAAM,CAACnC,cAAc,CAACY,WAAW,EAAE,oBAAoB;EAChF,CAAC;EACDP,MAAM,EAAE;IACN,CAAC,EAAE,mBAAmB,CAAC8B,MAAM,CAACnC,cAAc,CAACK,MAAM,EAAE,oBAAoB;EAC3E,CAAC;EACDkC,GAAG,EAAE;IACH,CAAC,EAAE;EACL,CAAC;EACDC,KAAK,EAAE,UAAU,CAACL,MAAM,CAACnC,cAAc,CAACI,GAAG,EAAE,wBAAwB,CAAC;EACtEqC,GAAG,EAAE;IACH,CAAC,EAAE;EACL,CAAC;EACDC,KAAK,EAAE;IACL,CAAC,EAAE;EACL,CAAC;EACDC,QAAQ,EAAE;IACR,CAAC,EAAE;EACL,CAAC;EACDC,GAAG,EAAE,6BAA6B;EAClCC,KAAK,EAAE,+BAA+B;EACtCC,GAAG,EAAE;IACH,CAAC,EAAE,+BAA+B;IAClC,CAAC,EAAE;EACL,CAAC;EACDC,KAAK,EAAE;IACL,CAAC,EAAE;EACL,CAAC;EACDC,KAAK,EAAE;IACL,CAAC,EAAE,iCAAiC;IACpC,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE,oCAAoC;EAC1CnC,GAAG,EAAE;IACH,CAAC,EAAE,mBAAmB,CAACqB,MAAM,CAACnC,cAAc,CAACc,GAAG,EAAE,oBAAoB;EACxE,CAAC;EACDJ,QAAQ,EAAE;IACR,CAAC,EAAE,mBAAmB,CAACyB,MAAM,CAACnC,cAAc,CAACU,QAAQ,EAAE,oBAAoB;EAC7E,CAAC;EACDwC,IAAI,EAAE;IACJ,CAAC,EAAE,+BAA+B;IAClC,CAAC,EAAEnD,SAAS,CAAC;EACf,CAAC;EACDoD,OAAO,EAAE;IACP,CAAC,EAAE;EACL,CAAC;EACDC,QAAQ,EAAE;IACR,CAAC,EAAE;EACL,CAAC;EACDhD,GAAG,EAAE;IACH,CAAC,EAAE,2BAA2B,CAAC+B,MAAM,CAACnC,cAAc,CAACI,GAAG,EAAE,cAAc;EAC1E,CAAC;EACDiD,KAAK,EAAE;IACL,CAAC,EAAE,wCAAwC;IAC3C,CAAC,EAAEtD,SAAS,CAAC;EACf,CAAC;EACDuD,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,MAAM,EAAE;IACN,CAAC,EAAE;EACL,CAAC;EACDxC,QAAQ,EAAE;IACR,CAAC,EAAE,mBAAmB,CAACmB,MAAM,CAACnC,cAAc,CAACgB,QAAQ,EAAE,oBAAoB;EAC7E,CAAC;EACDT,UAAU,EAAE;IACV,CAAC,EAAE,EAAE,CAAC4B,MAAM,CAACnC,cAAc,CAACO,UAAU,EAAE,2BAA2B;EACrE,CAAC;EACDD,SAAS,EAAE;IACT,CAAC,EAAE,EAAE,CAAC6B,MAAM,CAACnC,cAAc,CAACM,SAAS,EAAE,2BAA2B;EACpE,CAAC;EACD;EACAqB,MAAM,EAAE;IACN,CAAC,EAAE,mBAAmB,CAACQ,MAAM,CAACnC,cAAc,CAAC2B,MAAM,EAAE,oBAAoB;EAC3E,CAAC;EACDnB,MAAM,EAAE;IACN,CAAC,EAAER,cAAc,CAACQ,MAAM,GAAG;EAC7B,CAAC;EACDqB,KAAK,EAAE;IACL,CAAC,EAAE,mBAAmB,CAACM,MAAM,CAACnC,cAAc,CAAC6B,KAAK,EAAE,oBAAoB;EAC1E,CAAC;EACDD,MAAM,EAAE;IACN,CAAC,EAAE,mBAAmB,CAACO,MAAM,CAACnC,cAAc,CAAC4B,MAAM,EAAE,oBAAoB;EAC3E,CAAC;EACDV,SAAS,EAAE;IACT,CAAC,EAAE,mBAAmB,CAACiB,MAAM,CAACnC,cAAc,CAACkB,SAAS,EAAE,oBAAoB;EAC9E,CAAC;EACDC,eAAe,EAAE;IACf,CAAC,EAAE,mBAAmB,CAACgB,MAAM,CAACnC,cAAc,CAACmB,eAAe,EAAE,oBAAoB;EACpF,CAAC;EACDC,aAAa,EAAE;IACb,CAAC,EAAE,mBAAmB,CAACe,MAAM,CAACnC,cAAc,CAACoB,aAAa,EAAE,oBAAoB;EAClF,CAAC;EACD;EACAqC,WAAW,EAAE;IACX,CAAC,EAAE;EACL,CAAC;EACDC,OAAO,EAAE;IACP,CAAC,EAAE;EACL,CAAC;EACDC,UAAU,EAAE;IACV,CAAC,EAAE;EACL,CAAC;EACD;EACAC,GAAG,EAAE;IACH,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,EAAE,EAAE;IACF,CAAC,EAAE;EACL,CAAC;EACDC,EAAE,EAAE;IACF,CAAC,EAAE;EACL,CAAC;EACD;EACAjC,GAAG,EAAE;IACH,CAAC,EAAE,mBAAmB,CAACK,MAAM,CAACnC,cAAc,CAAC8B,GAAG,EAAE,oBAAoB;EACxE,CAAC;EACDrB,GAAG,EAAE;IACH,CAAC,EAAET,cAAc,CAACS,GAAG,GAAG;EAC1B,CAAC;EACDuB,EAAE,EAAE;IACF,CAAC,EAAE,mBAAmB,CAACG,MAAM,CAACnC,cAAc,CAACgC,EAAE,EAAE,oBAAoB;EACvE,CAAC;EACDD,GAAG,EAAE;IACH,CAAC,EAAE,mBAAmB,CAACI,MAAM,CAACnC,cAAc,CAAC+B,GAAG,EAAE,oBAAoB;EACxE,CAAC;EACD;EACAiC,KAAK,EAAE;IACL,CAAC,EAAE;EACL,CAAC;EACD9D,UAAU,EAAE;IACV,CAAC,EAAE,2BAA2B,CAACiC,MAAM,CAACnC,cAAc,CAACE,UAAU;EACjE,CAAC;EACD+D,GAAG,EAAE;IACH,CAAC,EAAE;EACL,CAAC;EACDC,GAAG,EAAE;IACH,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,GAAG,EAAE;IACH,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,KAAK,EAAE;IACL,CAAC,EAAE,cAAc,CAACnC,MAAM,CAACnC,cAAc,CAACI,GAAG,EAAE,gBAAgB;EAC/D,CAAC;EACDmE,KAAK,EAAE;IACL,CAAC,EAAE;EACL,CAAC;EACDtE,SAAS,EAAE;IACT,CAAC,EAAE,2BAA2B,CAACkC,MAAM,CAACnC,cAAc,CAACC,SAAS;EAChE,CAAC;EACD;EACAuE,YAAY,EAAE;IACZ,CAAC,EAAE;EACL,CAAC;EACDC,mBAAmB,EAAE;IACnB,CAAC,EAAE;EACL,CAAC;EACDtE,SAAS,EAAE;IACT,CAAC,EAAE,2BAA2B,CAACgC,MAAM,CAACnC,cAAc,CAACG,SAAS;EAChE,CAAC;EACD/D,KAAK,EAAE;IACL,CAAC,EAAE;EACL,CAAC;EACDsI,MAAM,EAAE;IACN,CAAC,EAAE;EACL,CAAC;EACD;EACArD,KAAK,EAAE;IACL,CAAC,EAAE,mBAAmB,CAACc,MAAM,CAACnC,cAAc,CAACqB,KAAK,EAAE,oBAAoB;EAC1E,CAAC;EACDG,MAAM,EAAE;IACN,CAAC,EAAE,mBAAmB,CAACW,MAAM,CAACnC,cAAc,CAACwB,MAAM,EAAE,oBAAoB;EAC3E,CAAC;EACDE,QAAQ,EAAE;IACR,CAAC,EAAE,mBAAmB,CAACS,MAAM,CAACnC,cAAc,CAAC0B,QAAQ,EAAE,oBAAoB;EAC7E,CAAC;EACDH,OAAO,EAAE;IACP,CAAC,EAAE,mBAAmB,CAACY,MAAM,CAACnC,cAAc,CAACuB,OAAO,EAAE,oBAAoB;EAC5E,CAAC;EACDE,SAAS,EAAE;IACT,CAAC,EAAE,mBAAmB,CAACU,MAAM,CAACnC,cAAc,CAACyB,SAAS,EAAE,oBAAoB;EAC9E,CAAC;EACDH,OAAO,EAAE;IACP,CAAC,EAAE,mBAAmB,CAACa,MAAM,CAACnC,cAAc,CAACsB,OAAO,EAAE,oBAAoB;EAC5E,CAAC;EACD;EACAqD,GAAG,EAAE;IACH,CAAC,EAAE;EACL,CAAC;EACD;EACAC,GAAG,EAAE,6BAA6B;EAClCC,GAAG,EAAE,6BAA6B;EAClCC,QAAQ,EAAE,qCAAqC;EAC/C;EACAC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,KAAK,EAAE;IACL,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,KAAK,EAAE;IACL,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,KAAK,EAAE;IACL,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,KAAK,EAAE;IACL,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,KAAK,EAAE;IACL,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,KAAK,EAAE;IACL,CAAC,EAAE;EACL,CAAC;EACDC,KAAK,EAAE;IACL,CAAC,EAAE;EACL,CAAC;EACDC,GAAG,EAAE;IACH,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,GAAG,EAAE;IACH,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,GAAG,EAAE;IACH,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,GAAG,EAAE;IACH,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,GAAG,EAAE;IACH,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACDC,GAAG,EAAE;IACH,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE;EACL,CAAC;EACD;EACAtF,EAAE,EAAE;IACF,CAAC,EAAE,mBAAmB,CAACkB,MAAM,CAACnC,cAAc,CAACiB,EAAE,EAAE,oBAAoB;EACvE,CAAC;EACD;EACAuF,OAAO,EAAE,SAASA,OAAOA,CAACC,IAAI,EAAEC,OAAO,EAAE;IACvC;IACA,OAAOD,IAAI,CAACE,IAAI,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EAC7B,CAAC;EACD;EACAC,MAAM,EAAE;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,2BAA2B;IAC9B,CAAC,EAAE;EACL,CAAC;EACDC,MAAM,EAAE;IACN,CAAC,EAAE,cAAc;IACjB,CAAC,EAAE;EACL,CAAC;EACDC,SAAS,EAAE;IACT,CAAC,EAAE,GAAG;IACN,CAAC,EAAE;EACL,CAAC;EACDC,MAAM,EAAE;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE;EACL,CAAC;EACDC,OAAO,EAAE;IACP,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,2BAA2B;IAC9B,CAAC,EAAE,mCAAmC,CAAC9E,MAAM,CAACrG,YAAY,CAAC0D,CAAC,EAAE,yCAAyC;EACzG,CAAC;EACD0H,MAAM,EAAE;IACN,CAAC,EAAE,gCAAgC;IACnC,CAAC,EAAE,2BAA2B;IAC9B,CAAC,EAAE;EACL,CAAC;EACDC,MAAM,EAAE;IACN,CAAC,EAAE,gCAAgC;IACnC,CAAC,EAAE;EACL,CAAC;EACDC,IAAI,EAAE;IACJ,CAAC,EAAE,2BAA2B;IAC9B,CAAC,EAAE;EACL;AACF,CAAC;AACD,OAAO,IAAIC,eAAe,GAAG,yCAAyC;AACtE,IAAIC,UAAU,GAAG;EACfC,GAAG,EAAE;AACP,CAAC;AACD,OAAO,SAASC,WAAWA,CAACV,MAAM,EAAE;EAClC,OAAOlL,cAAc,CAACkL,MAAM,EAAE;IAC5BW,kBAAkB,EAAE;EACtB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,MAAM,EAAE;EACrCA,MAAM,GAAG,OAAOA,MAAM,KAAK,WAAW,GAAG,KAAK,GAAGA,MAAM;EACvD,IAAIA,MAAM,EAAE;IACV,IAAI/L,cAAc,CAACyL,UAAU,EAAEK,IAAI,CAAC,EAAE;MACpC,OAAOL,UAAU,CAACK,IAAI,CAAC;IACzB;IACA,OAAO,WAAW,GAAGH,WAAW,CAACG,IAAI,CAAC,GAAG,GAAG;EAC9C;EACA,IAAI9L,cAAc,CAACC,YAAY,EAAE6L,IAAI,CAAC,EAAE;IACtC,OAAO7L,YAAY,CAAC6L,IAAI,CAAC;EAC3B;EACA,OAAOH,WAAW,CAACG,IAAI,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}