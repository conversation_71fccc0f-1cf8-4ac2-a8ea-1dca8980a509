{"ast": null, "code": "export var floorDocs = {\n  name: 'floor',\n  category: 'Arithmetic',\n  syntax: ['floor(x)', 'floor(x, n)', 'floor(unit, valuelessUnit)', 'floor(unit, n, valuelessUnit)'],\n  description: 'Round a value towards minus infinity.If x is complex, both real and imaginary part are rounded towards minus infinity.',\n  examples: ['floor(3.2)', 'floor(3.8)', 'floor(-4.2)', 'floor(3.241cm, cm)', 'floor(3.241cm, 2, cm)'],\n  seealso: ['ceil', 'fix', 'round']\n};", "map": {"version": 3, "names": ["floorDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/floor.js"], "sourcesContent": ["export var floorDocs = {\n  name: 'floor',\n  category: 'Arithmetic',\n  syntax: ['floor(x)', 'floor(x, n)', 'floor(unit, valuelessUnit)', 'floor(unit, n, valuelessUnit)'],\n  description: 'Round a value towards minus infinity.If x is complex, both real and imaginary part are rounded towards minus infinity.',\n  examples: ['floor(3.2)', 'floor(3.8)', 'floor(-4.2)', 'floor(3.241cm, cm)', 'floor(3.241cm, 2, cm)'],\n  seealso: ['ceil', 'fix', 'round']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,4BAA4B,EAAE,+BAA+B,CAAC;EAClGC,WAAW,EAAE,wHAAwH;EACrIC,QAAQ,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,oBAAoB,EAAE,uBAAuB,CAAC;EACpGC,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}