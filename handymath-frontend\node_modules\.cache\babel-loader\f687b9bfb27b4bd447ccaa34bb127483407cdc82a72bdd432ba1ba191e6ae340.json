{"ast": null, "code": "export var pinvDocs = {\n  name: 'pinv',\n  category: 'Matrix',\n  syntax: ['pinv(x)'],\n  description: 'Calculate the <PERSON>–<PERSON> inverse of a matrix',\n  examples: ['pinv([1, 2; 3, 4])', 'pinv([[1, 0], [0, 1], [0, 1]])', 'pinv(4)'],\n  seealso: ['inv']\n};", "map": {"version": 3, "names": ["pinvDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/pinv.js"], "sourcesContent": ["export var pinvDocs = {\n  name: 'pinv',\n  category: 'Matrix',\n  syntax: ['pinv(x)'],\n  description: 'Calculate the <PERSON>–<PERSON> inverse of a matrix',\n  examples: ['pinv([1, 2; 3, 4])', 'pinv([[1, 0], [0, 1], [0, 1]])', 'pinv(4)'],\n  seealso: ['inv']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,iDAAiD;EAC9DC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,gCAAgC,EAAE,SAAS,CAAC;EAC7EC,OAAO,EAAE,CAAC,KAAK;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}