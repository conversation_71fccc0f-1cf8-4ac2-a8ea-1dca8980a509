{"ast": null, "code": "export var resizeDocs = {\n  name: 'resize',\n  category: 'Matrix',\n  syntax: ['resize(x, size)', 'resize(x, size, defaultValue)'],\n  description: 'Resize a matrix.',\n  examples: ['resize([1,2,3,4,5], [3])', 'resize([1,2,3], [5])', 'resize([1,2,3], [5], -1)', 'resize(2, [2, 3])', 'resize(\"hello\", [8], \"!\")'],\n  seealso: ['size', 'subset', 'squeeze', 'reshape']\n};", "map": {"version": 3, "names": ["resizeDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/resize.js"], "sourcesContent": ["export var resizeDocs = {\n  name: 'resize',\n  category: 'Matrix',\n  syntax: ['resize(x, size)', 'resize(x, size, defaultValue)'],\n  description: 'Resize a matrix.',\n  examples: ['resize([1,2,3,4,5], [3])', 'resize([1,2,3], [5])', 'resize([1,2,3], [5], -1)', 'resize(2, [2, 3])', 'resize(\"hello\", [8], \"!\")'],\n  seealso: ['size', 'subset', 'squeeze', 'reshape']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,iBAAiB,EAAE,+BAA+B,CAAC;EAC5DC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC,0BAA0B,EAAE,sBAAsB,EAAE,0BAA0B,EAAE,mBAAmB,EAAE,2BAA2B,CAAC;EAC5IC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS;AAClD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}