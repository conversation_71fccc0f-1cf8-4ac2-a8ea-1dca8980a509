{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { flattenDependencies } from './dependenciesFlatten.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { sizeDependencies } from './dependenciesSize.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMatrixFromRows } from '../../factoriesAny.js';\nexport var matrixFromRowsDependencies = {\n  flattenDependencies,\n  matrixDependencies,\n  sizeDependencies,\n  typedDependencies,\n  createMatrixFromRows\n};", "map": {"version": 3, "names": ["flattenDependencies", "matrixDependencies", "sizeDependencies", "typedDependencies", "createMatrixFromRows", "matrixFromRowsDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMatrixFromRows.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { flattenDependencies } from './dependenciesFlatten.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { sizeDependencies } from './dependenciesSize.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMatrixFromRows } from '../../factoriesAny.js';\nexport var matrixFromRowsDependencies = {\n  flattenDependencies,\n  matrixDependencies,\n  sizeDependencies,\n  typedDependencies,\n  createMatrixFromRows\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,OAAO,IAAIC,0BAA0B,GAAG;EACtCL,mBAAmB;EACnBC,kBAAkB;EAClBC,gBAAgB;EAChBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}