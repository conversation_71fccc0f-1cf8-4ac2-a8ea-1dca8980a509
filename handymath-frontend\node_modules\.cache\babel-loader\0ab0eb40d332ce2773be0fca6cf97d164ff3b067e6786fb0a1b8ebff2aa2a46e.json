{"ast": null, "code": "export var matrixFromColumnsDocs = {\n  name: 'matrixFromColumns',\n  category: 'Matrix',\n  syntax: ['matrixFromColumns(...arr)', 'matrixFromColumns(row1, row2)', 'matrixFromColumns(row1, row2, row3)'],\n  description: 'Create a dense matrix from vectors as individual columns.',\n  examples: ['matrixFromColumns([1, 2, 3], [[4],[5],[6]])'],\n  seealso: ['matrix', 'matrixFromRows', 'matrixFromFunction', 'zeros']\n};", "map": {"version": 3, "names": ["matrixFromColumnsDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/matrixFromColumns.js"], "sourcesContent": ["export var matrixFromColumnsDocs = {\n  name: 'matrixFromColumns',\n  category: 'Matrix',\n  syntax: ['matrixFromColumns(...arr)', 'matrixFromColumns(row1, row2)', 'matrixFromColumns(row1, row2, row3)'],\n  description: 'Create a dense matrix from vectors as individual columns.',\n  examples: ['matrixFromColumns([1, 2, 3], [[4],[5],[6]])'],\n  seealso: ['matrix', 'matrixFromRows', 'matrixFromFunction', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,qBAAqB,GAAG;EACjCC,IAAI,EAAE,mBAAmB;EACzBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,2BAA2B,EAAE,+BAA+B,EAAE,qCAAqC,CAAC;EAC7GC,WAAW,EAAE,2DAA2D;EACxEC,QAAQ,EAAE,CAAC,6CAA6C,CAAC;EACzDC,OAAO,EAAE,CAAC,QAAQ,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,OAAO;AACrE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}