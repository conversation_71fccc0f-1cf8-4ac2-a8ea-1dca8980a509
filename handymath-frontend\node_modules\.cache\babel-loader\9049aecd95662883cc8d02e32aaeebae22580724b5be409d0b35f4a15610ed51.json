{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { fractionDependencies } from './dependenciesFraction.generated.js';\nimport { identityDependencies } from './dependenciesIdentity.generated.js';\nimport { invDependencies } from './dependenciesInv.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { numberDependencies } from './dependenciesNumber.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createPow } from '../../factoriesAny.js';\nexport var powDependencies = {\n  ComplexDependencies,\n  fractionDependencies,\n  identityDependencies,\n  invDependencies,\n  matrixDependencies,\n  multiplyDependencies,\n  numberDependencies,\n  typedDependencies,\n  createPow\n};", "map": {"version": 3, "names": ["ComplexDependencies", "fractionDependencies", "identityDependencies", "invDependencies", "matrixDependencies", "multiplyDependencies", "numberDependencies", "typedDependencies", "createPow", "powDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesPow.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { fractionDependencies } from './dependenciesFraction.generated.js';\nimport { identityDependencies } from './dependenciesIdentity.generated.js';\nimport { invDependencies } from './dependenciesInv.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { numberDependencies } from './dependenciesNumber.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createPow } from '../../factoriesAny.js';\nexport var powDependencies = {\n  ComplexDependencies,\n  fractionDependencies,\n  identityDependencies,\n  invDependencies,\n  matrixDependencies,\n  multiplyDependencies,\n  numberDependencies,\n  typedDependencies,\n  createPow\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAO,IAAIC,eAAe,GAAG;EAC3BT,mBAAmB;EACnBC,oBAAoB;EACpBC,oBAAoB;EACpBC,eAAe;EACfC,kBAAkB;EAClBC,oBAAoB;EACpBC,kBAAkB;EAClBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}