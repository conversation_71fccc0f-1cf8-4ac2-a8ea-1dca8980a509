{"ast": null, "code": "export var sqrtmDocs = {\n  name: 'sqrtm',\n  category: 'Arithmetic',\n  syntax: ['sqrtm(x)'],\n  description: 'Calculate the principal square root of a square matrix. The principal square root matrix `X` of another matrix `A` is such that `X * X = A`.',\n  examples: ['sqrtm([[33, 24], [48, 57]])'],\n  seealso: ['sqrt', 'abs', 'square', 'multiply']\n};", "map": {"version": 3, "names": ["sqrtmDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/sqrtm.js"], "sourcesContent": ["export var sqrtmDocs = {\n  name: 'sqrtm',\n  category: 'Arithmetic',\n  syntax: ['sqrtm(x)'],\n  description: 'Calculate the principal square root of a square matrix. The principal square root matrix `X` of another matrix `A` is such that `X * X = A`.',\n  examples: ['sqrtm([[33, 24], [48, 57]])'],\n  seealso: ['sqrt', 'abs', 'square', 'multiply']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,8IAA8I;EAC3JC,QAAQ,EAAE,CAAC,6BAA6B,CAAC;EACzCC,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU;AAC/C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}