{"ast": null, "code": "export var detDocs = {\n  name: 'det',\n  category: 'Matrix',\n  syntax: ['det(x)'],\n  description: 'Calculate the determinant of a matrix',\n  examples: ['det([1, 2; 3, 4])', 'det([-2, 2, 3; -1, 1, 3; 2, 0, -1])'],\n  seealso: ['concat', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};", "map": {"version": 3, "names": ["detDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/det.js"], "sourcesContent": ["export var detDocs = {\n  name: 'det',\n  category: 'Matrix',\n  syntax: ['det(x)'],\n  description: 'Calculate the determinant of a matrix',\n  examples: ['det([1, 2; 3, 4])', 'det([-2, 2, 3; -1, 1, 3; 2, 0, -1])'],\n  seealso: ['concat', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,uCAAuC;EACpDC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,qCAAqC,CAAC;EACtEC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO;AAC5H,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}