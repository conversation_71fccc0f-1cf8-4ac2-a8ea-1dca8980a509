{"ast": null, "code": "export var flattenDocs = {\n  name: 'flatten',\n  category: 'Matrix',\n  syntax: ['flatten(x)'],\n  description: 'Flatten a multi dimensional matrix into a single dimensional matrix.',\n  examples: ['a = [1, 2, 3; 4, 5, 6]', 'size(a)', 'b = flatten(a)', 'size(b)'],\n  seealso: ['concat', 'resize', 'size', 'squeeze']\n};", "map": {"version": 3, "names": ["flattenDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/flatten.js"], "sourcesContent": ["export var flattenDocs = {\n  name: 'flatten',\n  category: 'Matrix',\n  syntax: ['flatten(x)'],\n  description: 'Flatten a multi dimensional matrix into a single dimensional matrix.',\n  examples: ['a = [1, 2, 3; 4, 5, 6]', 'size(a)', 'b = flatten(a)', 'size(b)'],\n  seealso: ['concat', 'resize', 'size', 'squeeze']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,YAAY,CAAC;EACtBC,WAAW,EAAE,sEAAsE;EACnFC,QAAQ,EAAE,CAAC,wBAAwB,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,CAAC;EAC5EC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS;AACjD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}