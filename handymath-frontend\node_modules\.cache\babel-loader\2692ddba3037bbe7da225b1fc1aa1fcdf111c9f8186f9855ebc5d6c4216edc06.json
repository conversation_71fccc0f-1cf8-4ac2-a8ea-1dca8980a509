{"ast": null, "code": "export var ctransposeDocs = {\n  name: 'ctranspose',\n  category: 'Matrix',\n  syntax: ['x\\'', 'ctranspose(x)'],\n  description: 'Complex Conjugate and Transpose a matrix',\n  examples: ['a = [1, 2, 3; 4, 5, 6]', 'a\\'', 'ctranspose(a)'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'zeros']\n};", "map": {"version": 3, "names": ["ctransposeDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/ctranspose.js"], "sourcesContent": ["export var ctransposeDocs = {\n  name: 'ctranspose',\n  category: 'Matrix',\n  syntax: ['x\\'', 'ctranspose(x)'],\n  description: 'Complex Conjugate and Transpose a matrix',\n  examples: ['a = [1, 2, 3; 4, 5, 6]', 'a\\'', 'ctranspose(a)'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,cAAc,GAAG;EAC1BC,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,KAAK,EAAE,eAAe,CAAC;EAChCC,WAAW,EAAE,0CAA0C;EACvDC,QAAQ,EAAE,CAAC,wBAAwB,EAAE,KAAK,EAAE,eAAe,CAAC;EAC5DC,OAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO;AACtH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}