{"ast": null, "code": "export var dotMultiplyDocs = {\n  name: 'dotMultiply',\n  category: 'Operators',\n  syntax: ['x .* y', 'dotMultiply(x, y)'],\n  description: 'Multiply two values element wise.',\n  examples: ['a = [1, 2, 3; 4, 5, 6]', 'b = [2, 1, 1; 3, 2, 5]', 'a .* b'],\n  seealso: ['multiply', 'divide', 'dotDivide']\n};", "map": {"version": 3, "names": ["dotMultiplyDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/dotMultiply.js"], "sourcesContent": ["export var dotMultiplyDocs = {\n  name: 'dotMultiply',\n  category: 'Operators',\n  syntax: ['x .* y', 'dotMultiply(x, y)'],\n  description: 'Multiply two values element wise.',\n  examples: ['a = [1, 2, 3; 4, 5, 6]', 'b = [2, 1, 1; 3, 2, 5]', 'a .* b'],\n  seealso: ['multiply', 'divide', 'dotDivide']\n};"], "mappings": "AAAA,OAAO,IAAIA,eAAe,GAAG;EAC3BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,QAAQ,EAAE,mBAAmB,CAAC;EACvCC,WAAW,EAAE,mCAAmC;EAChDC,QAAQ,EAAE,CAAC,wBAAwB,EAAE,wBAAwB,EAAE,QAAQ,CAAC;EACxEC,OAAO,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,WAAW;AAC7C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}