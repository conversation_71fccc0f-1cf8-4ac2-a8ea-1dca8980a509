import React from 'react';
import { Link } from 'react-router-dom';

interface LogoProps {
  variant?: 'default' | 'admin' | 'student';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showText?: boolean;
  className?: string;
  linkTo?: string;
}

const Logo: React.FC<LogoProps> = ({
  variant = 'default',
  size = 'md',
  showText = true,
  className = '',
  linkTo = '/'
}) => {
  // Tailles du logo
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10',
    xl: 'w-12 h-12'
  };

  // Tailles du texte
  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-3xl'
  };

  // Couleurs selon la variante
  const getLogoColors = () => {
    switch (variant) {
      case 'admin':
        return 'bg-red-600 text-white';
      case 'student':
        return 'bg-primary-600 text-white';
      default:
        return 'bg-primary-600 text-white';
    }
  };

  // Couleurs du texte selon la variante
  const getTextColors = () => {
    switch (variant) {
      case 'admin':
        return 'text-gray-900 dark:text-white';
      case 'student':
        return 'text-gray-900 dark:text-white';
      default:
        return 'text-primary-600 hover:text-primary-700 dark:text-white';
    }
  };

  const logoElement = (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Icône du logo */}
      <div className={`${sizeClasses[size]} ${getLogoColors()} rounded-lg flex items-center justify-center`}>
        <span className="font-bold text-lg">H</span>
      </div>
      
      {/* Texte du logo */}
      {showText && (
        <span className={`${textSizeClasses[size]} font-bold ${getTextColors()} transition-colors`}>
          HandyMath
        </span>
      )}
    </div>
  );

  // Si linkTo est fourni, envelopper dans un Link
  if (linkTo) {
    return (
      <Link to={linkTo} className="inline-block">
        {logoElement}
      </Link>
    );
  }

  return logoElement;
};

export default Logo;
