{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createRe } from '../../factoriesAny.js';\nexport var reDependencies = {\n  typedDependencies,\n  createRe\n};", "map": {"version": 3, "names": ["typedDependencies", "createRe", "reDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesRe.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createRe } from '../../factoriesAny.js';\nexport var reDependencies = {\n  typedDependencies,\n  createRe\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,OAAO,IAAIC,cAAc,GAAG;EAC1BF,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}