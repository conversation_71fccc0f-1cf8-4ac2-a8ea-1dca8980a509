{"ast": null, "code": "export var nullDocs = {\n  name: 'null',\n  category: 'Constants',\n  syntax: ['null'],\n  description: 'Value null',\n  examples: ['null'],\n  seealso: ['true', 'false']\n};", "map": {"version": 3, "names": ["nullDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/null.js"], "sourcesContent": ["export var nullDocs = {\n  name: 'null',\n  category: 'Constants',\n  syntax: ['null'],\n  description: 'Value null',\n  examples: ['null'],\n  seealso: ['true', 'false']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,MAAM,CAAC;EAChBC,WAAW,EAAE,YAAY;EACzBC,QAAQ,EAAE,CAAC,MAAM,CAAC;EAClBC,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}