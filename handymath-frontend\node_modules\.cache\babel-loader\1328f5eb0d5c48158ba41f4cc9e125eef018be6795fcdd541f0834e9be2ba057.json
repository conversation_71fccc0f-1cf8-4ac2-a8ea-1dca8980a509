{"ast": null, "code": "export var qrDocs = {\n  name: 'qr',\n  category: 'Algebra',\n  syntax: ['qr(A)'],\n  description: 'Calculates the Matrix QR decomposition. Matrix `A` is decomposed in two matrices (`Q`, `R`) where `Q` is an orthogonal matrix and `R` is an upper triangular matrix.',\n  examples: ['qr([[1, -1,  4], [1,  4, -2], [1,  4,  2], [1,  -1, 0]])'],\n  seealso: ['lup', 'slu', 'matrix']\n};", "map": {"version": 3, "names": ["qrDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/qr.js"], "sourcesContent": ["export var qrDocs = {\n  name: 'qr',\n  category: 'Algebra',\n  syntax: ['qr(A)'],\n  description: 'Calculates the Matrix QR decomposition. Matrix `A` is decomposed in two matrices (`Q`, `R`) where `Q` is an orthogonal matrix and `R` is an upper triangular matrix.',\n  examples: ['qr([[1, -1,  4], [1,  4, -2], [1,  4,  2], [1,  -1, 0]])'],\n  seealso: ['lup', 'slu', 'matrix']\n};"], "mappings": "AAAA,OAAO,IAAIA,MAAM,GAAG;EAClBC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,OAAO,CAAC;EACjBC,WAAW,EAAE,sKAAsK;EACnLC,QAAQ,EAAE,CAAC,0DAA0D,CAAC;EACtEC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}