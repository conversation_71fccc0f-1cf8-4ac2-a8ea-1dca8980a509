{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { IndexDependencies } from './dependenciesIndexClass.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { setDifferenceDependencies } from './dependenciesSetDifference.generated.js';\nimport { sizeDependencies } from './dependenciesSize.generated.js';\nimport { subsetDependencies } from './dependenciesSubset.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSetSymDifference } from '../../factoriesAny.js';\nexport var setSymDifferenceDependencies = {\n  IndexDependencies,\n  concatDependencies,\n  setDifferenceDependencies,\n  sizeDependencies,\n  subsetDependencies,\n  typedDependencies,\n  createSetSymDifference\n};", "map": {"version": 3, "names": ["IndexDependencies", "concatDependencies", "setDifferenceDependencies", "sizeDependencies", "subsetDependencies", "typedDependencies", "createSetSymDifference", "setSymDifferenceDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSetSymDifference.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { IndexDependencies } from './dependenciesIndexClass.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { setDifferenceDependencies } from './dependenciesSetDifference.generated.js';\nimport { sizeDependencies } from './dependenciesSize.generated.js';\nimport { subsetDependencies } from './dependenciesSubset.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSetSymDifference } from '../../factoriesAny.js';\nexport var setSymDifferenceDependencies = {\n  IndexDependencies,\n  concatDependencies,\n  setDifferenceDependencies,\n  sizeDependencies,\n  subsetDependencies,\n  typedDependencies,\n  createSetSymDifference\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,uCAAuC;AACzE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,yBAAyB,QAAQ,0CAA0C;AACpF,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,sBAAsB,QAAQ,uBAAuB;AAC9D,OAAO,IAAIC,4BAA4B,GAAG;EACxCP,iBAAiB;EACjBC,kBAAkB;EAClBC,yBAAyB;EACzBC,gBAAgB;EAChBC,kBAAkB;EAClBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}