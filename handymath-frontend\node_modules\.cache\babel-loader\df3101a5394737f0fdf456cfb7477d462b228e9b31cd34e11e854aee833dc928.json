{"ast": null, "code": "export var eDocs = {\n  name: 'e',\n  category: 'Constants',\n  syntax: ['e'],\n  description: '<PERSON><PERSON><PERSON>\\'s number, the base of the natural logarithm. Approximately equal to 2.71828',\n  examples: ['e', 'e ^ 2', 'exp(2)', 'log(e)'],\n  seealso: ['exp']\n};", "map": {"version": 3, "names": ["eDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/e.js"], "sourcesContent": ["export var eDocs = {\n  name: 'e',\n  category: 'Constants',\n  syntax: ['e'],\n  description: '<PERSON><PERSON><PERSON>\\'s number, the base of the natural logarithm. Approximately equal to 2.71828',\n  examples: ['e', 'e ^ 2', 'exp(2)', 'log(e)'],\n  seealso: ['exp']\n};"], "mappings": "AAAA,OAAO,IAAIA,KAAK,GAAG;EACjBC,IAAI,EAAE,GAAG;EACTC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,GAAG,CAAC;EACbC,WAAW,EAAE,oFAAoF;EACjGC,QAAQ,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;EAC5CC,OAAO,EAAE,CAAC,KAAK;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}