{"ast": null, "code": "/** @param {number} i\n *  @param {number} n\n *  @returns {number} product of i to n\n */\nexport function product(i, n) {\n  if (n < i) {\n    return 1;\n  }\n  if (n === i) {\n    return n;\n  }\n  var half = n + i >> 1; // divide (n + i) by 2 and truncate to integer\n  return product(i, half) * product(half + 1, n);\n}", "map": {"version": 3, "names": ["product", "i", "n", "half"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/product.js"], "sourcesContent": ["/** @param {number} i\n *  @param {number} n\n *  @returns {number} product of i to n\n */\nexport function product(i, n) {\n  if (n < i) {\n    return 1;\n  }\n  if (n === i) {\n    return n;\n  }\n  var half = n + i >> 1; // divide (n + i) by 2 and truncate to integer\n  return product(i, half) * product(half + 1, n);\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAIA,CAAC,GAAGD,CAAC,EAAE;IACT,OAAO,CAAC;EACV;EACA,IAAIC,CAAC,KAAKD,CAAC,EAAE;IACX,OAAOC,CAAC;EACV;EACA,IAAIC,IAAI,GAAGD,CAAC,GAAGD,CAAC,IAAI,CAAC,CAAC,CAAC;EACvB,OAAOD,OAAO,CAACC,CAAC,EAAEE,IAAI,CAAC,GAAGH,OAAO,CAACG,IAAI,GAAG,CAAC,EAAED,CAAC,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}