{"ast": null, "code": "export var ceilDocs = {\n  name: 'ceil',\n  category: 'Arithmetic',\n  syntax: ['ceil(x)', 'ceil(x, n)', 'ceil(unit, valuelessUnit)', 'ceil(unit, n, valuelessUnit)'],\n  description: 'Round a value towards plus infinity. If x is complex, both real and imaginary part are rounded towards plus infinity.',\n  examples: ['ceil(3.2)', 'ceil(3.8)', 'ceil(-4.2)', 'ceil(3.241cm, cm)', 'ceil(3.241cm, 2, cm)'],\n  seealso: ['floor', 'fix', 'round']\n};", "map": {"version": 3, "names": ["ceilD<PERSON>s", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/ceil.js"], "sourcesContent": ["export var ceilDocs = {\n  name: 'ceil',\n  category: 'Arithmetic',\n  syntax: ['ceil(x)', 'ceil(x, n)', 'ceil(unit, valuelessUnit)', 'ceil(unit, n, valuelessUnit)'],\n  description: 'Round a value towards plus infinity. If x is complex, both real and imaginary part are rounded towards plus infinity.',\n  examples: ['ceil(3.2)', 'ceil(3.8)', 'ceil(-4.2)', 'ceil(3.241cm, cm)', 'ceil(3.241cm, 2, cm)'],\n  seealso: ['floor', 'fix', 'round']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,2BAA2B,EAAE,8BAA8B,CAAC;EAC9FC,WAAW,EAAE,uHAAuH;EACpIC,QAAQ,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,mBAAmB,EAAE,sBAAsB,CAAC;EAC/FC,OAAO,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO;AACnC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}