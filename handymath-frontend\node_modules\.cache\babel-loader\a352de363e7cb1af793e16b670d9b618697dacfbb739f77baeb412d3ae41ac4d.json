{"ast": null, "code": "export var zerosDocs = {\n  name: 'zeros',\n  category: 'Matrix',\n  syntax: ['zeros(m)', 'zeros(m, n)', 'zeros(m, n, p, ...)', 'zeros([m])', 'zeros([m, n])', 'zeros([m, n, p, ...])'],\n  description: 'Create a matrix containing zeros.',\n  examples: ['zeros(3)', 'zeros(3, 5)', 'a = [1, 2, 3; 4, 5, 6]', 'zeros(size(a))'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose']\n};", "map": {"version": 3, "names": ["zerosDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/zeros.js"], "sourcesContent": ["export var zerosDocs = {\n  name: 'zeros',\n  category: 'Matrix',\n  syntax: ['zeros(m)', 'zeros(m, n)', 'zeros(m, n, p, ...)', 'zeros([m])', 'zeros([m, n])', 'zeros([m, n, p, ...])'],\n  description: 'Create a matrix containing zeros.',\n  examples: ['zeros(3)', 'zeros(3, 5)', 'a = [1, 2, 3; 4, 5, 6]', 'zeros(size(a))'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,qBAAqB,EAAE,YAAY,EAAE,eAAe,EAAE,uBAAuB,CAAC;EAClHC,WAAW,EAAE,mCAAmC;EAChDC,QAAQ,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,wBAAwB,EAAE,gBAAgB,CAAC;EACjFC,OAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW;AAC1H,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}