{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSech } from '../../factoriesAny.js';\nexport var sechDependencies = {\n  BigNumberDependencies,\n  typedDependencies,\n  createSech\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "typedDependencies", "createSech", "sechDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSech.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSech } from '../../factoriesAny.js';\nexport var sechDependencies = {\n  BigNumberDependencies,\n  typedDependencies,\n  createSech\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAO,IAAIC,gBAAgB,GAAG;EAC5BH,qBAAqB;EACrBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}