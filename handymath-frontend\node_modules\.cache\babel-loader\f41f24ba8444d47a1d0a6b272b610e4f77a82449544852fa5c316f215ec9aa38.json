{"ast": null, "code": "export var rangeDocs = {\n  name: 'range',\n  category: 'Type',\n  syntax: ['start:end', 'start:step:end', 'range(start, end)', 'range(start, end, step)', 'range(string)'],\n  description: 'Create a range. Lower bound of the range is included, upper bound is excluded.',\n  examples: ['1:5', '3:-1:-3', 'range(3, 7)', 'range(0, 12, 2)', 'range(\"4:10\")', 'range(1m, 1m, 3m)', 'a = [1, 2, 3, 4; 5, 6, 7, 8]', 'a[1:2, 1:2]'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};", "map": {"version": 3, "names": ["rangeDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/range.js"], "sourcesContent": ["export var rangeDocs = {\n  name: 'range',\n  category: 'Type',\n  syntax: ['start:end', 'start:step:end', 'range(start, end)', 'range(start, end, step)', 'range(string)'],\n  description: 'Create a range. Lower bound of the range is included, upper bound is excluded.',\n  examples: ['1:5', '3:-1:-3', 'range(3, 7)', 'range(0, 12, 2)', 'range(\"4:10\")', 'range(1m, 1m, 3m)', 'a = [1, 2, 3, 4; 5, 6, 7, 8]', 'a[1:2, 1:2]'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,yBAAyB,EAAE,eAAe,CAAC;EACxGC,WAAW,EAAE,gFAAgF;EAC7FC,QAAQ,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,iBAAiB,EAAE,eAAe,EAAE,mBAAmB,EAAE,8BAA8B,EAAE,aAAa,CAAC;EACnJC,OAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO;AAC1H,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}