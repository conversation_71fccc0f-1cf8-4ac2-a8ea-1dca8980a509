{"ast": null, "code": "// (c) 2018, <PERSON><PERSON>\n// SPDX-License-Identifier: ISC\n// Derived from https://github.com/medikoo/lru-queue\nexport function lruQueue(limit) {\n  var size = 0;\n  var base = 1;\n  var queue = Object.create(null);\n  var map = Object.create(null);\n  var index = 0;\n  var del = function del(id) {\n    var oldIndex = map[id];\n    if (!oldIndex) return;\n    delete queue[oldIndex];\n    delete map[id];\n    --size;\n    if (base !== oldIndex) return;\n    if (!size) {\n      index = 0;\n      base = 1;\n      return;\n    }\n    while (!Object.prototype.hasOwnProperty.call(queue, ++base)) {/* empty */}\n  };\n  limit = Math.abs(limit);\n  return {\n    hit: function hit(id) {\n      var oldIndex = map[id];\n      var nuIndex = ++index;\n      queue[nuIndex] = id;\n      map[id] = nuIndex;\n      if (!oldIndex) {\n        ++size;\n        if (size <= limit) return undefined;\n        id = queue[base];\n        del(id);\n        return id;\n      }\n      delete queue[oldIndex];\n      if (base !== oldIndex) return undefined;\n      while (!Object.prototype.hasOwnProperty.call(queue, ++base)) {/* empty */}\n      return undefined;\n    },\n    delete: del,\n    clear: function clear() {\n      size = index = 0;\n      base = 1;\n      queue = Object.create(null);\n      map = Object.create(null);\n    }\n  };\n}", "map": {"version": 3, "names": ["lruQueue", "limit", "size", "base", "queue", "Object", "create", "map", "index", "del", "id", "oldIndex", "prototype", "hasOwnProperty", "call", "Math", "abs", "hit", "nuIndex", "undefined", "delete", "clear"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/lruQueue.js"], "sourcesContent": ["// (c) 2018, <PERSON><PERSON>\n// SPDX-License-Identifier: ISC\n// Derived from https://github.com/medikoo/lru-queue\nexport function lruQueue(limit) {\n  var size = 0;\n  var base = 1;\n  var queue = Object.create(null);\n  var map = Object.create(null);\n  var index = 0;\n  var del = function del(id) {\n    var oldIndex = map[id];\n    if (!oldIndex) return;\n    delete queue[oldIndex];\n    delete map[id];\n    --size;\n    if (base !== oldIndex) return;\n    if (!size) {\n      index = 0;\n      base = 1;\n      return;\n    }\n    while (!Object.prototype.hasOwnProperty.call(queue, ++base)) {/* empty */}\n  };\n  limit = Math.abs(limit);\n  return {\n    hit: function hit(id) {\n      var oldIndex = map[id];\n      var nuIndex = ++index;\n      queue[nuIndex] = id;\n      map[id] = nuIndex;\n      if (!oldIndex) {\n        ++size;\n        if (size <= limit) return undefined;\n        id = queue[base];\n        del(id);\n        return id;\n      }\n      delete queue[oldIndex];\n      if (base !== oldIndex) return undefined;\n      while (!Object.prototype.hasOwnProperty.call(queue, ++base)) {/* empty */}\n      return undefined;\n    },\n    delete: del,\n    clear: function clear() {\n      size = index = 0;\n      base = 1;\n      queue = Object.create(null);\n      map = Object.create(null);\n    }\n  };\n}"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,SAASA,QAAQA,CAACC,KAAK,EAAE;EAC9B,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC/B,IAAIC,GAAG,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC7B,IAAIE,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAG,SAASA,GAAGA,CAACC,EAAE,EAAE;IACzB,IAAIC,QAAQ,GAAGJ,GAAG,CAACG,EAAE,CAAC;IACtB,IAAI,CAACC,QAAQ,EAAE;IACf,OAAOP,KAAK,CAACO,QAAQ,CAAC;IACtB,OAAOJ,GAAG,CAACG,EAAE,CAAC;IACd,EAAER,IAAI;IACN,IAAIC,IAAI,KAAKQ,QAAQ,EAAE;IACvB,IAAI,CAACT,IAAI,EAAE;MACTM,KAAK,GAAG,CAAC;MACTL,IAAI,GAAG,CAAC;MACR;IACF;IACA,OAAO,CAACE,MAAM,CAACO,SAAS,CAACC,cAAc,CAACC,IAAI,CAACV,KAAK,EAAE,EAAED,IAAI,CAAC,EAAE,CAAC;EAChE,CAAC;EACDF,KAAK,GAAGc,IAAI,CAACC,GAAG,CAACf,KAAK,CAAC;EACvB,OAAO;IACLgB,GAAG,EAAE,SAASA,GAAGA,CAACP,EAAE,EAAE;MACpB,IAAIC,QAAQ,GAAGJ,GAAG,CAACG,EAAE,CAAC;MACtB,IAAIQ,OAAO,GAAG,EAAEV,KAAK;MACrBJ,KAAK,CAACc,OAAO,CAAC,GAAGR,EAAE;MACnBH,GAAG,CAACG,EAAE,CAAC,GAAGQ,OAAO;MACjB,IAAI,CAACP,QAAQ,EAAE;QACb,EAAET,IAAI;QACN,IAAIA,IAAI,IAAID,KAAK,EAAE,OAAOkB,SAAS;QACnCT,EAAE,GAAGN,KAAK,CAACD,IAAI,CAAC;QAChBM,GAAG,CAACC,EAAE,CAAC;QACP,OAAOA,EAAE;MACX;MACA,OAAON,KAAK,CAACO,QAAQ,CAAC;MACtB,IAAIR,IAAI,KAAKQ,QAAQ,EAAE,OAAOQ,SAAS;MACvC,OAAO,CAACd,MAAM,CAACO,SAAS,CAACC,cAAc,CAACC,IAAI,CAACV,KAAK,EAAE,EAAED,IAAI,CAAC,EAAE,CAAC;MAC9D,OAAOgB,SAAS;IAClB,CAAC;IACDC,MAAM,EAAEX,GAAG;IACXY,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtBnB,IAAI,GAAGM,KAAK,GAAG,CAAC;MAChBL,IAAI,GAAG,CAAC;MACRC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAC3BC,GAAG,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}