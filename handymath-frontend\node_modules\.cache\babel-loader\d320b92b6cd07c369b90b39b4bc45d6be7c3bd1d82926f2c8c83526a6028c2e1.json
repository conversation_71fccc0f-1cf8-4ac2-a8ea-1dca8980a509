{"ast": null, "code": "export var configDocs = {\n  name: 'config',\n  category: 'Core',\n  syntax: ['config()', 'config(options)'],\n  description: 'Get configuration or change configuration.',\n  examples: ['config()', '1/3 + 1/4', 'config({number: \"Fraction\"})', '1/3 + 1/4'],\n  seealso: []\n};", "map": {"version": 3, "names": ["configDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/core/config.js"], "sourcesContent": ["export var configDocs = {\n  name: 'config',\n  category: 'Core',\n  syntax: ['config()', 'config(options)'],\n  description: 'Get configuration or change configuration.',\n  examples: ['config()', '1/3 + 1/4', 'config({number: \"Fraction\"})', '1/3 + 1/4'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,CAAC,UAAU,EAAE,iBAAiB,CAAC;EACvCC,WAAW,EAAE,4CAA4C;EACzDC,QAAQ,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,8BAA8B,EAAE,WAAW,CAAC;EAChFC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}