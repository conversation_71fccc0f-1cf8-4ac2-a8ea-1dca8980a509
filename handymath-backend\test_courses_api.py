#!/usr/bin/env python3
"""
Script pour tester l'API des cours
"""

import os
import sys
import django
import requests
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from api.models import Course, CourseEnrollment
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

def get_test_token():
    """Créer un token de test pour un utilisateur"""
    # C<PERSON>er ou récupérer un utilisateur de test
    user, created = User.objects.get_or_create(
        username='test_student',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'Student',
            'role': 'student'
        }
    )
    
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"✅ Utilisateur de test créé: {user.username}")
    else:
        print(f"📚 Utilisateur de test existant: {user.username}")
    
    # G<PERSON><PERSON>rer un token JWT
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    return access_token, user

def test_courses_api():
    """Tester l'API des cours"""
    print("🚀 Test de l'API des cours\n")
    
    # Obtenir un token de test
    token, user = get_test_token()
    
    # Headers pour les requêtes
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # URL de base
    base_url = 'http://localhost:8000/api'
    
    print("📊 Statistiques de la base de données:")
    print(f"   Cours total: {Course.objects.count()}")
    print(f"   Cours publiés: {Course.objects.filter(status='published').count()}")
    print(f"   Cours en vedette: {Course.objects.filter(is_featured=True).count()}")
    
    # Afficher quelques cours
    print("\n📚 Premiers cours dans la base:")
    for course in Course.objects.all()[:5]:
        print(f"   - {course.title} (niveau: {course.level}, statut: {course.status})")
    
    print(f"\n🔑 Token généré pour l'utilisateur: {user.username}")
    print(f"Token: {token[:50]}...")
    
    # Test 1: Endpoint get_structured_courses
    print("\n🧪 Test 1: Endpoint get_structured_courses (/api/courses/)")
    try:
        response = requests.get(f'{base_url}/courses/', headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Succès! Structure de la réponse:")
            print(f"   Clés principales: {list(data.keys())}")
            
            if 'courses' in data:
                courses = data['courses']
                print(f"   Nombre de cours: {len(courses)}")
                if courses:
                    print(f"   Premier cours: {courses[0]['title']}")
                    print(f"   Clés du cours: {list(courses[0].keys())}")
            
            if 'pagination' in data:
                pagination = data['pagination']
                print(f"   Pagination: {pagination}")
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Réponse: {response.text}")
            
    except Exception as e:
        print(f"❌ Erreur lors de la requête: {e}")
    
    # Test 2: Endpoint CourseViewSet standard
    print("\n🧪 Test 2: Endpoint CourseViewSet standard")
    try:
        # Tester l'endpoint DRF standard
        from django.test import RequestFactory
        from api.views import CourseViewSet
        from rest_framework.test import force_authenticate
        
        factory = RequestFactory()
        request = factory.get('/api/courses/')
        force_authenticate(request, user=user)
        
        view = CourseViewSet.as_view({'get': 'list'})
        response = view(request)
        
        print(f"Status: {response.status_code}")
        if hasattr(response, 'data'):
            print(f"✅ Succès! Nombre de cours: {len(response.data)}")
            if response.data:
                print(f"   Premier cours: {response.data[0]['title']}")
                print(f"   Clés du cours: {list(response.data[0].keys())}")
        
    except Exception as e:
        print(f"❌ Erreur lors du test ViewSet: {e}")
    
    print("\n🎯 Recommandations:")
    print("1. Vérifiez que le serveur Django fonctionne sur le port 8000")
    print("2. Vérifiez que l'authentification JWT fonctionne")
    print("3. Vérifiez la configuration des URLs")

def test_direct_database():
    """Test direct de la base de données"""
    print("\n🗄️ Test direct de la base de données:")
    
    courses = Course.objects.filter(status='published').order_by('order', 'title')
    print(f"Cours publiés trouvés: {courses.count()}")
    
    for i, course in enumerate(courses[:10], 1):
        print(f"{i:2d}. {course.title}")
        print(f"    Niveau: {course.get_level_display()}")
        print(f"    Durée: {course.estimated_duration} min")
        print(f"    En vedette: {'Oui' if course.is_featured else 'Non'}")
        print(f"    Chapitres: {course.chapters.count()}")
        print()

if __name__ == "__main__":
    test_direct_database()
    test_courses_api()
