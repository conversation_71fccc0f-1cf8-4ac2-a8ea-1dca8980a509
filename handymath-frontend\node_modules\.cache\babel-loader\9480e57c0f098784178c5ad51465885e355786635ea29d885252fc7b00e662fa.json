{"ast": null, "code": "export var acotDocs = {\n  name: 'acot',\n  category: 'Trigonometry',\n  syntax: ['acot(x)'],\n  description: 'Calculate the inverse cotangent of a value.',\n  examples: ['acot(0.5)', 'acot(cot(0.5))', 'acot(2)'],\n  seealso: ['cot', 'atan']\n};", "map": {"version": 3, "names": ["acotDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/acot.js"], "sourcesContent": ["export var acotDocs = {\n  name: 'acot',\n  category: 'Trigonometry',\n  syntax: ['acot(x)'],\n  description: 'Calculate the inverse cotangent of a value.',\n  examples: ['acot(0.5)', 'acot(cot(0.5))', 'acot(2)'],\n  seealso: ['cot', 'atan']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,6CAA6C;EAC1DC,QAAQ,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,SAAS,CAAC;EACpDC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}