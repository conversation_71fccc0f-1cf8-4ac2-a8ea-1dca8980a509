{"ast": null, "code": "import { clone } from '../../../utils/object.js';\nexport function createRealSymmetric(_ref) {\n  var {\n    config,\n    addScalar,\n    subtract,\n    abs,\n    atan,\n    cos,\n    sin,\n    multiplyScalar,\n    inv,\n    bignumber,\n    multiply,\n    add\n  } = _ref;\n  /**\n   * @param {number[] | BigNumber[]} arr\n   * @param {number} N\n   * @param {number} prec\n   * @param {'number' | 'BigNumber'} type\n   */\n  function main(arr, N) {\n    var prec = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : config.relTol;\n    var type = arguments.length > 3 ? arguments[3] : undefined;\n    var computeVectors = arguments.length > 4 ? arguments[4] : undefined;\n    if (type === 'number') {\n      return diag(arr, prec, computeVectors);\n    }\n    if (type === 'BigNumber') {\n      return diagBig(arr, prec, computeVectors);\n    }\n    throw TypeError('Unsupported data type: ' + type);\n  }\n\n  // diagonalization implementation for number (efficient)\n  function diag(x, precision, computeVectors) {\n    var N = x.length;\n    var e0 = Math.abs(precision / N);\n    var psi;\n    var Sij;\n    if (computeVectors) {\n      Sij = new Array(N);\n      // Sij is Identity Matrix\n      for (var i = 0; i < N; i++) {\n        Sij[i] = Array(N).fill(0);\n        Sij[i][i] = 1.0;\n      }\n    }\n    // initial error\n    var Vab = getAij(x);\n    while (Math.abs(Vab[1]) >= Math.abs(e0)) {\n      var _i = Vab[0][0];\n      var j = Vab[0][1];\n      psi = getTheta(x[_i][_i], x[j][j], x[_i][j]);\n      x = x1(x, psi, _i, j);\n      if (computeVectors) Sij = Sij1(Sij, psi, _i, j);\n      Vab = getAij(x);\n    }\n    var Ei = Array(N).fill(0); // eigenvalues\n    for (var _i2 = 0; _i2 < N; _i2++) {\n      Ei[_i2] = x[_i2][_i2];\n    }\n    return sorting(clone(Ei), Sij, computeVectors);\n  }\n\n  // diagonalization implementation for bigNumber\n  function diagBig(x, precision, computeVectors) {\n    var N = x.length;\n    var e0 = abs(precision / N);\n    var psi;\n    var Sij;\n    if (computeVectors) {\n      Sij = new Array(N);\n      // Sij is Identity Matrix\n      for (var i = 0; i < N; i++) {\n        Sij[i] = Array(N).fill(0);\n        Sij[i][i] = 1.0;\n      }\n    }\n    // initial error\n    var Vab = getAijBig(x);\n    while (abs(Vab[1]) >= abs(e0)) {\n      var _i3 = Vab[0][0];\n      var j = Vab[0][1];\n      psi = getThetaBig(x[_i3][_i3], x[j][j], x[_i3][j]);\n      x = x1Big(x, psi, _i3, j);\n      if (computeVectors) Sij = Sij1Big(Sij, psi, _i3, j);\n      Vab = getAijBig(x);\n    }\n    var Ei = Array(N).fill(0); // eigenvalues\n    for (var _i4 = 0; _i4 < N; _i4++) {\n      Ei[_i4] = x[_i4][_i4];\n    }\n    // return [clone(Ei), clone(Sij)]\n    return sorting(clone(Ei), Sij, computeVectors);\n  }\n\n  // get angle\n  function getTheta(aii, ajj, aij) {\n    var denom = ajj - aii;\n    if (Math.abs(denom) <= config.relTol) {\n      return Math.PI / 4.0;\n    } else {\n      return 0.5 * Math.atan(2.0 * aij / (ajj - aii));\n    }\n  }\n\n  // get angle\n  function getThetaBig(aii, ajj, aij) {\n    var denom = subtract(ajj, aii);\n    if (abs(denom) <= config.relTol) {\n      return bignumber(-1).acos().div(4);\n    } else {\n      return multiplyScalar(0.5, atan(multiply(2.0, aij, inv(denom))));\n    }\n  }\n\n  // update eigvec\n  function Sij1(Sij, theta, i, j) {\n    var N = Sij.length;\n    var c = Math.cos(theta);\n    var s = Math.sin(theta);\n    var Ski = Array(N).fill(0);\n    var Skj = Array(N).fill(0);\n    for (var k = 0; k < N; k++) {\n      Ski[k] = c * Sij[k][i] - s * Sij[k][j];\n      Skj[k] = s * Sij[k][i] + c * Sij[k][j];\n    }\n    for (var _k = 0; _k < N; _k++) {\n      Sij[_k][i] = Ski[_k];\n      Sij[_k][j] = Skj[_k];\n    }\n    return Sij;\n  }\n  // update eigvec for overlap\n  function Sij1Big(Sij, theta, i, j) {\n    var N = Sij.length;\n    var c = cos(theta);\n    var s = sin(theta);\n    var Ski = Array(N).fill(bignumber(0));\n    var Skj = Array(N).fill(bignumber(0));\n    for (var k = 0; k < N; k++) {\n      Ski[k] = subtract(multiplyScalar(c, Sij[k][i]), multiplyScalar(s, Sij[k][j]));\n      Skj[k] = addScalar(multiplyScalar(s, Sij[k][i]), multiplyScalar(c, Sij[k][j]));\n    }\n    for (var _k2 = 0; _k2 < N; _k2++) {\n      Sij[_k2][i] = Ski[_k2];\n      Sij[_k2][j] = Skj[_k2];\n    }\n    return Sij;\n  }\n\n  // update matrix\n  function x1Big(Hij, theta, i, j) {\n    var N = Hij.length;\n    var c = bignumber(cos(theta));\n    var s = bignumber(sin(theta));\n    var c2 = multiplyScalar(c, c);\n    var s2 = multiplyScalar(s, s);\n    var Aki = Array(N).fill(bignumber(0));\n    var Akj = Array(N).fill(bignumber(0));\n    // 2cs Hij\n    var csHij = multiply(bignumber(2), c, s, Hij[i][j]);\n    //  Aii\n    var Aii = addScalar(subtract(multiplyScalar(c2, Hij[i][i]), csHij), multiplyScalar(s2, Hij[j][j]));\n    var Ajj = add(multiplyScalar(s2, Hij[i][i]), csHij, multiplyScalar(c2, Hij[j][j]));\n    // 0  to i\n    for (var k = 0; k < N; k++) {\n      Aki[k] = subtract(multiplyScalar(c, Hij[i][k]), multiplyScalar(s, Hij[j][k]));\n      Akj[k] = addScalar(multiplyScalar(s, Hij[i][k]), multiplyScalar(c, Hij[j][k]));\n    }\n    // Modify Hij\n    Hij[i][i] = Aii;\n    Hij[j][j] = Ajj;\n    Hij[i][j] = bignumber(0);\n    Hij[j][i] = bignumber(0);\n    // 0  to i\n    for (var _k3 = 0; _k3 < N; _k3++) {\n      if (_k3 !== i && _k3 !== j) {\n        Hij[i][_k3] = Aki[_k3];\n        Hij[_k3][i] = Aki[_k3];\n        Hij[j][_k3] = Akj[_k3];\n        Hij[_k3][j] = Akj[_k3];\n      }\n    }\n    return Hij;\n  }\n\n  // update matrix\n  function x1(Hij, theta, i, j) {\n    var N = Hij.length;\n    var c = Math.cos(theta);\n    var s = Math.sin(theta);\n    var c2 = c * c;\n    var s2 = s * s;\n    var Aki = Array(N).fill(0);\n    var Akj = Array(N).fill(0);\n    //  Aii\n    var Aii = c2 * Hij[i][i] - 2 * c * s * Hij[i][j] + s2 * Hij[j][j];\n    var Ajj = s2 * Hij[i][i] + 2 * c * s * Hij[i][j] + c2 * Hij[j][j];\n    // 0  to i\n    for (var k = 0; k < N; k++) {\n      Aki[k] = c * Hij[i][k] - s * Hij[j][k];\n      Akj[k] = s * Hij[i][k] + c * Hij[j][k];\n    }\n    // Modify Hij\n    Hij[i][i] = Aii;\n    Hij[j][j] = Ajj;\n    Hij[i][j] = 0;\n    Hij[j][i] = 0;\n    // 0  to i\n    for (var _k4 = 0; _k4 < N; _k4++) {\n      if (_k4 !== i && _k4 !== j) {\n        Hij[i][_k4] = Aki[_k4];\n        Hij[_k4][i] = Aki[_k4];\n        Hij[j][_k4] = Akj[_k4];\n        Hij[_k4][j] = Akj[_k4];\n      }\n    }\n    return Hij;\n  }\n\n  // get max off-diagonal value from Upper Diagonal\n  function getAij(Mij) {\n    var N = Mij.length;\n    var maxMij = 0;\n    var maxIJ = [0, 1];\n    for (var i = 0; i < N; i++) {\n      for (var j = i + 1; j < N; j++) {\n        if (Math.abs(maxMij) < Math.abs(Mij[i][j])) {\n          maxMij = Math.abs(Mij[i][j]);\n          maxIJ = [i, j];\n        }\n      }\n    }\n    return [maxIJ, maxMij];\n  }\n\n  // get max off-diagonal value from Upper Diagonal\n  function getAijBig(Mij) {\n    var N = Mij.length;\n    var maxMij = 0;\n    var maxIJ = [0, 1];\n    for (var i = 0; i < N; i++) {\n      for (var j = i + 1; j < N; j++) {\n        if (abs(maxMij) < abs(Mij[i][j])) {\n          maxMij = abs(Mij[i][j]);\n          maxIJ = [i, j];\n        }\n      }\n    }\n    return [maxIJ, maxMij];\n  }\n\n  // sort results\n  function sorting(E, S, computeVectors) {\n    var N = E.length;\n    var values = Array(N);\n    var vecs;\n    if (computeVectors) {\n      vecs = Array(N);\n      for (var k = 0; k < N; k++) {\n        vecs[k] = Array(N);\n      }\n    }\n    for (var i = 0; i < N; i++) {\n      var minID = 0;\n      var minE = E[0];\n      for (var j = 0; j < E.length; j++) {\n        if (abs(E[j]) < abs(minE)) {\n          minID = j;\n          minE = E[minID];\n        }\n      }\n      values[i] = E.splice(minID, 1)[0];\n      if (computeVectors) {\n        for (var _k5 = 0; _k5 < N; _k5++) {\n          vecs[i][_k5] = S[_k5][minID];\n          S[_k5].splice(minID, 1);\n        }\n      }\n    }\n    if (!computeVectors) return {\n      values\n    };\n    var eigenvectors = vecs.map((vector, i) => ({\n      value: values[i],\n      vector\n    }));\n    return {\n      values,\n      eigenvectors\n    };\n  }\n  return main;\n}", "map": {"version": 3, "names": ["clone", "createRealSymmetric", "_ref", "config", "addScalar", "subtract", "abs", "atan", "cos", "sin", "multiplyScalar", "inv", "bignumber", "multiply", "add", "main", "arr", "N", "prec", "arguments", "length", "undefined", "relTol", "type", "computeVectors", "diag", "diagBig", "TypeError", "x", "precision", "e0", "Math", "psi", "<PERSON><PERSON>", "Array", "i", "fill", "Vab", "get<PERSON><PERSON><PERSON>", "_i", "j", "get<PERSON><PERSON><PERSON>", "x1", "Sij1", "<PERSON>i", "_i2", "sorting", "getAijBig", "_i3", "getThetaBig", "x1Big", "Sij1Big", "_i4", "aii", "ajj", "aij", "denom", "PI", "acos", "div", "theta", "c", "s", "Ski", "Skj", "k", "_k", "_k2", "<PERSON>j", "c2", "s2", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "csHij", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "_k3", "_k4", "<PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "maxIJ", "E", "S", "values", "vecs", "minID", "minE", "splice", "_k5", "eigenvectors", "map", "vector", "value"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/matrix/eigs/realSymmetric.js"], "sourcesContent": ["import { clone } from '../../../utils/object.js';\nexport function createRealSymmetric(_ref) {\n  var {\n    config,\n    addScalar,\n    subtract,\n    abs,\n    atan,\n    cos,\n    sin,\n    multiplyScalar,\n    inv,\n    bignumber,\n    multiply,\n    add\n  } = _ref;\n  /**\n   * @param {number[] | BigNumber[]} arr\n   * @param {number} N\n   * @param {number} prec\n   * @param {'number' | 'BigNumber'} type\n   */\n  function main(arr, N) {\n    var prec = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : config.relTol;\n    var type = arguments.length > 3 ? arguments[3] : undefined;\n    var computeVectors = arguments.length > 4 ? arguments[4] : undefined;\n    if (type === 'number') {\n      return diag(arr, prec, computeVectors);\n    }\n    if (type === 'BigNumber') {\n      return diagBig(arr, prec, computeVectors);\n    }\n    throw TypeError('Unsupported data type: ' + type);\n  }\n\n  // diagonalization implementation for number (efficient)\n  function diag(x, precision, computeVectors) {\n    var N = x.length;\n    var e0 = Math.abs(precision / N);\n    var psi;\n    var Sij;\n    if (computeVectors) {\n      Sij = new Array(N);\n      // Sij is Identity Matrix\n      for (var i = 0; i < N; i++) {\n        Sij[i] = Array(N).fill(0);\n        Sij[i][i] = 1.0;\n      }\n    }\n    // initial error\n    var Vab = getAij(x);\n    while (Math.abs(Vab[1]) >= Math.abs(e0)) {\n      var _i = Vab[0][0];\n      var j = Vab[0][1];\n      psi = getTheta(x[_i][_i], x[j][j], x[_i][j]);\n      x = x1(x, psi, _i, j);\n      if (computeVectors) Sij = Sij1(Sij, psi, _i, j);\n      Vab = getAij(x);\n    }\n    var Ei = Array(N).fill(0); // eigenvalues\n    for (var _i2 = 0; _i2 < N; _i2++) {\n      Ei[_i2] = x[_i2][_i2];\n    }\n    return sorting(clone(Ei), Sij, computeVectors);\n  }\n\n  // diagonalization implementation for bigNumber\n  function diagBig(x, precision, computeVectors) {\n    var N = x.length;\n    var e0 = abs(precision / N);\n    var psi;\n    var Sij;\n    if (computeVectors) {\n      Sij = new Array(N);\n      // Sij is Identity Matrix\n      for (var i = 0; i < N; i++) {\n        Sij[i] = Array(N).fill(0);\n        Sij[i][i] = 1.0;\n      }\n    }\n    // initial error\n    var Vab = getAijBig(x);\n    while (abs(Vab[1]) >= abs(e0)) {\n      var _i3 = Vab[0][0];\n      var j = Vab[0][1];\n      psi = getThetaBig(x[_i3][_i3], x[j][j], x[_i3][j]);\n      x = x1Big(x, psi, _i3, j);\n      if (computeVectors) Sij = Sij1Big(Sij, psi, _i3, j);\n      Vab = getAijBig(x);\n    }\n    var Ei = Array(N).fill(0); // eigenvalues\n    for (var _i4 = 0; _i4 < N; _i4++) {\n      Ei[_i4] = x[_i4][_i4];\n    }\n    // return [clone(Ei), clone(Sij)]\n    return sorting(clone(Ei), Sij, computeVectors);\n  }\n\n  // get angle\n  function getTheta(aii, ajj, aij) {\n    var denom = ajj - aii;\n    if (Math.abs(denom) <= config.relTol) {\n      return Math.PI / 4.0;\n    } else {\n      return 0.5 * Math.atan(2.0 * aij / (ajj - aii));\n    }\n  }\n\n  // get angle\n  function getThetaBig(aii, ajj, aij) {\n    var denom = subtract(ajj, aii);\n    if (abs(denom) <= config.relTol) {\n      return bignumber(-1).acos().div(4);\n    } else {\n      return multiplyScalar(0.5, atan(multiply(2.0, aij, inv(denom))));\n    }\n  }\n\n  // update eigvec\n  function Sij1(Sij, theta, i, j) {\n    var N = Sij.length;\n    var c = Math.cos(theta);\n    var s = Math.sin(theta);\n    var Ski = Array(N).fill(0);\n    var Skj = Array(N).fill(0);\n    for (var k = 0; k < N; k++) {\n      Ski[k] = c * Sij[k][i] - s * Sij[k][j];\n      Skj[k] = s * Sij[k][i] + c * Sij[k][j];\n    }\n    for (var _k = 0; _k < N; _k++) {\n      Sij[_k][i] = Ski[_k];\n      Sij[_k][j] = Skj[_k];\n    }\n    return Sij;\n  }\n  // update eigvec for overlap\n  function Sij1Big(Sij, theta, i, j) {\n    var N = Sij.length;\n    var c = cos(theta);\n    var s = sin(theta);\n    var Ski = Array(N).fill(bignumber(0));\n    var Skj = Array(N).fill(bignumber(0));\n    for (var k = 0; k < N; k++) {\n      Ski[k] = subtract(multiplyScalar(c, Sij[k][i]), multiplyScalar(s, Sij[k][j]));\n      Skj[k] = addScalar(multiplyScalar(s, Sij[k][i]), multiplyScalar(c, Sij[k][j]));\n    }\n    for (var _k2 = 0; _k2 < N; _k2++) {\n      Sij[_k2][i] = Ski[_k2];\n      Sij[_k2][j] = Skj[_k2];\n    }\n    return Sij;\n  }\n\n  // update matrix\n  function x1Big(Hij, theta, i, j) {\n    var N = Hij.length;\n    var c = bignumber(cos(theta));\n    var s = bignumber(sin(theta));\n    var c2 = multiplyScalar(c, c);\n    var s2 = multiplyScalar(s, s);\n    var Aki = Array(N).fill(bignumber(0));\n    var Akj = Array(N).fill(bignumber(0));\n    // 2cs Hij\n    var csHij = multiply(bignumber(2), c, s, Hij[i][j]);\n    //  Aii\n    var Aii = addScalar(subtract(multiplyScalar(c2, Hij[i][i]), csHij), multiplyScalar(s2, Hij[j][j]));\n    var Ajj = add(multiplyScalar(s2, Hij[i][i]), csHij, multiplyScalar(c2, Hij[j][j]));\n    // 0  to i\n    for (var k = 0; k < N; k++) {\n      Aki[k] = subtract(multiplyScalar(c, Hij[i][k]), multiplyScalar(s, Hij[j][k]));\n      Akj[k] = addScalar(multiplyScalar(s, Hij[i][k]), multiplyScalar(c, Hij[j][k]));\n    }\n    // Modify Hij\n    Hij[i][i] = Aii;\n    Hij[j][j] = Ajj;\n    Hij[i][j] = bignumber(0);\n    Hij[j][i] = bignumber(0);\n    // 0  to i\n    for (var _k3 = 0; _k3 < N; _k3++) {\n      if (_k3 !== i && _k3 !== j) {\n        Hij[i][_k3] = Aki[_k3];\n        Hij[_k3][i] = Aki[_k3];\n        Hij[j][_k3] = Akj[_k3];\n        Hij[_k3][j] = Akj[_k3];\n      }\n    }\n    return Hij;\n  }\n\n  // update matrix\n  function x1(Hij, theta, i, j) {\n    var N = Hij.length;\n    var c = Math.cos(theta);\n    var s = Math.sin(theta);\n    var c2 = c * c;\n    var s2 = s * s;\n    var Aki = Array(N).fill(0);\n    var Akj = Array(N).fill(0);\n    //  Aii\n    var Aii = c2 * Hij[i][i] - 2 * c * s * Hij[i][j] + s2 * Hij[j][j];\n    var Ajj = s2 * Hij[i][i] + 2 * c * s * Hij[i][j] + c2 * Hij[j][j];\n    // 0  to i\n    for (var k = 0; k < N; k++) {\n      Aki[k] = c * Hij[i][k] - s * Hij[j][k];\n      Akj[k] = s * Hij[i][k] + c * Hij[j][k];\n    }\n    // Modify Hij\n    Hij[i][i] = Aii;\n    Hij[j][j] = Ajj;\n    Hij[i][j] = 0;\n    Hij[j][i] = 0;\n    // 0  to i\n    for (var _k4 = 0; _k4 < N; _k4++) {\n      if (_k4 !== i && _k4 !== j) {\n        Hij[i][_k4] = Aki[_k4];\n        Hij[_k4][i] = Aki[_k4];\n        Hij[j][_k4] = Akj[_k4];\n        Hij[_k4][j] = Akj[_k4];\n      }\n    }\n    return Hij;\n  }\n\n  // get max off-diagonal value from Upper Diagonal\n  function getAij(Mij) {\n    var N = Mij.length;\n    var maxMij = 0;\n    var maxIJ = [0, 1];\n    for (var i = 0; i < N; i++) {\n      for (var j = i + 1; j < N; j++) {\n        if (Math.abs(maxMij) < Math.abs(Mij[i][j])) {\n          maxMij = Math.abs(Mij[i][j]);\n          maxIJ = [i, j];\n        }\n      }\n    }\n    return [maxIJ, maxMij];\n  }\n\n  // get max off-diagonal value from Upper Diagonal\n  function getAijBig(Mij) {\n    var N = Mij.length;\n    var maxMij = 0;\n    var maxIJ = [0, 1];\n    for (var i = 0; i < N; i++) {\n      for (var j = i + 1; j < N; j++) {\n        if (abs(maxMij) < abs(Mij[i][j])) {\n          maxMij = abs(Mij[i][j]);\n          maxIJ = [i, j];\n        }\n      }\n    }\n    return [maxIJ, maxMij];\n  }\n\n  // sort results\n  function sorting(E, S, computeVectors) {\n    var N = E.length;\n    var values = Array(N);\n    var vecs;\n    if (computeVectors) {\n      vecs = Array(N);\n      for (var k = 0; k < N; k++) {\n        vecs[k] = Array(N);\n      }\n    }\n    for (var i = 0; i < N; i++) {\n      var minID = 0;\n      var minE = E[0];\n      for (var j = 0; j < E.length; j++) {\n        if (abs(E[j]) < abs(minE)) {\n          minID = j;\n          minE = E[minID];\n        }\n      }\n      values[i] = E.splice(minID, 1)[0];\n      if (computeVectors) {\n        for (var _k5 = 0; _k5 < N; _k5++) {\n          vecs[i][_k5] = S[_k5][minID];\n          S[_k5].splice(minID, 1);\n        }\n      }\n    }\n    if (!computeVectors) return {\n      values\n    };\n    var eigenvectors = vecs.map((vector, i) => ({\n      value: values[i],\n      vector\n    }));\n    return {\n      values,\n      eigenvectors\n    };\n  }\n  return main;\n}"], "mappings": "AAAA,SAASA,KAAK,QAAQ,0BAA0B;AAChD,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,IAAI;IACFC,MAAM;IACNC,SAAS;IACTC,QAAQ;IACRC,GAAG;IACHC,IAAI;IACJC,GAAG;IACHC,GAAG;IACHC,cAAc;IACdC,GAAG;IACHC,SAAS;IACTC,QAAQ;IACRC;EACF,CAAC,GAAGZ,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;EACE,SAASa,IAAIA,CAACC,GAAG,EAAEC,CAAC,EAAE;IACpB,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGhB,MAAM,CAACmB,MAAM;IAC5F,IAAIC,IAAI,GAAGJ,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;IAC1D,IAAIG,cAAc,GAAGL,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;IACpE,IAAIE,IAAI,KAAK,QAAQ,EAAE;MACrB,OAAOE,IAAI,CAACT,GAAG,EAAEE,IAAI,EAAEM,cAAc,CAAC;IACxC;IACA,IAAID,IAAI,KAAK,WAAW,EAAE;MACxB,OAAOG,OAAO,CAACV,GAAG,EAAEE,IAAI,EAAEM,cAAc,CAAC;IAC3C;IACA,MAAMG,SAAS,CAAC,yBAAyB,GAAGJ,IAAI,CAAC;EACnD;;EAEA;EACA,SAASE,IAAIA,CAACG,CAAC,EAAEC,SAAS,EAAEL,cAAc,EAAE;IAC1C,IAAIP,CAAC,GAAGW,CAAC,CAACR,MAAM;IAChB,IAAIU,EAAE,GAAGC,IAAI,CAACzB,GAAG,CAACuB,SAAS,GAAGZ,CAAC,CAAC;IAChC,IAAIe,GAAG;IACP,IAAIC,GAAG;IACP,IAAIT,cAAc,EAAE;MAClBS,GAAG,GAAG,IAAIC,KAAK,CAACjB,CAAC,CAAC;MAClB;MACA,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,CAAC,EAAEkB,CAAC,EAAE,EAAE;QAC1BF,GAAG,CAACE,CAAC,CAAC,GAAGD,KAAK,CAACjB,CAAC,CAAC,CAACmB,IAAI,CAAC,CAAC,CAAC;QACzBH,GAAG,CAACE,CAAC,CAAC,CAACA,CAAC,CAAC,GAAG,GAAG;MACjB;IACF;IACA;IACA,IAAIE,GAAG,GAAGC,MAAM,CAACV,CAAC,CAAC;IACnB,OAAOG,IAAI,CAACzB,GAAG,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIN,IAAI,CAACzB,GAAG,CAACwB,EAAE,CAAC,EAAE;MACvC,IAAIS,EAAE,GAAGF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,IAAIG,CAAC,GAAGH,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjBL,GAAG,GAAGS,QAAQ,CAACb,CAAC,CAACW,EAAE,CAAC,CAACA,EAAE,CAAC,EAAEX,CAAC,CAACY,CAAC,CAAC,CAACA,CAAC,CAAC,EAAEZ,CAAC,CAACW,EAAE,CAAC,CAACC,CAAC,CAAC,CAAC;MAC5CZ,CAAC,GAAGc,EAAE,CAACd,CAAC,EAAEI,GAAG,EAAEO,EAAE,EAAEC,CAAC,CAAC;MACrB,IAAIhB,cAAc,EAAES,GAAG,GAAGU,IAAI,CAACV,GAAG,EAAED,GAAG,EAAEO,EAAE,EAAEC,CAAC,CAAC;MAC/CH,GAAG,GAAGC,MAAM,CAACV,CAAC,CAAC;IACjB;IACA,IAAIgB,EAAE,GAAGV,KAAK,CAACjB,CAAC,CAAC,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,KAAK,IAAIS,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG5B,CAAC,EAAE4B,GAAG,EAAE,EAAE;MAChCD,EAAE,CAACC,GAAG,CAAC,GAAGjB,CAAC,CAACiB,GAAG,CAAC,CAACA,GAAG,CAAC;IACvB;IACA,OAAOC,OAAO,CAAC9C,KAAK,CAAC4C,EAAE,CAAC,EAAEX,GAAG,EAAET,cAAc,CAAC;EAChD;;EAEA;EACA,SAASE,OAAOA,CAACE,CAAC,EAAEC,SAAS,EAAEL,cAAc,EAAE;IAC7C,IAAIP,CAAC,GAAGW,CAAC,CAACR,MAAM;IAChB,IAAIU,EAAE,GAAGxB,GAAG,CAACuB,SAAS,GAAGZ,CAAC,CAAC;IAC3B,IAAIe,GAAG;IACP,IAAIC,GAAG;IACP,IAAIT,cAAc,EAAE;MAClBS,GAAG,GAAG,IAAIC,KAAK,CAACjB,CAAC,CAAC;MAClB;MACA,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,CAAC,EAAEkB,CAAC,EAAE,EAAE;QAC1BF,GAAG,CAACE,CAAC,CAAC,GAAGD,KAAK,CAACjB,CAAC,CAAC,CAACmB,IAAI,CAAC,CAAC,CAAC;QACzBH,GAAG,CAACE,CAAC,CAAC,CAACA,CAAC,CAAC,GAAG,GAAG;MACjB;IACF;IACA;IACA,IAAIE,GAAG,GAAGU,SAAS,CAACnB,CAAC,CAAC;IACtB,OAAOtB,GAAG,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI/B,GAAG,CAACwB,EAAE,CAAC,EAAE;MAC7B,IAAIkB,GAAG,GAAGX,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,IAAIG,CAAC,GAAGH,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjBL,GAAG,GAAGiB,WAAW,CAACrB,CAAC,CAACoB,GAAG,CAAC,CAACA,GAAG,CAAC,EAAEpB,CAAC,CAACY,CAAC,CAAC,CAACA,CAAC,CAAC,EAAEZ,CAAC,CAACoB,GAAG,CAAC,CAACR,CAAC,CAAC,CAAC;MAClDZ,CAAC,GAAGsB,KAAK,CAACtB,CAAC,EAAEI,GAAG,EAAEgB,GAAG,EAAER,CAAC,CAAC;MACzB,IAAIhB,cAAc,EAAES,GAAG,GAAGkB,OAAO,CAAClB,GAAG,EAAED,GAAG,EAAEgB,GAAG,EAAER,CAAC,CAAC;MACnDH,GAAG,GAAGU,SAAS,CAACnB,CAAC,CAAC;IACpB;IACA,IAAIgB,EAAE,GAAGV,KAAK,CAACjB,CAAC,CAAC,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,KAAK,IAAIgB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGnC,CAAC,EAAEmC,GAAG,EAAE,EAAE;MAChCR,EAAE,CAACQ,GAAG,CAAC,GAAGxB,CAAC,CAACwB,GAAG,CAAC,CAACA,GAAG,CAAC;IACvB;IACA;IACA,OAAON,OAAO,CAAC9C,KAAK,CAAC4C,EAAE,CAAC,EAAEX,GAAG,EAAET,cAAc,CAAC;EAChD;;EAEA;EACA,SAASiB,QAAQA,CAACY,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE;IAC/B,IAAIC,KAAK,GAAGF,GAAG,GAAGD,GAAG;IACrB,IAAItB,IAAI,CAACzB,GAAG,CAACkD,KAAK,CAAC,IAAIrD,MAAM,CAACmB,MAAM,EAAE;MACpC,OAAOS,IAAI,CAAC0B,EAAE,GAAG,GAAG;IACtB,CAAC,MAAM;MACL,OAAO,GAAG,GAAG1B,IAAI,CAACxB,IAAI,CAAC,GAAG,GAAGgD,GAAG,IAAID,GAAG,GAAGD,GAAG,CAAC,CAAC;IACjD;EACF;;EAEA;EACA,SAASJ,WAAWA,CAACI,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE;IAClC,IAAIC,KAAK,GAAGnD,QAAQ,CAACiD,GAAG,EAAED,GAAG,CAAC;IAC9B,IAAI/C,GAAG,CAACkD,KAAK,CAAC,IAAIrD,MAAM,CAACmB,MAAM,EAAE;MAC/B,OAAOV,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC8C,IAAI,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC,MAAM;MACL,OAAOjD,cAAc,CAAC,GAAG,EAAEH,IAAI,CAACM,QAAQ,CAAC,GAAG,EAAE0C,GAAG,EAAE5C,GAAG,CAAC6C,KAAK,CAAC,CAAC,CAAC,CAAC;IAClE;EACF;;EAEA;EACA,SAASb,IAAIA,CAACV,GAAG,EAAE2B,KAAK,EAAEzB,CAAC,EAAEK,CAAC,EAAE;IAC9B,IAAIvB,CAAC,GAAGgB,GAAG,CAACb,MAAM;IAClB,IAAIyC,CAAC,GAAG9B,IAAI,CAACvB,GAAG,CAACoD,KAAK,CAAC;IACvB,IAAIE,CAAC,GAAG/B,IAAI,CAACtB,GAAG,CAACmD,KAAK,CAAC;IACvB,IAAIG,GAAG,GAAG7B,KAAK,CAACjB,CAAC,CAAC,CAACmB,IAAI,CAAC,CAAC,CAAC;IAC1B,IAAI4B,GAAG,GAAG9B,KAAK,CAACjB,CAAC,CAAC,CAACmB,IAAI,CAAC,CAAC,CAAC;IAC1B,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhD,CAAC,EAAEgD,CAAC,EAAE,EAAE;MAC1BF,GAAG,CAACE,CAAC,CAAC,GAAGJ,CAAC,GAAG5B,GAAG,CAACgC,CAAC,CAAC,CAAC9B,CAAC,CAAC,GAAG2B,CAAC,GAAG7B,GAAG,CAACgC,CAAC,CAAC,CAACzB,CAAC,CAAC;MACtCwB,GAAG,CAACC,CAAC,CAAC,GAAGH,CAAC,GAAG7B,GAAG,CAACgC,CAAC,CAAC,CAAC9B,CAAC,CAAC,GAAG0B,CAAC,GAAG5B,GAAG,CAACgC,CAAC,CAAC,CAACzB,CAAC,CAAC;IACxC;IACA,KAAK,IAAI0B,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGjD,CAAC,EAAEiD,EAAE,EAAE,EAAE;MAC7BjC,GAAG,CAACiC,EAAE,CAAC,CAAC/B,CAAC,CAAC,GAAG4B,GAAG,CAACG,EAAE,CAAC;MACpBjC,GAAG,CAACiC,EAAE,CAAC,CAAC1B,CAAC,CAAC,GAAGwB,GAAG,CAACE,EAAE,CAAC;IACtB;IACA,OAAOjC,GAAG;EACZ;EACA;EACA,SAASkB,OAAOA,CAAClB,GAAG,EAAE2B,KAAK,EAAEzB,CAAC,EAAEK,CAAC,EAAE;IACjC,IAAIvB,CAAC,GAAGgB,GAAG,CAACb,MAAM;IAClB,IAAIyC,CAAC,GAAGrD,GAAG,CAACoD,KAAK,CAAC;IAClB,IAAIE,CAAC,GAAGrD,GAAG,CAACmD,KAAK,CAAC;IAClB,IAAIG,GAAG,GAAG7B,KAAK,CAACjB,CAAC,CAAC,CAACmB,IAAI,CAACxB,SAAS,CAAC,CAAC,CAAC,CAAC;IACrC,IAAIoD,GAAG,GAAG9B,KAAK,CAACjB,CAAC,CAAC,CAACmB,IAAI,CAACxB,SAAS,CAAC,CAAC,CAAC,CAAC;IACrC,KAAK,IAAIqD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhD,CAAC,EAAEgD,CAAC,EAAE,EAAE;MAC1BF,GAAG,CAACE,CAAC,CAAC,GAAG5D,QAAQ,CAACK,cAAc,CAACmD,CAAC,EAAE5B,GAAG,CAACgC,CAAC,CAAC,CAAC9B,CAAC,CAAC,CAAC,EAAEzB,cAAc,CAACoD,CAAC,EAAE7B,GAAG,CAACgC,CAAC,CAAC,CAACzB,CAAC,CAAC,CAAC,CAAC;MAC7EwB,GAAG,CAACC,CAAC,CAAC,GAAG7D,SAAS,CAACM,cAAc,CAACoD,CAAC,EAAE7B,GAAG,CAACgC,CAAC,CAAC,CAAC9B,CAAC,CAAC,CAAC,EAAEzB,cAAc,CAACmD,CAAC,EAAE5B,GAAG,CAACgC,CAAC,CAAC,CAACzB,CAAC,CAAC,CAAC,CAAC;IAChF;IACA,KAAK,IAAI2B,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGlD,CAAC,EAAEkD,GAAG,EAAE,EAAE;MAChClC,GAAG,CAACkC,GAAG,CAAC,CAAChC,CAAC,CAAC,GAAG4B,GAAG,CAACI,GAAG,CAAC;MACtBlC,GAAG,CAACkC,GAAG,CAAC,CAAC3B,CAAC,CAAC,GAAGwB,GAAG,CAACG,GAAG,CAAC;IACxB;IACA,OAAOlC,GAAG;EACZ;;EAEA;EACA,SAASiB,KAAKA,CAACkB,GAAG,EAAER,KAAK,EAAEzB,CAAC,EAAEK,CAAC,EAAE;IAC/B,IAAIvB,CAAC,GAAGmD,GAAG,CAAChD,MAAM;IAClB,IAAIyC,CAAC,GAAGjD,SAAS,CAACJ,GAAG,CAACoD,KAAK,CAAC,CAAC;IAC7B,IAAIE,CAAC,GAAGlD,SAAS,CAACH,GAAG,CAACmD,KAAK,CAAC,CAAC;IAC7B,IAAIS,EAAE,GAAG3D,cAAc,CAACmD,CAAC,EAAEA,CAAC,CAAC;IAC7B,IAAIS,EAAE,GAAG5D,cAAc,CAACoD,CAAC,EAAEA,CAAC,CAAC;IAC7B,IAAIS,GAAG,GAAGrC,KAAK,CAACjB,CAAC,CAAC,CAACmB,IAAI,CAACxB,SAAS,CAAC,CAAC,CAAC,CAAC;IACrC,IAAI4D,GAAG,GAAGtC,KAAK,CAACjB,CAAC,CAAC,CAACmB,IAAI,CAACxB,SAAS,CAAC,CAAC,CAAC,CAAC;IACrC;IACA,IAAI6D,KAAK,GAAG5D,QAAQ,CAACD,SAAS,CAAC,CAAC,CAAC,EAAEiD,CAAC,EAAEC,CAAC,EAAEM,GAAG,CAACjC,CAAC,CAAC,CAACK,CAAC,CAAC,CAAC;IACnD;IACA,IAAIkC,GAAG,GAAGtE,SAAS,CAACC,QAAQ,CAACK,cAAc,CAAC2D,EAAE,EAAED,GAAG,CAACjC,CAAC,CAAC,CAACA,CAAC,CAAC,CAAC,EAAEsC,KAAK,CAAC,EAAE/D,cAAc,CAAC4D,EAAE,EAAEF,GAAG,CAAC5B,CAAC,CAAC,CAACA,CAAC,CAAC,CAAC,CAAC;IAClG,IAAImC,GAAG,GAAG7D,GAAG,CAACJ,cAAc,CAAC4D,EAAE,EAAEF,GAAG,CAACjC,CAAC,CAAC,CAACA,CAAC,CAAC,CAAC,EAAEsC,KAAK,EAAE/D,cAAc,CAAC2D,EAAE,EAAED,GAAG,CAAC5B,CAAC,CAAC,CAACA,CAAC,CAAC,CAAC,CAAC;IAClF;IACA,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhD,CAAC,EAAEgD,CAAC,EAAE,EAAE;MAC1BM,GAAG,CAACN,CAAC,CAAC,GAAG5D,QAAQ,CAACK,cAAc,CAACmD,CAAC,EAAEO,GAAG,CAACjC,CAAC,CAAC,CAAC8B,CAAC,CAAC,CAAC,EAAEvD,cAAc,CAACoD,CAAC,EAAEM,GAAG,CAAC5B,CAAC,CAAC,CAACyB,CAAC,CAAC,CAAC,CAAC;MAC7EO,GAAG,CAACP,CAAC,CAAC,GAAG7D,SAAS,CAACM,cAAc,CAACoD,CAAC,EAAEM,GAAG,CAACjC,CAAC,CAAC,CAAC8B,CAAC,CAAC,CAAC,EAAEvD,cAAc,CAACmD,CAAC,EAAEO,GAAG,CAAC5B,CAAC,CAAC,CAACyB,CAAC,CAAC,CAAC,CAAC;IAChF;IACA;IACAG,GAAG,CAACjC,CAAC,CAAC,CAACA,CAAC,CAAC,GAAGuC,GAAG;IACfN,GAAG,CAAC5B,CAAC,CAAC,CAACA,CAAC,CAAC,GAAGmC,GAAG;IACfP,GAAG,CAACjC,CAAC,CAAC,CAACK,CAAC,CAAC,GAAG5B,SAAS,CAAC,CAAC,CAAC;IACxBwD,GAAG,CAAC5B,CAAC,CAAC,CAACL,CAAC,CAAC,GAAGvB,SAAS,CAAC,CAAC,CAAC;IACxB;IACA,KAAK,IAAIgE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG3D,CAAC,EAAE2D,GAAG,EAAE,EAAE;MAChC,IAAIA,GAAG,KAAKzC,CAAC,IAAIyC,GAAG,KAAKpC,CAAC,EAAE;QAC1B4B,GAAG,CAACjC,CAAC,CAAC,CAACyC,GAAG,CAAC,GAAGL,GAAG,CAACK,GAAG,CAAC;QACtBR,GAAG,CAACQ,GAAG,CAAC,CAACzC,CAAC,CAAC,GAAGoC,GAAG,CAACK,GAAG,CAAC;QACtBR,GAAG,CAAC5B,CAAC,CAAC,CAACoC,GAAG,CAAC,GAAGJ,GAAG,CAACI,GAAG,CAAC;QACtBR,GAAG,CAACQ,GAAG,CAAC,CAACpC,CAAC,CAAC,GAAGgC,GAAG,CAACI,GAAG,CAAC;MACxB;IACF;IACA,OAAOR,GAAG;EACZ;;EAEA;EACA,SAAS1B,EAAEA,CAAC0B,GAAG,EAAER,KAAK,EAAEzB,CAAC,EAAEK,CAAC,EAAE;IAC5B,IAAIvB,CAAC,GAAGmD,GAAG,CAAChD,MAAM;IAClB,IAAIyC,CAAC,GAAG9B,IAAI,CAACvB,GAAG,CAACoD,KAAK,CAAC;IACvB,IAAIE,CAAC,GAAG/B,IAAI,CAACtB,GAAG,CAACmD,KAAK,CAAC;IACvB,IAAIS,EAAE,GAAGR,CAAC,GAAGA,CAAC;IACd,IAAIS,EAAE,GAAGR,CAAC,GAAGA,CAAC;IACd,IAAIS,GAAG,GAAGrC,KAAK,CAACjB,CAAC,CAAC,CAACmB,IAAI,CAAC,CAAC,CAAC;IAC1B,IAAIoC,GAAG,GAAGtC,KAAK,CAACjB,CAAC,CAAC,CAACmB,IAAI,CAAC,CAAC,CAAC;IAC1B;IACA,IAAIsC,GAAG,GAAGL,EAAE,GAAGD,GAAG,CAACjC,CAAC,CAAC,CAACA,CAAC,CAAC,GAAG,CAAC,GAAG0B,CAAC,GAAGC,CAAC,GAAGM,GAAG,CAACjC,CAAC,CAAC,CAACK,CAAC,CAAC,GAAG8B,EAAE,GAAGF,GAAG,CAAC5B,CAAC,CAAC,CAACA,CAAC,CAAC;IACjE,IAAImC,GAAG,GAAGL,EAAE,GAAGF,GAAG,CAACjC,CAAC,CAAC,CAACA,CAAC,CAAC,GAAG,CAAC,GAAG0B,CAAC,GAAGC,CAAC,GAAGM,GAAG,CAACjC,CAAC,CAAC,CAACK,CAAC,CAAC,GAAG6B,EAAE,GAAGD,GAAG,CAAC5B,CAAC,CAAC,CAACA,CAAC,CAAC;IACjE;IACA,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhD,CAAC,EAAEgD,CAAC,EAAE,EAAE;MAC1BM,GAAG,CAACN,CAAC,CAAC,GAAGJ,CAAC,GAAGO,GAAG,CAACjC,CAAC,CAAC,CAAC8B,CAAC,CAAC,GAAGH,CAAC,GAAGM,GAAG,CAAC5B,CAAC,CAAC,CAACyB,CAAC,CAAC;MACtCO,GAAG,CAACP,CAAC,CAAC,GAAGH,CAAC,GAAGM,GAAG,CAACjC,CAAC,CAAC,CAAC8B,CAAC,CAAC,GAAGJ,CAAC,GAAGO,GAAG,CAAC5B,CAAC,CAAC,CAACyB,CAAC,CAAC;IACxC;IACA;IACAG,GAAG,CAACjC,CAAC,CAAC,CAACA,CAAC,CAAC,GAAGuC,GAAG;IACfN,GAAG,CAAC5B,CAAC,CAAC,CAACA,CAAC,CAAC,GAAGmC,GAAG;IACfP,GAAG,CAACjC,CAAC,CAAC,CAACK,CAAC,CAAC,GAAG,CAAC;IACb4B,GAAG,CAAC5B,CAAC,CAAC,CAACL,CAAC,CAAC,GAAG,CAAC;IACb;IACA,KAAK,IAAI0C,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG5D,CAAC,EAAE4D,GAAG,EAAE,EAAE;MAChC,IAAIA,GAAG,KAAK1C,CAAC,IAAI0C,GAAG,KAAKrC,CAAC,EAAE;QAC1B4B,GAAG,CAACjC,CAAC,CAAC,CAAC0C,GAAG,CAAC,GAAGN,GAAG,CAACM,GAAG,CAAC;QACtBT,GAAG,CAACS,GAAG,CAAC,CAAC1C,CAAC,CAAC,GAAGoC,GAAG,CAACM,GAAG,CAAC;QACtBT,GAAG,CAAC5B,CAAC,CAAC,CAACqC,GAAG,CAAC,GAAGL,GAAG,CAACK,GAAG,CAAC;QACtBT,GAAG,CAACS,GAAG,CAAC,CAACrC,CAAC,CAAC,GAAGgC,GAAG,CAACK,GAAG,CAAC;MACxB;IACF;IACA,OAAOT,GAAG;EACZ;;EAEA;EACA,SAAS9B,MAAMA,CAACwC,GAAG,EAAE;IACnB,IAAI7D,CAAC,GAAG6D,GAAG,CAAC1D,MAAM;IAClB,IAAI2D,MAAM,GAAG,CAAC;IACd,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAClB,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,CAAC,EAAEkB,CAAC,EAAE,EAAE;MAC1B,KAAK,IAAIK,CAAC,GAAGL,CAAC,GAAG,CAAC,EAAEK,CAAC,GAAGvB,CAAC,EAAEuB,CAAC,EAAE,EAAE;QAC9B,IAAIT,IAAI,CAACzB,GAAG,CAACyE,MAAM,CAAC,GAAGhD,IAAI,CAACzB,GAAG,CAACwE,GAAG,CAAC3C,CAAC,CAAC,CAACK,CAAC,CAAC,CAAC,EAAE;UAC1CuC,MAAM,GAAGhD,IAAI,CAACzB,GAAG,CAACwE,GAAG,CAAC3C,CAAC,CAAC,CAACK,CAAC,CAAC,CAAC;UAC5BwC,KAAK,GAAG,CAAC7C,CAAC,EAAEK,CAAC,CAAC;QAChB;MACF;IACF;IACA,OAAO,CAACwC,KAAK,EAAED,MAAM,CAAC;EACxB;;EAEA;EACA,SAAShC,SAASA,CAAC+B,GAAG,EAAE;IACtB,IAAI7D,CAAC,GAAG6D,GAAG,CAAC1D,MAAM;IAClB,IAAI2D,MAAM,GAAG,CAAC;IACd,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAClB,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,CAAC,EAAEkB,CAAC,EAAE,EAAE;MAC1B,KAAK,IAAIK,CAAC,GAAGL,CAAC,GAAG,CAAC,EAAEK,CAAC,GAAGvB,CAAC,EAAEuB,CAAC,EAAE,EAAE;QAC9B,IAAIlC,GAAG,CAACyE,MAAM,CAAC,GAAGzE,GAAG,CAACwE,GAAG,CAAC3C,CAAC,CAAC,CAACK,CAAC,CAAC,CAAC,EAAE;UAChCuC,MAAM,GAAGzE,GAAG,CAACwE,GAAG,CAAC3C,CAAC,CAAC,CAACK,CAAC,CAAC,CAAC;UACvBwC,KAAK,GAAG,CAAC7C,CAAC,EAAEK,CAAC,CAAC;QAChB;MACF;IACF;IACA,OAAO,CAACwC,KAAK,EAAED,MAAM,CAAC;EACxB;;EAEA;EACA,SAASjC,OAAOA,CAACmC,CAAC,EAAEC,CAAC,EAAE1D,cAAc,EAAE;IACrC,IAAIP,CAAC,GAAGgE,CAAC,CAAC7D,MAAM;IAChB,IAAI+D,MAAM,GAAGjD,KAAK,CAACjB,CAAC,CAAC;IACrB,IAAImE,IAAI;IACR,IAAI5D,cAAc,EAAE;MAClB4D,IAAI,GAAGlD,KAAK,CAACjB,CAAC,CAAC;MACf,KAAK,IAAIgD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhD,CAAC,EAAEgD,CAAC,EAAE,EAAE;QAC1BmB,IAAI,CAACnB,CAAC,CAAC,GAAG/B,KAAK,CAACjB,CAAC,CAAC;MACpB;IACF;IACA,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,CAAC,EAAEkB,CAAC,EAAE,EAAE;MAC1B,IAAIkD,KAAK,GAAG,CAAC;MACb,IAAIC,IAAI,GAAGL,CAAC,CAAC,CAAC,CAAC;MACf,KAAK,IAAIzC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyC,CAAC,CAAC7D,MAAM,EAAEoB,CAAC,EAAE,EAAE;QACjC,IAAIlC,GAAG,CAAC2E,CAAC,CAACzC,CAAC,CAAC,CAAC,GAAGlC,GAAG,CAACgF,IAAI,CAAC,EAAE;UACzBD,KAAK,GAAG7C,CAAC;UACT8C,IAAI,GAAGL,CAAC,CAACI,KAAK,CAAC;QACjB;MACF;MACAF,MAAM,CAAChD,CAAC,CAAC,GAAG8C,CAAC,CAACM,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,IAAI7D,cAAc,EAAE;QAClB,KAAK,IAAIgE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGvE,CAAC,EAAEuE,GAAG,EAAE,EAAE;UAChCJ,IAAI,CAACjD,CAAC,CAAC,CAACqD,GAAG,CAAC,GAAGN,CAAC,CAACM,GAAG,CAAC,CAACH,KAAK,CAAC;UAC5BH,CAAC,CAACM,GAAG,CAAC,CAACD,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QACzB;MACF;IACF;IACA,IAAI,CAAC7D,cAAc,EAAE,OAAO;MAC1B2D;IACF,CAAC;IACD,IAAIM,YAAY,GAAGL,IAAI,CAACM,GAAG,CAAC,CAACC,MAAM,EAAExD,CAAC,MAAM;MAC1CyD,KAAK,EAAET,MAAM,CAAChD,CAAC,CAAC;MAChBwD;IACF,CAAC,CAAC,CAAC;IACH,OAAO;MACLR,MAAM;MACNM;IACF,CAAC;EACH;EACA,OAAO1E,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}