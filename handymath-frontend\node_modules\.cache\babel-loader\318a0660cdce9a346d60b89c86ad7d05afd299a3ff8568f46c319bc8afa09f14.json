{"ast": null, "code": "export var matrixDocs = {\n  name: 'matrix',\n  category: 'Construction',\n  syntax: ['[]', '[a1, b1, ...; a2, b2, ...]', 'matrix()', 'matrix(\"dense\")', 'matrix([...])'],\n  description: 'Create a matrix.',\n  examples: ['[]', '[1, 2, 3]', '[1, 2, 3; 4, 5, 6]', 'matrix()', 'matrix([3, 4])', 'matrix([3, 4; 5, 6], \"sparse\")', 'matrix([3, 4; 5, 6], \"sparse\", \"number\")'],\n  seealso: ['bignumber', 'boolean', 'complex', 'index', 'number', 'string', 'unit', 'sparse']\n};", "map": {"version": 3, "names": ["matrixDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/construction/matrix.js"], "sourcesContent": ["export var matrixDocs = {\n  name: 'matrix',\n  category: 'Construction',\n  syntax: ['[]', '[a1, b1, ...; a2, b2, ...]', 'matrix()', 'matrix(\"dense\")', 'matrix([...])'],\n  description: 'Create a matrix.',\n  examples: ['[]', '[1, 2, 3]', '[1, 2, 3; 4, 5, 6]', 'matrix()', 'matrix([3, 4])', 'matrix([3, 4; 5, 6], \"sparse\")', 'matrix([3, 4; 5, 6], \"sparse\", \"number\")'],\n  seealso: ['bignumber', 'boolean', 'complex', 'index', 'number', 'string', 'unit', 'sparse']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,IAAI,EAAE,4BAA4B,EAAE,UAAU,EAAE,iBAAiB,EAAE,eAAe,CAAC;EAC5FC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,oBAAoB,EAAE,UAAU,EAAE,gBAAgB,EAAE,gCAAgC,EAAE,0CAA0C,CAAC;EAC/JC,OAAO,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ;AAC5F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}