{"ast": null, "code": "export var fractionDocs = {\n  name: 'fraction',\n  category: 'Construction',\n  syntax: ['fraction(num)', 'fraction(matrix)', 'fraction(num,den)', 'fraction({n: num, d: den})'],\n  description: 'Create a fraction from a number or from integer numerator and denominator.',\n  examples: ['fraction(0.125)', 'fraction(1, 3) + fraction(2, 5)', 'fraction({n: 333, d: 53})', 'fraction([sqrt(9), sqrt(10), sqrt(11)])'],\n  seealso: ['bignumber', 'boolean', 'complex', 'index', 'matrix', 'string', 'unit']\n};", "map": {"version": 3, "names": ["fractionDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/construction/fraction.js"], "sourcesContent": ["export var fractionDocs = {\n  name: 'fraction',\n  category: 'Construction',\n  syntax: ['fraction(num)', 'fraction(matrix)', 'fraction(num,den)', 'fraction({n: num, d: den})'],\n  description: 'Create a fraction from a number or from integer numerator and denominator.',\n  examples: ['fraction(0.125)', 'fraction(1, 3) + fraction(2, 5)', 'fraction({n: 333, d: 53})', 'fraction([sqrt(9), sqrt(10), sqrt(11)])'],\n  seealso: ['bignumber', 'boolean', 'complex', 'index', 'matrix', 'string', 'unit']\n};"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAG;EACxBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,eAAe,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,4BAA4B,CAAC;EAChGC,WAAW,EAAE,4EAA4E;EACzFC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,iCAAiC,EAAE,2BAA2B,EAAE,yCAAyC,CAAC;EACxIC,OAAO,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM;AAClF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}