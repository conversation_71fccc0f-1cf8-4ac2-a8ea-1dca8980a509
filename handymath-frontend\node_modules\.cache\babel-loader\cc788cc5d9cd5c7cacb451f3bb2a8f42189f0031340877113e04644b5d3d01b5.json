{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createNode } from '../../factoriesAny.js';\nexport var NodeDependencies = {\n  createNode\n};", "map": {"version": 3, "names": ["createNode", "NodeDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesNode.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createNode } from '../../factoriesAny.js';\nexport var NodeDependencies = {\n  createNode\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,UAAU,QAAQ,uBAAuB;AAClD,OAAO,IAAIC,gBAAgB,GAAG;EAC5BD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}