{"ast": null, "code": "export var secDocs = {\n  name: 'sec',\n  category: 'Trigonometry',\n  syntax: ['sec(x)'],\n  description: 'Compute the secant of x in radians. Defined as 1/cos(x)',\n  examples: ['sec(2)', '1 / cos(2)'],\n  seealso: ['cot', 'csc', 'cos']\n};", "map": {"version": 3, "names": ["secDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/sec.js"], "sourcesContent": ["export var secDocs = {\n  name: 'sec',\n  category: 'Trigonometry',\n  syntax: ['sec(x)'],\n  description: 'Compute the secant of x in radians. Defined as 1/cos(x)',\n  examples: ['sec(2)', '1 / cos(2)'],\n  seealso: ['cot', 'csc', 'cos']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,yDAAyD;EACtEC,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;EAClCC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}