{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\LessonDetailPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport api from '../services/api';\n\n// Types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LessonDetailPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useAuth();\n  const {\n    addNotification\n  } = useNotifications();\n\n  // Helper functions for notifications\n  const showSuccess = (title, message) => {\n    addNotification({\n      type: 'success',\n      title,\n      message\n    });\n  };\n  const showError = (title, message) => {\n    addNotification({\n      type: 'error',\n      title,\n      message\n    });\n  };\n\n  // State\n  const [lesson, setLesson] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [completing, setCompleting] = useState(false);\n  const [startTime] = useState(Date.now());\n\n  // Effects\n  useEffect(() => {\n    if (user && id) {\n      fetchLesson();\n    }\n  }, [user, id]);\n\n  // API Functions\n  const fetchLesson = async () => {\n    try {\n      setLoading(true);\n      const response = await api.get(`/api/lessons/${id}/`);\n      if (response.data) {\n        setLesson(response.data);\n      }\n    } catch (error) {\n      var _error$response, _error$response2;\n      console.error('Erreur lors de la récupération de la leçon:', error);\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 404) {\n        showError('Leçon non trouvée', 'La leçon demandée n\\'existe pas.');\n      } else if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 403) {\n        showError('Accès refusé', error.response.data.error || 'Vous n\\'avez pas accès à cette leçon.');\n      } else {\n        showError('Erreur', 'Impossible de charger la leçon');\n      }\n      navigate('/courses');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCompleteLesson = async () => {\n    if (!lesson) return;\n    try {\n      setCompleting(true);\n      const timeSpent = Math.floor((Date.now() - startTime) / 1000); // en secondes\n\n      const response = await api.post(`/lessons/${lesson.id}/complete/`, {\n        time_spent: timeSpent\n      });\n      if (response.data) {\n        showSuccess('Leçon terminée !', response.data.message);\n        // Rediriger vers le cours\n        setTimeout(() => {\n          navigate(`/courses/${lesson.chapter.course.id}`);\n        }, 1500);\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('Erreur lors de la completion:', error);\n      showError('Erreur', ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.error) || 'Impossible de marquer la leçon comme terminée');\n    } finally {\n      setCompleting(false);\n    }\n  };\n\n  // Utility Functions\n  const getLessonTypeIcon = type => {\n    switch (type) {\n      case 'theory':\n        return '📚';\n      case 'example':\n        return '💡';\n      case 'exercise':\n        return '✏️';\n      case 'quiz':\n        return '❓';\n      case 'video':\n        return '🎥';\n      default:\n        return '📖';\n    }\n  };\n  const getLessonTypeColor = type => {\n    switch (type) {\n      case 'theory':\n        return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300';\n      case 'example':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'exercise':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'quiz':\n        return 'text-purple-600 bg-purple-100 dark:bg-purple-900 dark:text-purple-300';\n      case 'video':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n  const formatContent = content => {\n    // Convertir le contenu en HTML simple\n    return content.split('\\n\\n').map((paragraph, index) => {\n      if (paragraph.trim() === '') return null;\n\n      // Détecter les listes\n      if (paragraph.includes('- ') || paragraph.includes('• ')) {\n        const items = paragraph.split('\\n').filter(line => line.trim().startsWith('- ') || line.trim().startsWith('• '));\n        return /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"list-disc list-inside mb-4 space-y-1\",\n          children: items.map((item, itemIndex) => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"text-gray-700 dark:text-gray-300\",\n            children: item.replace(/^[- •]\\s*/, '')\n          }, itemIndex, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 17\n          }, this))\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this);\n      }\n\n      // Détecter les titres\n      if (paragraph.endsWith(':') && paragraph.length < 100) {\n        return /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-2 text-gray-900 dark:text-white\",\n          children: paragraph\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this);\n      }\n\n      // Paragraphe normal\n      return /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mb-4 text-gray-700 dark:text-gray-300 leading-relaxed\",\n        children: paragraph\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this);\n    }).filter(Boolean);\n  };\n\n  // Loading and Error States\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Acc\\xE8s restreint\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Vous devez \\xEAtre connect\\xE9 pour acc\\xE9der \\xE0 cette le\\xE7on.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Chargement de la le\\xE7on...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this);\n  }\n  if (!lesson) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Le\\xE7on non trouv\\xE9e\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"La le\\xE7on demand\\xE9e n'existe pas ou n'est plus disponible.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/courses'),\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Retour aux cours\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Main Render\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 flex items-center text-sm text-gray-600 dark:text-gray-400\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/courses'),\n        className: \"hover:text-primary-600 transition-colors\",\n        children: \"Cours\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"mx-2\",\n        children: \"\\u203A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate(`/courses/${lesson.chapter.course.id}`),\n        className: \"hover:text-primary-600 transition-colors\",\n        children: lesson.chapter.course.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"mx-2\",\n        children: \"\\u203A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-gray-900 dark:text-white font-medium\",\n        children: lesson.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start justify-between mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-3xl mr-4\",\n              children: getLessonTypeIcon(lesson.lesson_type)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                children: lesson.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mt-2 space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-3 py-1 rounded-full text-sm font-medium ${getLessonTypeColor(lesson.lesson_type)}`,\n                  children: lesson.type_display\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600 dark:text-gray-400\",\n                  children: [\"\\u23F1 \", lesson.estimated_duration, \" minutes\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this), lesson.progress.is_completed && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-green-600 dark:text-green-400 font-medium\",\n                  children: \"\\u2705 Termin\\xE9e\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"Chapitre:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this), \" \", lesson.chapter.title]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), lesson.progress.time_spent > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600 dark:text-gray-400\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: \"Temps d\\xE9j\\xE0 pass\\xE9:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this), ' ', Math.floor(lesson.progress.time_spent / 60), \" minutes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"prose prose-lg max-w-none dark:prose-invert\",\n          children: formatContent(lesson.content)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate(`/courses/${lesson.chapter.course.id}`),\n              className: \"bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n              children: \"\\u2190 Retour au cours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this), lesson.exercise && /*#__PURE__*/_jsxDEV(\"a\", {\n              href: `/exercises/${lesson.exercise.id}`,\n              className: \"bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n              children: \"\\uD83D\\uDCDD Faire l'exercice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), !lesson.progress.is_completed && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCompleteLesson,\n            disabled: completing,\n            className: \"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n            children: completing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 21\n              }, this), \"Finalisation...\"]\n            }, void 0, true) : '✅ Marquer comme terminée'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this), lesson.progress.is_completed && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 p-4 bg-green-50 dark:bg-green-900 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-800 dark:text-green-200 font-medium\",\n              children: [\"\\u2705 Le\\xE7on termin\\xE9e le\", ' ', new Date(lesson.progress.completed_at).toLocaleDateString('fr-FR')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 247,\n    columnNumber: 5\n  }, this);\n};\n_s(LessonDetailPage, \"u+ksw+R1ouMfr8AwEgvIB+gFYPY=\", false, function () {\n  return [useParams, useNavigate, useAuth, useNotifications];\n});\n_c = LessonDetailPage;\nexport default LessonDetailPage;\nvar _c;\n$RefreshReg$(_c, \"LessonDetailPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "motion", "useAuth", "useNotifications", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LessonDetailPage", "_s", "id", "navigate", "user", "addNotification", "showSuccess", "title", "message", "type", "showError", "lesson", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "completing", "setCompleting", "startTime", "Date", "now", "<PERSON><PERSON><PERSON><PERSON>", "response", "get", "data", "error", "_error$response", "_error$response2", "console", "status", "handleCompleteLesson", "timeSpent", "Math", "floor", "post", "time_spent", "setTimeout", "chapter", "course", "_error$response3", "_error$response3$data", "getLessonTypeIcon", "getLessonTypeColor", "formatContent", "content", "split", "map", "paragraph", "index", "trim", "includes", "items", "filter", "line", "startsWith", "className", "children", "item", "itemIndex", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "endsWith", "length", "Boolean", "href", "onClick", "div", "initial", "opacity", "y", "animate", "lesson_type", "type_display", "estimated_duration", "progress", "is_completed", "transition", "delay", "exercise", "disabled", "completed_at", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/pages/LessonDetailPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport api from '../services/api';\n\n// Types\ninterface Lesson {\n  id: number;\n  title: string;\n  content: string;\n  lesson_type: string;\n  type_display: string;\n  estimated_duration: number;\n  chapter: {\n    id: number;\n    title: string;\n    course: {\n      id: number;\n      title: string;\n    };\n  };\n  exercise?: {\n    id: number;\n    title: string;\n  };\n  progress: {\n    is_completed: boolean;\n    time_spent: number;\n    started_at?: string;\n    completed_at?: string;\n  };\n}\n\nconst LessonDetailPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const { user } = useAuth();\n  const { addNotification } = useNotifications();\n\n  // Helper functions for notifications\n  const showSuccess = (title: string, message: string) => {\n    addNotification({ type: 'success', title, message });\n  };\n  const showError = (title: string, message: string) => {\n    addNotification({ type: 'error', title, message });\n  };\n\n  // State\n  const [lesson, setLesson] = useState<Lesson | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [completing, setCompleting] = useState(false);\n  const [startTime] = useState(Date.now());\n\n  // Effects\n  useEffect(() => {\n    if (user && id) {\n      fetchLesson();\n    }\n  }, [user, id]);\n\n  // API Functions\n  const fetchLesson = async () => {\n    try {\n      setLoading(true);\n      const response = await api.get(`/api/lessons/${id}/`);\n      if (response.data) {\n        setLesson(response.data);\n      }\n    } catch (error: any) {\n      console.error('Erreur lors de la récupération de la leçon:', error);\n      if (error.response?.status === 404) {\n        showError('Leçon non trouvée', 'La leçon demandée n\\'existe pas.');\n      } else if (error.response?.status === 403) {\n        showError(\n          'Accès refusé',\n          error.response.data.error || 'Vous n\\'avez pas accès à cette leçon.'\n        );\n      } else {\n        showError('Erreur', 'Impossible de charger la leçon');\n      }\n      navigate('/courses');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCompleteLesson = async () => {\n    if (!lesson) return;\n\n    try {\n      setCompleting(true);\n      const timeSpent = Math.floor((Date.now() - startTime) / 1000); // en secondes\n\n      const response = await api.post(`/lessons/${lesson.id}/complete/`, {\n        time_spent: timeSpent\n      });\n\n      if (response.data) {\n        showSuccess('Leçon terminée !', response.data.message);\n        // Rediriger vers le cours\n        setTimeout(() => {\n          navigate(`/courses/${lesson.chapter.course.id}`);\n        }, 1500);\n      }\n    } catch (error: any) {\n      console.error('Erreur lors de la completion:', error);\n      showError(\n        'Erreur',\n        error.response?.data?.error || 'Impossible de marquer la leçon comme terminée'\n      );\n    } finally {\n      setCompleting(false);\n    }\n  };\n\n  // Utility Functions\n  const getLessonTypeIcon = (type: string) => {\n    switch (type) {\n      case 'theory':\n        return '📚';\n      case 'example':\n        return '💡';\n      case 'exercise':\n        return '✏️';\n      case 'quiz':\n        return '❓';\n      case 'video':\n        return '🎥';\n      default:\n        return '📖';\n    }\n  };\n\n  const getLessonTypeColor = (type: string) => {\n    switch (type) {\n      case 'theory':\n        return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300';\n      case 'example':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'exercise':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'quiz':\n        return 'text-purple-600 bg-purple-100 dark:bg-purple-900 dark:text-purple-300';\n      case 'video':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n\n  const formatContent = (content: string) => {\n    // Convertir le contenu en HTML simple\n    return content\n      .split('\\n\\n')\n      .map((paragraph, index) => {\n        if (paragraph.trim() === '') return null;\n\n        // Détecter les listes\n        if (paragraph.includes('- ') || paragraph.includes('• ')) {\n          const items = paragraph\n            .split('\\n')\n            .filter(line => line.trim().startsWith('- ') || line.trim().startsWith('• '));\n          return (\n            <ul key={index} className=\"list-disc list-inside mb-4 space-y-1\">\n              {items.map((item, itemIndex) => (\n                <li key={itemIndex} className=\"text-gray-700 dark:text-gray-300\">\n                  {item.replace(/^[- •]\\s*/, '')}\n                </li>\n              ))}\n            </ul>\n          );\n        }\n\n        // Détecter les titres\n        if (paragraph.endsWith(':') && paragraph.length < 100) {\n          return (\n            <h3 key={index} className=\"text-lg font-semibold mb-2 text-gray-900 dark:text-white\">\n              {paragraph}\n            </h3>\n          );\n        }\n\n        // Paragraphe normal\n        return (\n          <p key={index} className=\"mb-4 text-gray-700 dark:text-gray-300 leading-relaxed\">\n            {paragraph}\n          </p>\n        );\n      })\n      .filter(Boolean);\n  };\n\n  // Loading and Error States\n  if (!user) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Accès restreint</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            Vous devez être connecté pour accéder à cette leçon.\n          </p>\n          <a\n            href=\"/login\"\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Se connecter\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Chargement de la leçon...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!lesson) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Leçon non trouvée</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            La leçon demandée n'existe pas ou n'est plus disponible.\n          </p>\n          <button\n            onClick={() => navigate('/courses')}\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Retour aux cours\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Main Render\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* Navigation */}\n      <div className=\"mb-6 flex items-center text-sm text-gray-600 dark:text-gray-400\">\n        <button\n          onClick={() => navigate('/courses')}\n          className=\"hover:text-primary-600 transition-colors\"\n        >\n          Cours\n        </button>\n        <span className=\"mx-2\">›</span>\n        <button\n          onClick={() => navigate(`/courses/${lesson.chapter.course.id}`)}\n          className=\"hover:text-primary-600 transition-colors\"\n        >\n          {lesson.chapter.course.title}\n        </button>\n        <span className=\"mx-2\">›</span>\n        <span className=\"text-gray-900 dark:text-white font-medium\">\n          {lesson.title}\n        </span>\n      </div>\n\n      <div className=\"max-w-4xl mx-auto\">\n        {/* En-tête de la leçon */}\n        <motion.div\n          className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n        >\n          <div className=\"flex items-start justify-between mb-4\">\n            <div className=\"flex items-center\">\n              <span className=\"text-3xl mr-4\">{getLessonTypeIcon(lesson.lesson_type)}</span>\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                  {lesson.title}\n                </h1>\n                <div className=\"flex items-center mt-2 space-x-4\">\n                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getLessonTypeColor(lesson.lesson_type)}`}>\n                    {lesson.type_display}\n                  </span>\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    ⏱ {lesson.estimated_duration} minutes\n                  </span>\n                  {lesson.progress.is_completed && (\n                    <span className=\"text-sm text-green-600 dark:text-green-400 font-medium\">\n                      ✅ Terminée\n                    </span>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n            <span className=\"font-medium\">Chapitre:</span> {lesson.chapter.title}\n          </div>\n\n          {lesson.progress.time_spent > 0 && (\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              <span className=\"font-medium\">Temps déjà passé:</span>{' '}\n              {Math.floor(lesson.progress.time_spent / 60)} minutes\n            </div>\n          )}\n        </motion.div>\n\n        {/* Contenu de la leçon */}\n        <motion.div\n          className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n        >\n          <div className=\"prose prose-lg max-w-none dark:prose-invert\">\n            {formatContent(lesson.content)}\n          </div>\n        </motion.div>\n\n        {/* Actions */}\n        <motion.div\n          className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => navigate(`/courses/${lesson.chapter.course.id}`)}\n                className=\"bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n              >\n                ← Retour au cours\n              </button>\n              {lesson.exercise && (\n                <a\n                  href={`/exercises/${lesson.exercise.id}`}\n                  className=\"bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n                >\n                  📝 Faire l'exercice\n                </a>\n              )}\n            </div>\n\n            {!lesson.progress.is_completed && (\n              <button\n                onClick={handleCompleteLesson}\n                disabled={completing}\n                className=\"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n              >\n                {completing ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2\"></div>\n                    Finalisation...\n                  </>\n                ) : (\n                  '✅ Marquer comme terminée'\n                )}\n              </button>\n            )}\n          </div>\n\n          {lesson.progress.is_completed && (\n            <div className=\"mt-4 p-4 bg-green-50 dark:bg-green-900 rounded-lg\">\n              <div className=\"flex items-center\">\n                <span className=\"text-green-800 dark:text-green-200 font-medium\">\n                  ✅ Leçon terminée le{' '}\n                  {new Date(lesson.progress.completed_at!).toLocaleDateString('fr-FR')}\n                </span>\n              </div>\n            </div>\n          )}\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default LessonDetailPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,OAAOC,GAAG,MAAM,iBAAiB;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA4BA,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAG,CAAC,GAAGZ,SAAS,CAAiB,CAAC;EAC1C,MAAMa,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEa;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEY;EAAgB,CAAC,GAAGX,gBAAgB,CAAC,CAAC;;EAE9C;EACA,MAAMY,WAAW,GAAGA,CAACC,KAAa,EAAEC,OAAe,KAAK;IACtDH,eAAe,CAAC;MAAEI,IAAI,EAAE,SAAS;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACtD,CAAC;EACD,MAAME,SAAS,GAAGA,CAACH,KAAa,EAAEC,OAAe,KAAK;IACpDH,eAAe,CAAC;MAAEI,IAAI,EAAE,OAAO;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACpD,CAAC;;EAED;EACA,MAAM,CAACG,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;EACzD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6B,SAAS,CAAC,GAAG7B,QAAQ,CAAC8B,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;;EAExC;EACA9B,SAAS,CAAC,MAAM;IACd,IAAIe,IAAI,IAAIF,EAAE,EAAE;MACdkB,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAAChB,IAAI,EAAEF,EAAE,CAAC,CAAC;;EAEd;EACA,MAAMkB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMO,QAAQ,GAAG,MAAM1B,GAAG,CAAC2B,GAAG,CAAC,gBAAgBpB,EAAE,GAAG,CAAC;MACrD,IAAImB,QAAQ,CAACE,IAAI,EAAE;QACjBX,SAAS,CAACS,QAAQ,CAACE,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,gBAAA;MACnBC,OAAO,CAACH,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE,IAAI,EAAAC,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBG,MAAM,MAAK,GAAG,EAAE;QAClClB,SAAS,CAAC,mBAAmB,EAAE,kCAAkC,CAAC;MACpE,CAAC,MAAM,IAAI,EAAAgB,gBAAA,GAAAF,KAAK,CAACH,QAAQ,cAAAK,gBAAA,uBAAdA,gBAAA,CAAgBE,MAAM,MAAK,GAAG,EAAE;QACzClB,SAAS,CACP,cAAc,EACdc,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACC,KAAK,IAAI,uCAC/B,CAAC;MACH,CAAC,MAAM;QACLd,SAAS,CAAC,QAAQ,EAAE,gCAAgC,CAAC;MACvD;MACAP,QAAQ,CAAC,UAAU,CAAC;IACtB,CAAC,SAAS;MACRW,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMe,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAAClB,MAAM,EAAE;IAEb,IAAI;MACFK,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMc,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACd,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC;;MAE/D,MAAMI,QAAQ,GAAG,MAAM1B,GAAG,CAACsC,IAAI,CAAC,YAAYtB,MAAM,CAACT,EAAE,YAAY,EAAE;QACjEgC,UAAU,EAAEJ;MACd,CAAC,CAAC;MAEF,IAAIT,QAAQ,CAACE,IAAI,EAAE;QACjBjB,WAAW,CAAC,kBAAkB,EAAEe,QAAQ,CAACE,IAAI,CAACf,OAAO,CAAC;QACtD;QACA2B,UAAU,CAAC,MAAM;UACfhC,QAAQ,CAAC,YAAYQ,MAAM,CAACyB,OAAO,CAACC,MAAM,CAACnC,EAAE,EAAE,CAAC;QAClD,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC,OAAOsB,KAAU,EAAE;MAAA,IAAAc,gBAAA,EAAAC,qBAAA;MACnBZ,OAAO,CAACH,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDd,SAAS,CACP,QAAQ,EACR,EAAA4B,gBAAA,GAAAd,KAAK,CAACH,QAAQ,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBf,KAAK,KAAI,+CACjC,CAAC;IACH,CAAC,SAAS;MACRR,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMwB,iBAAiB,GAAI/B,IAAY,IAAK;IAC1C,QAAQA,IAAI;MACV,KAAK,QAAQ;QACX,OAAO,IAAI;MACb,KAAK,SAAS;QACZ,OAAO,IAAI;MACb,KAAK,UAAU;QACb,OAAO,IAAI;MACb,KAAK,MAAM;QACT,OAAO,GAAG;MACZ,KAAK,OAAO;QACV,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMgC,kBAAkB,GAAIhC,IAAY,IAAK;IAC3C,QAAQA,IAAI;MACV,KAAK,QAAQ;QACX,OAAO,+DAA+D;MACxE,KAAK,SAAS;QACZ,OAAO,uEAAuE;MAChF,KAAK,UAAU;QACb,OAAO,mEAAmE;MAC5E,KAAK,MAAM;QACT,OAAO,uEAAuE;MAChF,KAAK,OAAO;QACV,OAAO,2DAA2D;MACpE;QACE,OAAO,+DAA+D;IAC1E;EACF,CAAC;EAED,MAAMiC,aAAa,GAAIC,OAAe,IAAK;IACzC;IACA,OAAOA,OAAO,CACXC,KAAK,CAAC,MAAM,CAAC,CACbC,GAAG,CAAC,CAACC,SAAS,EAAEC,KAAK,KAAK;MACzB,IAAID,SAAS,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,OAAO,IAAI;;MAExC;MACA,IAAIF,SAAS,CAACG,QAAQ,CAAC,IAAI,CAAC,IAAIH,SAAS,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAE;QACxD,MAAMC,KAAK,GAAGJ,SAAS,CACpBF,KAAK,CAAC,IAAI,CAAC,CACXO,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACJ,IAAI,CAAC,CAAC,CAACK,UAAU,CAAC,IAAI,CAAC,IAAID,IAAI,CAACJ,IAAI,CAAC,CAAC,CAACK,UAAU,CAAC,IAAI,CAAC,CAAC;QAC/E,oBACExD,OAAA;UAAgByD,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAC7DL,KAAK,CAACL,GAAG,CAAC,CAACW,IAAI,EAAEC,SAAS,kBACzB5D,OAAA;YAAoByD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAC7DC,IAAI,CAACE,OAAO,CAAC,WAAW,EAAE,EAAE;UAAC,GADvBD,SAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEd,CACL;QAAC,GALKf,KAAK;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMV,CAAC;MAET;;MAEA;MACA,IAAIhB,SAAS,CAACiB,QAAQ,CAAC,GAAG,CAAC,IAAIjB,SAAS,CAACkB,MAAM,GAAG,GAAG,EAAE;QACrD,oBACEnE,OAAA;UAAgByD,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EACjFT;QAAS,GADHC,KAAK;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CAAC;MAET;;MAEA;MACA,oBACEjE,OAAA;QAAeyD,SAAS,EAAC,uDAAuD;QAAAC,QAAA,EAC7ET;MAAS,GADJC,KAAK;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CAAC;IAER,CAAC,CAAC,CACDX,MAAM,CAACc,OAAO,CAAC;EACpB,CAAC;;EAED;EACA,IAAI,CAAC7D,IAAI,EAAE;IACT,oBACEP,OAAA;MAAKyD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C1D,OAAA;QAAKyD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1D,OAAA;UAAIyD,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5DjE,OAAA;UAAGyD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJjE,OAAA;UACEqE,IAAI,EAAC,QAAQ;UACbZ,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIjD,OAAO,EAAE;IACX,oBACEhB,OAAA;MAAKyD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C1D,OAAA;QAAKyD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC1D,OAAA;UAAKyD,SAAS,EAAC;QAAgF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGjE,OAAA;UAAGyD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAyB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACnD,MAAM,EAAE;IACX,oBACEd,OAAA;MAAKyD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C1D,OAAA;QAAKyD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1D,OAAA;UAAIyD,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DjE,OAAA;UAAGyD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJjE,OAAA;UACEsE,OAAO,EAAEA,CAAA,KAAMhE,QAAQ,CAAC,UAAU,CAAE;UACpCmD,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACEjE,OAAA;IAAKyD,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAE1C1D,OAAA;MAAKyD,SAAS,EAAC,iEAAiE;MAAAC,QAAA,gBAC9E1D,OAAA;QACEsE,OAAO,EAAEA,CAAA,KAAMhE,QAAQ,CAAC,UAAU,CAAE;QACpCmD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EACrD;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjE,OAAA;QAAMyD,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/BjE,OAAA;QACEsE,OAAO,EAAEA,CAAA,KAAMhE,QAAQ,CAAC,YAAYQ,MAAM,CAACyB,OAAO,CAACC,MAAM,CAACnC,EAAE,EAAE,CAAE;QAChEoD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAEnD5C,MAAM,CAACyB,OAAO,CAACC,MAAM,CAAC9B;MAAK;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACTjE,OAAA;QAAMyD,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/BjE,OAAA;QAAMyD,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EACxD5C,MAAM,CAACJ;MAAK;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENjE,OAAA;MAAKyD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAEhC1D,OAAA,CAACL,MAAM,CAAC4E,GAAG;QACTd,SAAS,EAAC,yDAAyD;QACnEe,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAhB,QAAA,gBAE9B1D,OAAA;UAAKyD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpD1D,OAAA;YAAKyD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC1D,OAAA;cAAMyD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEf,iBAAiB,CAAC7B,MAAM,CAAC8D,WAAW;YAAC;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9EjE,OAAA;cAAA0D,QAAA,gBACE1D,OAAA;gBAAIyD,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,EAC7D5C,MAAM,CAACJ;cAAK;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACLjE,OAAA;gBAAKyD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C1D,OAAA;kBAAMyD,SAAS,EAAE,8CAA8Cb,kBAAkB,CAAC9B,MAAM,CAAC8D,WAAW,CAAC,EAAG;kBAAAlB,QAAA,EACrG5C,MAAM,CAAC+D;gBAAY;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACPjE,OAAA;kBAAMyD,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,GAAC,SACvD,EAAC5C,MAAM,CAACgE,kBAAkB,EAAC,UAC/B;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACNnD,MAAM,CAACiE,QAAQ,CAACC,YAAY,iBAC3BhF,OAAA;kBAAMyD,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EAAC;gBAEzE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjE,OAAA;UAAKyD,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5D1D,OAAA;YAAMyD,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACnD,MAAM,CAACyB,OAAO,CAAC7B,KAAK;QAAA;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,EAELnD,MAAM,CAACiE,QAAQ,CAAC1C,UAAU,GAAG,CAAC,iBAC7BrC,OAAA;UAAKyD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,gBACvD1D,OAAA;YAAMyD,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAiB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAAC,GAAG,EACzD/B,IAAI,CAACC,KAAK,CAACrB,MAAM,CAACiE,QAAQ,CAAC1C,UAAU,GAAG,EAAE,CAAC,EAAC,UAC/C;QAAA;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAGbjE,OAAA,CAACL,MAAM,CAAC4E,GAAG;QACTd,SAAS,EAAC,yDAAyD;QACnEe,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BO,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAxB,QAAA,eAE3B1D,OAAA;UAAKyD,SAAS,EAAC,6CAA6C;UAAAC,QAAA,EACzDb,aAAa,CAAC/B,MAAM,CAACgC,OAAO;QAAC;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbjE,OAAA,CAACL,MAAM,CAAC4E,GAAG;QACTd,SAAS,EAAC,oDAAoD;QAC9De,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BO,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAxB,QAAA,gBAE3B1D,OAAA;UAAKyD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD1D,OAAA;YAAKyD,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C1D,OAAA;cACEsE,OAAO,EAAEA,CAAA,KAAMhE,QAAQ,CAAC,YAAYQ,MAAM,CAACyB,OAAO,CAACC,MAAM,CAACnC,EAAE,EAAE,CAAE;cAChEoD,SAAS,EAAC,6FAA6F;cAAAC,QAAA,EACxG;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRnD,MAAM,CAACqE,QAAQ,iBACdnF,OAAA;cACEqE,IAAI,EAAE,cAAcvD,MAAM,CAACqE,QAAQ,CAAC9E,EAAE,EAAG;cACzCoD,SAAS,EAAC,+FAA+F;cAAAC,QAAA,EAC1G;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAEL,CAACnD,MAAM,CAACiE,QAAQ,CAACC,YAAY,iBAC5BhF,OAAA;YACEsE,OAAO,EAAEtC,oBAAqB;YAC9BoD,QAAQ,EAAElE,UAAW;YACrBuC,SAAS,EAAC,2HAA2H;YAAAC,QAAA,EAEpIxC,UAAU,gBACTlB,OAAA,CAAAE,SAAA;cAAAwD,QAAA,gBACE1D,OAAA;gBAAKyD,SAAS,EAAC;cAA6E;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,mBAErG;YAAA,eAAE,CAAC,GAEH;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAELnD,MAAM,CAACiE,QAAQ,CAACC,YAAY,iBAC3BhF,OAAA;UAAKyD,SAAS,EAAC,mDAAmD;UAAAC,QAAA,eAChE1D,OAAA;YAAKyD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChC1D,OAAA;cAAMyD,SAAS,EAAC,gDAAgD;cAAAC,QAAA,GAAC,gCAC5C,EAAC,GAAG,EACtB,IAAIrC,IAAI,CAACP,MAAM,CAACiE,QAAQ,CAACM,YAAa,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC;YAAA;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7D,EAAA,CAzVID,gBAA0B;EAAA,QACfV,SAAS,EACPC,WAAW,EACXE,OAAO,EACIC,gBAAgB;AAAA;AAAA0F,EAAA,GAJxCpF,gBAA0B;AA2VhC,eAAeA,gBAAgB;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}