{"ast": null, "code": "// list of identifiers of nodes in order of their precedence\n// also contains information about left/right associativity\n// and which other operator the operator is associative with\n// Example:\n// addition is associative with addition and subtraction, because:\n// (a+b)+c=a+(b+c)\n// (a+b)-c=a+(b-c)\n//\n// postfix operators are left associative, prefix operators\n// are right associative\n//\n// It's also possible to set the following properties:\n// latexParens: if set to false, this node doesn't need to be enclosed\n//              in parentheses when using LaTeX\n// latexLeftParens: if set to false, this !OperatorNode's!\n//                  left argument doesn't need to be enclosed\n//                  in parentheses\n// latexRightParens: the same for the right argument\nimport { hasOwnProperty } from '../utils/object.js';\nimport { isConstantNode, isParenthesisNode, rule2Node } from '../utils/is.js';\nexport var properties = [{\n  // assignment\n  AssignmentNode: {},\n  FunctionAssignmentNode: {}\n}, {\n  // conditional expression\n  ConditionalNode: {\n    latexLeftParens: false,\n    latexRightParens: false,\n    latexParens: false\n    // conditionals don't need parentheses in LaTeX because\n    // they are 2 dimensional\n  }\n}, {\n  // logical or\n  'OperatorNode:or': {\n    op: 'or',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // logical xor\n  'OperatorNode:xor': {\n    op: 'xor',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // logical and\n  'OperatorNode:and': {\n    op: 'and',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // bitwise or\n  'OperatorNode:bitOr': {\n    op: '|',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // bitwise xor\n  'OperatorNode:bitXor': {\n    op: '^|',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // bitwise and\n  'OperatorNode:bitAnd': {\n    op: '&',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // relational operators\n  'OperatorNode:equal': {\n    op: '==',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:unequal': {\n    op: '!=',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:smaller': {\n    op: '<',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:larger': {\n    op: '>',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:smallerEq': {\n    op: '<=',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:largerEq': {\n    op: '>=',\n    associativity: 'left',\n    associativeWith: []\n  },\n  RelationalNode: {\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // bitshift operators\n  'OperatorNode:leftShift': {\n    op: '<<',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:rightArithShift': {\n    op: '>>',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:rightLogShift': {\n    op: '>>>',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // unit conversion\n  'OperatorNode:to': {\n    op: 'to',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // range\n  RangeNode: {}\n}, {\n  // addition, subtraction\n  'OperatorNode:add': {\n    op: '+',\n    associativity: 'left',\n    associativeWith: ['OperatorNode:add', 'OperatorNode:subtract']\n  },\n  'OperatorNode:subtract': {\n    op: '-',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // multiply, divide, modulus\n  'OperatorNode:multiply': {\n    op: '*',\n    associativity: 'left',\n    associativeWith: ['OperatorNode:multiply', 'OperatorNode:divide', 'Operator:dotMultiply', 'Operator:dotDivide']\n  },\n  'OperatorNode:divide': {\n    op: '/',\n    associativity: 'left',\n    associativeWith: [],\n    latexLeftParens: false,\n    latexRightParens: false,\n    latexParens: false\n    // fractions don't require parentheses because\n    // they're 2 dimensional, so parens aren't needed\n    // in LaTeX\n  },\n  'OperatorNode:dotMultiply': {\n    op: '.*',\n    associativity: 'left',\n    associativeWith: ['OperatorNode:multiply', 'OperatorNode:divide', 'OperatorNode:dotMultiply', 'OperatorNode:doDivide']\n  },\n  'OperatorNode:dotDivide': {\n    op: './',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:mod': {\n    op: 'mod',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // Repeat multiplication for implicit multiplication\n  'OperatorNode:multiply': {\n    associativity: 'left',\n    associativeWith: ['OperatorNode:multiply', 'OperatorNode:divide', 'Operator:dotMultiply', 'Operator:dotDivide']\n  }\n}, {\n  // unary prefix operators\n  'OperatorNode:unaryPlus': {\n    op: '+',\n    associativity: 'right'\n  },\n  'OperatorNode:unaryMinus': {\n    op: '-',\n    associativity: 'right'\n  },\n  'OperatorNode:bitNot': {\n    op: '~',\n    associativity: 'right'\n  },\n  'OperatorNode:not': {\n    op: 'not',\n    associativity: 'right'\n  }\n}, {\n  // exponentiation\n  'OperatorNode:pow': {\n    op: '^',\n    associativity: 'right',\n    associativeWith: [],\n    latexRightParens: false\n    // the exponent doesn't need parentheses in\n    // LaTeX because it's 2 dimensional\n    // (it's on top)\n  },\n  'OperatorNode:dotPow': {\n    op: '.^',\n    associativity: 'right',\n    associativeWith: []\n  }\n}, {\n  // factorial\n  'OperatorNode:factorial': {\n    op: '!',\n    associativity: 'left'\n  }\n}, {\n  // matrix transpose\n  'OperatorNode:ctranspose': {\n    op: \"'\",\n    associativity: 'left'\n  }\n}];\n\n/**\n * Returns the first non-parenthesis internal node, but only\n * when the 'parenthesis' option is unset or auto.\n * @param {Node} _node\n * @param {string} parenthesis\n * @return {Node}\n */\nfunction unwrapParen(_node, parenthesis) {\n  if (!parenthesis || parenthesis !== 'auto') return _node;\n  var node = _node;\n  while (isParenthesisNode(node)) node = node.content;\n  return node;\n}\n\n/**\n * Get the precedence of a Node.\n * Higher number for higher precedence, starting with 0.\n * Returns null if the precedence is undefined.\n *\n * @param {Node} _node\n * @param {string} parenthesis\n * @param {string} implicit\n * @param {Node} parent (for determining context for implicit multiplication)\n * @return {number | null}\n */\nexport function getPrecedence(_node, parenthesis, implicit, parent) {\n  var node = _node;\n  if (parenthesis !== 'keep') {\n    // ParenthesisNodes are only ignored when not in 'keep' mode\n    node = _node.getContent();\n  }\n  var identifier = node.getIdentifier();\n  var precedence = null;\n  for (var i = 0; i < properties.length; i++) {\n    if (identifier in properties[i]) {\n      precedence = i;\n      break;\n    }\n  }\n  // Bump up precedence of implicit multiplication, except when preceded\n  // by a \"Rule 2\" fraction ( [unaryOp]constant / constant )\n  if (identifier === 'OperatorNode:multiply' && node.implicit && implicit !== 'show') {\n    var leftArg = unwrapParen(node.args[0], parenthesis);\n    if (!(isConstantNode(leftArg) && parent && parent.getIdentifier() === 'OperatorNode:divide' && rule2Node(unwrapParen(parent.args[0], parenthesis))) && !(leftArg.getIdentifier() === 'OperatorNode:divide' && rule2Node(unwrapParen(leftArg.args[0], parenthesis)) && isConstantNode(unwrapParen(leftArg.args[1])))) {\n      precedence += 1;\n    }\n  }\n  return precedence;\n}\n\n/**\n * Get the associativity of an operator (left or right).\n * Returns a string containing 'left' or 'right' or null if\n * the associativity is not defined.\n *\n * @param {Node} _node\n * @param {string} parenthesis\n * @return {string|null}\n * @throws {Error}\n */\nexport function getAssociativity(_node, parenthesis) {\n  var node = _node;\n  if (parenthesis !== 'keep') {\n    // ParenthesisNodes are only ignored when not in 'keep' mode\n    node = _node.getContent();\n  }\n  var identifier = node.getIdentifier();\n  var index = getPrecedence(node, parenthesis);\n  if (index === null) {\n    // node isn't in the list\n    return null;\n  }\n  var property = properties[index][identifier];\n  if (hasOwnProperty(property, 'associativity')) {\n    if (property.associativity === 'left') {\n      return 'left';\n    }\n    if (property.associativity === 'right') {\n      return 'right';\n    }\n    // associativity is invalid\n    throw Error('\\'' + identifier + '\\' has the invalid associativity \\'' + property.associativity + '\\'.');\n  }\n\n  // associativity is undefined\n  return null;\n}\n\n/**\n * Check if an operator is associative with another operator.\n * Returns either true or false or null if not defined.\n *\n * @param {Node} nodeA\n * @param {Node} nodeB\n * @param {string} parenthesis\n * @return {boolean | null}\n */\nexport function isAssociativeWith(nodeA, nodeB, parenthesis) {\n  // ParenthesisNodes are only ignored when not in 'keep' mode\n  var a = parenthesis !== 'keep' ? nodeA.getContent() : nodeA;\n  var b = parenthesis !== 'keep' ? nodeA.getContent() : nodeB;\n  var identifierA = a.getIdentifier();\n  var identifierB = b.getIdentifier();\n  var index = getPrecedence(a, parenthesis);\n  if (index === null) {\n    // node isn't in the list\n    return null;\n  }\n  var property = properties[index][identifierA];\n  if (hasOwnProperty(property, 'associativeWith') && property.associativeWith instanceof Array) {\n    for (var i = 0; i < property.associativeWith.length; i++) {\n      if (property.associativeWith[i] === identifierB) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  // associativeWith is not defined\n  return null;\n}\n\n/**\n * Get the operator associated with a function name.\n * Returns a string with the operator symbol, or null if the\n * input is not the name of a function associated with an\n * operator.\n *\n * @param {string} Function name\n * @return {string | null} Associated operator symbol, if any\n */\nexport function getOperator(fn) {\n  var identifier = 'OperatorNode:' + fn;\n  for (var group of properties) {\n    if (identifier in group) {\n      return group[identifier].op;\n    }\n  }\n  return null;\n}", "map": {"version": 3, "names": ["hasOwnProperty", "isConstantNode", "isParenthesisNode", "rule2Node", "properties", "AssignmentNode", "FunctionAssignmentNode", "ConditionalNode", "latexLeftParens", "latexRightParens", "latexParens", "op", "associativity", "associativeWith", "RelationalNode", "RangeNode", "unwrapParen", "_node", "parenthesis", "node", "content", "getPrecedence", "implicit", "parent", "get<PERSON>ontent", "identifier", "getIdentifier", "precedence", "i", "length", "leftArg", "args", "getAssociativity", "index", "property", "Error", "isAssociativeWith", "nodeA", "nodeB", "a", "b", "identifierA", "identifierB", "Array", "getOperator", "fn", "group"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/operators.js"], "sourcesContent": ["// list of identifiers of nodes in order of their precedence\n// also contains information about left/right associativity\n// and which other operator the operator is associative with\n// Example:\n// addition is associative with addition and subtraction, because:\n// (a+b)+c=a+(b+c)\n// (a+b)-c=a+(b-c)\n//\n// postfix operators are left associative, prefix operators\n// are right associative\n//\n// It's also possible to set the following properties:\n// latexParens: if set to false, this node doesn't need to be enclosed\n//              in parentheses when using LaTeX\n// latexLeftParens: if set to false, this !OperatorNode's!\n//                  left argument doesn't need to be enclosed\n//                  in parentheses\n// latexRightParens: the same for the right argument\nimport { hasOwnProperty } from '../utils/object.js';\nimport { isConstantNode, isParenthesisNode, rule2Node } from '../utils/is.js';\nexport var properties = [{\n  // assignment\n  AssignmentNode: {},\n  FunctionAssignmentNode: {}\n}, {\n  // conditional expression\n  ConditionalNode: {\n    latexLeftParens: false,\n    latexRightParens: false,\n    latexParens: false\n    // conditionals don't need parentheses in LaTeX because\n    // they are 2 dimensional\n  }\n}, {\n  // logical or\n  'OperatorNode:or': {\n    op: 'or',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // logical xor\n  'OperatorNode:xor': {\n    op: 'xor',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // logical and\n  'OperatorNode:and': {\n    op: 'and',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // bitwise or\n  'OperatorNode:bitOr': {\n    op: '|',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // bitwise xor\n  'OperatorNode:bitXor': {\n    op: '^|',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // bitwise and\n  'OperatorNode:bitAnd': {\n    op: '&',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // relational operators\n  'OperatorNode:equal': {\n    op: '==',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:unequal': {\n    op: '!=',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:smaller': {\n    op: '<',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:larger': {\n    op: '>',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:smallerEq': {\n    op: '<=',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:largerEq': {\n    op: '>=',\n    associativity: 'left',\n    associativeWith: []\n  },\n  RelationalNode: {\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // bitshift operators\n  'OperatorNode:leftShift': {\n    op: '<<',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:rightArithShift': {\n    op: '>>',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:rightLogShift': {\n    op: '>>>',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // unit conversion\n  'OperatorNode:to': {\n    op: 'to',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // range\n  RangeNode: {}\n}, {\n  // addition, subtraction\n  'OperatorNode:add': {\n    op: '+',\n    associativity: 'left',\n    associativeWith: ['OperatorNode:add', 'OperatorNode:subtract']\n  },\n  'OperatorNode:subtract': {\n    op: '-',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // multiply, divide, modulus\n  'OperatorNode:multiply': {\n    op: '*',\n    associativity: 'left',\n    associativeWith: ['OperatorNode:multiply', 'OperatorNode:divide', 'Operator:dotMultiply', 'Operator:dotDivide']\n  },\n  'OperatorNode:divide': {\n    op: '/',\n    associativity: 'left',\n    associativeWith: [],\n    latexLeftParens: false,\n    latexRightParens: false,\n    latexParens: false\n    // fractions don't require parentheses because\n    // they're 2 dimensional, so parens aren't needed\n    // in LaTeX\n  },\n  'OperatorNode:dotMultiply': {\n    op: '.*',\n    associativity: 'left',\n    associativeWith: ['OperatorNode:multiply', 'OperatorNode:divide', 'OperatorNode:dotMultiply', 'OperatorNode:doDivide']\n  },\n  'OperatorNode:dotDivide': {\n    op: './',\n    associativity: 'left',\n    associativeWith: []\n  },\n  'OperatorNode:mod': {\n    op: 'mod',\n    associativity: 'left',\n    associativeWith: []\n  }\n}, {\n  // Repeat multiplication for implicit multiplication\n  'OperatorNode:multiply': {\n    associativity: 'left',\n    associativeWith: ['OperatorNode:multiply', 'OperatorNode:divide', 'Operator:dotMultiply', 'Operator:dotDivide']\n  }\n}, {\n  // unary prefix operators\n  'OperatorNode:unaryPlus': {\n    op: '+',\n    associativity: 'right'\n  },\n  'OperatorNode:unaryMinus': {\n    op: '-',\n    associativity: 'right'\n  },\n  'OperatorNode:bitNot': {\n    op: '~',\n    associativity: 'right'\n  },\n  'OperatorNode:not': {\n    op: 'not',\n    associativity: 'right'\n  }\n}, {\n  // exponentiation\n  'OperatorNode:pow': {\n    op: '^',\n    associativity: 'right',\n    associativeWith: [],\n    latexRightParens: false\n    // the exponent doesn't need parentheses in\n    // LaTeX because it's 2 dimensional\n    // (it's on top)\n  },\n  'OperatorNode:dotPow': {\n    op: '.^',\n    associativity: 'right',\n    associativeWith: []\n  }\n}, {\n  // factorial\n  'OperatorNode:factorial': {\n    op: '!',\n    associativity: 'left'\n  }\n}, {\n  // matrix transpose\n  'OperatorNode:ctranspose': {\n    op: \"'\",\n    associativity: 'left'\n  }\n}];\n\n/**\n * Returns the first non-parenthesis internal node, but only\n * when the 'parenthesis' option is unset or auto.\n * @param {Node} _node\n * @param {string} parenthesis\n * @return {Node}\n */\nfunction unwrapParen(_node, parenthesis) {\n  if (!parenthesis || parenthesis !== 'auto') return _node;\n  var node = _node;\n  while (isParenthesisNode(node)) node = node.content;\n  return node;\n}\n\n/**\n * Get the precedence of a Node.\n * Higher number for higher precedence, starting with 0.\n * Returns null if the precedence is undefined.\n *\n * @param {Node} _node\n * @param {string} parenthesis\n * @param {string} implicit\n * @param {Node} parent (for determining context for implicit multiplication)\n * @return {number | null}\n */\nexport function getPrecedence(_node, parenthesis, implicit, parent) {\n  var node = _node;\n  if (parenthesis !== 'keep') {\n    // ParenthesisNodes are only ignored when not in 'keep' mode\n    node = _node.getContent();\n  }\n  var identifier = node.getIdentifier();\n  var precedence = null;\n  for (var i = 0; i < properties.length; i++) {\n    if (identifier in properties[i]) {\n      precedence = i;\n      break;\n    }\n  }\n  // Bump up precedence of implicit multiplication, except when preceded\n  // by a \"Rule 2\" fraction ( [unaryOp]constant / constant )\n  if (identifier === 'OperatorNode:multiply' && node.implicit && implicit !== 'show') {\n    var leftArg = unwrapParen(node.args[0], parenthesis);\n    if (!(isConstantNode(leftArg) && parent && parent.getIdentifier() === 'OperatorNode:divide' && rule2Node(unwrapParen(parent.args[0], parenthesis))) && !(leftArg.getIdentifier() === 'OperatorNode:divide' && rule2Node(unwrapParen(leftArg.args[0], parenthesis)) && isConstantNode(unwrapParen(leftArg.args[1])))) {\n      precedence += 1;\n    }\n  }\n  return precedence;\n}\n\n/**\n * Get the associativity of an operator (left or right).\n * Returns a string containing 'left' or 'right' or null if\n * the associativity is not defined.\n *\n * @param {Node} _node\n * @param {string} parenthesis\n * @return {string|null}\n * @throws {Error}\n */\nexport function getAssociativity(_node, parenthesis) {\n  var node = _node;\n  if (parenthesis !== 'keep') {\n    // ParenthesisNodes are only ignored when not in 'keep' mode\n    node = _node.getContent();\n  }\n  var identifier = node.getIdentifier();\n  var index = getPrecedence(node, parenthesis);\n  if (index === null) {\n    // node isn't in the list\n    return null;\n  }\n  var property = properties[index][identifier];\n  if (hasOwnProperty(property, 'associativity')) {\n    if (property.associativity === 'left') {\n      return 'left';\n    }\n    if (property.associativity === 'right') {\n      return 'right';\n    }\n    // associativity is invalid\n    throw Error('\\'' + identifier + '\\' has the invalid associativity \\'' + property.associativity + '\\'.');\n  }\n\n  // associativity is undefined\n  return null;\n}\n\n/**\n * Check if an operator is associative with another operator.\n * Returns either true or false or null if not defined.\n *\n * @param {Node} nodeA\n * @param {Node} nodeB\n * @param {string} parenthesis\n * @return {boolean | null}\n */\nexport function isAssociativeWith(nodeA, nodeB, parenthesis) {\n  // ParenthesisNodes are only ignored when not in 'keep' mode\n  var a = parenthesis !== 'keep' ? nodeA.getContent() : nodeA;\n  var b = parenthesis !== 'keep' ? nodeA.getContent() : nodeB;\n  var identifierA = a.getIdentifier();\n  var identifierB = b.getIdentifier();\n  var index = getPrecedence(a, parenthesis);\n  if (index === null) {\n    // node isn't in the list\n    return null;\n  }\n  var property = properties[index][identifierA];\n  if (hasOwnProperty(property, 'associativeWith') && property.associativeWith instanceof Array) {\n    for (var i = 0; i < property.associativeWith.length; i++) {\n      if (property.associativeWith[i] === identifierB) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  // associativeWith is not defined\n  return null;\n}\n\n/**\n * Get the operator associated with a function name.\n * Returns a string with the operator symbol, or null if the\n * input is not the name of a function associated with an\n * operator.\n *\n * @param {string} Function name\n * @return {string | null} Associated operator symbol, if any\n */\nexport function getOperator(fn) {\n  var identifier = 'OperatorNode:' + fn;\n  for (var group of properties) {\n    if (identifier in group) {\n      return group[identifier].op;\n    }\n  }\n  return null;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAc,QAAQ,oBAAoB;AACnD,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,SAAS,QAAQ,gBAAgB;AAC7E,OAAO,IAAIC,UAAU,GAAG,CAAC;EACvB;EACAC,cAAc,EAAE,CAAC,CAAC;EAClBC,sBAAsB,EAAE,CAAC;AAC3B,CAAC,EAAE;EACD;EACAC,eAAe,EAAE;IACfC,eAAe,EAAE,KAAK;IACtBC,gBAAgB,EAAE,KAAK;IACvBC,WAAW,EAAE;IACb;IACA;EACF;AACF,CAAC,EAAE;EACD;EACA,iBAAiB,EAAE;IACjBC,EAAE,EAAE,IAAI;IACRC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB;AACF,CAAC,EAAE;EACD;EACA,kBAAkB,EAAE;IAClBF,EAAE,EAAE,KAAK;IACTC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB;AACF,CAAC,EAAE;EACD;EACA,kBAAkB,EAAE;IAClBF,EAAE,EAAE,KAAK;IACTC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB;AACF,CAAC,EAAE;EACD;EACA,oBAAoB,EAAE;IACpBF,EAAE,EAAE,GAAG;IACPC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB;AACF,CAAC,EAAE;EACD;EACA,qBAAqB,EAAE;IACrBF,EAAE,EAAE,IAAI;IACRC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB;AACF,CAAC,EAAE;EACD;EACA,qBAAqB,EAAE;IACrBF,EAAE,EAAE,GAAG;IACPC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB;AACF,CAAC,EAAE;EACD;EACA,oBAAoB,EAAE;IACpBF,EAAE,EAAE,IAAI;IACRC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB,CAAC;EACD,sBAAsB,EAAE;IACtBF,EAAE,EAAE,IAAI;IACRC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB,CAAC;EACD,sBAAsB,EAAE;IACtBF,EAAE,EAAE,GAAG;IACPC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB,CAAC;EACD,qBAAqB,EAAE;IACrBF,EAAE,EAAE,GAAG;IACPC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB,CAAC;EACD,wBAAwB,EAAE;IACxBF,EAAE,EAAE,IAAI;IACRC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB,CAAC;EACD,uBAAuB,EAAE;IACvBF,EAAE,EAAE,IAAI;IACRC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB,CAAC;EACDC,cAAc,EAAE;IACdF,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB;AACF,CAAC,EAAE;EACD;EACA,wBAAwB,EAAE;IACxBF,EAAE,EAAE,IAAI;IACRC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB,CAAC;EACD,8BAA8B,EAAE;IAC9BF,EAAE,EAAE,IAAI;IACRC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB,CAAC;EACD,4BAA4B,EAAE;IAC5BF,EAAE,EAAE,KAAK;IACTC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB;AACF,CAAC,EAAE;EACD;EACA,iBAAiB,EAAE;IACjBF,EAAE,EAAE,IAAI;IACRC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB;AACF,CAAC,EAAE;EACD;EACAE,SAAS,EAAE,CAAC;AACd,CAAC,EAAE;EACD;EACA,kBAAkB,EAAE;IAClBJ,EAAE,EAAE,GAAG;IACPC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE,CAAC,kBAAkB,EAAE,uBAAuB;EAC/D,CAAC;EACD,uBAAuB,EAAE;IACvBF,EAAE,EAAE,GAAG;IACPC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB;AACF,CAAC,EAAE;EACD;EACA,uBAAuB,EAAE;IACvBF,EAAE,EAAE,GAAG;IACPC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE,CAAC,uBAAuB,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,oBAAoB;EAChH,CAAC;EACD,qBAAqB,EAAE;IACrBF,EAAE,EAAE,GAAG;IACPC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE,EAAE;IACnBL,eAAe,EAAE,KAAK;IACtBC,gBAAgB,EAAE,KAAK;IACvBC,WAAW,EAAE;IACb;IACA;IACA;EACF,CAAC;EACD,0BAA0B,EAAE;IAC1BC,EAAE,EAAE,IAAI;IACRC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE,CAAC,uBAAuB,EAAE,qBAAqB,EAAE,0BAA0B,EAAE,uBAAuB;EACvH,CAAC;EACD,wBAAwB,EAAE;IACxBF,EAAE,EAAE,IAAI;IACRC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB,CAAC;EACD,kBAAkB,EAAE;IAClBF,EAAE,EAAE,KAAK;IACTC,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE;EACnB;AACF,CAAC,EAAE;EACD;EACA,uBAAuB,EAAE;IACvBD,aAAa,EAAE,MAAM;IACrBC,eAAe,EAAE,CAAC,uBAAuB,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,oBAAoB;EAChH;AACF,CAAC,EAAE;EACD;EACA,wBAAwB,EAAE;IACxBF,EAAE,EAAE,GAAG;IACPC,aAAa,EAAE;EACjB,CAAC;EACD,yBAAyB,EAAE;IACzBD,EAAE,EAAE,GAAG;IACPC,aAAa,EAAE;EACjB,CAAC;EACD,qBAAqB,EAAE;IACrBD,EAAE,EAAE,GAAG;IACPC,aAAa,EAAE;EACjB,CAAC;EACD,kBAAkB,EAAE;IAClBD,EAAE,EAAE,KAAK;IACTC,aAAa,EAAE;EACjB;AACF,CAAC,EAAE;EACD;EACA,kBAAkB,EAAE;IAClBD,EAAE,EAAE,GAAG;IACPC,aAAa,EAAE,OAAO;IACtBC,eAAe,EAAE,EAAE;IACnBJ,gBAAgB,EAAE;IAClB;IACA;IACA;EACF,CAAC;EACD,qBAAqB,EAAE;IACrBE,EAAE,EAAE,IAAI;IACRC,aAAa,EAAE,OAAO;IACtBC,eAAe,EAAE;EACnB;AACF,CAAC,EAAE;EACD;EACA,wBAAwB,EAAE;IACxBF,EAAE,EAAE,GAAG;IACPC,aAAa,EAAE;EACjB;AACF,CAAC,EAAE;EACD;EACA,yBAAyB,EAAE;IACzBD,EAAE,EAAE,GAAG;IACPC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,WAAWA,CAACC,KAAK,EAAEC,WAAW,EAAE;EACvC,IAAI,CAACA,WAAW,IAAIA,WAAW,KAAK,MAAM,EAAE,OAAOD,KAAK;EACxD,IAAIE,IAAI,GAAGF,KAAK;EAChB,OAAOf,iBAAiB,CAACiB,IAAI,CAAC,EAAEA,IAAI,GAAGA,IAAI,CAACC,OAAO;EACnD,OAAOD,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,aAAaA,CAACJ,KAAK,EAAEC,WAAW,EAAEI,QAAQ,EAAEC,MAAM,EAAE;EAClE,IAAIJ,IAAI,GAAGF,KAAK;EAChB,IAAIC,WAAW,KAAK,MAAM,EAAE;IAC1B;IACAC,IAAI,GAAGF,KAAK,CAACO,UAAU,CAAC,CAAC;EAC3B;EACA,IAAIC,UAAU,GAAGN,IAAI,CAACO,aAAa,CAAC,CAAC;EACrC,IAAIC,UAAU,GAAG,IAAI;EACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,UAAU,CAACyB,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1C,IAAIH,UAAU,IAAIrB,UAAU,CAACwB,CAAC,CAAC,EAAE;MAC/BD,UAAU,GAAGC,CAAC;MACd;IACF;EACF;EACA;EACA;EACA,IAAIH,UAAU,KAAK,uBAAuB,IAAIN,IAAI,CAACG,QAAQ,IAAIA,QAAQ,KAAK,MAAM,EAAE;IAClF,IAAIQ,OAAO,GAAGd,WAAW,CAACG,IAAI,CAACY,IAAI,CAAC,CAAC,CAAC,EAAEb,WAAW,CAAC;IACpD,IAAI,EAAEjB,cAAc,CAAC6B,OAAO,CAAC,IAAIP,MAAM,IAAIA,MAAM,CAACG,aAAa,CAAC,CAAC,KAAK,qBAAqB,IAAIvB,SAAS,CAACa,WAAW,CAACO,MAAM,CAACQ,IAAI,CAAC,CAAC,CAAC,EAAEb,WAAW,CAAC,CAAC,CAAC,IAAI,EAAEY,OAAO,CAACJ,aAAa,CAAC,CAAC,KAAK,qBAAqB,IAAIvB,SAAS,CAACa,WAAW,CAACc,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEb,WAAW,CAAC,CAAC,IAAIjB,cAAc,CAACe,WAAW,CAACc,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnTJ,UAAU,IAAI,CAAC;IACjB;EACF;EACA,OAAOA,UAAU;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,gBAAgBA,CAACf,KAAK,EAAEC,WAAW,EAAE;EACnD,IAAIC,IAAI,GAAGF,KAAK;EAChB,IAAIC,WAAW,KAAK,MAAM,EAAE;IAC1B;IACAC,IAAI,GAAGF,KAAK,CAACO,UAAU,CAAC,CAAC;EAC3B;EACA,IAAIC,UAAU,GAAGN,IAAI,CAACO,aAAa,CAAC,CAAC;EACrC,IAAIO,KAAK,GAAGZ,aAAa,CAACF,IAAI,EAAED,WAAW,CAAC;EAC5C,IAAIe,KAAK,KAAK,IAAI,EAAE;IAClB;IACA,OAAO,IAAI;EACb;EACA,IAAIC,QAAQ,GAAG9B,UAAU,CAAC6B,KAAK,CAAC,CAACR,UAAU,CAAC;EAC5C,IAAIzB,cAAc,CAACkC,QAAQ,EAAE,eAAe,CAAC,EAAE;IAC7C,IAAIA,QAAQ,CAACtB,aAAa,KAAK,MAAM,EAAE;MACrC,OAAO,MAAM;IACf;IACA,IAAIsB,QAAQ,CAACtB,aAAa,KAAK,OAAO,EAAE;MACtC,OAAO,OAAO;IAChB;IACA;IACA,MAAMuB,KAAK,CAAC,IAAI,GAAGV,UAAU,GAAG,qCAAqC,GAAGS,QAAQ,CAACtB,aAAa,GAAG,KAAK,CAAC;EACzG;;EAEA;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASwB,iBAAiBA,CAACC,KAAK,EAAEC,KAAK,EAAEpB,WAAW,EAAE;EAC3D;EACA,IAAIqB,CAAC,GAAGrB,WAAW,KAAK,MAAM,GAAGmB,KAAK,CAACb,UAAU,CAAC,CAAC,GAAGa,KAAK;EAC3D,IAAIG,CAAC,GAAGtB,WAAW,KAAK,MAAM,GAAGmB,KAAK,CAACb,UAAU,CAAC,CAAC,GAAGc,KAAK;EAC3D,IAAIG,WAAW,GAAGF,CAAC,CAACb,aAAa,CAAC,CAAC;EACnC,IAAIgB,WAAW,GAAGF,CAAC,CAACd,aAAa,CAAC,CAAC;EACnC,IAAIO,KAAK,GAAGZ,aAAa,CAACkB,CAAC,EAAErB,WAAW,CAAC;EACzC,IAAIe,KAAK,KAAK,IAAI,EAAE;IAClB;IACA,OAAO,IAAI;EACb;EACA,IAAIC,QAAQ,GAAG9B,UAAU,CAAC6B,KAAK,CAAC,CAACQ,WAAW,CAAC;EAC7C,IAAIzC,cAAc,CAACkC,QAAQ,EAAE,iBAAiB,CAAC,IAAIA,QAAQ,CAACrB,eAAe,YAAY8B,KAAK,EAAE;IAC5F,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,QAAQ,CAACrB,eAAe,CAACgB,MAAM,EAAED,CAAC,EAAE,EAAE;MACxD,IAAIM,QAAQ,CAACrB,eAAe,CAACe,CAAC,CAAC,KAAKc,WAAW,EAAE;QAC/C,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;;EAEA;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,WAAWA,CAACC,EAAE,EAAE;EAC9B,IAAIpB,UAAU,GAAG,eAAe,GAAGoB,EAAE;EACrC,KAAK,IAAIC,KAAK,IAAI1C,UAAU,EAAE;IAC5B,IAAIqB,UAAU,IAAIqB,KAAK,EAAE;MACvB,OAAOA,KAAK,CAACrB,UAAU,CAAC,CAACd,EAAE;IAC7B;EACF;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}