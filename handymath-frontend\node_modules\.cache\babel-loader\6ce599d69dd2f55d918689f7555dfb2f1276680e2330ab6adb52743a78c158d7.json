{"ast": null, "code": "export var isPrimeDocs = {\n  name: 'isPrime',\n  category: 'Utils',\n  syntax: ['isPrime(x)'],\n  description: 'Test whether a value is prime: has no divisors other than itself and one.',\n  examples: ['isPrime(3)', 'isPrime(-2)', 'isPrime([2, 17, 100])'],\n  seealso: ['isInteger', 'isNumeric', 'isNegative', 'isZero']\n};", "map": {"version": 3, "names": ["isPrimeDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/isPrime.js"], "sourcesContent": ["export var isPrimeDocs = {\n  name: 'isPrime',\n  category: 'Utils',\n  syntax: ['isPrime(x)'],\n  description: 'Test whether a value is prime: has no divisors other than itself and one.',\n  examples: ['isPrime(3)', 'isPrime(-2)', 'isPrime([2, 17, 100])'],\n  seealso: ['isInteger', 'isNumeric', 'isNegative', 'isZero']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,YAAY,CAAC;EACtBC,WAAW,EAAE,2EAA2E;EACxFC,QAAQ,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,uBAAuB,CAAC;EAChEC,OAAO,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ;AAC5D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}