{"ast": null, "code": "export var minDocs = {\n  name: 'min',\n  category: 'Statistics',\n  syntax: ['min(a, b, c, ...)', 'min(A)', 'min(A, dimension)'],\n  description: 'Compute the minimum value of a list of values. If any NaN values are found, the function yields the last NaN in the input.',\n  examples: ['min(2, 3, 4, 1)', 'min([2, 3, 4, 1])', 'min([2, 5; 4, 3])', 'min([2, 5; 4, 3], 1)', 'min([2, 5; 4, 3], 2)', 'min(2.7, 7.1, -4.5, 2.0, 4.1)', 'max(2.7, 7.1, -4.5, 2.0, 4.1)'],\n  seealso: ['max', 'mean', 'median', 'prod', 'std', 'sum', 'variance']\n};", "map": {"version": 3, "names": ["minDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/statistics/min.js"], "sourcesContent": ["export var minDocs = {\n  name: 'min',\n  category: 'Statistics',\n  syntax: ['min(a, b, c, ...)', 'min(A)', 'min(A, dimension)'],\n  description: 'Compute the minimum value of a list of values. If any NaN values are found, the function yields the last NaN in the input.',\n  examples: ['min(2, 3, 4, 1)', 'min([2, 3, 4, 1])', 'min([2, 5; 4, 3])', 'min([2, 5; 4, 3], 1)', 'min([2, 5; 4, 3], 2)', 'min(2.7, 7.1, -4.5, 2.0, 4.1)', 'max(2.7, 7.1, -4.5, 2.0, 4.1)'],\n  seealso: ['max', 'mean', 'median', 'prod', 'std', 'sum', 'variance']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,mBAAmB,CAAC;EAC5DC,WAAW,EAAE,4HAA4H;EACzIC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,+BAA+B,EAAE,+BAA+B,CAAC;EACzLC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU;AACrE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}