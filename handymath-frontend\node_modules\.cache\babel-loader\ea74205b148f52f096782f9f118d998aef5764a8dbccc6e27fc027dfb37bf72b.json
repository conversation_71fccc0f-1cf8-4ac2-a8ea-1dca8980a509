{"ast": null, "code": "export var setSizeDocs = {\n  name: 'setSize',\n  category: 'Set',\n  syntax: ['setSize(set)', 'setSize(set, unique)'],\n  description: 'Count the number of elements of a (multi)set. When the second parameter \"unique\" is true, count only the unique values. A multi-dimension array will be converted to a single-dimension array before the operation.',\n  examples: ['setSize([1, 2, 2, 4])', 'setSize([1, 2, 2, 4], true)'],\n  seealso: ['setUnion', 'setIntersect', 'setDifference']\n};", "map": {"version": 3, "names": ["setSizeDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/set/setSize.js"], "sourcesContent": ["export var setSizeDocs = {\n  name: 'setSize',\n  category: 'Set',\n  syntax: ['setSize(set)', 'setSize(set, unique)'],\n  description: 'Count the number of elements of a (multi)set. When the second parameter \"unique\" is true, count only the unique values. A multi-dimension array will be converted to a single-dimension array before the operation.',\n  examples: ['setSize([1, 2, 2, 4])', 'setSize([1, 2, 2, 4], true)'],\n  seealso: ['setUnion', 'setIntersect', 'setDifference']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,CAAC,cAAc,EAAE,sBAAsB,CAAC;EAChDC,WAAW,EAAE,qNAAqN;EAClOC,QAAQ,EAAE,CAAC,uBAAuB,EAAE,6BAA6B,CAAC;EAClEC,OAAO,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,eAAe;AACvD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}