{"ast": null, "code": "export var cloneDocs = {\n  name: 'clone',\n  category: 'Utils',\n  syntax: ['clone(x)'],\n  description: 'Clone a variable. Creates a copy of primitive variables, and a deep copy of matrices',\n  examples: ['clone(3.5)', 'clone(2 - 4i)', 'clone(45 deg)', 'clone([1, 2; 3, 4])', 'clone(\"hello world\")'],\n  seealso: []\n};", "map": {"version": 3, "names": ["cloneDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/clone.js"], "sourcesContent": ["export var cloneDocs = {\n  name: 'clone',\n  category: 'Utils',\n  syntax: ['clone(x)'],\n  description: 'Clone a variable. Creates a copy of primitive variables, and a deep copy of matrices',\n  examples: ['clone(3.5)', 'clone(2 - 4i)', 'clone(45 deg)', 'clone([1, 2; 3, 4])', 'clone(\"hello world\")'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,sFAAsF;EACnGC,QAAQ,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,qBAAqB,EAAE,sBAAsB,CAAC;EACzGC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}