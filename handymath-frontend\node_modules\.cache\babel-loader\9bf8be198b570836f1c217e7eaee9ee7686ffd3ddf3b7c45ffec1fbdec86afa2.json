{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { numericDependencies } from './dependenciesNumeric.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createProd } from '../../factoriesAny.js';\nexport var prodDependencies = {\n  multiplyScalarDependencies,\n  numericDependencies,\n  typedDependencies,\n  createProd\n};", "map": {"version": 3, "names": ["multiplyScalarDependencies", "numericDependencies", "typedDependencies", "createProd", "prodDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesProd.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { numericDependencies } from './dependenciesNumeric.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createProd } from '../../factoriesAny.js';\nexport var prodDependencies = {\n  multiplyScalarDependencies,\n  numericDependencies,\n  typedDependencies,\n  createProd\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,0BAA0B,QAAQ,2CAA2C;AACtF,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAO,IAAIC,gBAAgB,GAAG;EAC5BJ,0BAA0B;EAC1BC,mBAAmB;EACnBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}