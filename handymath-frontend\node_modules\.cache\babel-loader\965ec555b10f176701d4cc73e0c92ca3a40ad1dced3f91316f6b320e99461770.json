{"ast": null, "code": "export var roundDocs = {\n  name: 'round',\n  category: 'Arithmetic',\n  syntax: ['round(x)', 'round(x, n)', 'round(unit, valuelessUnit)', 'round(unit, n, valuelessUnit)'],\n  description: 'round a value towards the nearest integer.If x is complex, both real and imaginary part are rounded towards the nearest integer. When n is specified, the value is rounded to n decimals.',\n  examples: ['round(3.2)', 'round(3.8)', 'round(-4.2)', 'round(-4.8)', 'round(pi, 3)', 'round(123.45678, 2)', 'round(3.241cm, 2, cm)', 'round([3.2, 3.8, -4.7])'],\n  seealso: ['ceil', 'floor', 'fix']\n};", "map": {"version": 3, "names": ["roundDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/round.js"], "sourcesContent": ["export var roundDocs = {\n  name: 'round',\n  category: 'Arithmetic',\n  syntax: ['round(x)', 'round(x, n)', 'round(unit, valuelessUnit)', 'round(unit, n, valuelessUnit)'],\n  description: 'round a value towards the nearest integer.If x is complex, both real and imaginary part are rounded towards the nearest integer. When n is specified, the value is rounded to n decimals.',\n  examples: ['round(3.2)', 'round(3.8)', 'round(-4.2)', 'round(-4.8)', 'round(pi, 3)', 'round(123.45678, 2)', 'round(3.241cm, 2, cm)', 'round([3.2, 3.8, -4.7])'],\n  seealso: ['ceil', 'floor', 'fix']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,4BAA4B,EAAE,+BAA+B,CAAC;EAClGC,WAAW,EAAE,2LAA2L;EACxMC,QAAQ,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,EAAE,qBAAqB,EAAE,uBAAuB,EAAE,yBAAyB,CAAC;EAC/JC,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}