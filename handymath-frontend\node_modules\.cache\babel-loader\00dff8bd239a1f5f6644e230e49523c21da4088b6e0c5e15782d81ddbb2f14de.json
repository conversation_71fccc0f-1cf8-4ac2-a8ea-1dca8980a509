{"ast": null, "code": "export var partitionSelectDocs = {\n  name: 'partitionSelect',\n  category: 'Matrix',\n  syntax: ['partitionSelect(x, k)', 'partitionSelect(x, k, compare)'],\n  description: 'Partition-based selection of an array or 1D matrix. Will find the kth smallest value, and mutates the input array. Uses Quickselect.',\n  examples: ['partitionSelect([5, 10, 1], 2)', 'partitionSelect([\"C\", \"B\", \"A\", \"D\"], 1, compareText)', 'arr = [5, 2, 1]', 'partitionSelect(arr, 0) # returns 1, arr is now: [1, 2, 5]', 'arr', 'partitionSelect(arr, 1, \\'desc\\') # returns 2, arr is now: [5, 2, 1]', 'arr'],\n  seealso: ['sort']\n};", "map": {"version": 3, "names": ["partitionSelectDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/partitionSelect.js"], "sourcesContent": ["export var partitionSelectDocs = {\n  name: 'partitionSelect',\n  category: 'Matrix',\n  syntax: ['partitionSelect(x, k)', 'partitionSelect(x, k, compare)'],\n  description: 'Partition-based selection of an array or 1D matrix. Will find the kth smallest value, and mutates the input array. Uses Quickselect.',\n  examples: ['partitionSelect([5, 10, 1], 2)', 'partitionSelect([\"C\", \"B\", \"A\", \"D\"], 1, compareText)', 'arr = [5, 2, 1]', 'partitionSelect(arr, 0) # returns 1, arr is now: [1, 2, 5]', 'arr', 'partitionSelect(arr, 1, \\'desc\\') # returns 2, arr is now: [5, 2, 1]', 'arr'],\n  seealso: ['sort']\n};"], "mappings": "AAAA,OAAO,IAAIA,mBAAmB,GAAG;EAC/BC,IAAI,EAAE,iBAAiB;EACvBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,uBAAuB,EAAE,gCAAgC,CAAC;EACnEC,WAAW,EAAE,sIAAsI;EACnJC,QAAQ,EAAE,CAAC,gCAAgC,EAAE,uDAAuD,EAAE,iBAAiB,EAAE,4DAA4D,EAAE,KAAK,EAAE,sEAAsE,EAAE,KAAK,CAAC;EAC5QC,OAAO,EAAE,CAAC,MAAM;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}