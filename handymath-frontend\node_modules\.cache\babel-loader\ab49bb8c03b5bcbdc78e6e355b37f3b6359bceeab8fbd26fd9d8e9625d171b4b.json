{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * This function \"flips\" its input about the integer -1.\n *\n * @param {Number}  i               The value to flip\n */\nexport function csFlip(i) {\n  // flip the value\n  return -i - 2;\n}", "map": {"version": 3, "names": ["csFlip", "i"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csFlip.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * This function \"flips\" its input about the integer -1.\n *\n * @param {Number}  i               The value to flip\n */\nexport function csFlip(i) {\n  // flip the value\n  return -i - 2;\n}"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,MAAMA,CAACC,CAAC,EAAE;EACxB;EACA,OAAO,CAACA,CAAC,GAAG,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}