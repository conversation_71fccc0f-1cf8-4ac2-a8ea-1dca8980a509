{"ast": null, "code": "export var invmodDocs = {\n  name: 'invmod',\n  category: 'Arithmetic',\n  syntax: ['invmod(a, b)'],\n  description: 'Calculate the (modular) multiplicative inverse of a modulo b. Solution to the equation ax ≣ 1 (mod b)',\n  examples: ['invmod(8, 12)', 'invmod(7, 13)', 'invmod(15151, 15122)'],\n  seealso: ['gcd', 'xgcd']\n};", "map": {"version": 3, "names": ["invmodDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/invmod.js"], "sourcesContent": ["export var invmodDocs = {\n  name: 'invmod',\n  category: 'Arithmetic',\n  syntax: ['invmod(a, b)'],\n  description: 'Calculate the (modular) multiplicative inverse of a modulo b. Solution to the equation ax ≣ 1 (mod b)',\n  examples: ['invmod(8, 12)', 'invmod(7, 13)', 'invmod(15151, 15122)'],\n  seealso: ['gcd', 'xgcd']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,cAAc,CAAC;EACxBC,WAAW,EAAE,uGAAuG;EACpHC,QAAQ,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,sBAAsB,CAAC;EACpEC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}