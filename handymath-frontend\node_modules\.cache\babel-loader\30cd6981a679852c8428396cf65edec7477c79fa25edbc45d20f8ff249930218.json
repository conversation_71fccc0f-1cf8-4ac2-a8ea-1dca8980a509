{"ast": null, "code": "export var parserDocs = {\n  name: 'parser',\n  category: 'Expression',\n  syntax: ['parser()'],\n  description: 'Create a parser object that keeps a context of variables and their values, allowing the evaluation of expressions in that context.',\n  examples: ['myParser = parser()', 'myParser.evaluate(\"sqrt(3^2 + 4^2)\")', 'myParser.set(\"x\", 3)', 'myParser.evaluate(\"y = x + 3\")', 'myParser.evaluate([\"y = x + 3\", \"y = y + 1\"])', 'myParser.get(\"y\")'],\n  seealso: ['evaluate', 'parse', 'compile']\n};", "map": {"version": 3, "names": ["parserDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/expression/parser.js"], "sourcesContent": ["export var parserDocs = {\n  name: 'parser',\n  category: 'Expression',\n  syntax: ['parser()'],\n  description: 'Create a parser object that keeps a context of variables and their values, allowing the evaluation of expressions in that context.',\n  examples: ['myParser = parser()', 'myParser.evaluate(\"sqrt(3^2 + 4^2)\")', 'myParser.set(\"x\", 3)', 'myParser.evaluate(\"y = x + 3\")', 'myParser.evaluate([\"y = x + 3\", \"y = y + 1\"])', 'myParser.get(\"y\")'],\n  seealso: ['evaluate', 'parse', 'compile']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,oIAAoI;EACjJC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,sCAAsC,EAAE,sBAAsB,EAAE,gCAAgC,EAAE,+CAA+C,EAAE,mBAAmB,CAAC;EACzMC,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}