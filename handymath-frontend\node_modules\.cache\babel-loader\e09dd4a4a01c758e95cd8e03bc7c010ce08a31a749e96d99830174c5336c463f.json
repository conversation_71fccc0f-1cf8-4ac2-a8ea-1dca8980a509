{"ast": null, "code": "export var forEachDocs = {\n  name: 'forEach',\n  category: 'Matrix',\n  syntax: ['forEach(x, callback)'],\n  description: 'Iterates over all elements of a matrix/array, and executes the given callback function.',\n  examples: ['numberOfPets = {}', 'addPet(n) = numberOfPets[n] = (numberOfPets[n] ? numberOfPets[n]:0 ) + 1;', 'forEach([\"<PERSON>\",\"<PERSON>\",\"Cat\"], addPet)', 'numberOfPets'],\n  seealso: ['map', 'sort', 'filter']\n};", "map": {"version": 3, "names": ["forEachDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/forEach.js"], "sourcesContent": ["export var forEachDocs = {\n  name: 'forEach',\n  category: 'Matrix',\n  syntax: ['forEach(x, callback)'],\n  description: 'Iterates over all elements of a matrix/array, and executes the given callback function.',\n  examples: ['numberOfPets = {}', 'addPet(n) = numberOfPets[n] = (numberOfPets[n] ? numberOfPets[n]:0 ) + 1;', 'forEach([\"<PERSON>\",\"<PERSON>\",\"Cat\"], addPet)', 'numberOfPets'],\n  seealso: ['map', 'sort', 'filter']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,sBAAsB,CAAC;EAChCC,WAAW,EAAE,yFAAyF;EACtGC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,2EAA2E,EAAE,sCAAsC,EAAE,cAAc,CAAC;EACpKC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ;AACnC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}