{"ast": null, "code": "export var freqzDocs = {\n  name: 'freqz',\n  category: 'Signal',\n  syntax: ['freqz(b, a)', 'freqz(b, a, w)'],\n  description: 'Calculates the frequency response of a filter given its numerator and denominator coefficients.',\n  examples: ['freqz([1, 2], [1, 2, 3])', 'freqz([1, 2], [1, 2, 3], [0, 1])', 'freqz([1, 2], [1, 2, 3], 512)'],\n  seealso: []\n};", "map": {"version": 3, "names": ["freqzDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/signal/freqz.js"], "sourcesContent": ["export var freqzDocs = {\n  name: 'freqz',\n  category: 'Signal',\n  syntax: ['freqz(b, a)', 'freqz(b, a, w)'],\n  description: 'Calculates the frequency response of a filter given its numerator and denominator coefficients.',\n  examples: ['freqz([1, 2], [1, 2, 3])', 'freqz([1, 2], [1, 2, 3], [0, 1])', 'freqz([1, 2], [1, 2, 3], 512)'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,aAAa,EAAE,gBAAgB,CAAC;EACzCC,WAAW,EAAE,iGAAiG;EAC9GC,QAAQ,EAAE,CAAC,0BAA0B,EAAE,kCAAkC,EAAE,+BAA+B,CAAC;EAC3GC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}