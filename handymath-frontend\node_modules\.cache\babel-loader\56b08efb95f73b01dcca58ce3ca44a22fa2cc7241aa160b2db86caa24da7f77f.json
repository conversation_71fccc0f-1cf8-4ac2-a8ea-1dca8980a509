{"ast": null, "code": "export var SQRT2Docs = {\n  name: 'SQRT2',\n  category: 'Constants',\n  syntax: ['SQRT2'],\n  description: 'Returns the square root of 2, approximately equal to 1.414',\n  examples: ['SQRT2', 'sqrt(2)'],\n  seealso: []\n};", "map": {"version": 3, "names": ["SQRT2Docs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/SQRT2.js"], "sourcesContent": ["export var SQRT2Docs = {\n  name: 'SQRT2',\n  category: 'Constants',\n  syntax: ['SQRT2'],\n  description: 'Returns the square root of 2, approximately equal to 1.414',\n  examples: ['SQRT2', 'sqrt(2)'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,OAAO,CAAC;EACjBC,WAAW,EAAE,4DAA4D;EACzEC,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;EAC9BC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}