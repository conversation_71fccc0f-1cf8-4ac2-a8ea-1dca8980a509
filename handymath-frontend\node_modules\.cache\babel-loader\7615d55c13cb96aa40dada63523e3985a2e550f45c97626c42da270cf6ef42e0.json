{"ast": null, "code": "export var binDocs = {\n  name: 'bin',\n  category: 'Utils',\n  syntax: ['bin(value)'],\n  description: 'Format a number as binary',\n  examples: ['bin(2)'],\n  seealso: ['oct', 'hex']\n};", "map": {"version": 3, "names": ["binDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/bin.js"], "sourcesContent": ["export var binDocs = {\n  name: 'bin',\n  category: 'Utils',\n  syntax: ['bin(value)'],\n  description: 'Format a number as binary',\n  examples: ['bin(2)'],\n  seealso: ['oct', 'hex']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,YAAY,CAAC;EACtBC,WAAW,EAAE,2BAA2B;EACxCC,QAAQ,EAAE,CAAC,QAAQ,CAAC;EACpBC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}