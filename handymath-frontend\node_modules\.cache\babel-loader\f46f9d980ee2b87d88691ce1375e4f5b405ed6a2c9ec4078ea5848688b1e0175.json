{"ast": null, "code": "export var argDocs = {\n  name: 'arg',\n  category: 'Complex',\n  syntax: ['arg(x)'],\n  description: 'Compute the argument of a complex value. If x = a+bi, the argument is computed as atan2(b, a).',\n  examples: ['arg(2 + 2i)', 'atan2(3, 2)', 'arg(2 + 3i)'],\n  seealso: ['re', 'im', 'conj', 'abs']\n};", "map": {"version": 3, "names": ["argDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/complex/arg.js"], "sourcesContent": ["export var argDocs = {\n  name: 'arg',\n  category: 'Complex',\n  syntax: ['arg(x)'],\n  description: 'Compute the argument of a complex value. If x = a+bi, the argument is computed as atan2(b, a).',\n  examples: ['arg(2 + 2i)', 'atan2(3, 2)', 'arg(2 + 3i)'],\n  seealso: ['re', 'im', 'conj', 'abs']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,gGAAgG;EAC7GC,QAAQ,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;EACvDC,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}