{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { identityDependencies } from './dependenciesIdentity.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { normDependencies } from './dependenciesNorm.generated.js';\nimport { qrDependencies } from './dependenciesQr.generated.js';\nimport { subtractDependencies } from './dependenciesSubtract.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSchur } from '../../factoriesAny.js';\nexport var schurDependencies = {\n  identityDependencies,\n  matrixDependencies,\n  multiplyDependencies,\n  normDependencies,\n  qrDependencies,\n  subtractDependencies,\n  typedDependencies,\n  createSchur\n};", "map": {"version": 3, "names": ["identityDependencies", "matrixDependencies", "multiplyDependencies", "normDependencies", "qrDependencies", "subtractDependencies", "typedDependencies", "createSchur", "schurDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSchur.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { identityDependencies } from './dependenciesIdentity.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { normDependencies } from './dependenciesNorm.generated.js';\nimport { qrDependencies } from './dependenciesQr.generated.js';\nimport { subtractDependencies } from './dependenciesSubtract.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSchur } from '../../factoriesAny.js';\nexport var schurDependencies = {\n  identityDependencies,\n  matrixDependencies,\n  multiplyDependencies,\n  normDependencies,\n  qrDependencies,\n  subtractDependencies,\n  typedDependencies,\n  createSchur\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,WAAW,QAAQ,uBAAuB;AACnD,OAAO,IAAIC,iBAAiB,GAAG;EAC7BR,oBAAoB;EACpBC,kBAAkB;EAClBC,oBAAoB;EACpBC,gBAAgB;EAChBC,cAAc;EACdC,oBAAoB;EACpBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}