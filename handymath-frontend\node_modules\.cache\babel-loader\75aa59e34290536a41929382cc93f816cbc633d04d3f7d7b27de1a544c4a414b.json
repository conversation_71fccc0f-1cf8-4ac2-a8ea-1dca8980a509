{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { rotationMatrixDependencies } from './dependenciesRotationMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createRotate } from '../../factoriesAny.js';\nexport var rotateDependencies = {\n  multiplyDependencies,\n  rotationMatrixDependencies,\n  typedDependencies,\n  createRotate\n};", "map": {"version": 3, "names": ["multiplyDependencies", "rotationMatrixDependencies", "typedDependencies", "createRotate", "rotateDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesRotate.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { rotationMatrixDependencies } from './dependenciesRotationMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createRotate } from '../../factoriesAny.js';\nexport var rotateDependencies = {\n  multiplyDependencies,\n  rotationMatrixDependencies,\n  typedDependencies,\n  createRotate\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,0BAA0B,QAAQ,2CAA2C;AACtF,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAO,IAAIC,kBAAkB,GAAG;EAC9BJ,oBAAoB;EACpBC,0BAA0B;EAC1BC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}