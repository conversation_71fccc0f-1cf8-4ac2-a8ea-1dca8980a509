{"ast": null, "code": "import { isSymbolNode } from '../../../utils/is.js';\nimport { PartitionedMap } from '../../../utils/map.js';\n\n/**\n * Compile an inline expression like \"x > 0\"\n * @param {Node} expression\n * @param {Object} math\n * @param {Map} scope\n * @return {function} Returns a function with one argument which fills in the\n *                    undefined variable (like \"x\") and evaluates the expression\n */\nexport function compileInlineExpression(expression, math, scope) {\n  // find an undefined symbol\n  var symbol = expression.filter(function (node) {\n    return isSymbolNode(node) && !(node.name in math) && !scope.has(node.name);\n  })[0];\n  if (!symbol) {\n    throw new Error('No undefined variable found in inline expression \"' + expression + '\"');\n  }\n\n  // create a test function for this equation\n  var name = symbol.name; // variable name\n  var argsScope = new Map();\n  var subScope = new PartitionedMap(scope, argsScope, new Set([name]));\n  var eq = expression.compile();\n  return function inlineExpression(x) {\n    argsScope.set(name, x);\n    return eq.evaluate(subScope);\n  };\n}", "map": {"version": 3, "names": ["isSymbolNode", "PartitionedMap", "compileInlineExpression", "expression", "math", "scope", "symbol", "filter", "node", "name", "has", "Error", "argsScope", "Map", "subScope", "Set", "eq", "compile", "inlineExpression", "x", "set", "evaluate"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/transform/utils/compileInlineExpression.js"], "sourcesContent": ["import { isSymbolNode } from '../../../utils/is.js';\nimport { PartitionedMap } from '../../../utils/map.js';\n\n/**\n * Compile an inline expression like \"x > 0\"\n * @param {Node} expression\n * @param {Object} math\n * @param {Map} scope\n * @return {function} Returns a function with one argument which fills in the\n *                    undefined variable (like \"x\") and evaluates the expression\n */\nexport function compileInlineExpression(expression, math, scope) {\n  // find an undefined symbol\n  var symbol = expression.filter(function (node) {\n    return isSymbolNode(node) && !(node.name in math) && !scope.has(node.name);\n  })[0];\n  if (!symbol) {\n    throw new Error('No undefined variable found in inline expression \"' + expression + '\"');\n  }\n\n  // create a test function for this equation\n  var name = symbol.name; // variable name\n  var argsScope = new Map();\n  var subScope = new PartitionedMap(scope, argsScope, new Set([name]));\n  var eq = expression.compile();\n  return function inlineExpression(x) {\n    argsScope.set(name, x);\n    return eq.evaluate(subScope);\n  };\n}"], "mappings": "AAAA,SAASA,YAAY,QAAQ,sBAAsB;AACnD,SAASC,cAAc,QAAQ,uBAAuB;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAACC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAE;EAC/D;EACA,IAAIC,MAAM,GAAGH,UAAU,CAACI,MAAM,CAAC,UAAUC,IAAI,EAAE;IAC7C,OAAOR,YAAY,CAACQ,IAAI,CAAC,IAAI,EAAEA,IAAI,CAACC,IAAI,IAAIL,IAAI,CAAC,IAAI,CAACC,KAAK,CAACK,GAAG,CAACF,IAAI,CAACC,IAAI,CAAC;EAC5E,CAAC,CAAC,CAAC,CAAC,CAAC;EACL,IAAI,CAACH,MAAM,EAAE;IACX,MAAM,IAAIK,KAAK,CAAC,oDAAoD,GAAGR,UAAU,GAAG,GAAG,CAAC;EAC1F;;EAEA;EACA,IAAIM,IAAI,GAAGH,MAAM,CAACG,IAAI,CAAC,CAAC;EACxB,IAAIG,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EACzB,IAAIC,QAAQ,GAAG,IAAIb,cAAc,CAACI,KAAK,EAAEO,SAAS,EAAE,IAAIG,GAAG,CAAC,CAACN,IAAI,CAAC,CAAC,CAAC;EACpE,IAAIO,EAAE,GAAGb,UAAU,CAACc,OAAO,CAAC,CAAC;EAC7B,OAAO,SAASC,gBAAgBA,CAACC,CAAC,EAAE;IAClCP,SAAS,CAACQ,GAAG,CAACX,IAAI,EAAEU,CAAC,CAAC;IACtB,OAAOH,EAAE,CAACK,QAAQ,CAACP,QAAQ,CAAC;EAC9B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}