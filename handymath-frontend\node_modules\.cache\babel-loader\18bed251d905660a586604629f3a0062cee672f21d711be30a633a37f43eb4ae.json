{"ast": null, "code": "export var asechDocs = {\n  name: 'asech',\n  category: 'Trigonometry',\n  syntax: ['asech(x)'],\n  description: 'Calculate the inverse secant of a value.',\n  examples: ['asech(0.5)'],\n  seealso: ['acsch', 'acoth']\n};", "map": {"version": 3, "names": ["asechDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/asech.js"], "sourcesContent": ["export var asechDocs = {\n  name: 'asech',\n  category: 'Trigonometry',\n  syntax: ['asech(x)'],\n  description: 'Calculate the inverse secant of a value.',\n  examples: ['asech(0.5)'],\n  seealso: ['acsch', 'acoth']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,0CAA0C;EACvDC,QAAQ,EAAE,CAAC,YAAY,CAAC;EACxBC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}