{"ast": null, "code": "export var squareDocs = {\n  name: 'square',\n  category: 'Arithmetic',\n  syntax: ['square(x)'],\n  description: 'Compute the square of a value. The square of x is x * x.',\n  examples: ['square(3)', 'sqrt(9)', '3^2', '3 * 3'],\n  seealso: ['multiply', 'pow', 'sqrt', 'cube']\n};", "map": {"version": 3, "names": ["squareDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/square.js"], "sourcesContent": ["export var squareDocs = {\n  name: 'square',\n  category: 'Arithmetic',\n  syntax: ['square(x)'],\n  description: 'Compute the square of a value. The square of x is x * x.',\n  examples: ['square(3)', 'sqrt(9)', '3^2', '3 * 3'],\n  seealso: ['multiply', 'pow', 'sqrt', 'cube']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,WAAW,CAAC;EACrBC,WAAW,EAAE,0DAA0D;EACvEC,QAAQ,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC;EAClDC,OAAO,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM;AAC7C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}