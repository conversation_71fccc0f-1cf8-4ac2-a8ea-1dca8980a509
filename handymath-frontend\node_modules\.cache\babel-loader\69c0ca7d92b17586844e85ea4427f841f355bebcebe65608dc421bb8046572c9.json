{"ast": null, "code": "import { clone } from '../../../utils/object.js';\nexport function createComplexEigs(_ref) {\n  var {\n    addScalar,\n    subtract,\n    flatten,\n    multiply,\n    multiplyScalar,\n    divideScalar,\n    sqrt,\n    abs,\n    bignumber,\n    diag,\n    size,\n    reshape,\n    inv,\n    qr,\n    usolve,\n    usolveAll,\n    equal,\n    complex,\n    larger,\n    smaller,\n    matrixFromColumns,\n    dot\n  } = _ref;\n  /**\n   * @param {number[][]} arr the matrix to find eigenvalues of\n   * @param {number} N size of the matrix\n   * @param {number|BigNumber} prec precision, anything lower will be considered zero\n   * @param {'number'|'BigNumber'|'Complex'} type\n   * @param {boolean} findVectors should we find eigenvectors?\n   *\n   * @returns {{ values: number[], vectors: number[][] }}\n   */\n  function complexEigs(arr, N, prec, type) {\n    var findVectors = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n    // TODO check if any row/col are zero except the diagonal\n\n    // make sure corresponding rows and columns have similar magnitude\n    // important because of numerical stability\n    // MODIFIES arr by side effect!\n    var R = balance(arr, N, prec, type, findVectors);\n\n    // R is the row transformation matrix\n    // arr = A' = R A R^-1, A is the original matrix\n    // (if findVectors is false, R is undefined)\n    // (And so to return to original matrix: A = R^-1 arr R)\n\n    // TODO if magnitudes of elements vary over many orders,\n    // move greatest elements to the top left corner\n\n    // using similarity transformations, reduce the matrix\n    // to Hessenberg form (upper triangular plus one subdiagonal row)\n    // updates the transformation matrix R with new row operationsq\n    // MODIFIES arr by side effect!\n    reduceToHessenberg(arr, N, prec, type, findVectors, R);\n    // still true that original A = R^-1 arr R)\n\n    // find eigenvalues\n    var {\n      values,\n      C\n    } = iterateUntilTriangular(arr, N, prec, type, findVectors);\n\n    // values is the list of eigenvalues, C is the column\n    // transformation matrix that transforms arr, the hessenberg\n    // matrix, to upper triangular\n    // (So U = C^-1 arr C and the relationship between current arr\n    // and original A is unchanged.)\n\n    if (findVectors) {\n      var eigenvectors = findEigenvectors(arr, N, C, R, values, prec, type);\n      return {\n        values,\n        eigenvectors\n      };\n    }\n    return {\n      values\n    };\n  }\n\n  /**\n   * @param {number[][]} arr\n   * @param {number} N\n   * @param {number} prec\n   * @param {'number'|'BigNumber'|'Complex'} type\n   * @returns {number[][]}\n   */\n  function balance(arr, N, prec, type, findVectors) {\n    var big = type === 'BigNumber';\n    var cplx = type === 'Complex';\n    var realzero = big ? bignumber(0) : 0;\n    var one = big ? bignumber(1) : cplx ? complex(1) : 1;\n    var realone = big ? bignumber(1) : 1;\n\n    // base of the floating-point arithmetic\n    var radix = big ? bignumber(10) : 2;\n    var radixSq = multiplyScalar(radix, radix);\n\n    // the diagonal transformation matrix R\n    var Rdiag;\n    if (findVectors) {\n      Rdiag = Array(N).fill(one);\n    }\n\n    // this isn't the only time we loop thru the matrix...\n    var last = false;\n    while (!last) {\n      // ...haha I'm joking! unless...\n      last = true;\n      for (var i = 0; i < N; i++) {\n        // compute the taxicab norm of i-th column and row\n        // TODO optimize for complex numbers\n        var colNorm = realzero;\n        var rowNorm = realzero;\n        for (var j = 0; j < N; j++) {\n          if (i === j) continue;\n          colNorm = addScalar(colNorm, abs(arr[j][i]));\n          rowNorm = addScalar(rowNorm, abs(arr[i][j]));\n        }\n        if (!equal(colNorm, 0) && !equal(rowNorm, 0)) {\n          // find integer power closest to balancing the matrix\n          // (we want to scale only by integer powers of radix,\n          // so that we don't lose any precision due to round-off)\n\n          var f = realone;\n          var c = colNorm;\n          var rowDivRadix = divideScalar(rowNorm, radix);\n          var rowMulRadix = multiplyScalar(rowNorm, radix);\n          while (smaller(c, rowDivRadix)) {\n            c = multiplyScalar(c, radixSq);\n            f = multiplyScalar(f, radix);\n          }\n          while (larger(c, rowMulRadix)) {\n            c = divideScalar(c, radixSq);\n            f = divideScalar(f, radix);\n          }\n\n          // check whether balancing is needed\n          // condition = (c + rowNorm) / f < 0.95 * (colNorm + rowNorm)\n          var condition = smaller(divideScalar(addScalar(c, rowNorm), f), multiplyScalar(addScalar(colNorm, rowNorm), 0.95));\n\n          // apply balancing similarity transformation\n          if (condition) {\n            // we should loop once again to check whether\n            // another rebalancing is needed\n            last = false;\n            var g = divideScalar(1, f);\n            for (var _j = 0; _j < N; _j++) {\n              if (i === _j) {\n                continue;\n              }\n              arr[i][_j] = multiplyScalar(arr[i][_j], g);\n              arr[_j][i] = multiplyScalar(arr[_j][i], f);\n            }\n\n            // keep track of transformations\n            if (findVectors) {\n              Rdiag[i] = multiplyScalar(Rdiag[i], g);\n            }\n          }\n        }\n      }\n    }\n\n    // return the diagonal row transformation matrix\n    return findVectors ? diag(Rdiag) : null;\n  }\n\n  /**\n   * @param {number[][]} arr\n   * @param {number} N\n   * @param {number} prec\n   * @param {'number'|'BigNumber'|'Complex'} type\n   * @param {boolean} findVectors\n   * @param {number[][]} R the row transformation matrix that will be modified\n   */\n  function reduceToHessenberg(arr, N, prec, type, findVectors, R) {\n    var big = type === 'BigNumber';\n    var cplx = type === 'Complex';\n    var zero = big ? bignumber(0) : cplx ? complex(0) : 0;\n    if (big) {\n      prec = bignumber(prec);\n    }\n    for (var i = 0; i < N - 2; i++) {\n      // Find the largest subdiag element in the i-th col\n\n      var maxIndex = 0;\n      var max = zero;\n      for (var j = i + 1; j < N; j++) {\n        var el = arr[j][i];\n        if (smaller(abs(max), abs(el))) {\n          max = el;\n          maxIndex = j;\n        }\n      }\n\n      // This col is pivoted, no need to do anything\n      if (smaller(abs(max), prec)) {\n        continue;\n      }\n      if (maxIndex !== i + 1) {\n        // Interchange maxIndex-th and (i+1)-th row\n        var tmp1 = arr[maxIndex];\n        arr[maxIndex] = arr[i + 1];\n        arr[i + 1] = tmp1;\n\n        // Interchange maxIndex-th and (i+1)-th column\n        for (var _j2 = 0; _j2 < N; _j2++) {\n          var tmp2 = arr[_j2][maxIndex];\n          arr[_j2][maxIndex] = arr[_j2][i + 1];\n          arr[_j2][i + 1] = tmp2;\n        }\n\n        // keep track of transformations\n        if (findVectors) {\n          var tmp3 = R[maxIndex];\n          R[maxIndex] = R[i + 1];\n          R[i + 1] = tmp3;\n        }\n      }\n\n      // Reduce following rows and columns\n      for (var _j3 = i + 2; _j3 < N; _j3++) {\n        var n = divideScalar(arr[_j3][i], max);\n        if (n === 0) {\n          continue;\n        }\n\n        // from j-th row subtract n-times (i+1)th row\n        for (var k = 0; k < N; k++) {\n          arr[_j3][k] = subtract(arr[_j3][k], multiplyScalar(n, arr[i + 1][k]));\n        }\n\n        // to (i+1)th column add n-times j-th column\n        for (var _k = 0; _k < N; _k++) {\n          arr[_k][i + 1] = addScalar(arr[_k][i + 1], multiplyScalar(n, arr[_k][_j3]));\n        }\n\n        // keep track of transformations\n        if (findVectors) {\n          for (var _k2 = 0; _k2 < N; _k2++) {\n            R[_j3][_k2] = subtract(R[_j3][_k2], multiplyScalar(n, R[i + 1][_k2]));\n          }\n        }\n      }\n    }\n    return R;\n  }\n\n  /**\n   * @returns {{values: values, C: Matrix}}\n   * @see Press, Wiliams: Numerical recipes in Fortran 77\n   * @see https://en.wikipedia.org/wiki/QR_algorithm\n   */\n  function iterateUntilTriangular(A, N, prec, type, findVectors) {\n    var big = type === 'BigNumber';\n    var cplx = type === 'Complex';\n    var one = big ? bignumber(1) : cplx ? complex(1) : 1;\n    if (big) {\n      prec = bignumber(prec);\n    }\n\n    // The Francis Algorithm\n    // The core idea of this algorithm is that doing successive\n    // A' = QtAQ transformations will eventually converge to block-\n    // upper-triangular with diagonal blocks either 1x1 or 2x2.\n    // The Q here is the one from the QR decomposition, A = QR.\n    // Since the eigenvalues of a block-upper-triangular matrix are\n    // the eigenvalues of its diagonal blocks and we know how to find\n    // eigenvalues of a 2x2 matrix, we know the eigenvalues of A.\n\n    var arr = clone(A);\n\n    // the list of converged eigenvalues\n    var lambdas = [];\n\n    // size of arr, which will get smaller as eigenvalues converge\n    var n = N;\n\n    // the diagonal of the block-diagonal matrix that turns\n    // converged 2x2 matrices into upper triangular matrices\n    var Sdiag = [];\n\n    // N×N matrix describing the overall transformation done during the QR algorithm\n    var Qtotal = findVectors ? diag(Array(N).fill(one)) : undefined;\n\n    // nxn matrix describing the QR transformations done since last convergence\n    var Qpartial = findVectors ? diag(Array(n).fill(one)) : undefined;\n\n    // last eigenvalue converged before this many steps\n    var lastConvergenceBefore = 0;\n    while (lastConvergenceBefore <= 100) {\n      lastConvergenceBefore += 1;\n\n      // TODO if the convergence is slow, do something clever\n\n      // Perform the factorization\n\n      var k = arr[n - 1][n - 1]; // TODO this is apparently a somewhat\n      // old-fashioned choice; ideally set close to an eigenvalue, or\n      // perhaps better yet switch to the implicit QR version that is sometimes\n      // specifically called the \"Francis algorithm\" that is alluded to\n      // in the following TODO. (Or perhaps we switch to an independently\n      // optimized third-party package for the linear algebra operations...)\n\n      for (var i = 0; i < n; i++) {\n        arr[i][i] = subtract(arr[i][i], k);\n      }\n\n      // TODO do an implicit QR transformation\n      var {\n        Q,\n        R\n      } = qr(arr);\n      arr = multiply(R, Q);\n      for (var _i = 0; _i < n; _i++) {\n        arr[_i][_i] = addScalar(arr[_i][_i], k);\n      }\n\n      // keep track of transformations\n      if (findVectors) {\n        Qpartial = multiply(Qpartial, Q);\n      }\n\n      // The rightmost diagonal element converged to an eigenvalue\n      if (n === 1 || smaller(abs(arr[n - 1][n - 2]), prec)) {\n        lastConvergenceBefore = 0;\n        lambdas.push(arr[n - 1][n - 1]);\n\n        // keep track of transformations\n        if (findVectors) {\n          Sdiag.unshift([[1]]);\n          inflateMatrix(Qpartial, N);\n          Qtotal = multiply(Qtotal, Qpartial);\n          if (n > 1) {\n            Qpartial = diag(Array(n - 1).fill(one));\n          }\n        }\n\n        // reduce the matrix size\n        n -= 1;\n        arr.pop();\n        for (var _i2 = 0; _i2 < n; _i2++) {\n          arr[_i2].pop();\n        }\n\n        // The rightmost diagonal 2x2 block converged\n      } else if (n === 2 || smaller(abs(arr[n - 2][n - 3]), prec)) {\n        lastConvergenceBefore = 0;\n        var ll = eigenvalues2x2(arr[n - 2][n - 2], arr[n - 2][n - 1], arr[n - 1][n - 2], arr[n - 1][n - 1]);\n        lambdas.push(...ll);\n\n        // keep track of transformations\n        if (findVectors) {\n          Sdiag.unshift(jordanBase2x2(arr[n - 2][n - 2], arr[n - 2][n - 1], arr[n - 1][n - 2], arr[n - 1][n - 1], ll[0], ll[1], prec, type));\n          inflateMatrix(Qpartial, N);\n          Qtotal = multiply(Qtotal, Qpartial);\n          if (n > 2) {\n            Qpartial = diag(Array(n - 2).fill(one));\n          }\n        }\n\n        // reduce the matrix size\n        n -= 2;\n        arr.pop();\n        arr.pop();\n        for (var _i3 = 0; _i3 < n; _i3++) {\n          arr[_i3].pop();\n          arr[_i3].pop();\n        }\n      }\n      if (n === 0) {\n        break;\n      }\n    }\n\n    // standard sorting\n    lambdas.sort((a, b) => +subtract(abs(a), abs(b)));\n\n    // the algorithm didn't converge\n    if (lastConvergenceBefore > 100) {\n      var err = Error('The eigenvalues failed to converge. Only found these eigenvalues: ' + lambdas.join(', '));\n      err.values = lambdas;\n      err.vectors = [];\n      throw err;\n    }\n\n    // combine the overall QR transformation Qtotal with the subsequent\n    // transformation S that turns the diagonal 2x2 blocks to upper triangular\n    var C = findVectors ? multiply(Qtotal, blockDiag(Sdiag, N)) : undefined;\n    return {\n      values: lambdas,\n      C\n    };\n  }\n\n  /**\n   * @param {Matrix} A hessenberg-form matrix\n   * @param {number} N size of A\n   * @param {Matrix} C column transformation matrix that turns A into upper triangular\n   * @param {Matrix} R similarity that turns original matrix into A\n   * @param {number[]} values array of eigenvalues of A\n   * @param {'number'|'BigNumber'|'Complex'} type\n   * @returns {number[][]} eigenvalues\n   */\n  function findEigenvectors(A, N, C, R, values, prec, type) {\n    var Cinv = inv(C);\n    var U = multiply(Cinv, A, C);\n    var big = type === 'BigNumber';\n    var cplx = type === 'Complex';\n    var zero = big ? bignumber(0) : cplx ? complex(0) : 0;\n    var one = big ? bignumber(1) : cplx ? complex(1) : 1;\n\n    // turn values into a kind of \"multiset\"\n    // this way it is easier to find eigenvectors\n    var uniqueValues = [];\n    var multiplicities = [];\n    for (var lambda of values) {\n      var i = indexOf(uniqueValues, lambda, equal);\n      if (i === -1) {\n        uniqueValues.push(lambda);\n        multiplicities.push(1);\n      } else {\n        multiplicities[i] += 1;\n      }\n    }\n\n    // find eigenvectors by solving U − lambdaE = 0\n    // TODO replace with an iterative eigenvector algorithm\n    // (this one might fail for imprecise eigenvalues)\n\n    var vectors = [];\n    var len = uniqueValues.length;\n    var b = Array(N).fill(zero);\n    var E = diag(Array(N).fill(one));\n    var _loop = function _loop() {\n      var lambda = uniqueValues[_i4];\n      var S = subtract(U, multiply(lambda, E)); // the characteristic matrix\n\n      var solutions = usolveAll(S, b);\n      solutions.shift(); // ignore the null vector\n\n      // looks like we missed something, try inverse iteration\n      // But if that fails, just presume that the original matrix truly\n      // was defective.\n      while (solutions.length < multiplicities[_i4]) {\n        var approxVec = inverseIterate(S, N, solutions, prec, type);\n        if (approxVec === null) {\n          break;\n        } // no more vectors were found\n        solutions.push(approxVec);\n      }\n\n      // Transform back into original array coordinates\n      var correction = multiply(inv(R), C);\n      solutions = solutions.map(v => multiply(correction, v));\n      vectors.push(...solutions.map(v => ({\n        value: lambda,\n        vector: flatten(v)\n      })));\n    };\n    for (var _i4 = 0; _i4 < len; _i4++) {\n      _loop();\n    }\n    return vectors;\n  }\n\n  /**\n   * Compute the eigenvalues of an 2x2 matrix\n   * @return {[number,number]}\n   */\n  function eigenvalues2x2(a, b, c, d) {\n    // lambda_+- = 1/2 trA +- 1/2 sqrt( tr^2 A - 4 detA )\n    var trA = addScalar(a, d);\n    var detA = subtract(multiplyScalar(a, d), multiplyScalar(b, c));\n    var x = multiplyScalar(trA, 0.5);\n    var y = multiplyScalar(sqrt(subtract(multiplyScalar(trA, trA), multiplyScalar(4, detA))), 0.5);\n    return [addScalar(x, y), subtract(x, y)];\n  }\n\n  /**\n   * For an 2x2 matrix compute the transformation matrix S,\n   * so that SAS^-1 is an upper triangular matrix\n   * @return {[[number,number],[number,number]]}\n   * @see https://math.berkeley.edu/~ogus/old/Math_54-05/webfoils/jordan.pdf\n   * @see http://people.math.harvard.edu/~knill/teaching/math21b2004/exhibits/2dmatrices/index.html\n   */\n  function jordanBase2x2(a, b, c, d, l1, l2, prec, type) {\n    var big = type === 'BigNumber';\n    var cplx = type === 'Complex';\n    var zero = big ? bignumber(0) : cplx ? complex(0) : 0;\n    var one = big ? bignumber(1) : cplx ? complex(1) : 1;\n\n    // matrix is already upper triangular\n    // return an identity matrix\n    if (smaller(abs(c), prec)) {\n      return [[one, zero], [zero, one]];\n    }\n\n    // matrix is diagonalizable\n    // return its eigenvectors as columns\n    if (larger(abs(subtract(l1, l2)), prec)) {\n      return [[subtract(l1, d), subtract(l2, d)], [c, c]];\n    }\n\n    // matrix is not diagonalizable\n    // compute diagonal elements of N = A - lambdaI\n    var na = subtract(a, l1);\n    var nd = subtract(d, l1);\n\n    // col(N,2) = 0  implies  S = ( col(N,1), e_1 )\n    // col(N,2) != 0 implies  S = ( col(N,2), e_2 )\n\n    if (smaller(abs(b), prec) && smaller(abs(nd), prec)) {\n      return [[na, one], [c, zero]];\n    } else {\n      return [[b, zero], [nd, one]];\n    }\n  }\n\n  /**\n   * Enlarge the matrix from nxn to NxN, setting the new\n   * elements to 1 on diagonal and 0 elsewhere\n   */\n  function inflateMatrix(arr, N) {\n    // add columns\n    for (var i = 0; i < arr.length; i++) {\n      arr[i].push(...Array(N - arr[i].length).fill(0));\n    }\n\n    // add rows\n    for (var _i5 = arr.length; _i5 < N; _i5++) {\n      arr.push(Array(N).fill(0));\n      arr[_i5][_i5] = 1;\n    }\n    return arr;\n  }\n\n  /**\n   * Create a block-diagonal matrix with the given square matrices on the diagonal\n   * @param {Matrix[] | number[][][]} arr array of matrices to be placed on the diagonal\n   * @param {number} N the size of the resulting matrix\n   */\n  function blockDiag(arr, N) {\n    var M = [];\n    for (var i = 0; i < N; i++) {\n      M[i] = Array(N).fill(0);\n    }\n    var I = 0;\n    for (var sub of arr) {\n      var n = sub.length;\n      for (var _i6 = 0; _i6 < n; _i6++) {\n        for (var j = 0; j < n; j++) {\n          M[I + _i6][I + j] = sub[_i6][j];\n        }\n      }\n      I += n;\n    }\n    return M;\n  }\n\n  /**\n   * Finds the index of an element in an array using a custom equality function\n   * @template T\n   * @param {Array<T>} arr array in which to search\n   * @param {T} el the element to find\n   * @param {function(T, T): boolean} fn the equality function, first argument is an element of `arr`, the second is always `el`\n   * @returns {number} the index of `el`, or -1 when it's not in `arr`\n   */\n  function indexOf(arr, el, fn) {\n    for (var i = 0; i < arr.length; i++) {\n      if (fn(arr[i], el)) {\n        return i;\n      }\n    }\n    return -1;\n  }\n\n  /**\n   * Provided a near-singular upper-triangular matrix A and a list of vectors,\n   * finds an eigenvector of A with the smallest eigenvalue, which is orthogonal\n   * to each vector in the list\n   * @template T\n   * @param {T[][]} A near-singular square matrix\n   * @param {number} N dimension\n   * @param {T[][]} orthog list of vectors\n   * @param {number} prec epsilon\n   * @param {'number'|'BigNumber'|'Complex'} type\n   * @return {T[] | null} eigenvector\n   *\n   * @see Numerical Recipes for Fortran 77 – 11.7 Eigenvalues or Eigenvectors by Inverse Iteration\n   */\n  function inverseIterate(A, N, orthog, prec, type) {\n    var largeNum = type === 'BigNumber' ? bignumber(1000) : 1000;\n    var b; // the vector\n\n    // you better choose a random vector before I count to five\n    var i = 0;\n    for (; i < 5; ++i) {\n      b = randomOrthogonalVector(N, orthog, type);\n      try {\n        b = usolve(A, b);\n      } catch (_unused) {\n        // That direction didn't work, likely because the original matrix\n        // was defective. But still make the full number of tries...\n        continue;\n      }\n      if (larger(norm(b), largeNum)) {\n        break;\n      }\n    }\n    if (i >= 5) {\n      return null; // couldn't find any orthogonal vector in the image\n    }\n\n    // you better converge before I count to ten\n    i = 0;\n    while (true) {\n      var c = usolve(A, b);\n      if (smaller(norm(orthogonalComplement(b, [c])), prec)) {\n        break;\n      }\n      if (++i >= 10) {\n        return null;\n      }\n      b = normalize(c);\n    }\n    return b;\n  }\n\n  /**\n   * Generates a random unit vector of dimension N, orthogonal to each vector in the list\n   * @template T\n   * @param {number} N dimension\n   * @param {T[][]} orthog list of vectors\n   * @param {'number'|'BigNumber'|'Complex'} type\n   * @returns {T[]} random vector\n   */\n  function randomOrthogonalVector(N, orthog, type) {\n    var big = type === 'BigNumber';\n    var cplx = type === 'Complex';\n\n    // generate random vector with the correct type\n    var v = Array(N).fill(0).map(_ => 2 * Math.random() - 1);\n    if (big) {\n      v = v.map(n => bignumber(n));\n    }\n    if (cplx) {\n      v = v.map(n => complex(n));\n    }\n\n    // project to orthogonal complement\n    v = orthogonalComplement(v, orthog);\n\n    // normalize\n    return normalize(v, type);\n  }\n\n  /**\n   * Project vector v to the orthogonal complement of an array of vectors\n   */\n  function orthogonalComplement(v, orthog) {\n    var vectorShape = size(v);\n    for (var w of orthog) {\n      w = reshape(w, vectorShape); // make sure this is just a vector computation\n      // v := v − (w, v)/|w|^2 w\n      v = subtract(v, multiply(divideScalar(dot(w, v), dot(w, w)), w));\n    }\n    return v;\n  }\n\n  /**\n   * Calculate the norm of a vector.\n   * We can't use math.norm because factory can't handle circular dependency.\n   * Seriously, I'm really fed up with factory.\n   */\n  function norm(v) {\n    return abs(sqrt(dot(v, v)));\n  }\n\n  /**\n   * Normalize a vector\n   * @template T\n   * @param {T[]} v\n   * @param {'number'|'BigNumber'|'Complex'} type\n   * @returns {T[]} normalized vec\n   */\n  function normalize(v, type) {\n    var big = type === 'BigNumber';\n    var cplx = type === 'Complex';\n    var one = big ? bignumber(1) : cplx ? complex(1) : 1;\n    return multiply(divideScalar(one, norm(v)), v);\n  }\n  return complexEigs;\n}", "map": {"version": 3, "names": ["clone", "createComplexEigs", "_ref", "addScalar", "subtract", "flatten", "multiply", "multiplyScalar", "divideScalar", "sqrt", "abs", "bignumber", "diag", "size", "reshape", "inv", "qr", "usolve", "usolveAll", "equal", "complex", "larger", "smaller", "matrixFromColumns", "dot", "complexEigs", "arr", "N", "prec", "type", "findVectors", "arguments", "length", "undefined", "R", "balance", "reduceToHessenberg", "values", "C", "iterateUntilTriangular", "eigenvectors", "findEigenvectors", "big", "cplx", "realzero", "one", "realone", "radix", "radixSq", "Rdiag", "Array", "fill", "last", "i", "colNorm", "rowNorm", "j", "f", "c", "rowDivRadix", "rowMulRadix", "condition", "g", "_j", "zero", "maxIndex", "max", "el", "tmp1", "_j2", "tmp2", "tmp3", "_j3", "n", "k", "_k", "_k2", "A", "lambdas", "Sdiag", "Qtotal", "Qpartial", "lastConvergenceBefore", "Q", "_i", "push", "unshift", "inflateMatrix", "pop", "_i2", "ll", "eigenvalues2x2", "jordanBase2x2", "_i3", "sort", "a", "b", "err", "Error", "join", "vectors", "blockDiag", "Cinv", "U", "uniqueValues", "multiplicities", "lambda", "indexOf", "len", "E", "_loop", "_i4", "S", "solutions", "shift", "approxVec", "inverseIterate", "correction", "map", "v", "value", "vector", "d", "trA", "detA", "x", "y", "l1", "l2", "na", "nd", "_i5", "M", "I", "sub", "_i6", "fn", "orthog", "largeNum", "randomOrthogonalVector", "_unused", "norm", "orthogonalComplement", "normalize", "_", "Math", "random", "vectorShape", "w"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/matrix/eigs/complexEigs.js"], "sourcesContent": ["import { clone } from '../../../utils/object.js';\nexport function createComplexEigs(_ref) {\n  var {\n    addScalar,\n    subtract,\n    flatten,\n    multiply,\n    multiplyScalar,\n    divideScalar,\n    sqrt,\n    abs,\n    bignumber,\n    diag,\n    size,\n    reshape,\n    inv,\n    qr,\n    usolve,\n    usolveAll,\n    equal,\n    complex,\n    larger,\n    smaller,\n    matrixFromColumns,\n    dot\n  } = _ref;\n  /**\n   * @param {number[][]} arr the matrix to find eigenvalues of\n   * @param {number} N size of the matrix\n   * @param {number|BigNumber} prec precision, anything lower will be considered zero\n   * @param {'number'|'BigNumber'|'Complex'} type\n   * @param {boolean} findVectors should we find eigenvectors?\n   *\n   * @returns {{ values: number[], vectors: number[][] }}\n   */\n  function complexEigs(arr, N, prec, type) {\n    var findVectors = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n    // TODO check if any row/col are zero except the diagonal\n\n    // make sure corresponding rows and columns have similar magnitude\n    // important because of numerical stability\n    // MODIFIES arr by side effect!\n    var R = balance(arr, N, prec, type, findVectors);\n\n    // R is the row transformation matrix\n    // arr = A' = R A R^-1, A is the original matrix\n    // (if findVectors is false, R is undefined)\n    // (And so to return to original matrix: A = R^-1 arr R)\n\n    // TODO if magnitudes of elements vary over many orders,\n    // move greatest elements to the top left corner\n\n    // using similarity transformations, reduce the matrix\n    // to Hessenberg form (upper triangular plus one subdiagonal row)\n    // updates the transformation matrix R with new row operationsq\n    // MODIFIES arr by side effect!\n    reduceToHessenberg(arr, N, prec, type, findVectors, R);\n    // still true that original A = R^-1 arr R)\n\n    // find eigenvalues\n    var {\n      values,\n      C\n    } = iterateUntilTriangular(arr, N, prec, type, findVectors);\n\n    // values is the list of eigenvalues, C is the column\n    // transformation matrix that transforms arr, the hessenberg\n    // matrix, to upper triangular\n    // (So U = C^-1 arr C and the relationship between current arr\n    // and original A is unchanged.)\n\n    if (findVectors) {\n      var eigenvectors = findEigenvectors(arr, N, C, R, values, prec, type);\n      return {\n        values,\n        eigenvectors\n      };\n    }\n    return {\n      values\n    };\n  }\n\n  /**\n   * @param {number[][]} arr\n   * @param {number} N\n   * @param {number} prec\n   * @param {'number'|'BigNumber'|'Complex'} type\n   * @returns {number[][]}\n   */\n  function balance(arr, N, prec, type, findVectors) {\n    var big = type === 'BigNumber';\n    var cplx = type === 'Complex';\n    var realzero = big ? bignumber(0) : 0;\n    var one = big ? bignumber(1) : cplx ? complex(1) : 1;\n    var realone = big ? bignumber(1) : 1;\n\n    // base of the floating-point arithmetic\n    var radix = big ? bignumber(10) : 2;\n    var radixSq = multiplyScalar(radix, radix);\n\n    // the diagonal transformation matrix R\n    var Rdiag;\n    if (findVectors) {\n      Rdiag = Array(N).fill(one);\n    }\n\n    // this isn't the only time we loop thru the matrix...\n    var last = false;\n    while (!last) {\n      // ...haha I'm joking! unless...\n      last = true;\n      for (var i = 0; i < N; i++) {\n        // compute the taxicab norm of i-th column and row\n        // TODO optimize for complex numbers\n        var colNorm = realzero;\n        var rowNorm = realzero;\n        for (var j = 0; j < N; j++) {\n          if (i === j) continue;\n          colNorm = addScalar(colNorm, abs(arr[j][i]));\n          rowNorm = addScalar(rowNorm, abs(arr[i][j]));\n        }\n        if (!equal(colNorm, 0) && !equal(rowNorm, 0)) {\n          // find integer power closest to balancing the matrix\n          // (we want to scale only by integer powers of radix,\n          // so that we don't lose any precision due to round-off)\n\n          var f = realone;\n          var c = colNorm;\n          var rowDivRadix = divideScalar(rowNorm, radix);\n          var rowMulRadix = multiplyScalar(rowNorm, radix);\n          while (smaller(c, rowDivRadix)) {\n            c = multiplyScalar(c, radixSq);\n            f = multiplyScalar(f, radix);\n          }\n          while (larger(c, rowMulRadix)) {\n            c = divideScalar(c, radixSq);\n            f = divideScalar(f, radix);\n          }\n\n          // check whether balancing is needed\n          // condition = (c + rowNorm) / f < 0.95 * (colNorm + rowNorm)\n          var condition = smaller(divideScalar(addScalar(c, rowNorm), f), multiplyScalar(addScalar(colNorm, rowNorm), 0.95));\n\n          // apply balancing similarity transformation\n          if (condition) {\n            // we should loop once again to check whether\n            // another rebalancing is needed\n            last = false;\n            var g = divideScalar(1, f);\n            for (var _j = 0; _j < N; _j++) {\n              if (i === _j) {\n                continue;\n              }\n              arr[i][_j] = multiplyScalar(arr[i][_j], g);\n              arr[_j][i] = multiplyScalar(arr[_j][i], f);\n            }\n\n            // keep track of transformations\n            if (findVectors) {\n              Rdiag[i] = multiplyScalar(Rdiag[i], g);\n            }\n          }\n        }\n      }\n    }\n\n    // return the diagonal row transformation matrix\n    return findVectors ? diag(Rdiag) : null;\n  }\n\n  /**\n   * @param {number[][]} arr\n   * @param {number} N\n   * @param {number} prec\n   * @param {'number'|'BigNumber'|'Complex'} type\n   * @param {boolean} findVectors\n   * @param {number[][]} R the row transformation matrix that will be modified\n   */\n  function reduceToHessenberg(arr, N, prec, type, findVectors, R) {\n    var big = type === 'BigNumber';\n    var cplx = type === 'Complex';\n    var zero = big ? bignumber(0) : cplx ? complex(0) : 0;\n    if (big) {\n      prec = bignumber(prec);\n    }\n    for (var i = 0; i < N - 2; i++) {\n      // Find the largest subdiag element in the i-th col\n\n      var maxIndex = 0;\n      var max = zero;\n      for (var j = i + 1; j < N; j++) {\n        var el = arr[j][i];\n        if (smaller(abs(max), abs(el))) {\n          max = el;\n          maxIndex = j;\n        }\n      }\n\n      // This col is pivoted, no need to do anything\n      if (smaller(abs(max), prec)) {\n        continue;\n      }\n      if (maxIndex !== i + 1) {\n        // Interchange maxIndex-th and (i+1)-th row\n        var tmp1 = arr[maxIndex];\n        arr[maxIndex] = arr[i + 1];\n        arr[i + 1] = tmp1;\n\n        // Interchange maxIndex-th and (i+1)-th column\n        for (var _j2 = 0; _j2 < N; _j2++) {\n          var tmp2 = arr[_j2][maxIndex];\n          arr[_j2][maxIndex] = arr[_j2][i + 1];\n          arr[_j2][i + 1] = tmp2;\n        }\n\n        // keep track of transformations\n        if (findVectors) {\n          var tmp3 = R[maxIndex];\n          R[maxIndex] = R[i + 1];\n          R[i + 1] = tmp3;\n        }\n      }\n\n      // Reduce following rows and columns\n      for (var _j3 = i + 2; _j3 < N; _j3++) {\n        var n = divideScalar(arr[_j3][i], max);\n        if (n === 0) {\n          continue;\n        }\n\n        // from j-th row subtract n-times (i+1)th row\n        for (var k = 0; k < N; k++) {\n          arr[_j3][k] = subtract(arr[_j3][k], multiplyScalar(n, arr[i + 1][k]));\n        }\n\n        // to (i+1)th column add n-times j-th column\n        for (var _k = 0; _k < N; _k++) {\n          arr[_k][i + 1] = addScalar(arr[_k][i + 1], multiplyScalar(n, arr[_k][_j3]));\n        }\n\n        // keep track of transformations\n        if (findVectors) {\n          for (var _k2 = 0; _k2 < N; _k2++) {\n            R[_j3][_k2] = subtract(R[_j3][_k2], multiplyScalar(n, R[i + 1][_k2]));\n          }\n        }\n      }\n    }\n    return R;\n  }\n\n  /**\n   * @returns {{values: values, C: Matrix}}\n   * @see Press, Wiliams: Numerical recipes in Fortran 77\n   * @see https://en.wikipedia.org/wiki/QR_algorithm\n   */\n  function iterateUntilTriangular(A, N, prec, type, findVectors) {\n    var big = type === 'BigNumber';\n    var cplx = type === 'Complex';\n    var one = big ? bignumber(1) : cplx ? complex(1) : 1;\n    if (big) {\n      prec = bignumber(prec);\n    }\n\n    // The Francis Algorithm\n    // The core idea of this algorithm is that doing successive\n    // A' = QtAQ transformations will eventually converge to block-\n    // upper-triangular with diagonal blocks either 1x1 or 2x2.\n    // The Q here is the one from the QR decomposition, A = QR.\n    // Since the eigenvalues of a block-upper-triangular matrix are\n    // the eigenvalues of its diagonal blocks and we know how to find\n    // eigenvalues of a 2x2 matrix, we know the eigenvalues of A.\n\n    var arr = clone(A);\n\n    // the list of converged eigenvalues\n    var lambdas = [];\n\n    // size of arr, which will get smaller as eigenvalues converge\n    var n = N;\n\n    // the diagonal of the block-diagonal matrix that turns\n    // converged 2x2 matrices into upper triangular matrices\n    var Sdiag = [];\n\n    // N×N matrix describing the overall transformation done during the QR algorithm\n    var Qtotal = findVectors ? diag(Array(N).fill(one)) : undefined;\n\n    // nxn matrix describing the QR transformations done since last convergence\n    var Qpartial = findVectors ? diag(Array(n).fill(one)) : undefined;\n\n    // last eigenvalue converged before this many steps\n    var lastConvergenceBefore = 0;\n    while (lastConvergenceBefore <= 100) {\n      lastConvergenceBefore += 1;\n\n      // TODO if the convergence is slow, do something clever\n\n      // Perform the factorization\n\n      var k = arr[n - 1][n - 1]; // TODO this is apparently a somewhat\n      // old-fashioned choice; ideally set close to an eigenvalue, or\n      // perhaps better yet switch to the implicit QR version that is sometimes\n      // specifically called the \"Francis algorithm\" that is alluded to\n      // in the following TODO. (Or perhaps we switch to an independently\n      // optimized third-party package for the linear algebra operations...)\n\n      for (var i = 0; i < n; i++) {\n        arr[i][i] = subtract(arr[i][i], k);\n      }\n\n      // TODO do an implicit QR transformation\n      var {\n        Q,\n        R\n      } = qr(arr);\n      arr = multiply(R, Q);\n      for (var _i = 0; _i < n; _i++) {\n        arr[_i][_i] = addScalar(arr[_i][_i], k);\n      }\n\n      // keep track of transformations\n      if (findVectors) {\n        Qpartial = multiply(Qpartial, Q);\n      }\n\n      // The rightmost diagonal element converged to an eigenvalue\n      if (n === 1 || smaller(abs(arr[n - 1][n - 2]), prec)) {\n        lastConvergenceBefore = 0;\n        lambdas.push(arr[n - 1][n - 1]);\n\n        // keep track of transformations\n        if (findVectors) {\n          Sdiag.unshift([[1]]);\n          inflateMatrix(Qpartial, N);\n          Qtotal = multiply(Qtotal, Qpartial);\n          if (n > 1) {\n            Qpartial = diag(Array(n - 1).fill(one));\n          }\n        }\n\n        // reduce the matrix size\n        n -= 1;\n        arr.pop();\n        for (var _i2 = 0; _i2 < n; _i2++) {\n          arr[_i2].pop();\n        }\n\n        // The rightmost diagonal 2x2 block converged\n      } else if (n === 2 || smaller(abs(arr[n - 2][n - 3]), prec)) {\n        lastConvergenceBefore = 0;\n        var ll = eigenvalues2x2(arr[n - 2][n - 2], arr[n - 2][n - 1], arr[n - 1][n - 2], arr[n - 1][n - 1]);\n        lambdas.push(...ll);\n\n        // keep track of transformations\n        if (findVectors) {\n          Sdiag.unshift(jordanBase2x2(arr[n - 2][n - 2], arr[n - 2][n - 1], arr[n - 1][n - 2], arr[n - 1][n - 1], ll[0], ll[1], prec, type));\n          inflateMatrix(Qpartial, N);\n          Qtotal = multiply(Qtotal, Qpartial);\n          if (n > 2) {\n            Qpartial = diag(Array(n - 2).fill(one));\n          }\n        }\n\n        // reduce the matrix size\n        n -= 2;\n        arr.pop();\n        arr.pop();\n        for (var _i3 = 0; _i3 < n; _i3++) {\n          arr[_i3].pop();\n          arr[_i3].pop();\n        }\n      }\n      if (n === 0) {\n        break;\n      }\n    }\n\n    // standard sorting\n    lambdas.sort((a, b) => +subtract(abs(a), abs(b)));\n\n    // the algorithm didn't converge\n    if (lastConvergenceBefore > 100) {\n      var err = Error('The eigenvalues failed to converge. Only found these eigenvalues: ' + lambdas.join(', '));\n      err.values = lambdas;\n      err.vectors = [];\n      throw err;\n    }\n\n    // combine the overall QR transformation Qtotal with the subsequent\n    // transformation S that turns the diagonal 2x2 blocks to upper triangular\n    var C = findVectors ? multiply(Qtotal, blockDiag(Sdiag, N)) : undefined;\n    return {\n      values: lambdas,\n      C\n    };\n  }\n\n  /**\n   * @param {Matrix} A hessenberg-form matrix\n   * @param {number} N size of A\n   * @param {Matrix} C column transformation matrix that turns A into upper triangular\n   * @param {Matrix} R similarity that turns original matrix into A\n   * @param {number[]} values array of eigenvalues of A\n   * @param {'number'|'BigNumber'|'Complex'} type\n   * @returns {number[][]} eigenvalues\n   */\n  function findEigenvectors(A, N, C, R, values, prec, type) {\n    var Cinv = inv(C);\n    var U = multiply(Cinv, A, C);\n    var big = type === 'BigNumber';\n    var cplx = type === 'Complex';\n    var zero = big ? bignumber(0) : cplx ? complex(0) : 0;\n    var one = big ? bignumber(1) : cplx ? complex(1) : 1;\n\n    // turn values into a kind of \"multiset\"\n    // this way it is easier to find eigenvectors\n    var uniqueValues = [];\n    var multiplicities = [];\n    for (var lambda of values) {\n      var i = indexOf(uniqueValues, lambda, equal);\n      if (i === -1) {\n        uniqueValues.push(lambda);\n        multiplicities.push(1);\n      } else {\n        multiplicities[i] += 1;\n      }\n    }\n\n    // find eigenvectors by solving U − lambdaE = 0\n    // TODO replace with an iterative eigenvector algorithm\n    // (this one might fail for imprecise eigenvalues)\n\n    var vectors = [];\n    var len = uniqueValues.length;\n    var b = Array(N).fill(zero);\n    var E = diag(Array(N).fill(one));\n    var _loop = function _loop() {\n      var lambda = uniqueValues[_i4];\n      var S = subtract(U, multiply(lambda, E)); // the characteristic matrix\n\n      var solutions = usolveAll(S, b);\n      solutions.shift(); // ignore the null vector\n\n      // looks like we missed something, try inverse iteration\n      // But if that fails, just presume that the original matrix truly\n      // was defective.\n      while (solutions.length < multiplicities[_i4]) {\n        var approxVec = inverseIterate(S, N, solutions, prec, type);\n        if (approxVec === null) {\n          break;\n        } // no more vectors were found\n        solutions.push(approxVec);\n      }\n\n      // Transform back into original array coordinates\n      var correction = multiply(inv(R), C);\n      solutions = solutions.map(v => multiply(correction, v));\n      vectors.push(...solutions.map(v => ({\n        value: lambda,\n        vector: flatten(v)\n      })));\n    };\n    for (var _i4 = 0; _i4 < len; _i4++) {\n      _loop();\n    }\n    return vectors;\n  }\n\n  /**\n   * Compute the eigenvalues of an 2x2 matrix\n   * @return {[number,number]}\n   */\n  function eigenvalues2x2(a, b, c, d) {\n    // lambda_+- = 1/2 trA +- 1/2 sqrt( tr^2 A - 4 detA )\n    var trA = addScalar(a, d);\n    var detA = subtract(multiplyScalar(a, d), multiplyScalar(b, c));\n    var x = multiplyScalar(trA, 0.5);\n    var y = multiplyScalar(sqrt(subtract(multiplyScalar(trA, trA), multiplyScalar(4, detA))), 0.5);\n    return [addScalar(x, y), subtract(x, y)];\n  }\n\n  /**\n   * For an 2x2 matrix compute the transformation matrix S,\n   * so that SAS^-1 is an upper triangular matrix\n   * @return {[[number,number],[number,number]]}\n   * @see https://math.berkeley.edu/~ogus/old/Math_54-05/webfoils/jordan.pdf\n   * @see http://people.math.harvard.edu/~knill/teaching/math21b2004/exhibits/2dmatrices/index.html\n   */\n  function jordanBase2x2(a, b, c, d, l1, l2, prec, type) {\n    var big = type === 'BigNumber';\n    var cplx = type === 'Complex';\n    var zero = big ? bignumber(0) : cplx ? complex(0) : 0;\n    var one = big ? bignumber(1) : cplx ? complex(1) : 1;\n\n    // matrix is already upper triangular\n    // return an identity matrix\n    if (smaller(abs(c), prec)) {\n      return [[one, zero], [zero, one]];\n    }\n\n    // matrix is diagonalizable\n    // return its eigenvectors as columns\n    if (larger(abs(subtract(l1, l2)), prec)) {\n      return [[subtract(l1, d), subtract(l2, d)], [c, c]];\n    }\n\n    // matrix is not diagonalizable\n    // compute diagonal elements of N = A - lambdaI\n    var na = subtract(a, l1);\n    var nd = subtract(d, l1);\n\n    // col(N,2) = 0  implies  S = ( col(N,1), e_1 )\n    // col(N,2) != 0 implies  S = ( col(N,2), e_2 )\n\n    if (smaller(abs(b), prec) && smaller(abs(nd), prec)) {\n      return [[na, one], [c, zero]];\n    } else {\n      return [[b, zero], [nd, one]];\n    }\n  }\n\n  /**\n   * Enlarge the matrix from nxn to NxN, setting the new\n   * elements to 1 on diagonal and 0 elsewhere\n   */\n  function inflateMatrix(arr, N) {\n    // add columns\n    for (var i = 0; i < arr.length; i++) {\n      arr[i].push(...Array(N - arr[i].length).fill(0));\n    }\n\n    // add rows\n    for (var _i5 = arr.length; _i5 < N; _i5++) {\n      arr.push(Array(N).fill(0));\n      arr[_i5][_i5] = 1;\n    }\n    return arr;\n  }\n\n  /**\n   * Create a block-diagonal matrix with the given square matrices on the diagonal\n   * @param {Matrix[] | number[][][]} arr array of matrices to be placed on the diagonal\n   * @param {number} N the size of the resulting matrix\n   */\n  function blockDiag(arr, N) {\n    var M = [];\n    for (var i = 0; i < N; i++) {\n      M[i] = Array(N).fill(0);\n    }\n    var I = 0;\n    for (var sub of arr) {\n      var n = sub.length;\n      for (var _i6 = 0; _i6 < n; _i6++) {\n        for (var j = 0; j < n; j++) {\n          M[I + _i6][I + j] = sub[_i6][j];\n        }\n      }\n      I += n;\n    }\n    return M;\n  }\n\n  /**\n   * Finds the index of an element in an array using a custom equality function\n   * @template T\n   * @param {Array<T>} arr array in which to search\n   * @param {T} el the element to find\n   * @param {function(T, T): boolean} fn the equality function, first argument is an element of `arr`, the second is always `el`\n   * @returns {number} the index of `el`, or -1 when it's not in `arr`\n   */\n  function indexOf(arr, el, fn) {\n    for (var i = 0; i < arr.length; i++) {\n      if (fn(arr[i], el)) {\n        return i;\n      }\n    }\n    return -1;\n  }\n\n  /**\n   * Provided a near-singular upper-triangular matrix A and a list of vectors,\n   * finds an eigenvector of A with the smallest eigenvalue, which is orthogonal\n   * to each vector in the list\n   * @template T\n   * @param {T[][]} A near-singular square matrix\n   * @param {number} N dimension\n   * @param {T[][]} orthog list of vectors\n   * @param {number} prec epsilon\n   * @param {'number'|'BigNumber'|'Complex'} type\n   * @return {T[] | null} eigenvector\n   *\n   * @see Numerical Recipes for Fortran 77 – 11.7 Eigenvalues or Eigenvectors by Inverse Iteration\n   */\n  function inverseIterate(A, N, orthog, prec, type) {\n    var largeNum = type === 'BigNumber' ? bignumber(1000) : 1000;\n    var b; // the vector\n\n    // you better choose a random vector before I count to five\n    var i = 0;\n    for (; i < 5; ++i) {\n      b = randomOrthogonalVector(N, orthog, type);\n      try {\n        b = usolve(A, b);\n      } catch (_unused) {\n        // That direction didn't work, likely because the original matrix\n        // was defective. But still make the full number of tries...\n        continue;\n      }\n      if (larger(norm(b), largeNum)) {\n        break;\n      }\n    }\n    if (i >= 5) {\n      return null; // couldn't find any orthogonal vector in the image\n    }\n\n    // you better converge before I count to ten\n    i = 0;\n    while (true) {\n      var c = usolve(A, b);\n      if (smaller(norm(orthogonalComplement(b, [c])), prec)) {\n        break;\n      }\n      if (++i >= 10) {\n        return null;\n      }\n      b = normalize(c);\n    }\n    return b;\n  }\n\n  /**\n   * Generates a random unit vector of dimension N, orthogonal to each vector in the list\n   * @template T\n   * @param {number} N dimension\n   * @param {T[][]} orthog list of vectors\n   * @param {'number'|'BigNumber'|'Complex'} type\n   * @returns {T[]} random vector\n   */\n  function randomOrthogonalVector(N, orthog, type) {\n    var big = type === 'BigNumber';\n    var cplx = type === 'Complex';\n\n    // generate random vector with the correct type\n    var v = Array(N).fill(0).map(_ => 2 * Math.random() - 1);\n    if (big) {\n      v = v.map(n => bignumber(n));\n    }\n    if (cplx) {\n      v = v.map(n => complex(n));\n    }\n\n    // project to orthogonal complement\n    v = orthogonalComplement(v, orthog);\n\n    // normalize\n    return normalize(v, type);\n  }\n\n  /**\n   * Project vector v to the orthogonal complement of an array of vectors\n   */\n  function orthogonalComplement(v, orthog) {\n    var vectorShape = size(v);\n    for (var w of orthog) {\n      w = reshape(w, vectorShape); // make sure this is just a vector computation\n      // v := v − (w, v)/|w|^2 w\n      v = subtract(v, multiply(divideScalar(dot(w, v), dot(w, w)), w));\n    }\n    return v;\n  }\n\n  /**\n   * Calculate the norm of a vector.\n   * We can't use math.norm because factory can't handle circular dependency.\n   * Seriously, I'm really fed up with factory.\n   */\n  function norm(v) {\n    return abs(sqrt(dot(v, v)));\n  }\n\n  /**\n   * Normalize a vector\n   * @template T\n   * @param {T[]} v\n   * @param {'number'|'BigNumber'|'Complex'} type\n   * @returns {T[]} normalized vec\n   */\n  function normalize(v, type) {\n    var big = type === 'BigNumber';\n    var cplx = type === 'Complex';\n    var one = big ? bignumber(1) : cplx ? complex(1) : 1;\n    return multiply(divideScalar(one, norm(v)), v);\n  }\n  return complexEigs;\n}"], "mappings": "AAAA,SAASA,KAAK,QAAQ,0BAA0B;AAChD,OAAO,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EACtC,IAAI;IACFC,SAAS;IACTC,QAAQ;IACRC,OAAO;IACPC,QAAQ;IACRC,cAAc;IACdC,YAAY;IACZC,IAAI;IACJC,GAAG;IACHC,SAAS;IACTC,IAAI;IACJC,IAAI;IACJC,OAAO;IACPC,GAAG;IACHC,EAAE;IACFC,MAAM;IACNC,SAAS;IACTC,KAAK;IACLC,OAAO;IACPC,MAAM;IACNC,OAAO;IACPC,iBAAiB;IACjBC;EACF,CAAC,GAAGtB,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASuB,WAAWA,CAACC,GAAG,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAE;IACvC,IAAIC,WAAW,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IAC1F;;IAEA;IACA;IACA;IACA,IAAIG,CAAC,GAAGC,OAAO,CAACT,GAAG,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,CAAC;;IAEhD;IACA;IACA;IACA;;IAEA;IACA;;IAEA;IACA;IACA;IACA;IACAM,kBAAkB,CAACV,GAAG,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,EAAEI,CAAC,CAAC;IACtD;;IAEA;IACA,IAAI;MACFG,MAAM;MACNC;IACF,CAAC,GAAGC,sBAAsB,CAACb,GAAG,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,CAAC;;IAE3D;IACA;IACA;IACA;IACA;;IAEA,IAAIA,WAAW,EAAE;MACf,IAAIU,YAAY,GAAGC,gBAAgB,CAACf,GAAG,EAAEC,CAAC,EAAEW,CAAC,EAAEJ,CAAC,EAAEG,MAAM,EAAET,IAAI,EAAEC,IAAI,CAAC;MACrE,OAAO;QACLQ,MAAM;QACNG;MACF,CAAC;IACH;IACA,OAAO;MACLH;IACF,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASF,OAAOA,CAACT,GAAG,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,EAAE;IAChD,IAAIY,GAAG,GAAGb,IAAI,KAAK,WAAW;IAC9B,IAAIc,IAAI,GAAGd,IAAI,KAAK,SAAS;IAC7B,IAAIe,QAAQ,GAAGF,GAAG,GAAG/B,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IACrC,IAAIkC,GAAG,GAAGH,GAAG,GAAG/B,SAAS,CAAC,CAAC,CAAC,GAAGgC,IAAI,GAAGvB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpD,IAAI0B,OAAO,GAAGJ,GAAG,GAAG/B,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;;IAEpC;IACA,IAAIoC,KAAK,GAAGL,GAAG,GAAG/B,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC;IACnC,IAAIqC,OAAO,GAAGzC,cAAc,CAACwC,KAAK,EAAEA,KAAK,CAAC;;IAE1C;IACA,IAAIE,KAAK;IACT,IAAInB,WAAW,EAAE;MACfmB,KAAK,GAAGC,KAAK,CAACvB,CAAC,CAAC,CAACwB,IAAI,CAACN,GAAG,CAAC;IAC5B;;IAEA;IACA,IAAIO,IAAI,GAAG,KAAK;IAChB,OAAO,CAACA,IAAI,EAAE;MACZ;MACAA,IAAI,GAAG,IAAI;MACX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,CAAC,EAAE0B,CAAC,EAAE,EAAE;QAC1B;QACA;QACA,IAAIC,OAAO,GAAGV,QAAQ;QACtB,IAAIW,OAAO,GAAGX,QAAQ;QACtB,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7B,CAAC,EAAE6B,CAAC,EAAE,EAAE;UAC1B,IAAIH,CAAC,KAAKG,CAAC,EAAE;UACbF,OAAO,GAAGnD,SAAS,CAACmD,OAAO,EAAE5C,GAAG,CAACgB,GAAG,CAAC8B,CAAC,CAAC,CAACH,CAAC,CAAC,CAAC,CAAC;UAC5CE,OAAO,GAAGpD,SAAS,CAACoD,OAAO,EAAE7C,GAAG,CAACgB,GAAG,CAAC2B,CAAC,CAAC,CAACG,CAAC,CAAC,CAAC,CAAC;QAC9C;QACA,IAAI,CAACrC,KAAK,CAACmC,OAAO,EAAE,CAAC,CAAC,IAAI,CAACnC,KAAK,CAACoC,OAAO,EAAE,CAAC,CAAC,EAAE;UAC5C;UACA;UACA;;UAEA,IAAIE,CAAC,GAAGX,OAAO;UACf,IAAIY,CAAC,GAAGJ,OAAO;UACf,IAAIK,WAAW,GAAGnD,YAAY,CAAC+C,OAAO,EAAER,KAAK,CAAC;UAC9C,IAAIa,WAAW,GAAGrD,cAAc,CAACgD,OAAO,EAAER,KAAK,CAAC;UAChD,OAAOzB,OAAO,CAACoC,CAAC,EAAEC,WAAW,CAAC,EAAE;YAC9BD,CAAC,GAAGnD,cAAc,CAACmD,CAAC,EAAEV,OAAO,CAAC;YAC9BS,CAAC,GAAGlD,cAAc,CAACkD,CAAC,EAAEV,KAAK,CAAC;UAC9B;UACA,OAAO1B,MAAM,CAACqC,CAAC,EAAEE,WAAW,CAAC,EAAE;YAC7BF,CAAC,GAAGlD,YAAY,CAACkD,CAAC,EAAEV,OAAO,CAAC;YAC5BS,CAAC,GAAGjD,YAAY,CAACiD,CAAC,EAAEV,KAAK,CAAC;UAC5B;;UAEA;UACA;UACA,IAAIc,SAAS,GAAGvC,OAAO,CAACd,YAAY,CAACL,SAAS,CAACuD,CAAC,EAAEH,OAAO,CAAC,EAAEE,CAAC,CAAC,EAAElD,cAAc,CAACJ,SAAS,CAACmD,OAAO,EAAEC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC;;UAElH;UACA,IAAIM,SAAS,EAAE;YACb;YACA;YACAT,IAAI,GAAG,KAAK;YACZ,IAAIU,CAAC,GAAGtD,YAAY,CAAC,CAAC,EAAEiD,CAAC,CAAC;YAC1B,KAAK,IAAIM,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGpC,CAAC,EAAEoC,EAAE,EAAE,EAAE;cAC7B,IAAIV,CAAC,KAAKU,EAAE,EAAE;gBACZ;cACF;cACArC,GAAG,CAAC2B,CAAC,CAAC,CAACU,EAAE,CAAC,GAAGxD,cAAc,CAACmB,GAAG,CAAC2B,CAAC,CAAC,CAACU,EAAE,CAAC,EAAED,CAAC,CAAC;cAC1CpC,GAAG,CAACqC,EAAE,CAAC,CAACV,CAAC,CAAC,GAAG9C,cAAc,CAACmB,GAAG,CAACqC,EAAE,CAAC,CAACV,CAAC,CAAC,EAAEI,CAAC,CAAC;YAC5C;;YAEA;YACA,IAAI3B,WAAW,EAAE;cACfmB,KAAK,CAACI,CAAC,CAAC,GAAG9C,cAAc,CAAC0C,KAAK,CAACI,CAAC,CAAC,EAAES,CAAC,CAAC;YACxC;UACF;QACF;MACF;IACF;;IAEA;IACA,OAAOhC,WAAW,GAAGlB,IAAI,CAACqC,KAAK,CAAC,GAAG,IAAI;EACzC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASb,kBAAkBA,CAACV,GAAG,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,EAAEI,CAAC,EAAE;IAC9D,IAAIQ,GAAG,GAAGb,IAAI,KAAK,WAAW;IAC9B,IAAIc,IAAI,GAAGd,IAAI,KAAK,SAAS;IAC7B,IAAImC,IAAI,GAAGtB,GAAG,GAAG/B,SAAS,CAAC,CAAC,CAAC,GAAGgC,IAAI,GAAGvB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACrD,IAAIsB,GAAG,EAAE;MACPd,IAAI,GAAGjB,SAAS,CAACiB,IAAI,CAAC;IACxB;IACA,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,CAAC,GAAG,CAAC,EAAE0B,CAAC,EAAE,EAAE;MAC9B;;MAEA,IAAIY,QAAQ,GAAG,CAAC;MAChB,IAAIC,GAAG,GAAGF,IAAI;MACd,KAAK,IAAIR,CAAC,GAAGH,CAAC,GAAG,CAAC,EAAEG,CAAC,GAAG7B,CAAC,EAAE6B,CAAC,EAAE,EAAE;QAC9B,IAAIW,EAAE,GAAGzC,GAAG,CAAC8B,CAAC,CAAC,CAACH,CAAC,CAAC;QAClB,IAAI/B,OAAO,CAACZ,GAAG,CAACwD,GAAG,CAAC,EAAExD,GAAG,CAACyD,EAAE,CAAC,CAAC,EAAE;UAC9BD,GAAG,GAAGC,EAAE;UACRF,QAAQ,GAAGT,CAAC;QACd;MACF;;MAEA;MACA,IAAIlC,OAAO,CAACZ,GAAG,CAACwD,GAAG,CAAC,EAAEtC,IAAI,CAAC,EAAE;QAC3B;MACF;MACA,IAAIqC,QAAQ,KAAKZ,CAAC,GAAG,CAAC,EAAE;QACtB;QACA,IAAIe,IAAI,GAAG1C,GAAG,CAACuC,QAAQ,CAAC;QACxBvC,GAAG,CAACuC,QAAQ,CAAC,GAAGvC,GAAG,CAAC2B,CAAC,GAAG,CAAC,CAAC;QAC1B3B,GAAG,CAAC2B,CAAC,GAAG,CAAC,CAAC,GAAGe,IAAI;;QAEjB;QACA,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG1C,CAAC,EAAE0C,GAAG,EAAE,EAAE;UAChC,IAAIC,IAAI,GAAG5C,GAAG,CAAC2C,GAAG,CAAC,CAACJ,QAAQ,CAAC;UAC7BvC,GAAG,CAAC2C,GAAG,CAAC,CAACJ,QAAQ,CAAC,GAAGvC,GAAG,CAAC2C,GAAG,CAAC,CAAChB,CAAC,GAAG,CAAC,CAAC;UACpC3B,GAAG,CAAC2C,GAAG,CAAC,CAAChB,CAAC,GAAG,CAAC,CAAC,GAAGiB,IAAI;QACxB;;QAEA;QACA,IAAIxC,WAAW,EAAE;UACf,IAAIyC,IAAI,GAAGrC,CAAC,CAAC+B,QAAQ,CAAC;UACtB/B,CAAC,CAAC+B,QAAQ,CAAC,GAAG/B,CAAC,CAACmB,CAAC,GAAG,CAAC,CAAC;UACtBnB,CAAC,CAACmB,CAAC,GAAG,CAAC,CAAC,GAAGkB,IAAI;QACjB;MACF;;MAEA;MACA,KAAK,IAAIC,GAAG,GAAGnB,CAAC,GAAG,CAAC,EAAEmB,GAAG,GAAG7C,CAAC,EAAE6C,GAAG,EAAE,EAAE;QACpC,IAAIC,CAAC,GAAGjE,YAAY,CAACkB,GAAG,CAAC8C,GAAG,CAAC,CAACnB,CAAC,CAAC,EAAEa,GAAG,CAAC;QACtC,IAAIO,CAAC,KAAK,CAAC,EAAE;UACX;QACF;;QAEA;QACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/C,CAAC,EAAE+C,CAAC,EAAE,EAAE;UAC1BhD,GAAG,CAAC8C,GAAG,CAAC,CAACE,CAAC,CAAC,GAAGtE,QAAQ,CAACsB,GAAG,CAAC8C,GAAG,CAAC,CAACE,CAAC,CAAC,EAAEnE,cAAc,CAACkE,CAAC,EAAE/C,GAAG,CAAC2B,CAAC,GAAG,CAAC,CAAC,CAACqB,CAAC,CAAC,CAAC,CAAC;QACvE;;QAEA;QACA,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGhD,CAAC,EAAEgD,EAAE,EAAE,EAAE;UAC7BjD,GAAG,CAACiD,EAAE,CAAC,CAACtB,CAAC,GAAG,CAAC,CAAC,GAAGlD,SAAS,CAACuB,GAAG,CAACiD,EAAE,CAAC,CAACtB,CAAC,GAAG,CAAC,CAAC,EAAE9C,cAAc,CAACkE,CAAC,EAAE/C,GAAG,CAACiD,EAAE,CAAC,CAACH,GAAG,CAAC,CAAC,CAAC;QAC7E;;QAEA;QACA,IAAI1C,WAAW,EAAE;UACf,KAAK,IAAI8C,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGjD,CAAC,EAAEiD,GAAG,EAAE,EAAE;YAChC1C,CAAC,CAACsC,GAAG,CAAC,CAACI,GAAG,CAAC,GAAGxE,QAAQ,CAAC8B,CAAC,CAACsC,GAAG,CAAC,CAACI,GAAG,CAAC,EAAErE,cAAc,CAACkE,CAAC,EAAEvC,CAAC,CAACmB,CAAC,GAAG,CAAC,CAAC,CAACuB,GAAG,CAAC,CAAC,CAAC;UACvE;QACF;MACF;IACF;IACA,OAAO1C,CAAC;EACV;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASK,sBAAsBA,CAACsC,CAAC,EAAElD,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,EAAE;IAC7D,IAAIY,GAAG,GAAGb,IAAI,KAAK,WAAW;IAC9B,IAAIc,IAAI,GAAGd,IAAI,KAAK,SAAS;IAC7B,IAAIgB,GAAG,GAAGH,GAAG,GAAG/B,SAAS,CAAC,CAAC,CAAC,GAAGgC,IAAI,GAAGvB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpD,IAAIsB,GAAG,EAAE;MACPd,IAAI,GAAGjB,SAAS,CAACiB,IAAI,CAAC;IACxB;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,IAAIF,GAAG,GAAG1B,KAAK,CAAC6E,CAAC,CAAC;;IAElB;IACA,IAAIC,OAAO,GAAG,EAAE;;IAEhB;IACA,IAAIL,CAAC,GAAG9C,CAAC;;IAET;IACA;IACA,IAAIoD,KAAK,GAAG,EAAE;;IAEd;IACA,IAAIC,MAAM,GAAGlD,WAAW,GAAGlB,IAAI,CAACsC,KAAK,CAACvB,CAAC,CAAC,CAACwB,IAAI,CAACN,GAAG,CAAC,CAAC,GAAGZ,SAAS;;IAE/D;IACA,IAAIgD,QAAQ,GAAGnD,WAAW,GAAGlB,IAAI,CAACsC,KAAK,CAACuB,CAAC,CAAC,CAACtB,IAAI,CAACN,GAAG,CAAC,CAAC,GAAGZ,SAAS;;IAEjE;IACA,IAAIiD,qBAAqB,GAAG,CAAC;IAC7B,OAAOA,qBAAqB,IAAI,GAAG,EAAE;MACnCA,qBAAqB,IAAI,CAAC;;MAE1B;;MAEA;;MAEA,IAAIR,CAAC,GAAGhD,GAAG,CAAC+C,CAAC,GAAG,CAAC,CAAC,CAACA,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC3B;MACA;MACA;MACA;MACA;;MAEA,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,CAAC,EAAEpB,CAAC,EAAE,EAAE;QAC1B3B,GAAG,CAAC2B,CAAC,CAAC,CAACA,CAAC,CAAC,GAAGjD,QAAQ,CAACsB,GAAG,CAAC2B,CAAC,CAAC,CAACA,CAAC,CAAC,EAAEqB,CAAC,CAAC;MACpC;;MAEA;MACA,IAAI;QACFS,CAAC;QACDjD;MACF,CAAC,GAAGlB,EAAE,CAACU,GAAG,CAAC;MACXA,GAAG,GAAGpB,QAAQ,CAAC4B,CAAC,EAAEiD,CAAC,CAAC;MACpB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGX,CAAC,EAAEW,EAAE,EAAE,EAAE;QAC7B1D,GAAG,CAAC0D,EAAE,CAAC,CAACA,EAAE,CAAC,GAAGjF,SAAS,CAACuB,GAAG,CAAC0D,EAAE,CAAC,CAACA,EAAE,CAAC,EAAEV,CAAC,CAAC;MACzC;;MAEA;MACA,IAAI5C,WAAW,EAAE;QACfmD,QAAQ,GAAG3E,QAAQ,CAAC2E,QAAQ,EAAEE,CAAC,CAAC;MAClC;;MAEA;MACA,IAAIV,CAAC,KAAK,CAAC,IAAInD,OAAO,CAACZ,GAAG,CAACgB,GAAG,CAAC+C,CAAC,GAAG,CAAC,CAAC,CAACA,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE7C,IAAI,CAAC,EAAE;QACpDsD,qBAAqB,GAAG,CAAC;QACzBJ,OAAO,CAACO,IAAI,CAAC3D,GAAG,CAAC+C,CAAC,GAAG,CAAC,CAAC,CAACA,CAAC,GAAG,CAAC,CAAC,CAAC;;QAE/B;QACA,IAAI3C,WAAW,EAAE;UACfiD,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpBC,aAAa,CAACN,QAAQ,EAAEtD,CAAC,CAAC;UAC1BqD,MAAM,GAAG1E,QAAQ,CAAC0E,MAAM,EAAEC,QAAQ,CAAC;UACnC,IAAIR,CAAC,GAAG,CAAC,EAAE;YACTQ,QAAQ,GAAGrE,IAAI,CAACsC,KAAK,CAACuB,CAAC,GAAG,CAAC,CAAC,CAACtB,IAAI,CAACN,GAAG,CAAC,CAAC;UACzC;QACF;;QAEA;QACA4B,CAAC,IAAI,CAAC;QACN/C,GAAG,CAAC8D,GAAG,CAAC,CAAC;QACT,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGhB,CAAC,EAAEgB,GAAG,EAAE,EAAE;UAChC/D,GAAG,CAAC+D,GAAG,CAAC,CAACD,GAAG,CAAC,CAAC;QAChB;;QAEA;MACF,CAAC,MAAM,IAAIf,CAAC,KAAK,CAAC,IAAInD,OAAO,CAACZ,GAAG,CAACgB,GAAG,CAAC+C,CAAC,GAAG,CAAC,CAAC,CAACA,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE7C,IAAI,CAAC,EAAE;QAC3DsD,qBAAqB,GAAG,CAAC;QACzB,IAAIQ,EAAE,GAAGC,cAAc,CAACjE,GAAG,CAAC+C,CAAC,GAAG,CAAC,CAAC,CAACA,CAAC,GAAG,CAAC,CAAC,EAAE/C,GAAG,CAAC+C,CAAC,GAAG,CAAC,CAAC,CAACA,CAAC,GAAG,CAAC,CAAC,EAAE/C,GAAG,CAAC+C,CAAC,GAAG,CAAC,CAAC,CAACA,CAAC,GAAG,CAAC,CAAC,EAAE/C,GAAG,CAAC+C,CAAC,GAAG,CAAC,CAAC,CAACA,CAAC,GAAG,CAAC,CAAC,CAAC;QACnGK,OAAO,CAACO,IAAI,CAAC,GAAGK,EAAE,CAAC;;QAEnB;QACA,IAAI5D,WAAW,EAAE;UACfiD,KAAK,CAACO,OAAO,CAACM,aAAa,CAAClE,GAAG,CAAC+C,CAAC,GAAG,CAAC,CAAC,CAACA,CAAC,GAAG,CAAC,CAAC,EAAE/C,GAAG,CAAC+C,CAAC,GAAG,CAAC,CAAC,CAACA,CAAC,GAAG,CAAC,CAAC,EAAE/C,GAAG,CAAC+C,CAAC,GAAG,CAAC,CAAC,CAACA,CAAC,GAAG,CAAC,CAAC,EAAE/C,GAAG,CAAC+C,CAAC,GAAG,CAAC,CAAC,CAACA,CAAC,GAAG,CAAC,CAAC,EAAEiB,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAE9D,IAAI,EAAEC,IAAI,CAAC,CAAC;UAClI0D,aAAa,CAACN,QAAQ,EAAEtD,CAAC,CAAC;UAC1BqD,MAAM,GAAG1E,QAAQ,CAAC0E,MAAM,EAAEC,QAAQ,CAAC;UACnC,IAAIR,CAAC,GAAG,CAAC,EAAE;YACTQ,QAAQ,GAAGrE,IAAI,CAACsC,KAAK,CAACuB,CAAC,GAAG,CAAC,CAAC,CAACtB,IAAI,CAACN,GAAG,CAAC,CAAC;UACzC;QACF;;QAEA;QACA4B,CAAC,IAAI,CAAC;QACN/C,GAAG,CAAC8D,GAAG,CAAC,CAAC;QACT9D,GAAG,CAAC8D,GAAG,CAAC,CAAC;QACT,KAAK,IAAIK,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGpB,CAAC,EAAEoB,GAAG,EAAE,EAAE;UAChCnE,GAAG,CAACmE,GAAG,CAAC,CAACL,GAAG,CAAC,CAAC;UACd9D,GAAG,CAACmE,GAAG,CAAC,CAACL,GAAG,CAAC,CAAC;QAChB;MACF;MACA,IAAIf,CAAC,KAAK,CAAC,EAAE;QACX;MACF;IACF;;IAEA;IACAK,OAAO,CAACgB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAAC5F,QAAQ,CAACM,GAAG,CAACqF,CAAC,CAAC,EAAErF,GAAG,CAACsF,CAAC,CAAC,CAAC,CAAC;;IAEjD;IACA,IAAId,qBAAqB,GAAG,GAAG,EAAE;MAC/B,IAAIe,GAAG,GAAGC,KAAK,CAAC,oEAAoE,GAAGpB,OAAO,CAACqB,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1GF,GAAG,CAAC5D,MAAM,GAAGyC,OAAO;MACpBmB,GAAG,CAACG,OAAO,GAAG,EAAE;MAChB,MAAMH,GAAG;IACX;;IAEA;IACA;IACA,IAAI3D,CAAC,GAAGR,WAAW,GAAGxB,QAAQ,CAAC0E,MAAM,EAAEqB,SAAS,CAACtB,KAAK,EAAEpD,CAAC,CAAC,CAAC,GAAGM,SAAS;IACvE,OAAO;MACLI,MAAM,EAAEyC,OAAO;MACfxC;IACF,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASG,gBAAgBA,CAACoC,CAAC,EAAElD,CAAC,EAAEW,CAAC,EAAEJ,CAAC,EAAEG,MAAM,EAAET,IAAI,EAAEC,IAAI,EAAE;IACxD,IAAIyE,IAAI,GAAGvF,GAAG,CAACuB,CAAC,CAAC;IACjB,IAAIiE,CAAC,GAAGjG,QAAQ,CAACgG,IAAI,EAAEzB,CAAC,EAAEvC,CAAC,CAAC;IAC5B,IAAII,GAAG,GAAGb,IAAI,KAAK,WAAW;IAC9B,IAAIc,IAAI,GAAGd,IAAI,KAAK,SAAS;IAC7B,IAAImC,IAAI,GAAGtB,GAAG,GAAG/B,SAAS,CAAC,CAAC,CAAC,GAAGgC,IAAI,GAAGvB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACrD,IAAIyB,GAAG,GAAGH,GAAG,GAAG/B,SAAS,CAAC,CAAC,CAAC,GAAGgC,IAAI,GAAGvB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;;IAEpD;IACA;IACA,IAAIoF,YAAY,GAAG,EAAE;IACrB,IAAIC,cAAc,GAAG,EAAE;IACvB,KAAK,IAAIC,MAAM,IAAIrE,MAAM,EAAE;MACzB,IAAIgB,CAAC,GAAGsD,OAAO,CAACH,YAAY,EAAEE,MAAM,EAAEvF,KAAK,CAAC;MAC5C,IAAIkC,CAAC,KAAK,CAAC,CAAC,EAAE;QACZmD,YAAY,CAACnB,IAAI,CAACqB,MAAM,CAAC;QACzBD,cAAc,CAACpB,IAAI,CAAC,CAAC,CAAC;MACxB,CAAC,MAAM;QACLoB,cAAc,CAACpD,CAAC,CAAC,IAAI,CAAC;MACxB;IACF;;IAEA;IACA;IACA;;IAEA,IAAI+C,OAAO,GAAG,EAAE;IAChB,IAAIQ,GAAG,GAAGJ,YAAY,CAACxE,MAAM;IAC7B,IAAIgE,CAAC,GAAG9C,KAAK,CAACvB,CAAC,CAAC,CAACwB,IAAI,CAACa,IAAI,CAAC;IAC3B,IAAI6C,CAAC,GAAGjG,IAAI,CAACsC,KAAK,CAACvB,CAAC,CAAC,CAACwB,IAAI,CAACN,GAAG,CAAC,CAAC;IAChC,IAAIiE,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;MAC3B,IAAIJ,MAAM,GAAGF,YAAY,CAACO,GAAG,CAAC;MAC9B,IAAIC,CAAC,GAAG5G,QAAQ,CAACmG,CAAC,EAAEjG,QAAQ,CAACoG,MAAM,EAAEG,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1C,IAAII,SAAS,GAAG/F,SAAS,CAAC8F,CAAC,EAAEhB,CAAC,CAAC;MAC/BiB,SAAS,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;;MAEnB;MACA;MACA;MACA,OAAOD,SAAS,CAACjF,MAAM,GAAGyE,cAAc,CAACM,GAAG,CAAC,EAAE;QAC7C,IAAII,SAAS,GAAGC,cAAc,CAACJ,CAAC,EAAErF,CAAC,EAAEsF,SAAS,EAAErF,IAAI,EAAEC,IAAI,CAAC;QAC3D,IAAIsF,SAAS,KAAK,IAAI,EAAE;UACtB;QACF,CAAC,CAAC;QACFF,SAAS,CAAC5B,IAAI,CAAC8B,SAAS,CAAC;MAC3B;;MAEA;MACA,IAAIE,UAAU,GAAG/G,QAAQ,CAACS,GAAG,CAACmB,CAAC,CAAC,EAAEI,CAAC,CAAC;MACpC2E,SAAS,GAAGA,SAAS,CAACK,GAAG,CAACC,CAAC,IAAIjH,QAAQ,CAAC+G,UAAU,EAAEE,CAAC,CAAC,CAAC;MACvDnB,OAAO,CAACf,IAAI,CAAC,GAAG4B,SAAS,CAACK,GAAG,CAACC,CAAC,KAAK;QAClCC,KAAK,EAAEd,MAAM;QACbe,MAAM,EAAEpH,OAAO,CAACkH,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC;IACN,CAAC;IACD,KAAK,IAAIR,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGH,GAAG,EAAEG,GAAG,EAAE,EAAE;MAClCD,KAAK,CAAC,CAAC;IACT;IACA,OAAOV,OAAO;EAChB;;EAEA;AACF;AACA;AACA;EACE,SAAST,cAAcA,CAACI,CAAC,EAAEC,CAAC,EAAEtC,CAAC,EAAEgE,CAAC,EAAE;IAClC;IACA,IAAIC,GAAG,GAAGxH,SAAS,CAAC4F,CAAC,EAAE2B,CAAC,CAAC;IACzB,IAAIE,IAAI,GAAGxH,QAAQ,CAACG,cAAc,CAACwF,CAAC,EAAE2B,CAAC,CAAC,EAAEnH,cAAc,CAACyF,CAAC,EAAEtC,CAAC,CAAC,CAAC;IAC/D,IAAImE,CAAC,GAAGtH,cAAc,CAACoH,GAAG,EAAE,GAAG,CAAC;IAChC,IAAIG,CAAC,GAAGvH,cAAc,CAACE,IAAI,CAACL,QAAQ,CAACG,cAAc,CAACoH,GAAG,EAAEA,GAAG,CAAC,EAAEpH,cAAc,CAAC,CAAC,EAAEqH,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;IAC9F,OAAO,CAACzH,SAAS,CAAC0H,CAAC,EAAEC,CAAC,CAAC,EAAE1H,QAAQ,CAACyH,CAAC,EAAEC,CAAC,CAAC,CAAC;EAC1C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASlC,aAAaA,CAACG,CAAC,EAAEC,CAAC,EAAEtC,CAAC,EAAEgE,CAAC,EAAEK,EAAE,EAAEC,EAAE,EAAEpG,IAAI,EAAEC,IAAI,EAAE;IACrD,IAAIa,GAAG,GAAGb,IAAI,KAAK,WAAW;IAC9B,IAAIc,IAAI,GAAGd,IAAI,KAAK,SAAS;IAC7B,IAAImC,IAAI,GAAGtB,GAAG,GAAG/B,SAAS,CAAC,CAAC,CAAC,GAAGgC,IAAI,GAAGvB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACrD,IAAIyB,GAAG,GAAGH,GAAG,GAAG/B,SAAS,CAAC,CAAC,CAAC,GAAGgC,IAAI,GAAGvB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;;IAEpD;IACA;IACA,IAAIE,OAAO,CAACZ,GAAG,CAACgD,CAAC,CAAC,EAAE9B,IAAI,CAAC,EAAE;MACzB,OAAO,CAAC,CAACiB,GAAG,EAAEmB,IAAI,CAAC,EAAE,CAACA,IAAI,EAAEnB,GAAG,CAAC,CAAC;IACnC;;IAEA;IACA;IACA,IAAIxB,MAAM,CAACX,GAAG,CAACN,QAAQ,CAAC2H,EAAE,EAAEC,EAAE,CAAC,CAAC,EAAEpG,IAAI,CAAC,EAAE;MACvC,OAAO,CAAC,CAACxB,QAAQ,CAAC2H,EAAE,EAAEL,CAAC,CAAC,EAAEtH,QAAQ,CAAC4H,EAAE,EAAEN,CAAC,CAAC,CAAC,EAAE,CAAChE,CAAC,EAAEA,CAAC,CAAC,CAAC;IACrD;;IAEA;IACA;IACA,IAAIuE,EAAE,GAAG7H,QAAQ,CAAC2F,CAAC,EAAEgC,EAAE,CAAC;IACxB,IAAIG,EAAE,GAAG9H,QAAQ,CAACsH,CAAC,EAAEK,EAAE,CAAC;;IAExB;IACA;;IAEA,IAAIzG,OAAO,CAACZ,GAAG,CAACsF,CAAC,CAAC,EAAEpE,IAAI,CAAC,IAAIN,OAAO,CAACZ,GAAG,CAACwH,EAAE,CAAC,EAAEtG,IAAI,CAAC,EAAE;MACnD,OAAO,CAAC,CAACqG,EAAE,EAAEpF,GAAG,CAAC,EAAE,CAACa,CAAC,EAAEM,IAAI,CAAC,CAAC;IAC/B,CAAC,MAAM;MACL,OAAO,CAAC,CAACgC,CAAC,EAAEhC,IAAI,CAAC,EAAE,CAACkE,EAAE,EAAErF,GAAG,CAAC,CAAC;IAC/B;EACF;;EAEA;AACF;AACA;AACA;EACE,SAAS0C,aAAaA,CAAC7D,GAAG,EAAEC,CAAC,EAAE;IAC7B;IACA,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,GAAG,CAACM,MAAM,EAAEqB,CAAC,EAAE,EAAE;MACnC3B,GAAG,CAAC2B,CAAC,CAAC,CAACgC,IAAI,CAAC,GAAGnC,KAAK,CAACvB,CAAC,GAAGD,GAAG,CAAC2B,CAAC,CAAC,CAACrB,MAAM,CAAC,CAACmB,IAAI,CAAC,CAAC,CAAC,CAAC;IAClD;;IAEA;IACA,KAAK,IAAIgF,GAAG,GAAGzG,GAAG,CAACM,MAAM,EAAEmG,GAAG,GAAGxG,CAAC,EAAEwG,GAAG,EAAE,EAAE;MACzCzG,GAAG,CAAC2D,IAAI,CAACnC,KAAK,CAACvB,CAAC,CAAC,CAACwB,IAAI,CAAC,CAAC,CAAC,CAAC;MAC1BzB,GAAG,CAACyG,GAAG,CAAC,CAACA,GAAG,CAAC,GAAG,CAAC;IACnB;IACA,OAAOzG,GAAG;EACZ;;EAEA;AACF;AACA;AACA;AACA;EACE,SAAS2E,SAASA,CAAC3E,GAAG,EAAEC,CAAC,EAAE;IACzB,IAAIyG,CAAC,GAAG,EAAE;IACV,KAAK,IAAI/E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,CAAC,EAAE0B,CAAC,EAAE,EAAE;MAC1B+E,CAAC,CAAC/E,CAAC,CAAC,GAAGH,KAAK,CAACvB,CAAC,CAAC,CAACwB,IAAI,CAAC,CAAC,CAAC;IACzB;IACA,IAAIkF,CAAC,GAAG,CAAC;IACT,KAAK,IAAIC,GAAG,IAAI5G,GAAG,EAAE;MACnB,IAAI+C,CAAC,GAAG6D,GAAG,CAACtG,MAAM;MAClB,KAAK,IAAIuG,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG9D,CAAC,EAAE8D,GAAG,EAAE,EAAE;QAChC,KAAK,IAAI/E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,CAAC,EAAEjB,CAAC,EAAE,EAAE;UAC1B4E,CAAC,CAACC,CAAC,GAAGE,GAAG,CAAC,CAACF,CAAC,GAAG7E,CAAC,CAAC,GAAG8E,GAAG,CAACC,GAAG,CAAC,CAAC/E,CAAC,CAAC;QACjC;MACF;MACA6E,CAAC,IAAI5D,CAAC;IACR;IACA,OAAO2D,CAAC;EACV;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASzB,OAAOA,CAACjF,GAAG,EAAEyC,EAAE,EAAEqE,EAAE,EAAE;IAC5B,KAAK,IAAInF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,GAAG,CAACM,MAAM,EAAEqB,CAAC,EAAE,EAAE;MACnC,IAAImF,EAAE,CAAC9G,GAAG,CAAC2B,CAAC,CAAC,EAAEc,EAAE,CAAC,EAAE;QAClB,OAAOd,CAAC;MACV;IACF;IACA,OAAO,CAAC,CAAC;EACX;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS+D,cAAcA,CAACvC,CAAC,EAAElD,CAAC,EAAE8G,MAAM,EAAE7G,IAAI,EAAEC,IAAI,EAAE;IAChD,IAAI6G,QAAQ,GAAG7G,IAAI,KAAK,WAAW,GAAGlB,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI;IAC5D,IAAIqF,CAAC,CAAC,CAAC;;IAEP;IACA,IAAI3C,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MACjB2C,CAAC,GAAG2C,sBAAsB,CAAChH,CAAC,EAAE8G,MAAM,EAAE5G,IAAI,CAAC;MAC3C,IAAI;QACFmE,CAAC,GAAG/E,MAAM,CAAC4D,CAAC,EAAEmB,CAAC,CAAC;MAClB,CAAC,CAAC,OAAO4C,OAAO,EAAE;QAChB;QACA;QACA;MACF;MACA,IAAIvH,MAAM,CAACwH,IAAI,CAAC7C,CAAC,CAAC,EAAE0C,QAAQ,CAAC,EAAE;QAC7B;MACF;IACF;IACA,IAAIrF,CAAC,IAAI,CAAC,EAAE;MACV,OAAO,IAAI,CAAC,CAAC;IACf;;IAEA;IACAA,CAAC,GAAG,CAAC;IACL,OAAO,IAAI,EAAE;MACX,IAAIK,CAAC,GAAGzC,MAAM,CAAC4D,CAAC,EAAEmB,CAAC,CAAC;MACpB,IAAI1E,OAAO,CAACuH,IAAI,CAACC,oBAAoB,CAAC9C,CAAC,EAAE,CAACtC,CAAC,CAAC,CAAC,CAAC,EAAE9B,IAAI,CAAC,EAAE;QACrD;MACF;MACA,IAAI,EAAEyB,CAAC,IAAI,EAAE,EAAE;QACb,OAAO,IAAI;MACb;MACA2C,CAAC,GAAG+C,SAAS,CAACrF,CAAC,CAAC;IAClB;IACA,OAAOsC,CAAC;EACV;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS2C,sBAAsBA,CAAChH,CAAC,EAAE8G,MAAM,EAAE5G,IAAI,EAAE;IAC/C,IAAIa,GAAG,GAAGb,IAAI,KAAK,WAAW;IAC9B,IAAIc,IAAI,GAAGd,IAAI,KAAK,SAAS;;IAE7B;IACA,IAAI0F,CAAC,GAAGrE,KAAK,CAACvB,CAAC,CAAC,CAACwB,IAAI,CAAC,CAAC,CAAC,CAACmE,GAAG,CAAC0B,CAAC,IAAI,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACxD,IAAIxG,GAAG,EAAE;MACP6E,CAAC,GAAGA,CAAC,CAACD,GAAG,CAAC7C,CAAC,IAAI9D,SAAS,CAAC8D,CAAC,CAAC,CAAC;IAC9B;IACA,IAAI9B,IAAI,EAAE;MACR4E,CAAC,GAAGA,CAAC,CAACD,GAAG,CAAC7C,CAAC,IAAIrD,OAAO,CAACqD,CAAC,CAAC,CAAC;IAC5B;;IAEA;IACA8C,CAAC,GAAGuB,oBAAoB,CAACvB,CAAC,EAAEkB,MAAM,CAAC;;IAEnC;IACA,OAAOM,SAAS,CAACxB,CAAC,EAAE1F,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;EACE,SAASiH,oBAAoBA,CAACvB,CAAC,EAAEkB,MAAM,EAAE;IACvC,IAAIU,WAAW,GAAGtI,IAAI,CAAC0G,CAAC,CAAC;IACzB,KAAK,IAAI6B,CAAC,IAAIX,MAAM,EAAE;MACpBW,CAAC,GAAGtI,OAAO,CAACsI,CAAC,EAAED,WAAW,CAAC,CAAC,CAAC;MAC7B;MACA5B,CAAC,GAAGnH,QAAQ,CAACmH,CAAC,EAAEjH,QAAQ,CAACE,YAAY,CAACgB,GAAG,CAAC4H,CAAC,EAAE7B,CAAC,CAAC,EAAE/F,GAAG,CAAC4H,CAAC,EAAEA,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC;IAClE;IACA,OAAO7B,CAAC;EACV;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASsB,IAAIA,CAACtB,CAAC,EAAE;IACf,OAAO7G,GAAG,CAACD,IAAI,CAACe,GAAG,CAAC+F,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC;EAC7B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASwB,SAASA,CAACxB,CAAC,EAAE1F,IAAI,EAAE;IAC1B,IAAIa,GAAG,GAAGb,IAAI,KAAK,WAAW;IAC9B,IAAIc,IAAI,GAAGd,IAAI,KAAK,SAAS;IAC7B,IAAIgB,GAAG,GAAGH,GAAG,GAAG/B,SAAS,CAAC,CAAC,CAAC,GAAGgC,IAAI,GAAGvB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpD,OAAOd,QAAQ,CAACE,YAAY,CAACqC,GAAG,EAAEgG,IAAI,CAACtB,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC;EAChD;EACA,OAAO9F,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}