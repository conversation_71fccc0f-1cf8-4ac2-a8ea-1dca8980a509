{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { bignumberDependencies } from './dependenciesBignumber.generated.js';\nimport { fractionDependencies } from './dependenciesFraction.generated.js';\nimport { numberDependencies } from './dependenciesNumber.generated.js';\nimport { createNumeric } from '../../factoriesAny.js';\nexport var numericDependencies = {\n  bignumberDependencies,\n  fractionDependencies,\n  numberDependencies,\n  createNumeric\n};", "map": {"version": 3, "names": ["bignumberDependencies", "fractionDependencies", "numberDependencies", "createNumeric", "numericDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesNumeric.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { bignumberDependencies } from './dependenciesBignumber.generated.js';\nimport { fractionDependencies } from './dependenciesFraction.generated.js';\nimport { numberDependencies } from './dependenciesNumber.generated.js';\nimport { createNumeric } from '../../factoriesAny.js';\nexport var numericDependencies = {\n  bignumberDependencies,\n  fractionDependencies,\n  numberDependencies,\n  createNumeric\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAO,IAAIC,mBAAmB,GAAG;EAC/BJ,qBAAqB;EACrBC,oBAAoB;EACpBC,kBAAkB;EAClBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}