{"ast": null, "code": "export var unitDocs = {\n  name: 'unit',\n  category: 'Construction',\n  syntax: ['value unit', 'unit(value, unit)', 'unit(string)'],\n  description: 'Create a unit.',\n  examples: ['5.5 mm', '3 inch', 'unit(7.1, \"kilogram\")', 'unit(\"23 deg\")'],\n  seealso: ['bignumber', 'boolean', 'complex', 'index', 'matrix', 'number', 'string']\n};", "map": {"version": 3, "names": ["unitDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/construction/unit.js"], "sourcesContent": ["export var unitDocs = {\n  name: 'unit',\n  category: 'Construction',\n  syntax: ['value unit', 'unit(value, unit)', 'unit(string)'],\n  description: 'Create a unit.',\n  examples: ['5.5 mm', '3 inch', 'unit(7.1, \"kilogram\")', 'unit(\"23 deg\")'],\n  seealso: ['bignumber', 'boolean', 'complex', 'index', 'matrix', 'number', 'string']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,YAAY,EAAE,mBAAmB,EAAE,cAAc,CAAC;EAC3DC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,uBAAuB,EAAE,gBAAgB,CAAC;EACzEC,OAAO,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;AACpF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}