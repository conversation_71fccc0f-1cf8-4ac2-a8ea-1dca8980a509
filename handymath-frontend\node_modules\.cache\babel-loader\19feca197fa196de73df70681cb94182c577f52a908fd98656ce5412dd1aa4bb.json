{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nexport var createTrigUnit = /* #__PURE__ */factory('trigUnit', ['typed'], _ref => {\n  var {\n    typed\n  } = _ref;\n  return {\n    Unit: typed.referToSelf(self => x => {\n      if (!x.hasBase(x.constructor.BASE_UNITS.ANGLE)) {\n        throw new TypeError('Unit in function cot is no angle');\n      }\n      return typed.find(self, x.valueType())(x.value);\n    })\n  };\n});", "map": {"version": 3, "names": ["factory", "createTrigUnit", "_ref", "typed", "Unit", "referToSelf", "self", "x", "hasBase", "constructor", "BASE_UNITS", "ANGLE", "TypeError", "find", "valueType", "value"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/trigonometry/trigUnit.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nexport var createTrigUnit = /* #__PURE__ */factory('trigUnit', ['typed'], _ref => {\n  var {\n    typed\n  } = _ref;\n  return {\n    Unit: typed.referToSelf(self => x => {\n      if (!x.hasBase(x.constructor.BASE_UNITS.ANGLE)) {\n        throw new TypeError('Unit in function cot is no angle');\n      }\n      return typed.find(self, x.valueType())(x.value);\n    })\n  };\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,OAAO,IAAIC,cAAc,GAAG,eAAeD,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,EAAEE,IAAI,IAAI;EAChF,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR,OAAO;IACLE,IAAI,EAAED,KAAK,CAACE,WAAW,CAACC,IAAI,IAAIC,CAAC,IAAI;MACnC,IAAI,CAACA,CAAC,CAACC,OAAO,CAACD,CAAC,CAACE,WAAW,CAACC,UAAU,CAACC,KAAK,CAAC,EAAE;QAC9C,MAAM,IAAIC,SAAS,CAAC,kCAAkC,CAAC;MACzD;MACA,OAAOT,KAAK,CAACU,IAAI,CAACP,IAAI,EAAEC,CAAC,CAACO,SAAS,CAAC,CAAC,CAAC,CAACP,CAAC,CAACQ,KAAK,CAAC;IACjD,CAAC;EACH,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}