{"ast": null, "code": "import { getSafeProperty, isSafeProperty, setSafeProperty } from './customs.js';\nimport { isMap, isObject } from './is.js';\n\n/**\n * A map facade on a bare object.\n *\n * The small number of methods needed to implement a scope,\n * forwarding on to the SafeProperty functions. Over time, the codebase\n * will stop using this method, as all objects will be Maps, rather than\n * more security prone objects.\n */\nexport class ObjectWrappingMap {\n  constructor(object) {\n    this.wrappedObject = object;\n    this[Symbol.iterator] = this.entries;\n  }\n  keys() {\n    return Object.keys(this.wrappedObject).filter(key => this.has(key)).values();\n  }\n  get(key) {\n    return getSafeProperty(this.wrappedObject, key);\n  }\n  set(key, value) {\n    setSafeProperty(this.wrappedObject, key, value);\n    return this;\n  }\n  has(key) {\n    return isSafeProperty(this.wrappedObject, key) && key in this.wrappedObject;\n  }\n  entries() {\n    return mapIterator(this.keys(), key => [key, this.get(key)]);\n  }\n  forEach(callback) {\n    for (var key of this.keys()) {\n      callback(this.get(key), key, this);\n    }\n  }\n  delete(key) {\n    if (isSafeProperty(this.wrappedObject, key)) {\n      delete this.wrappedObject[key];\n    }\n  }\n  clear() {\n    for (var key of this.keys()) {\n      this.delete(key);\n    }\n  }\n  get size() {\n    return Object.keys(this.wrappedObject).length;\n  }\n}\n\n/**\n * Create a map with two partitions: a and b.\n * The set with bKeys determines which keys/values are read/written to map b,\n * all other values are read/written to map a\n *\n * For example:\n *\n *   const a = new Map()\n *   const b = new Map()\n *   const p = new PartitionedMap(a, b, new Set(['x', 'y']))\n *\n * In this case, values `x` and `y` are read/written to map `b`,\n * all other values are read/written to map `a`.\n */\nexport class PartitionedMap {\n  /**\n   * @param {Map} a\n   * @param {Map} b\n   * @param {Set} bKeys\n   */\n  constructor(a, b, bKeys) {\n    this.a = a;\n    this.b = b;\n    this.bKeys = bKeys;\n    this[Symbol.iterator] = this.entries;\n  }\n  get(key) {\n    return this.bKeys.has(key) ? this.b.get(key) : this.a.get(key);\n  }\n  set(key, value) {\n    if (this.bKeys.has(key)) {\n      this.b.set(key, value);\n    } else {\n      this.a.set(key, value);\n    }\n    return this;\n  }\n  has(key) {\n    return this.b.has(key) || this.a.has(key);\n  }\n  keys() {\n    return new Set([...this.a.keys(), ...this.b.keys()])[Symbol.iterator]();\n  }\n  entries() {\n    return mapIterator(this.keys(), key => [key, this.get(key)]);\n  }\n  forEach(callback) {\n    for (var key of this.keys()) {\n      callback(this.get(key), key, this);\n    }\n  }\n  delete(key) {\n    return this.bKeys.has(key) ? this.b.delete(key) : this.a.delete(key);\n  }\n  clear() {\n    this.a.clear();\n    this.b.clear();\n  }\n  get size() {\n    return [...this.keys()].length;\n  }\n}\n\n/**\n * Create a new iterator that maps over the provided iterator, applying a mapping function to each item\n */\nfunction mapIterator(it, callback) {\n  return {\n    next: () => {\n      var n = it.next();\n      return n.done ? n : {\n        value: callback(n.value),\n        done: false\n      };\n    }\n  };\n}\n\n/**\n * Creates an empty map, or whatever your platform's polyfill is.\n *\n * @returns an empty Map or Map like object.\n */\nexport function createEmptyMap() {\n  return new Map();\n}\n\n/**\n * Creates a Map from the given object.\n *\n * @param { Map | { [key: string]: unknown } | undefined } mapOrObject\n * @returns\n */\nexport function createMap(mapOrObject) {\n  if (!mapOrObject) {\n    return createEmptyMap();\n  }\n  if (isMap(mapOrObject)) {\n    return mapOrObject;\n  }\n  if (isObject(mapOrObject)) {\n    return new ObjectWrappingMap(mapOrObject);\n  }\n  throw new Error('createMap can create maps from objects or Maps');\n}\n\n/**\n * Unwraps a map into an object.\n *\n * @param {Map} map\n * @returns { [key: string]: unknown }\n */\nexport function toObject(map) {\n  if (map instanceof ObjectWrappingMap) {\n    return map.wrappedObject;\n  }\n  var object = {};\n  for (var key of map.keys()) {\n    var value = map.get(key);\n    setSafeProperty(object, key, value);\n  }\n  return object;\n}\n\n/**\n * Copies the contents of key-value pairs from each `objects` in to `map`.\n *\n * Object is `objects` can be a `Map` or object.\n *\n * This is the `Map` analog to `Object.assign`.\n */\nexport function assign(map) {\n  for (var _len = arguments.length, objects = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    objects[_key - 1] = arguments[_key];\n  }\n  for (var args of objects) {\n    if (!args) {\n      continue;\n    }\n    if (isMap(args)) {\n      for (var key of args.keys()) {\n        map.set(key, args.get(key));\n      }\n    } else if (isObject(args)) {\n      for (var _key2 of Object.keys(args)) {\n        map.set(_key2, args[_key2]);\n      }\n    }\n  }\n  return map;\n}", "map": {"version": 3, "names": ["getSafeProperty", "isSafeProperty", "setSafeProperty", "isMap", "isObject", "ObjectWrappingMap", "constructor", "object", "wrappedObject", "Symbol", "iterator", "entries", "keys", "Object", "filter", "key", "has", "values", "get", "set", "value", "mapIterator", "for<PERSON>ach", "callback", "delete", "clear", "size", "length", "PartitionedMap", "a", "b", "b<PERSON><PERSON><PERSON>", "Set", "it", "next", "n", "done", "createEmptyMap", "Map", "createMap", "mapOrObject", "Error", "toObject", "map", "assign", "_len", "arguments", "objects", "Array", "_key", "args", "_key2"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/map.js"], "sourcesContent": ["import { getSafeProperty, isSafeProperty, setSafeProperty } from './customs.js';\nimport { isMap, isObject } from './is.js';\n\n/**\n * A map facade on a bare object.\n *\n * The small number of methods needed to implement a scope,\n * forwarding on to the SafeProperty functions. Over time, the codebase\n * will stop using this method, as all objects will be Maps, rather than\n * more security prone objects.\n */\nexport class ObjectWrappingMap {\n  constructor(object) {\n    this.wrappedObject = object;\n    this[Symbol.iterator] = this.entries;\n  }\n  keys() {\n    return Object.keys(this.wrappedObject).filter(key => this.has(key)).values();\n  }\n  get(key) {\n    return getSafeProperty(this.wrappedObject, key);\n  }\n  set(key, value) {\n    setSafeProperty(this.wrappedObject, key, value);\n    return this;\n  }\n  has(key) {\n    return isSafeProperty(this.wrappedObject, key) && key in this.wrappedObject;\n  }\n  entries() {\n    return mapIterator(this.keys(), key => [key, this.get(key)]);\n  }\n  forEach(callback) {\n    for (var key of this.keys()) {\n      callback(this.get(key), key, this);\n    }\n  }\n  delete(key) {\n    if (isSafeProperty(this.wrappedObject, key)) {\n      delete this.wrappedObject[key];\n    }\n  }\n  clear() {\n    for (var key of this.keys()) {\n      this.delete(key);\n    }\n  }\n  get size() {\n    return Object.keys(this.wrappedObject).length;\n  }\n}\n\n/**\n * Create a map with two partitions: a and b.\n * The set with bKeys determines which keys/values are read/written to map b,\n * all other values are read/written to map a\n *\n * For example:\n *\n *   const a = new Map()\n *   const b = new Map()\n *   const p = new PartitionedMap(a, b, new Set(['x', 'y']))\n *\n * In this case, values `x` and `y` are read/written to map `b`,\n * all other values are read/written to map `a`.\n */\nexport class PartitionedMap {\n  /**\n   * @param {Map} a\n   * @param {Map} b\n   * @param {Set} bKeys\n   */\n  constructor(a, b, bKeys) {\n    this.a = a;\n    this.b = b;\n    this.bKeys = bKeys;\n    this[Symbol.iterator] = this.entries;\n  }\n  get(key) {\n    return this.bKeys.has(key) ? this.b.get(key) : this.a.get(key);\n  }\n  set(key, value) {\n    if (this.bKeys.has(key)) {\n      this.b.set(key, value);\n    } else {\n      this.a.set(key, value);\n    }\n    return this;\n  }\n  has(key) {\n    return this.b.has(key) || this.a.has(key);\n  }\n  keys() {\n    return new Set([...this.a.keys(), ...this.b.keys()])[Symbol.iterator]();\n  }\n  entries() {\n    return mapIterator(this.keys(), key => [key, this.get(key)]);\n  }\n  forEach(callback) {\n    for (var key of this.keys()) {\n      callback(this.get(key), key, this);\n    }\n  }\n  delete(key) {\n    return this.bKeys.has(key) ? this.b.delete(key) : this.a.delete(key);\n  }\n  clear() {\n    this.a.clear();\n    this.b.clear();\n  }\n  get size() {\n    return [...this.keys()].length;\n  }\n}\n\n/**\n * Create a new iterator that maps over the provided iterator, applying a mapping function to each item\n */\nfunction mapIterator(it, callback) {\n  return {\n    next: () => {\n      var n = it.next();\n      return n.done ? n : {\n        value: callback(n.value),\n        done: false\n      };\n    }\n  };\n}\n\n/**\n * Creates an empty map, or whatever your platform's polyfill is.\n *\n * @returns an empty Map or Map like object.\n */\nexport function createEmptyMap() {\n  return new Map();\n}\n\n/**\n * Creates a Map from the given object.\n *\n * @param { Map | { [key: string]: unknown } | undefined } mapOrObject\n * @returns\n */\nexport function createMap(mapOrObject) {\n  if (!mapOrObject) {\n    return createEmptyMap();\n  }\n  if (isMap(mapOrObject)) {\n    return mapOrObject;\n  }\n  if (isObject(mapOrObject)) {\n    return new ObjectWrappingMap(mapOrObject);\n  }\n  throw new Error('createMap can create maps from objects or Maps');\n}\n\n/**\n * Unwraps a map into an object.\n *\n * @param {Map} map\n * @returns { [key: string]: unknown }\n */\nexport function toObject(map) {\n  if (map instanceof ObjectWrappingMap) {\n    return map.wrappedObject;\n  }\n  var object = {};\n  for (var key of map.keys()) {\n    var value = map.get(key);\n    setSafeProperty(object, key, value);\n  }\n  return object;\n}\n\n/**\n * Copies the contents of key-value pairs from each `objects` in to `map`.\n *\n * Object is `objects` can be a `Map` or object.\n *\n * This is the `Map` analog to `Object.assign`.\n */\nexport function assign(map) {\n  for (var _len = arguments.length, objects = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    objects[_key - 1] = arguments[_key];\n  }\n  for (var args of objects) {\n    if (!args) {\n      continue;\n    }\n    if (isMap(args)) {\n      for (var key of args.keys()) {\n        map.set(key, args.get(key));\n      }\n    } else if (isObject(args)) {\n      for (var _key2 of Object.keys(args)) {\n        map.set(_key2, args[_key2]);\n      }\n    }\n  }\n  return map;\n}"], "mappings": "AAAA,SAASA,eAAe,EAAEC,cAAc,EAAEC,eAAe,QAAQ,cAAc;AAC/E,SAASC,KAAK,EAAEC,QAAQ,QAAQ,SAAS;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,CAAC;EAC7BC,WAAWA,CAACC,MAAM,EAAE;IAClB,IAAI,CAACC,aAAa,GAAGD,MAAM;IAC3B,IAAI,CAACE,MAAM,CAACC,QAAQ,CAAC,GAAG,IAAI,CAACC,OAAO;EACtC;EACAC,IAAIA,CAAA,EAAG;IACL,OAAOC,MAAM,CAACD,IAAI,CAAC,IAAI,CAACJ,aAAa,CAAC,CAACM,MAAM,CAACC,GAAG,IAAI,IAAI,CAACC,GAAG,CAACD,GAAG,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC;EAC9E;EACAC,GAAGA,CAACH,GAAG,EAAE;IACP,OAAOf,eAAe,CAAC,IAAI,CAACQ,aAAa,EAAEO,GAAG,CAAC;EACjD;EACAI,GAAGA,CAACJ,GAAG,EAAEK,KAAK,EAAE;IACdlB,eAAe,CAAC,IAAI,CAACM,aAAa,EAAEO,GAAG,EAAEK,KAAK,CAAC;IAC/C,OAAO,IAAI;EACb;EACAJ,GAAGA,CAACD,GAAG,EAAE;IACP,OAAOd,cAAc,CAAC,IAAI,CAACO,aAAa,EAAEO,GAAG,CAAC,IAAIA,GAAG,IAAI,IAAI,CAACP,aAAa;EAC7E;EACAG,OAAOA,CAAA,EAAG;IACR,OAAOU,WAAW,CAAC,IAAI,CAACT,IAAI,CAAC,CAAC,EAAEG,GAAG,IAAI,CAACA,GAAG,EAAE,IAAI,CAACG,GAAG,CAACH,GAAG,CAAC,CAAC,CAAC;EAC9D;EACAO,OAAOA,CAACC,QAAQ,EAAE;IAChB,KAAK,IAAIR,GAAG,IAAI,IAAI,CAACH,IAAI,CAAC,CAAC,EAAE;MAC3BW,QAAQ,CAAC,IAAI,CAACL,GAAG,CAACH,GAAG,CAAC,EAAEA,GAAG,EAAE,IAAI,CAAC;IACpC;EACF;EACAS,MAAMA,CAACT,GAAG,EAAE;IACV,IAAId,cAAc,CAAC,IAAI,CAACO,aAAa,EAAEO,GAAG,CAAC,EAAE;MAC3C,OAAO,IAAI,CAACP,aAAa,CAACO,GAAG,CAAC;IAChC;EACF;EACAU,KAAKA,CAAA,EAAG;IACN,KAAK,IAAIV,GAAG,IAAI,IAAI,CAACH,IAAI,CAAC,CAAC,EAAE;MAC3B,IAAI,CAACY,MAAM,CAACT,GAAG,CAAC;IAClB;EACF;EACA,IAAIW,IAAIA,CAAA,EAAG;IACT,OAAOb,MAAM,CAACD,IAAI,CAAC,IAAI,CAACJ,aAAa,CAAC,CAACmB,MAAM;EAC/C;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,CAAC;EAC1B;AACF;AACA;AACA;AACA;EACEtB,WAAWA,CAACuB,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAE;IACvB,IAAI,CAACF,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACtB,MAAM,CAACC,QAAQ,CAAC,GAAG,IAAI,CAACC,OAAO;EACtC;EACAO,GAAGA,CAACH,GAAG,EAAE;IACP,OAAO,IAAI,CAACgB,KAAK,CAACf,GAAG,CAACD,GAAG,CAAC,GAAG,IAAI,CAACe,CAAC,CAACZ,GAAG,CAACH,GAAG,CAAC,GAAG,IAAI,CAACc,CAAC,CAACX,GAAG,CAACH,GAAG,CAAC;EAChE;EACAI,GAAGA,CAACJ,GAAG,EAAEK,KAAK,EAAE;IACd,IAAI,IAAI,CAACW,KAAK,CAACf,GAAG,CAACD,GAAG,CAAC,EAAE;MACvB,IAAI,CAACe,CAAC,CAACX,GAAG,CAACJ,GAAG,EAAEK,KAAK,CAAC;IACxB,CAAC,MAAM;MACL,IAAI,CAACS,CAAC,CAACV,GAAG,CAACJ,GAAG,EAAEK,KAAK,CAAC;IACxB;IACA,OAAO,IAAI;EACb;EACAJ,GAAGA,CAACD,GAAG,EAAE;IACP,OAAO,IAAI,CAACe,CAAC,CAACd,GAAG,CAACD,GAAG,CAAC,IAAI,IAAI,CAACc,CAAC,CAACb,GAAG,CAACD,GAAG,CAAC;EAC3C;EACAH,IAAIA,CAAA,EAAG;IACL,OAAO,IAAIoB,GAAG,CAAC,CAAC,GAAG,IAAI,CAACH,CAAC,CAACjB,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI,CAACkB,CAAC,CAAClB,IAAI,CAAC,CAAC,CAAC,CAAC,CAACH,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;EACzE;EACAC,OAAOA,CAAA,EAAG;IACR,OAAOU,WAAW,CAAC,IAAI,CAACT,IAAI,CAAC,CAAC,EAAEG,GAAG,IAAI,CAACA,GAAG,EAAE,IAAI,CAACG,GAAG,CAACH,GAAG,CAAC,CAAC,CAAC;EAC9D;EACAO,OAAOA,CAACC,QAAQ,EAAE;IAChB,KAAK,IAAIR,GAAG,IAAI,IAAI,CAACH,IAAI,CAAC,CAAC,EAAE;MAC3BW,QAAQ,CAAC,IAAI,CAACL,GAAG,CAACH,GAAG,CAAC,EAAEA,GAAG,EAAE,IAAI,CAAC;IACpC;EACF;EACAS,MAAMA,CAACT,GAAG,EAAE;IACV,OAAO,IAAI,CAACgB,KAAK,CAACf,GAAG,CAACD,GAAG,CAAC,GAAG,IAAI,CAACe,CAAC,CAACN,MAAM,CAACT,GAAG,CAAC,GAAG,IAAI,CAACc,CAAC,CAACL,MAAM,CAACT,GAAG,CAAC;EACtE;EACAU,KAAKA,CAAA,EAAG;IACN,IAAI,CAACI,CAAC,CAACJ,KAAK,CAAC,CAAC;IACd,IAAI,CAACK,CAAC,CAACL,KAAK,CAAC,CAAC;EAChB;EACA,IAAIC,IAAIA,CAAA,EAAG;IACT,OAAO,CAAC,GAAG,IAAI,CAACd,IAAI,CAAC,CAAC,CAAC,CAACe,MAAM;EAChC;AACF;;AAEA;AACA;AACA;AACA,SAASN,WAAWA,CAACY,EAAE,EAAEV,QAAQ,EAAE;EACjC,OAAO;IACLW,IAAI,EAAEA,CAAA,KAAM;MACV,IAAIC,CAAC,GAAGF,EAAE,CAACC,IAAI,CAAC,CAAC;MACjB,OAAOC,CAAC,CAACC,IAAI,GAAGD,CAAC,GAAG;QAClBf,KAAK,EAAEG,QAAQ,CAACY,CAAC,CAACf,KAAK,CAAC;QACxBgB,IAAI,EAAE;MACR,CAAC;IACH;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAIC,GAAG,CAAC,CAAC;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,WAAW,EAAE;EACrC,IAAI,CAACA,WAAW,EAAE;IAChB,OAAOH,cAAc,CAAC,CAAC;EACzB;EACA,IAAIlC,KAAK,CAACqC,WAAW,CAAC,EAAE;IACtB,OAAOA,WAAW;EACpB;EACA,IAAIpC,QAAQ,CAACoC,WAAW,CAAC,EAAE;IACzB,OAAO,IAAInC,iBAAiB,CAACmC,WAAW,CAAC;EAC3C;EACA,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;AACnE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,GAAG,EAAE;EAC5B,IAAIA,GAAG,YAAYtC,iBAAiB,EAAE;IACpC,OAAOsC,GAAG,CAACnC,aAAa;EAC1B;EACA,IAAID,MAAM,GAAG,CAAC,CAAC;EACf,KAAK,IAAIQ,GAAG,IAAI4B,GAAG,CAAC/B,IAAI,CAAC,CAAC,EAAE;IAC1B,IAAIQ,KAAK,GAAGuB,GAAG,CAACzB,GAAG,CAACH,GAAG,CAAC;IACxBb,eAAe,CAACK,MAAM,EAAEQ,GAAG,EAAEK,KAAK,CAAC;EACrC;EACA,OAAOb,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASqC,MAAMA,CAACD,GAAG,EAAE;EAC1B,KAAK,IAAIE,IAAI,GAAGC,SAAS,CAACnB,MAAM,EAAEoB,OAAO,GAAG,IAAIC,KAAK,CAACH,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;IAC7GF,OAAO,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;EACrC;EACA,KAAK,IAAIC,IAAI,IAAIH,OAAO,EAAE;IACxB,IAAI,CAACG,IAAI,EAAE;MACT;IACF;IACA,IAAI/C,KAAK,CAAC+C,IAAI,CAAC,EAAE;MACf,KAAK,IAAInC,GAAG,IAAImC,IAAI,CAACtC,IAAI,CAAC,CAAC,EAAE;QAC3B+B,GAAG,CAACxB,GAAG,CAACJ,GAAG,EAAEmC,IAAI,CAAChC,GAAG,CAACH,GAAG,CAAC,CAAC;MAC7B;IACF,CAAC,MAAM,IAAIX,QAAQ,CAAC8C,IAAI,CAAC,EAAE;MACzB,KAAK,IAAIC,KAAK,IAAItC,MAAM,CAACD,IAAI,CAACsC,IAAI,CAAC,EAAE;QACnCP,GAAG,CAACxB,GAAG,CAACgC,KAAK,EAAED,IAAI,CAACC,KAAK,CAAC,CAAC;MAC7B;IACF;EACF;EACA,OAAOR,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}