{"ast": null, "code": "export function noBignumber() {\n  throw new Error('No \"bignumber\" implementation available');\n}\nexport function noFraction() {\n  throw new Error('No \"fraction\" implementation available');\n}\nexport function noMatrix() {\n  throw new Error('No \"matrix\" implementation available');\n}\nexport function noIndex() {\n  throw new Error('No \"index\" implementation available');\n}\nexport function noSubset() {\n  throw new Error('No \"matrix\" implementation available');\n}", "map": {"version": 3, "names": ["noBignumber", "Error", "noFraction", "noMatrix", "noIndex", "noSubset"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/noop.js"], "sourcesContent": ["export function noBignumber() {\n  throw new Error('No \"bignumber\" implementation available');\n}\nexport function noFraction() {\n  throw new Error('No \"fraction\" implementation available');\n}\nexport function noMatrix() {\n  throw new Error('No \"matrix\" implementation available');\n}\nexport function noIndex() {\n  throw new Error('No \"index\" implementation available');\n}\nexport function noSubset() {\n  throw new Error('No \"matrix\" implementation available');\n}"], "mappings": "AAAA,OAAO,SAASA,WAAWA,CAAA,EAAG;EAC5B,MAAM,IAAIC,KAAK,CAAC,yCAAyC,CAAC;AAC5D;AACA,OAAO,SAASC,UAAUA,CAAA,EAAG;EAC3B,MAAM,IAAID,KAAK,CAAC,wCAAwC,CAAC;AAC3D;AACA,OAAO,SAASE,QAAQA,CAAA,EAAG;EACzB,MAAM,IAAIF,KAAK,CAAC,sCAAsC,CAAC;AACzD;AACA,OAAO,SAASG,OAAOA,CAAA,EAAG;EACxB,MAAM,IAAIH,KAAK,CAAC,qCAAqC,CAAC;AACxD;AACA,OAAO,SAASI,QAAQA,CAAA,EAAG;EACzB,MAAM,IAAIJ,KAAK,CAAC,sCAAsC,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}