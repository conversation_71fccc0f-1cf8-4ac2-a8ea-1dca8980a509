{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMultiplyScalar } from '../../factoriesAny.js';\nexport var multiplyScalarDependencies = {\n  typedDependencies,\n  createMultiplyScalar\n};", "map": {"version": 3, "names": ["typedDependencies", "createMultiplyScalar", "multiplyScalarDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMultiplyScalar.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMultiplyScalar } from '../../factoriesAny.js';\nexport var multiplyScalarDependencies = {\n  typedDependencies,\n  createMultiplyScalar\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,OAAO,IAAIC,0BAA0B,GAAG;EACtCF,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}