{"ast": null, "code": "export var rowDocs = {\n  name: 'row',\n  category: 'Matrix',\n  syntax: ['row(x, index)'],\n  description: 'Return a row from a matrix or array.',\n  examples: ['A = [[1, 2], [3, 4]]', 'row(A, 1)', 'row(A, 2)'],\n  seealso: ['column', 'matrixFromRows']\n};", "map": {"version": 3, "names": ["rowDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/row.js"], "sourcesContent": ["export var rowDocs = {\n  name: 'row',\n  category: 'Matrix',\n  syntax: ['row(x, index)'],\n  description: 'Return a row from a matrix or array.',\n  examples: ['A = [[1, 2], [3, 4]]', 'row(A, 1)', 'row(A, 2)'],\n  seealso: ['column', 'matrixFromRows']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,eAAe,CAAC;EACzBC,WAAW,EAAE,sCAAsC;EACnDC,QAAQ,EAAE,CAAC,sBAAsB,EAAE,WAAW,EAAE,WAAW,CAAC;EAC5DC,OAAO,EAAE,CAAC,QAAQ,EAAE,gBAAgB;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}