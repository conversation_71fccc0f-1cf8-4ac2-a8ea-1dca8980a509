{"ast": null, "code": "export var lsolveAllDocs = {\n  name: 'lsolveAll',\n  category: 'Algebra',\n  syntax: ['x=lsolveAll(L, b)'],\n  description: 'Finds all solutions of the linear system L * x = b where L is an [n x n] lower triangular matrix and b is a [n] column vector.',\n  examples: ['a = [-2, 3; 2, 1]', 'b = [11, 9]', 'x = lsolve(a, b)'],\n  seealso: ['lsolve', 'lup', 'lusolve', 'usolve', 'matrix', 'sparse']\n};", "map": {"version": 3, "names": ["lsolveAllDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/lsolveAll.js"], "sourcesContent": ["export var lsolveAllDocs = {\n  name: 'lsolveAll',\n  category: 'Algebra',\n  syntax: ['x=lsolveAll(L, b)'],\n  description: 'Finds all solutions of the linear system L * x = b where L is an [n x n] lower triangular matrix and b is a [n] column vector.',\n  examples: ['a = [-2, 3; 2, 1]', 'b = [11, 9]', 'x = lsolve(a, b)'],\n  seealso: ['lsolve', 'lup', 'lusolve', 'usolve', 'matrix', 'sparse']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,mBAAmB,CAAC;EAC7BC,WAAW,EAAE,gIAAgI;EAC7IC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,aAAa,EAAE,kBAAkB,CAAC;EAClEC,OAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;AACpE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}