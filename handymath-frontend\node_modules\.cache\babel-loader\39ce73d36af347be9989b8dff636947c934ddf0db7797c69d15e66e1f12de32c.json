{"ast": null, "code": "export var lupDocs = {\n  name: 'lup',\n  category: 'Algebra',\n  syntax: ['lup(m)'],\n  description: 'Calculate the Matrix LU decomposition with partial pivoting. Matrix A is decomposed in three matrices (L, U, P) where P * A = L * U',\n  examples: ['lup([[2, 1], [1, 4]])', 'lup(matrix([[2, 1], [1, 4]]))', 'lup(sparse([[2, 1], [1, 4]]))'],\n  seealso: ['lusolve', 'lsolve', 'usolve', 'matrix', 'sparse', 'slu', 'qr']\n};", "map": {"version": 3, "names": ["lupDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/lup.js"], "sourcesContent": ["export var lupDocs = {\n  name: 'lup',\n  category: 'Algebra',\n  syntax: ['lup(m)'],\n  description: 'Calculate the Matrix LU decomposition with partial pivoting. Matrix A is decomposed in three matrices (L, U, P) where P * A = L * U',\n  examples: ['lup([[2, 1], [1, 4]])', 'lup(matrix([[2, 1], [1, 4]]))', 'lup(sparse([[2, 1], [1, 4]]))'],\n  seealso: ['lusolve', 'lsolve', 'usolve', 'matrix', 'sparse', 'slu', 'qr']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,qIAAqI;EAClJC,QAAQ,EAAE,CAAC,uBAAuB,EAAE,+BAA+B,EAAE,+BAA+B,CAAC;EACrGC,OAAO,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI;AAC1E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}