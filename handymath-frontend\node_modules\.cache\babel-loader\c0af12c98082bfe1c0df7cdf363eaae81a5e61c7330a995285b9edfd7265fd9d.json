{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo07xSSf';\nvar dependencies = ['typed', 'SparseMatrix'];\nexport var createMatAlgo07xSSf = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    SparseMatrix\n  } = _ref;\n  /**\n  * Iterates over SparseMatrix A and SparseMatrix B items (zero and nonzero) and invokes the callback function f(Aij, Bij).\n  * Callback function invoked MxN times.\n  *\n  * C(i,j) = f(Aij, Bij)\n  *\n  * @param {Matrix}   a                 The SparseMatrix instance (A)\n  * @param {Matrix}   b                 The SparseMatrix instance (B)\n  * @param {Function} callback          The f(Aij,Bij) operation to invoke\n  *\n  * @return {Matrix}                    SparseMatrix (C)\n  *\n  * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97620294\n  */\n  return function matAlgo07xSSf(a, b, callback) {\n    // sparse matrix arrays\n    var asize = a._size;\n    var adt = a._datatype || a._data === undefined ? a._datatype : a.getDataType();\n    var bsize = b._size;\n    var bdt = b._datatype || b._data === undefined ? b._datatype : b.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    var zero = 0;\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      dt = adt;\n      zero = typed.convert(0, dt);\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays for sparse format\n    var cvalues = [];\n    var cindex = [];\n    var cptr = new Array(columns + 1).fill(0); // Start with column pointer array\n\n    // workspaces\n    var xa = [];\n    var xb = [];\n    var wa = [];\n    var wb = [];\n\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      var mark = j + 1;\n      var nonZeroCount = 0;\n      _scatter(a, j, wa, xa, mark);\n      _scatter(b, j, wb, xb, mark);\n\n      // loop rows\n      for (var i = 0; i < rows; i++) {\n        var va = wa[i] === mark ? xa[i] : zero;\n        var vb = wb[i] === mark ? xb[i] : zero;\n\n        // invoke callback\n        var cij = cf(va, vb);\n        // Store all non zero and true values\n        if (cij !== 0 && cij !== false) {\n          cindex.push(i); // row index\n          cvalues.push(cij); // computed value\n          nonZeroCount++;\n        }\n      }\n\n      // Update column pointer with cumulative count of non-zero values\n      cptr[j + 1] = cptr[j] + nonZeroCount;\n    }\n\n    // Return the result as a sparse matrix\n    return new SparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === a._datatype && bdt === b._datatype ? dt : undefined\n    });\n  };\n  function _scatter(m, j, w, x, mark) {\n    // a arrays\n    var values = m._values;\n    var index = m._index;\n    var ptr = m._ptr;\n    // loop values in column j\n    for (var k = ptr[j], k1 = ptr[j + 1]; k < k1; k++) {\n      // row\n      var i = index[k];\n      // update workspace\n      w[i] = mark;\n      x[i] = values[k];\n    }\n  }\n});", "map": {"version": 3, "names": ["factory", "DimensionError", "name", "dependencies", "createMatAlgo07xSSf", "_ref", "typed", "SparseMatrix", "matAlgo07xSSf", "a", "b", "callback", "asize", "_size", "adt", "_datatype", "_data", "undefined", "getDataType", "bsize", "bdt", "length", "RangeError", "rows", "columns", "dt", "zero", "cf", "convert", "find", "cvalues", "cindex", "cptr", "Array", "fill", "xa", "xb", "wa", "wb", "j", "mark", "nonZeroCount", "_scatter", "i", "va", "vb", "cij", "push", "values", "index", "ptr", "size", "datatype", "m", "w", "x", "_values", "_index", "_ptr", "k", "k1"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/utils/matAlgo07xSSf.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo07xSSf';\nvar dependencies = ['typed', 'SparseMatrix'];\nexport var createMatAlgo07xSSf = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    SparseMatrix\n  } = _ref;\n  /**\n  * Iterates over SparseMatrix A and SparseMatrix B items (zero and nonzero) and invokes the callback function f(Aij, Bij).\n  * Callback function invoked MxN times.\n  *\n  * C(i,j) = f(Aij, Bij)\n  *\n  * @param {Matrix}   a                 The SparseMatrix instance (A)\n  * @param {Matrix}   b                 The SparseMatrix instance (B)\n  * @param {Function} callback          The f(Aij,Bij) operation to invoke\n  *\n  * @return {Matrix}                    SparseMatrix (C)\n  *\n  * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97620294\n  */\n  return function matAlgo07xSSf(a, b, callback) {\n    // sparse matrix arrays\n    var asize = a._size;\n    var adt = a._datatype || a._data === undefined ? a._datatype : a.getDataType();\n    var bsize = b._size;\n    var bdt = b._datatype || b._data === undefined ? b._datatype : b.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    var zero = 0;\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      dt = adt;\n      zero = typed.convert(0, dt);\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays for sparse format\n    var cvalues = [];\n    var cindex = [];\n    var cptr = new Array(columns + 1).fill(0); // Start with column pointer array\n\n    // workspaces\n    var xa = [];\n    var xb = [];\n    var wa = [];\n    var wb = [];\n\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      var mark = j + 1;\n      var nonZeroCount = 0;\n      _scatter(a, j, wa, xa, mark);\n      _scatter(b, j, wb, xb, mark);\n\n      // loop rows\n      for (var i = 0; i < rows; i++) {\n        var va = wa[i] === mark ? xa[i] : zero;\n        var vb = wb[i] === mark ? xb[i] : zero;\n\n        // invoke callback\n        var cij = cf(va, vb);\n        // Store all non zero and true values\n        if (cij !== 0 && cij !== false) {\n          cindex.push(i); // row index\n          cvalues.push(cij); // computed value\n          nonZeroCount++;\n        }\n      }\n\n      // Update column pointer with cumulative count of non-zero values\n      cptr[j + 1] = cptr[j] + nonZeroCount;\n    }\n\n    // Return the result as a sparse matrix\n    return new SparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === a._datatype && bdt === b._datatype ? dt : undefined\n    });\n  };\n  function _scatter(m, j, w, x, mark) {\n    // a arrays\n    var values = m._values;\n    var index = m._index;\n    var ptr = m._ptr;\n    // loop values in column j\n    for (var k = ptr[j], k1 = ptr[j + 1]; k < k1; k++) {\n      // row\n      var i = index[k];\n      // update workspace\n      w[i] = mark;\n      x[i] = values[k];\n    }\n  }\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,IAAIC,IAAI,GAAG,eAAe;AAC1B,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,cAAc,CAAC;AAC5C,OAAO,IAAIC,mBAAmB,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAClF,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASG,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAE;IAC5C;IACA,IAAIC,KAAK,GAAGH,CAAC,CAACI,KAAK;IACnB,IAAIC,GAAG,GAAGL,CAAC,CAACM,SAAS,IAAIN,CAAC,CAACO,KAAK,KAAKC,SAAS,GAAGR,CAAC,CAACM,SAAS,GAAGN,CAAC,CAACS,WAAW,CAAC,CAAC;IAC9E,IAAIC,KAAK,GAAGT,CAAC,CAACG,KAAK;IACnB,IAAIO,GAAG,GAAGV,CAAC,CAACK,SAAS,IAAIL,CAAC,CAACM,KAAK,KAAKC,SAAS,GAAGP,CAAC,CAACK,SAAS,GAAGL,CAAC,CAACQ,WAAW,CAAC,CAAC;;IAE9E;IACA,IAAIN,KAAK,CAACS,MAAM,KAAKF,KAAK,CAACE,MAAM,EAAE;MACjC,MAAM,IAAIpB,cAAc,CAACW,KAAK,CAACS,MAAM,EAAEF,KAAK,CAACE,MAAM,CAAC;IACtD;IACA,IAAIT,KAAK,CAAC,CAAC,CAAC,KAAKO,KAAK,CAAC,CAAC,CAAC,IAAIP,KAAK,CAAC,CAAC,CAAC,KAAKO,KAAK,CAAC,CAAC,CAAC,EAAE;MAClD,MAAM,IAAIG,UAAU,CAAC,gCAAgC,GAAGV,KAAK,GAAG,yBAAyB,GAAGO,KAAK,GAAG,GAAG,CAAC;IAC1G;;IAEA;IACA,IAAII,IAAI,GAAGX,KAAK,CAAC,CAAC,CAAC;IACnB,IAAIY,OAAO,GAAGZ,KAAK,CAAC,CAAC,CAAC;;IAEtB;IACA,IAAIa,EAAE;IACN,IAAIC,IAAI,GAAG,CAAC;IACZ,IAAIC,EAAE,GAAGhB,QAAQ;;IAEjB;IACA,IAAI,OAAOG,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAKM,GAAG,IAAIN,GAAG,KAAK,OAAO,EAAE;MAC7DW,EAAE,GAAGX,GAAG;MACRY,IAAI,GAAGpB,KAAK,CAACsB,OAAO,CAAC,CAAC,EAAEH,EAAE,CAAC;MAC3BE,EAAE,GAAGrB,KAAK,CAACuB,IAAI,CAAClB,QAAQ,EAAE,CAACc,EAAE,EAAEA,EAAE,CAAC,CAAC;IACrC;;IAEA;IACA,IAAIK,OAAO,GAAG,EAAE;IAChB,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIC,IAAI,GAAG,IAAIC,KAAK,CAACT,OAAO,GAAG,CAAC,CAAC,CAACU,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE3C;IACA,IAAIC,EAAE,GAAG,EAAE;IACX,IAAIC,EAAE,GAAG,EAAE;IACX,IAAIC,EAAE,GAAG,EAAE;IACX,IAAIC,EAAE,GAAG,EAAE;;IAEX;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,OAAO,EAAEe,CAAC,EAAE,EAAE;MAChC,IAAIC,IAAI,GAAGD,CAAC,GAAG,CAAC;MAChB,IAAIE,YAAY,GAAG,CAAC;MACpBC,QAAQ,CAACjC,CAAC,EAAE8B,CAAC,EAAEF,EAAE,EAAEF,EAAE,EAAEK,IAAI,CAAC;MAC5BE,QAAQ,CAAChC,CAAC,EAAE6B,CAAC,EAAED,EAAE,EAAEF,EAAE,EAAEI,IAAI,CAAC;;MAE5B;MACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,EAAEoB,CAAC,EAAE,EAAE;QAC7B,IAAIC,EAAE,GAAGP,EAAE,CAACM,CAAC,CAAC,KAAKH,IAAI,GAAGL,EAAE,CAACQ,CAAC,CAAC,GAAGjB,IAAI;QACtC,IAAImB,EAAE,GAAGP,EAAE,CAACK,CAAC,CAAC,KAAKH,IAAI,GAAGJ,EAAE,CAACO,CAAC,CAAC,GAAGjB,IAAI;;QAEtC;QACA,IAAIoB,GAAG,GAAGnB,EAAE,CAACiB,EAAE,EAAEC,EAAE,CAAC;QACpB;QACA,IAAIC,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,KAAK,EAAE;UAC9Bf,MAAM,CAACgB,IAAI,CAACJ,CAAC,CAAC,CAAC,CAAC;UAChBb,OAAO,CAACiB,IAAI,CAACD,GAAG,CAAC,CAAC,CAAC;UACnBL,YAAY,EAAE;QAChB;MACF;;MAEA;MACAT,IAAI,CAACO,CAAC,GAAG,CAAC,CAAC,GAAGP,IAAI,CAACO,CAAC,CAAC,GAAGE,YAAY;IACtC;;IAEA;IACA,OAAO,IAAIlC,YAAY,CAAC;MACtByC,MAAM,EAAElB,OAAO;MACfmB,KAAK,EAAElB,MAAM;MACbmB,GAAG,EAAElB,IAAI;MACTmB,IAAI,EAAE,CAAC5B,IAAI,EAAEC,OAAO,CAAC;MACrB4B,QAAQ,EAAEtC,GAAG,KAAKL,CAAC,CAACM,SAAS,IAAIK,GAAG,KAAKV,CAAC,CAACK,SAAS,GAAGU,EAAE,GAAGR;IAC9D,CAAC,CAAC;EACJ,CAAC;EACD,SAASyB,QAAQA,CAACW,CAAC,EAAEd,CAAC,EAAEe,CAAC,EAAEC,CAAC,EAAEf,IAAI,EAAE;IAClC;IACA,IAAIQ,MAAM,GAAGK,CAAC,CAACG,OAAO;IACtB,IAAIP,KAAK,GAAGI,CAAC,CAACI,MAAM;IACpB,IAAIP,GAAG,GAAGG,CAAC,CAACK,IAAI;IAChB;IACA,KAAK,IAAIC,CAAC,GAAGT,GAAG,CAACX,CAAC,CAAC,EAAEqB,EAAE,GAAGV,GAAG,CAACX,CAAC,GAAG,CAAC,CAAC,EAAEoB,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAE,EAAE;MACjD;MACA,IAAIhB,CAAC,GAAGM,KAAK,CAACU,CAAC,CAAC;MAChB;MACAL,CAAC,CAACX,CAAC,CAAC,GAAGH,IAAI;MACXe,CAAC,CAACZ,CAAC,CAAC,GAAGK,MAAM,CAACW,CAAC,CAAC;IAClB;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}