{"ast": null, "code": "export var asinhDocs = {\n  name: 'asinh',\n  category: 'Trigonometry',\n  syntax: ['asinh(x)'],\n  description: 'Calculate the hyperbolic arcsine of a value, defined as `asinh(x) = ln(x + sqrt(x^2 + 1))`.',\n  examples: ['asinh(0.5)'],\n  seealso: ['acosh', 'atanh']\n};", "map": {"version": 3, "names": ["asinhDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/asinh.js"], "sourcesContent": ["export var asinhDocs = {\n  name: 'asinh',\n  category: 'Trigonometry',\n  syntax: ['asinh(x)'],\n  description: 'Calculate the hyperbolic arcsine of a value, defined as `asinh(x) = ln(x + sqrt(x^2 + 1))`.',\n  examples: ['asinh(0.5)'],\n  seealso: ['acosh', 'atanh']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,6FAA6F;EAC1GC,QAAQ,EAAE,CAAC,YAAY,CAAC;EACxBC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}