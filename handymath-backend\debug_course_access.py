#!/usr/bin/env python3
"""
Script pour déboguer l'accès aux cours
"""

import os
import sys
import django
import requests
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from api.models import Course, CourseEnrollment
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

def debug_course_access():
    """Déboguer l'accès aux cours"""
    print("🔍 Débogage de l'accès aux cours\n")
    
    # Obtenir l'utilisateur lamiae
    try:
        user = User.objects.get(username='lamiae')
        print(f"✅ Utilisateur trouvé: {user.username} (ID: {user.id}, Role: {user.role})")
    except User.DoesNotExist:
        print("❌ Utilisateur 'lamiae' non trouvé")
        return
    
    # G<PERSON>érer un token
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    # Tester l'endpoint de liste des cours
    print(f"\n📚 Test de l'endpoint de liste des cours:")
    try:
        response = requests.get(f'{base_url}/courses/', headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            courses = data.get('courses', [])
            print(f"✅ {len(courses)} cours trouvés")
            
            # Afficher les premiers cours avec leurs IDs
            print(f"\n📋 Premiers cours disponibles:")
            for i, course in enumerate(courses[:5], 1):
                print(f"   {i}. ID: {course['id']} - {course['title']}")
                
                # Tester l'accès à ce cours spécifique
                print(f"      🧪 Test d'accès au cours ID {course['id']}:")
                try:
                    detail_response = requests.get(f"{base_url}/courses/{course['id']}/", headers=headers)
                    if detail_response.status_code == 200:
                        detail_data = detail_response.json()
                        print(f"      ✅ Accès réussi - Titre: {detail_data.get('title')}")
                        print(f"      📊 Chapitres: {len(detail_data.get('chapters', []))}")
                        print(f"      📈 Progression: {detail_data.get('progress_percentage')}%")
                    elif detail_response.status_code == 404:
                        print(f"      ❌ 404 - Cours non trouvé")
                        print(f"      Réponse: {detail_response.text}")
                    else:
                        print(f"      ⚠️ {detail_response.status_code} - {detail_response.text}")
                except Exception as e:
                    print(f"      ❌ Erreur: {e}")
                print()
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Réponse: {response.text}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    # Vérifier directement dans la base de données
    print(f"\n🗄️ Vérification directe dans la base de données:")
    courses = Course.objects.filter(status='published').order_by('id')
    print(f"Cours publiés: {courses.count()}")
    
    for course in courses[:10]:
        print(f"   ID: {course.id} - {course.title}")
        print(f"      Statut: {course.status}")
        print(f"      Chapitres: {course.chapters.count()}")
        print(f"      Créé: {course.created_at}")
        print()

def test_specific_course_ids():
    """Tester des IDs de cours spécifiques"""
    print(f"\n🎯 Test d'IDs de cours spécifiques:")
    
    user = User.objects.get(username='lamiae')
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    # Tester les IDs 1, 2, 3, 4, 5
    test_ids = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    
    for course_id in test_ids:
        print(f"🧪 Test cours ID {course_id}:")
        try:
            response = requests.get(f"{base_url}/courses/{course_id}/", headers=headers)
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Succès - {data.get('title')}")
            elif response.status_code == 404:
                print(f"   ❌ 404 - Cours non trouvé")
            else:
                print(f"   ⚠️ {response.status_code} - {response.text[:100]}")
        except Exception as e:
            print(f"   ❌ Erreur: {e}")

if __name__ == "__main__":
    debug_course_access()
    test_specific_course_ids()
