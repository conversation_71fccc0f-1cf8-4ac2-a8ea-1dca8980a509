{"ast": null, "code": "export var maxDocs = {\n  name: 'max',\n  category: 'Statistics',\n  syntax: ['max(a, b, c, ...)', 'max(A)', 'max(A, dimension)'],\n  description: 'Compute the maximum value of a list of values. If any NaN values are found, the function yields the last NaN in the input.',\n  examples: ['max(2, 3, 4, 1)', 'max([2, 3, 4, 1])', 'max([2, 5; 4, 3])', 'max([2, 5; 4, 3], 1)', 'max([2, 5; 4, 3], 2)', 'max(2.7, 7.1, -4.5, 2.0, 4.1)', 'min(2.7, 7.1, -4.5, 2.0, 4.1)'],\n  seealso: ['mean', 'median', 'min', 'prod', 'std', 'sum', 'variance']\n};", "map": {"version": 3, "names": ["maxDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/statistics/max.js"], "sourcesContent": ["export var maxDocs = {\n  name: 'max',\n  category: 'Statistics',\n  syntax: ['max(a, b, c, ...)', 'max(A)', 'max(A, dimension)'],\n  description: 'Compute the maximum value of a list of values. If any NaN values are found, the function yields the last NaN in the input.',\n  examples: ['max(2, 3, 4, 1)', 'max([2, 3, 4, 1])', 'max([2, 5; 4, 3])', 'max([2, 5; 4, 3], 1)', 'max([2, 5; 4, 3], 2)', 'max(2.7, 7.1, -4.5, 2.0, 4.1)', 'min(2.7, 7.1, -4.5, 2.0, 4.1)'],\n  seealso: ['mean', 'median', 'min', 'prod', 'std', 'sum', 'variance']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,mBAAmB,CAAC;EAC5DC,WAAW,EAAE,4HAA4H;EACzIC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,+BAA+B,EAAE,+BAA+B,CAAC;EACzLC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU;AACrE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}