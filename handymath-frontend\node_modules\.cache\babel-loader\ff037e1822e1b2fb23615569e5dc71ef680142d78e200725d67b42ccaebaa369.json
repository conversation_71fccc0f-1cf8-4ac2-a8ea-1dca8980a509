{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { isIntegerDependencies } from './dependenciesIsInteger.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMapSlicesTransform } from '../../factoriesAny.js';\nexport var mapSlicesTransformDependencies = {\n  isIntegerDependencies,\n  typedDependencies,\n  createMapSlicesTransform\n};", "map": {"version": 3, "names": ["isIntegerDependencies", "typedDependencies", "createMapSlicesTransform", "mapSlicesTransformDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMapSlicesTransform.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { isIntegerDependencies } from './dependenciesIsInteger.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMapSlicesTransform } from '../../factoriesAny.js';\nexport var mapSlicesTransformDependencies = {\n  isIntegerDependencies,\n  typedDependencies,\n  createMapSlicesTransform\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,OAAO,IAAIC,8BAA8B,GAAG;EAC1CH,qBAAqB;EACrBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}