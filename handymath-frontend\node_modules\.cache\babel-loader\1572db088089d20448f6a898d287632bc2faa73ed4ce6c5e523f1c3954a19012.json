{"ast": null, "code": "export var divideDocs = {\n  name: 'divide',\n  category: 'Operators',\n  syntax: ['x / y', 'divide(x, y)'],\n  description: 'Divide two values.',\n  examples: ['a = 2 / 3', 'a * 3', '4.5 / 2', '3 + 4 / 2', '(3 + 4) / 2', '18 km / 4.5'],\n  seealso: ['multiply']\n};", "map": {"version": 3, "names": ["divideDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/divide.js"], "sourcesContent": ["export var divideDocs = {\n  name: 'divide',\n  category: 'Operators',\n  syntax: ['x / y', 'divide(x, y)'],\n  description: 'Divide two values.',\n  examples: ['a = 2 / 3', 'a * 3', '4.5 / 2', '3 + 4 / 2', '(3 + 4) / 2', '18 km / 4.5'],\n  seealso: ['multiply']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,OAAO,EAAE,cAAc,CAAC;EACjCC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,CAAC;EACtFC,OAAO,EAAE,CAAC,UAAU;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}