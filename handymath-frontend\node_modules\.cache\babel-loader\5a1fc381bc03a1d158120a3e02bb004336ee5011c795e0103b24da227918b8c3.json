{"ast": null, "code": "export var conjDocs = {\n  name: 'conj',\n  category: 'Complex',\n  syntax: ['conj(x)'],\n  description: 'Compute the complex conjugate of a complex value. If x = a+bi, the complex conjugate is a-bi.',\n  examples: ['conj(2 + 3i)', 'conj(2 - 3i)', 'conj(-5.2i)'],\n  seealso: ['re', 'im', 'abs', 'arg']\n};", "map": {"version": 3, "names": ["conjD<PERSON>s", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/complex/conj.js"], "sourcesContent": ["export var conjDocs = {\n  name: 'conj',\n  category: 'Complex',\n  syntax: ['conj(x)'],\n  description: 'Compute the complex conjugate of a complex value. If x = a+bi, the complex conjugate is a-bi.',\n  examples: ['conj(2 + 3i)', 'conj(2 - 3i)', 'conj(-5.2i)'],\n  seealso: ['re', 'im', 'abs', 'arg']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,+FAA+F;EAC5GC,QAAQ,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,aAAa,CAAC;EACzDC,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}