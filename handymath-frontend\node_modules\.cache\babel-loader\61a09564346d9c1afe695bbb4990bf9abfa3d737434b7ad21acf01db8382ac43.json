{"ast": null, "code": "export var transposeDocs = {\n  name: 'transpose',\n  category: 'Matrix',\n  syntax: ['x\\'', 'transpose(x)'],\n  description: 'Transpose a matrix',\n  examples: ['a = [1, 2, 3; 4, 5, 6]', 'a\\'', 'transpose(a)'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'zeros']\n};", "map": {"version": 3, "names": ["transposeDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/transpose.js"], "sourcesContent": ["export var transposeDocs = {\n  name: 'transpose',\n  category: 'Matrix',\n  syntax: ['x\\'', 'transpose(x)'],\n  description: 'Transpose a matrix',\n  examples: ['a = [1, 2, 3; 4, 5, 6]', 'a\\'', 'transpose(a)'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,KAAK,EAAE,cAAc,CAAC;EAC/BC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE,CAAC,wBAAwB,EAAE,KAAK,EAAE,cAAc,CAAC;EAC3DC,OAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO;AACtH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}