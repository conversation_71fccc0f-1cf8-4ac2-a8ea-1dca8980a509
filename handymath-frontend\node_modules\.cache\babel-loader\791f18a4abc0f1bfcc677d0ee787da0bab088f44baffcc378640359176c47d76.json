{"ast": null, "code": "// A Javascript implementaion of the \"Tyche-i\" prng algorithm by\n// <PERSON> and <PERSON><PERSON><PERSON>.\n// See https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n\n(function (global, module, define) {\n  function XorGen(seed) {\n    var me = this,\n      strseed = '';\n\n    // Set up generator function.\n    me.next = function () {\n      var b = me.b,\n        c = me.c,\n        d = me.d,\n        a = me.a;\n      b = b << 25 ^ b >>> 7 ^ c;\n      c = c - d | 0;\n      d = d << 24 ^ d >>> 8 ^ a;\n      a = a - b | 0;\n      me.b = b = b << 20 ^ b >>> 12 ^ c;\n      me.c = c = c - d | 0;\n      me.d = d << 16 ^ c >>> 16 ^ a;\n      return me.a = a - b | 0;\n    };\n\n    /* The following is non-inverted tyche, which has better internal\n     * bit diffusion, but which is about 25% slower than tyche-i in JS.\n    me.next = function() {\n      var a = me.a, b = me.b, c = me.c, d = me.d;\n      a = (me.a + me.b | 0) >>> 0;\n      d = me.d ^ a; d = d << 16 ^ d >>> 16;\n      c = me.c + d | 0;\n      b = me.b ^ c; b = b << 12 ^ d >>> 20;\n      me.a = a = a + b | 0;\n      d = d ^ a; me.d = d = d << 8 ^ d >>> 24;\n      me.c = c = c + d | 0;\n      b = b ^ c;\n      return me.b = (b << 7 ^ b >>> 25);\n    }\n    */\n\n    me.a = 0;\n    me.b = 0;\n    me.c = 2654435769 | 0;\n    me.d = 1367130551;\n    if (seed === Math.floor(seed)) {\n      // Integer seed.\n      me.a = seed / 0x100000000 | 0;\n      me.b = seed | 0;\n    } else {\n      // String seed.\n      strseed += seed;\n    }\n\n    // Mix in string seed, then discard an initial batch of 64 values.\n    for (var k = 0; k < strseed.length + 20; k++) {\n      me.b ^= strseed.charCodeAt(k) | 0;\n      me.next();\n    }\n  }\n  function copy(f, t) {\n    t.a = f.a;\n    t.b = f.b;\n    t.c = f.c;\n    t.d = f.d;\n    return t;\n  }\n  ;\n  function impl(seed, opts) {\n    var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function () {\n        return (xg.next() >>> 0) / 0x100000000;\n      };\n    prng.double = function () {\n      do {\n        var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n      } while (result === 0);\n      return result;\n    };\n    prng.int32 = xg.next;\n    prng.quick = prng;\n    if (state) {\n      if (typeof state == 'object') copy(state, xg);\n      prng.state = function () {\n        return copy(xg, {});\n      };\n    }\n    return prng;\n  }\n  if (module && module.exports) {\n    module.exports = impl;\n  } else if (define && define.amd) {\n    define(function () {\n      return impl;\n    });\n  } else {\n    this.tychei = impl;\n  }\n})(this, typeof module == 'object' && module,\n// present in node.js\ntypeof define == 'function' && define // present with an AMD loader\n);", "map": {"version": 3, "names": ["global", "module", "define", "XorGen", "seed", "me", "strseed", "next", "b", "c", "d", "a", "Math", "floor", "k", "length", "charCodeAt", "copy", "f", "t", "impl", "opts", "xg", "state", "prng", "double", "top", "bot", "result", "int32", "quick", "exports", "amd", "tychei"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/seedrandom/lib/tychei.js"], "sourcesContent": ["// A Javascript implementaion of the \"Tyche-i\" prng algorithm by\n// <PERSON> and <PERSON><PERSON><PERSON>.\n// See https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var b = me.b, c = me.c, d = me.d, a = me.a;\n    b = (b << 25) ^ (b >>> 7) ^ c;\n    c = (c - d) | 0;\n    d = (d << 24) ^ (d >>> 8) ^ a;\n    a = (a - b) | 0;\n    me.b = b = (b << 20) ^ (b >>> 12) ^ c;\n    me.c = c = (c - d) | 0;\n    me.d = (d << 16) ^ (c >>> 16) ^ a;\n    return me.a = (a - b) | 0;\n  };\n\n  /* The following is non-inverted tyche, which has better internal\n   * bit diffusion, but which is about 25% slower than tyche-i in JS.\n  me.next = function() {\n    var a = me.a, b = me.b, c = me.c, d = me.d;\n    a = (me.a + me.b | 0) >>> 0;\n    d = me.d ^ a; d = d << 16 ^ d >>> 16;\n    c = me.c + d | 0;\n    b = me.b ^ c; b = b << 12 ^ d >>> 20;\n    me.a = a = a + b | 0;\n    d = d ^ a; me.d = d = d << 8 ^ d >>> 24;\n    me.c = c = c + d | 0;\n    b = b ^ c;\n    return me.b = (b << 7 ^ b >>> 25);\n  }\n  */\n\n  me.a = 0;\n  me.b = 0;\n  me.c = 2654435769 | 0;\n  me.d = 1367130551;\n\n  if (seed === Math.floor(seed)) {\n    // Integer seed.\n    me.a = (seed / 0x100000000) | 0;\n    me.b = seed | 0;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 20; k++) {\n    me.b ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.a = f.a;\n  t.b = f.b;\n  t.c = f.c;\n  t.d = f.d;\n  return t;\n};\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.tychei = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n"], "mappings": "AAAA;AACA;AACA;;AAEA,CAAC,UAASA,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAElC,SAASC,MAAMA,CAACC,IAAI,EAAE;IACpB,IAAIC,EAAE,GAAG,IAAI;MAAEC,OAAO,GAAG,EAAE;;IAE3B;IACAD,EAAE,CAACE,IAAI,GAAG,YAAW;MACnB,IAAIC,CAAC,GAAGH,EAAE,CAACG,CAAC;QAAEC,CAAC,GAAGJ,EAAE,CAACI,CAAC;QAAEC,CAAC,GAAGL,EAAE,CAACK,CAAC;QAAEC,CAAC,GAAGN,EAAE,CAACM,CAAC;MAC1CH,CAAC,GAAIA,CAAC,IAAI,EAAE,GAAKA,CAAC,KAAK,CAAE,GAAGC,CAAC;MAC7BA,CAAC,GAAIA,CAAC,GAAGC,CAAC,GAAI,CAAC;MACfA,CAAC,GAAIA,CAAC,IAAI,EAAE,GAAKA,CAAC,KAAK,CAAE,GAAGC,CAAC;MAC7BA,CAAC,GAAIA,CAAC,GAAGH,CAAC,GAAI,CAAC;MACfH,EAAE,CAACG,CAAC,GAAGA,CAAC,GAAIA,CAAC,IAAI,EAAE,GAAKA,CAAC,KAAK,EAAG,GAAGC,CAAC;MACrCJ,EAAE,CAACI,CAAC,GAAGA,CAAC,GAAIA,CAAC,GAAGC,CAAC,GAAI,CAAC;MACtBL,EAAE,CAACK,CAAC,GAAIA,CAAC,IAAI,EAAE,GAAKD,CAAC,KAAK,EAAG,GAAGE,CAAC;MACjC,OAAON,EAAE,CAACM,CAAC,GAAIA,CAAC,GAAGH,CAAC,GAAI,CAAC;IAC3B,CAAC;;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEEH,EAAE,CAACM,CAAC,GAAG,CAAC;IACRN,EAAE,CAACG,CAAC,GAAG,CAAC;IACRH,EAAE,CAACI,CAAC,GAAG,UAAU,GAAG,CAAC;IACrBJ,EAAE,CAACK,CAAC,GAAG,UAAU;IAEjB,IAAIN,IAAI,KAAKQ,IAAI,CAACC,KAAK,CAACT,IAAI,CAAC,EAAE;MAC7B;MACAC,EAAE,CAACM,CAAC,GAAIP,IAAI,GAAG,WAAW,GAAI,CAAC;MAC/BC,EAAE,CAACG,CAAC,GAAGJ,IAAI,GAAG,CAAC;IACjB,CAAC,MAAM;MACL;MACAE,OAAO,IAAIF,IAAI;IACjB;;IAEA;IACA,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,OAAO,CAACS,MAAM,GAAG,EAAE,EAAED,CAAC,EAAE,EAAE;MAC5CT,EAAE,CAACG,CAAC,IAAIF,OAAO,CAACU,UAAU,CAACF,CAAC,CAAC,GAAG,CAAC;MACjCT,EAAE,CAACE,IAAI,CAAC,CAAC;IACX;EACF;EAEA,SAASU,IAAIA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAClBA,CAAC,CAACR,CAAC,GAAGO,CAAC,CAACP,CAAC;IACTQ,CAAC,CAACX,CAAC,GAAGU,CAAC,CAACV,CAAC;IACTW,CAAC,CAACV,CAAC,GAAGS,CAAC,CAACT,CAAC;IACTU,CAAC,CAACT,CAAC,GAAGQ,CAAC,CAACR,CAAC;IACT,OAAOS,CAAC;EACV;EAAC;EAED,SAASC,IAAIA,CAAChB,IAAI,EAAEiB,IAAI,EAAE;IACxB,IAAIC,EAAE,GAAG,IAAInB,MAAM,CAACC,IAAI,CAAC;MACrBmB,KAAK,GAAGF,IAAI,IAAIA,IAAI,CAACE,KAAK;MAC1BC,IAAI,GAAG,SAAAA,CAAA,EAAW;QAAE,OAAO,CAACF,EAAE,CAACf,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,WAAW;MAAE,CAAC;IACjEiB,IAAI,CAACC,MAAM,GAAG,YAAW;MACvB,GAAG;QACD,IAAIC,GAAG,GAAGJ,EAAE,CAACf,IAAI,CAAC,CAAC,KAAK,EAAE;UACtBoB,GAAG,GAAG,CAACL,EAAE,CAACf,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,WAAW;UACrCqB,MAAM,GAAG,CAACF,GAAG,GAAGC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;MACtC,CAAC,QAAQC,MAAM,KAAK,CAAC;MACrB,OAAOA,MAAM;IACf,CAAC;IACDJ,IAAI,CAACK,KAAK,GAAGP,EAAE,CAACf,IAAI;IACpBiB,IAAI,CAACM,KAAK,GAAGN,IAAI;IACjB,IAAID,KAAK,EAAE;MACT,IAAI,OAAOA,KAAM,IAAI,QAAQ,EAAEN,IAAI,CAACM,KAAK,EAAED,EAAE,CAAC;MAC9CE,IAAI,CAACD,KAAK,GAAG,YAAW;QAAE,OAAON,IAAI,CAACK,EAAE,EAAE,CAAC,CAAC,CAAC;MAAE,CAAC;IAClD;IACA,OAAOE,IAAI;EACb;EAEA,IAAIvB,MAAM,IAAIA,MAAM,CAAC8B,OAAO,EAAE;IAC5B9B,MAAM,CAAC8B,OAAO,GAAGX,IAAI;EACvB,CAAC,MAAM,IAAIlB,MAAM,IAAIA,MAAM,CAAC8B,GAAG,EAAE;IAC/B9B,MAAM,CAAC,YAAW;MAAE,OAAOkB,IAAI;IAAE,CAAC,CAAC;EACrC,CAAC,MAAM;IACL,IAAI,CAACa,MAAM,GAAGb,IAAI;EACpB;AAEA,CAAC,EACC,IAAI,EACH,OAAOnB,MAAM,IAAK,QAAQ,IAAIA,MAAM;AAAK;AACzC,OAAOC,MAAM,IAAK,UAAU,IAAIA,MAAM,CAAG;AAC5C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}