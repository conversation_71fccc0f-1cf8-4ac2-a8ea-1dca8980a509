{"ast": null, "code": "export var bitNotDocs = {\n  name: 'bitNot',\n  category: 'Bitwise',\n  syntax: ['~x', 'bitNot(x)'],\n  description: 'Bitwise NOT operation. Performs a logical negation on each bit of the given value. Bits that are 0 become 1, and those that are 1 become 0.',\n  examples: ['~1', '~2', 'bitNot([2, -3, 4])'],\n  seealso: ['bitAnd', 'bitOr', 'bitXor', 'leftShift', 'rightArithShift', 'rightLogShift']\n};", "map": {"version": 3, "names": ["bitNotDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/bitwise/bitNot.js"], "sourcesContent": ["export var bitNotDocs = {\n  name: 'bitNot',\n  category: 'Bitwise',\n  syntax: ['~x', 'bitNot(x)'],\n  description: 'Bitwise NOT operation. Performs a logical negation on each bit of the given value. Bits that are 0 become 1, and those that are 1 become 0.',\n  examples: ['~1', '~2', 'bitNot([2, -3, 4])'],\n  seealso: ['bitAnd', 'bitOr', 'bitXor', 'leftShift', 'rightArithShift', 'rightLogShift']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;EAC3BC,WAAW,EAAE,6IAA6I;EAC1JC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,oBAAoB,CAAC;EAC5CC,OAAO,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,iBAAiB,EAAE,eAAe;AACxF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}