{"ast": null, "code": "export var gammaDocs = {\n  name: 'gamma',\n  category: 'Probability',\n  syntax: ['gamma(n)'],\n  description: 'Compute the gamma function. For small values, the <PERSON><PERSON><PERSON><PERSON> approximation is used, and for large values the extended <PERSON> approximation.',\n  examples: ['gamma(4)', '3!', 'gamma(1/2)', 'sqrt(pi)'],\n  seealso: ['factorial']\n};", "map": {"version": 3, "names": ["gammaDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/probability/gamma.js"], "sourcesContent": ["export var gammaDocs = {\n  name: 'gamma',\n  category: 'Probability',\n  syntax: ['gamma(n)'],\n  description: 'Compute the gamma function. For small values, the <PERSON><PERSON><PERSON><PERSON> approximation is used, and for large values the extended <PERSON> approximation.',\n  examples: ['gamma(4)', '3!', 'gamma(1/2)', 'sqrt(pi)'],\n  seealso: ['factorial']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,4IAA4I;EACzJC,QAAQ,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,CAAC;EACtDC,OAAO,EAAE,CAAC,WAAW;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}