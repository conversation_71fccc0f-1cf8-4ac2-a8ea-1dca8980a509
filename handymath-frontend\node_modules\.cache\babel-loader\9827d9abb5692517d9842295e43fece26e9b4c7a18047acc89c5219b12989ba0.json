{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csFlip } from './csFlip.js';\n\n/**\n * Flips the value if it is negative of returns the same value otherwise.\n *\n * @param {Number}  i               The value to flip\n */\nexport function csUnflip(i) {\n  // flip the value if it is negative\n  return i < 0 ? csFlip(i) : i;\n}", "map": {"version": 3, "names": ["csFlip", "csUnflip", "i"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csUnflip.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csFlip } from './csFlip.js';\n\n/**\n * Flips the value if it is negative of returns the same value otherwise.\n *\n * @param {Number}  i               The value to flip\n */\nexport function csUnflip(i) {\n  // flip the value if it is negative\n  return i < 0 ? csFlip(i) : i;\n}"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,CAAC,EAAE;EAC1B;EACA,OAAOA,CAAC,GAAG,CAAC,GAAGF,MAAM,CAACE,CAAC,CAAC,GAAGA,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}