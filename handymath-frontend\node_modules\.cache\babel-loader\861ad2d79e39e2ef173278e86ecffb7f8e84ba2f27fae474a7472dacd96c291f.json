{"ast": null, "code": "import typed from 'typed-function';\nimport { get, arraySize } from './array.js';\nimport { typeOf as _typeOf } from './is.js';\n\n/**\n * Simplifies a callback function by reducing its complexity and potentially improving its performance.\n *\n * @param {Function} callback The original callback function to simplify.\n * @param {Array|Matrix} array The array that will be used with the callback function.\n * @param {string} name The name of the function that is using the callback.\n * @param {boolean} [isUnary=false] If true, the callback function is unary and will be optimized as such.\n * @returns {Function} Returns a simplified version of the callback function.\n */\nexport function optimizeCallback(callback, array, name) {\n  var isUnary = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if (typed.isTypedFunction(callback)) {\n    var numberOfArguments;\n    if (isUnary) {\n      numberOfArguments = 1;\n    } else {\n      var firstIndex = (array.isMatrix ? array.size() : arraySize(array)).map(() => 0);\n      var firstValue = array.isMatrix ? array.get(firstIndex) : get(array, firstIndex);\n      numberOfArguments = _findNumberOfArgumentsTyped(callback, firstValue, firstIndex, array);\n    }\n    var fastCallback;\n    if (array.isMatrix && array.dataType !== 'mixed' && array.dataType !== undefined) {\n      var singleSignature = _findSingleSignatureWithArity(callback, numberOfArguments);\n      fastCallback = singleSignature !== undefined ? singleSignature : callback;\n    } else {\n      fastCallback = callback;\n    }\n    if (numberOfArguments >= 1 && numberOfArguments <= 3) {\n      return {\n        isUnary: numberOfArguments === 1,\n        fn: function fn() {\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n          return _tryFunctionWithArgs(fastCallback, args.slice(0, numberOfArguments), name, callback.name);\n        }\n      };\n    }\n    return {\n      isUnary: false,\n      fn: function fn() {\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n        return _tryFunctionWithArgs(fastCallback, args, name, callback.name);\n      }\n    };\n  }\n  if (isUnary === undefined) {\n    return {\n      isUnary: _findIfCallbackIsUnary(callback),\n      fn: callback\n    };\n  } else {\n    return {\n      isUnary,\n      fn: callback\n    };\n  }\n}\nfunction _findSingleSignatureWithArity(callback, arity) {\n  var matchingFunctions = [];\n  Object.entries(callback.signatures).forEach(_ref => {\n    var [signature, func] = _ref;\n    if (signature.split(',').length === arity) {\n      matchingFunctions.push(func);\n    }\n  });\n  if (matchingFunctions.length === 1) {\n    return matchingFunctions[0];\n  }\n}\n\n/**\n * Determines if a given callback function is unary (i.e., takes exactly one argument).\n *\n * This function checks the following conditions to determine if the callback is unary:\n * 1. The callback function should have exactly one parameter.\n * 2. The callback function should not use the `arguments` object.\n * 3. The callback function should not use rest parameters (`...`).\n * If in doubt, this function shall return `false` to be safe\n *\n * @param {Function} callback - The callback function to be checked.\n * @returns {boolean} - Returns `true` if the callback is unary, otherwise `false`.\n */\nfunction _findIfCallbackIsUnary(callback) {\n  if (callback.length !== 1) return false;\n  var callbackStr = callback.toString();\n  // Check if the callback function uses `arguments`\n  if (/arguments/.test(callbackStr)) return false;\n\n  // Extract the parameters of the callback function\n  var paramsStr = callbackStr.match(/\\(.*?\\)/);\n  // Check if the callback function uses rest parameters\n  if (/\\.\\.\\./.test(paramsStr)) return false;\n  return true;\n}\nfunction _findNumberOfArgumentsTyped(callback, value, index, array) {\n  var testArgs = [value, index, array];\n  for (var i = 3; i > 0; i--) {\n    var args = testArgs.slice(0, i);\n    if (typed.resolve(callback, args) !== null) {\n      return i;\n    }\n  }\n}\n\n/**\n   * @param {function} func The selected function taken from one of the signatures of the callback function\n   * @param {Array} args List with arguments to apply to the selected signature\n   * @param {string} mappingFnName the name of the function that is using the callback\n   * @param {string} callbackName the name of the callback function\n   * @returns {*} Returns the return value of the invoked signature\n   * @throws {TypeError} Throws an error when no matching signature was found\n   */\nfunction _tryFunctionWithArgs(func, args, mappingFnName, callbackName) {\n  try {\n    return func(...args);\n  } catch (err) {\n    _createCallbackError(err, args, mappingFnName, callbackName);\n  }\n}\n\n/**\n * Creates and throws a detailed TypeError when a callback function fails.\n *\n * @param {Error} err The original error thrown by the callback function.\n * @param {Array} args The arguments that were passed to the callback function.\n * @param {string} mappingFnName The name of the function that is using the callback.\n * @param {string} callbackName The name of the callback function.\n * @throws {TypeError} Throws a detailed TypeError with enriched error message.\n */\nfunction _createCallbackError(err, args, mappingFnName, callbackName) {\n  var _err$data;\n  // Enrich the error message so the user understands that it took place inside the callback function\n  if (err instanceof TypeError && ((_err$data = err.data) === null || _err$data === void 0 ? void 0 : _err$data.category) === 'wrongType') {\n    var argsDesc = [];\n    argsDesc.push(\"value: \".concat(_typeOf(args[0])));\n    if (args.length >= 2) {\n      argsDesc.push(\"index: \".concat(_typeOf(args[1])));\n    }\n    if (args.length >= 3) {\n      argsDesc.push(\"array: \".concat(_typeOf(args[2])));\n    }\n    throw new TypeError(\"Function \".concat(mappingFnName, \" cannot apply callback arguments \") + \"\".concat(callbackName, \"(\").concat(argsDesc.join(', '), \") at index \").concat(JSON.stringify(args[1])));\n  } else {\n    throw new TypeError(\"Function \".concat(mappingFnName, \" cannot apply callback arguments \") + \"to function \".concat(callbackName, \": \").concat(err.message));\n  }\n}", "map": {"version": 3, "names": ["typed", "get", "arraySize", "typeOf", "_typeOf", "optimizeCallback", "callback", "array", "name", "isUnary", "arguments", "length", "undefined", "isTypedFunction", "numberOfArguments", "firstIndex", "isMatrix", "size", "map", "firstValue", "_findNumberOfArgumentsTyped", "fastCallback", "dataType", "singleSignature", "_findSingleSignatureWithArity", "fn", "_len", "args", "Array", "_key", "_tryFunctionWithArgs", "slice", "_len2", "_key2", "_findIfCallbackIsUnary", "arity", "matchingFunctions", "Object", "entries", "signatures", "for<PERSON>ach", "_ref", "signature", "func", "split", "push", "callbackStr", "toString", "test", "paramsStr", "match", "value", "index", "testArgs", "i", "resolve", "mappingFnName", "callback<PERSON><PERSON>", "err", "_createCallbackError", "_err$data", "TypeError", "data", "category", "argsDesc", "concat", "join", "JSON", "stringify", "message"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/optimizeCallback.js"], "sourcesContent": ["import typed from 'typed-function';\nimport { get, arraySize } from './array.js';\nimport { typeOf as _typeOf } from './is.js';\n\n/**\n * Simplifies a callback function by reducing its complexity and potentially improving its performance.\n *\n * @param {Function} callback The original callback function to simplify.\n * @param {Array|Matrix} array The array that will be used with the callback function.\n * @param {string} name The name of the function that is using the callback.\n * @param {boolean} [isUnary=false] If true, the callback function is unary and will be optimized as such.\n * @returns {Function} Returns a simplified version of the callback function.\n */\nexport function optimizeCallback(callback, array, name) {\n  var isUnary = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if (typed.isTypedFunction(callback)) {\n    var numberOfArguments;\n    if (isUnary) {\n      numberOfArguments = 1;\n    } else {\n      var firstIndex = (array.isMatrix ? array.size() : arraySize(array)).map(() => 0);\n      var firstValue = array.isMatrix ? array.get(firstIndex) : get(array, firstIndex);\n      numberOfArguments = _findNumberOfArgumentsTyped(callback, firstValue, firstIndex, array);\n    }\n    var fastCallback;\n    if (array.isMatrix && array.dataType !== 'mixed' && array.dataType !== undefined) {\n      var singleSignature = _findSingleSignatureWithArity(callback, numberOfArguments);\n      fastCallback = singleSignature !== undefined ? singleSignature : callback;\n    } else {\n      fastCallback = callback;\n    }\n    if (numberOfArguments >= 1 && numberOfArguments <= 3) {\n      return {\n        isUnary: numberOfArguments === 1,\n        fn: function fn() {\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n          return _tryFunctionWithArgs(fastCallback, args.slice(0, numberOfArguments), name, callback.name);\n        }\n      };\n    }\n    return {\n      isUnary: false,\n      fn: function fn() {\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n        return _tryFunctionWithArgs(fastCallback, args, name, callback.name);\n      }\n    };\n  }\n  if (isUnary === undefined) {\n    return {\n      isUnary: _findIfCallbackIsUnary(callback),\n      fn: callback\n    };\n  } else {\n    return {\n      isUnary,\n      fn: callback\n    };\n  }\n}\nfunction _findSingleSignatureWithArity(callback, arity) {\n  var matchingFunctions = [];\n  Object.entries(callback.signatures).forEach(_ref => {\n    var [signature, func] = _ref;\n    if (signature.split(',').length === arity) {\n      matchingFunctions.push(func);\n    }\n  });\n  if (matchingFunctions.length === 1) {\n    return matchingFunctions[0];\n  }\n}\n\n/**\n * Determines if a given callback function is unary (i.e., takes exactly one argument).\n *\n * This function checks the following conditions to determine if the callback is unary:\n * 1. The callback function should have exactly one parameter.\n * 2. The callback function should not use the `arguments` object.\n * 3. The callback function should not use rest parameters (`...`).\n * If in doubt, this function shall return `false` to be safe\n *\n * @param {Function} callback - The callback function to be checked.\n * @returns {boolean} - Returns `true` if the callback is unary, otherwise `false`.\n */\nfunction _findIfCallbackIsUnary(callback) {\n  if (callback.length !== 1) return false;\n  var callbackStr = callback.toString();\n  // Check if the callback function uses `arguments`\n  if (/arguments/.test(callbackStr)) return false;\n\n  // Extract the parameters of the callback function\n  var paramsStr = callbackStr.match(/\\(.*?\\)/);\n  // Check if the callback function uses rest parameters\n  if (/\\.\\.\\./.test(paramsStr)) return false;\n  return true;\n}\nfunction _findNumberOfArgumentsTyped(callback, value, index, array) {\n  var testArgs = [value, index, array];\n  for (var i = 3; i > 0; i--) {\n    var args = testArgs.slice(0, i);\n    if (typed.resolve(callback, args) !== null) {\n      return i;\n    }\n  }\n}\n\n/**\n   * @param {function} func The selected function taken from one of the signatures of the callback function\n   * @param {Array} args List with arguments to apply to the selected signature\n   * @param {string} mappingFnName the name of the function that is using the callback\n   * @param {string} callbackName the name of the callback function\n   * @returns {*} Returns the return value of the invoked signature\n   * @throws {TypeError} Throws an error when no matching signature was found\n   */\nfunction _tryFunctionWithArgs(func, args, mappingFnName, callbackName) {\n  try {\n    return func(...args);\n  } catch (err) {\n    _createCallbackError(err, args, mappingFnName, callbackName);\n  }\n}\n\n/**\n * Creates and throws a detailed TypeError when a callback function fails.\n *\n * @param {Error} err The original error thrown by the callback function.\n * @param {Array} args The arguments that were passed to the callback function.\n * @param {string} mappingFnName The name of the function that is using the callback.\n * @param {string} callbackName The name of the callback function.\n * @throws {TypeError} Throws a detailed TypeError with enriched error message.\n */\nfunction _createCallbackError(err, args, mappingFnName, callbackName) {\n  var _err$data;\n  // Enrich the error message so the user understands that it took place inside the callback function\n  if (err instanceof TypeError && ((_err$data = err.data) === null || _err$data === void 0 ? void 0 : _err$data.category) === 'wrongType') {\n    var argsDesc = [];\n    argsDesc.push(\"value: \".concat(_typeOf(args[0])));\n    if (args.length >= 2) {\n      argsDesc.push(\"index: \".concat(_typeOf(args[1])));\n    }\n    if (args.length >= 3) {\n      argsDesc.push(\"array: \".concat(_typeOf(args[2])));\n    }\n    throw new TypeError(\"Function \".concat(mappingFnName, \" cannot apply callback arguments \") + \"\".concat(callbackName, \"(\").concat(argsDesc.join(', '), \") at index \").concat(JSON.stringify(args[1])));\n  } else {\n    throw new TypeError(\"Function \".concat(mappingFnName, \" cannot apply callback arguments \") + \"to function \".concat(callbackName, \": \").concat(err.message));\n  }\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,gBAAgB;AAClC,SAASC,GAAG,EAAEC,SAAS,QAAQ,YAAY;AAC3C,SAASC,MAAM,IAAIC,OAAO,QAAQ,SAAS;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE;EACtD,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACvF,IAAIV,KAAK,CAACa,eAAe,CAACP,QAAQ,CAAC,EAAE;IACnC,IAAIQ,iBAAiB;IACrB,IAAIL,OAAO,EAAE;MACXK,iBAAiB,GAAG,CAAC;IACvB,CAAC,MAAM;MACL,IAAIC,UAAU,GAAG,CAACR,KAAK,CAACS,QAAQ,GAAGT,KAAK,CAACU,IAAI,CAAC,CAAC,GAAGf,SAAS,CAACK,KAAK,CAAC,EAAEW,GAAG,CAAC,MAAM,CAAC,CAAC;MAChF,IAAIC,UAAU,GAAGZ,KAAK,CAACS,QAAQ,GAAGT,KAAK,CAACN,GAAG,CAACc,UAAU,CAAC,GAAGd,GAAG,CAACM,KAAK,EAAEQ,UAAU,CAAC;MAChFD,iBAAiB,GAAGM,2BAA2B,CAACd,QAAQ,EAAEa,UAAU,EAAEJ,UAAU,EAAER,KAAK,CAAC;IAC1F;IACA,IAAIc,YAAY;IAChB,IAAId,KAAK,CAACS,QAAQ,IAAIT,KAAK,CAACe,QAAQ,KAAK,OAAO,IAAIf,KAAK,CAACe,QAAQ,KAAKV,SAAS,EAAE;MAChF,IAAIW,eAAe,GAAGC,6BAA6B,CAAClB,QAAQ,EAAEQ,iBAAiB,CAAC;MAChFO,YAAY,GAAGE,eAAe,KAAKX,SAAS,GAAGW,eAAe,GAAGjB,QAAQ;IAC3E,CAAC,MAAM;MACLe,YAAY,GAAGf,QAAQ;IACzB;IACA,IAAIQ,iBAAiB,IAAI,CAAC,IAAIA,iBAAiB,IAAI,CAAC,EAAE;MACpD,OAAO;QACLL,OAAO,EAAEK,iBAAiB,KAAK,CAAC;QAChCW,EAAE,EAAE,SAASA,EAAEA,CAAA,EAAG;UAChB,KAAK,IAAIC,IAAI,GAAGhB,SAAS,CAACC,MAAM,EAAEgB,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;YACvFF,IAAI,CAACE,IAAI,CAAC,GAAGnB,SAAS,CAACmB,IAAI,CAAC;UAC9B;UACA,OAAOC,oBAAoB,CAACT,YAAY,EAAEM,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEjB,iBAAiB,CAAC,EAAEN,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAAC;QAClG;MACF,CAAC;IACH;IACA,OAAO;MACLC,OAAO,EAAE,KAAK;MACdgB,EAAE,EAAE,SAASA,EAAEA,CAAA,EAAG;QAChB,KAAK,IAAIO,KAAK,GAAGtB,SAAS,CAACC,MAAM,EAAEgB,IAAI,GAAG,IAAIC,KAAK,CAACI,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;UAC7FN,IAAI,CAACM,KAAK,CAAC,GAAGvB,SAAS,CAACuB,KAAK,CAAC;QAChC;QACA,OAAOH,oBAAoB,CAACT,YAAY,EAAEM,IAAI,EAAEnB,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAAC;MACtE;IACF,CAAC;EACH;EACA,IAAIC,OAAO,KAAKG,SAAS,EAAE;IACzB,OAAO;MACLH,OAAO,EAAEyB,sBAAsB,CAAC5B,QAAQ,CAAC;MACzCmB,EAAE,EAAEnB;IACN,CAAC;EACH,CAAC,MAAM;IACL,OAAO;MACLG,OAAO;MACPgB,EAAE,EAAEnB;IACN,CAAC;EACH;AACF;AACA,SAASkB,6BAA6BA,CAAClB,QAAQ,EAAE6B,KAAK,EAAE;EACtD,IAAIC,iBAAiB,GAAG,EAAE;EAC1BC,MAAM,CAACC,OAAO,CAAChC,QAAQ,CAACiC,UAAU,CAAC,CAACC,OAAO,CAACC,IAAI,IAAI;IAClD,IAAI,CAACC,SAAS,EAAEC,IAAI,CAAC,GAAGF,IAAI;IAC5B,IAAIC,SAAS,CAACE,KAAK,CAAC,GAAG,CAAC,CAACjC,MAAM,KAAKwB,KAAK,EAAE;MACzCC,iBAAiB,CAACS,IAAI,CAACF,IAAI,CAAC;IAC9B;EACF,CAAC,CAAC;EACF,IAAIP,iBAAiB,CAACzB,MAAM,KAAK,CAAC,EAAE;IAClC,OAAOyB,iBAAiB,CAAC,CAAC,CAAC;EAC7B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,sBAAsBA,CAAC5B,QAAQ,EAAE;EACxC,IAAIA,QAAQ,CAACK,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;EACvC,IAAImC,WAAW,GAAGxC,QAAQ,CAACyC,QAAQ,CAAC,CAAC;EACrC;EACA,IAAI,WAAW,CAACC,IAAI,CAACF,WAAW,CAAC,EAAE,OAAO,KAAK;;EAE/C;EACA,IAAIG,SAAS,GAAGH,WAAW,CAACI,KAAK,CAAC,SAAS,CAAC;EAC5C;EACA,IAAI,QAAQ,CAACF,IAAI,CAACC,SAAS,CAAC,EAAE,OAAO,KAAK;EAC1C,OAAO,IAAI;AACb;AACA,SAAS7B,2BAA2BA,CAACd,QAAQ,EAAE6C,KAAK,EAAEC,KAAK,EAAE7C,KAAK,EAAE;EAClE,IAAI8C,QAAQ,GAAG,CAACF,KAAK,EAAEC,KAAK,EAAE7C,KAAK,CAAC;EACpC,KAAK,IAAI+C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1B,IAAI3B,IAAI,GAAG0B,QAAQ,CAACtB,KAAK,CAAC,CAAC,EAAEuB,CAAC,CAAC;IAC/B,IAAItD,KAAK,CAACuD,OAAO,CAACjD,QAAQ,EAAEqB,IAAI,CAAC,KAAK,IAAI,EAAE;MAC1C,OAAO2B,CAAC;IACV;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASxB,oBAAoBA,CAACa,IAAI,EAAEhB,IAAI,EAAE6B,aAAa,EAAEC,YAAY,EAAE;EACrE,IAAI;IACF,OAAOd,IAAI,CAAC,GAAGhB,IAAI,CAAC;EACtB,CAAC,CAAC,OAAO+B,GAAG,EAAE;IACZC,oBAAoB,CAACD,GAAG,EAAE/B,IAAI,EAAE6B,aAAa,EAAEC,YAAY,CAAC;EAC9D;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,oBAAoBA,CAACD,GAAG,EAAE/B,IAAI,EAAE6B,aAAa,EAAEC,YAAY,EAAE;EACpE,IAAIG,SAAS;EACb;EACA,IAAIF,GAAG,YAAYG,SAAS,IAAI,CAAC,CAACD,SAAS,GAAGF,GAAG,CAACI,IAAI,MAAM,IAAI,IAAIF,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACG,QAAQ,MAAM,WAAW,EAAE;IACvI,IAAIC,QAAQ,GAAG,EAAE;IACjBA,QAAQ,CAACnB,IAAI,CAAC,SAAS,CAACoB,MAAM,CAAC7D,OAAO,CAACuB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,IAAIA,IAAI,CAAChB,MAAM,IAAI,CAAC,EAAE;MACpBqD,QAAQ,CAACnB,IAAI,CAAC,SAAS,CAACoB,MAAM,CAAC7D,OAAO,CAACuB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD;IACA,IAAIA,IAAI,CAAChB,MAAM,IAAI,CAAC,EAAE;MACpBqD,QAAQ,CAACnB,IAAI,CAAC,SAAS,CAACoB,MAAM,CAAC7D,OAAO,CAACuB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD;IACA,MAAM,IAAIkC,SAAS,CAAC,WAAW,CAACI,MAAM,CAACT,aAAa,EAAE,mCAAmC,CAAC,GAAG,EAAE,CAACS,MAAM,CAACR,YAAY,EAAE,GAAG,CAAC,CAACQ,MAAM,CAACD,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,aAAa,CAAC,CAACD,MAAM,CAACE,IAAI,CAACC,SAAS,CAACzC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvM,CAAC,MAAM;IACL,MAAM,IAAIkC,SAAS,CAAC,WAAW,CAACI,MAAM,CAACT,aAAa,EAAE,mCAAmC,CAAC,GAAG,cAAc,CAACS,MAAM,CAACR,YAAY,EAAE,IAAI,CAAC,CAACQ,MAAM,CAACP,GAAG,CAACW,OAAO,CAAC,CAAC;EAC7J;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}