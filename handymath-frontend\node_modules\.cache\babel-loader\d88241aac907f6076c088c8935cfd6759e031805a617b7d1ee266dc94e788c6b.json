{"ast": null, "code": "export var combinationsWithRepDocs = {\n  name: 'combinationsWithRep',\n  category: 'Probability',\n  syntax: ['combinationsWithRep(n, k)'],\n  description: 'Compute the number of combinations of n items taken k at a time with replacements.',\n  examples: ['combinationsWithRep(7, 5)'],\n  seealso: ['combinations', 'permutations', 'factorial']\n};", "map": {"version": 3, "names": ["combinationsWithRepDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/probability/combinationsWithRep.js"], "sourcesContent": ["export var combinationsWithRepDocs = {\n  name: 'combinationsWithRep',\n  category: 'Probability',\n  syntax: ['combinationsWithRep(n, k)'],\n  description: 'Compute the number of combinations of n items taken k at a time with replacements.',\n  examples: ['combinationsWithRep(7, 5)'],\n  seealso: ['combinations', 'permutations', 'factorial']\n};"], "mappings": "AAAA,OAAO,IAAIA,uBAAuB,GAAG;EACnCC,IAAI,EAAE,qBAAqB;EAC3BC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CAAC,2BAA2B,CAAC;EACrCC,WAAW,EAAE,oFAAoF;EACjGC,QAAQ,EAAE,CAAC,2BAA2B,CAAC;EACvCC,OAAO,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,WAAW;AACvD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}