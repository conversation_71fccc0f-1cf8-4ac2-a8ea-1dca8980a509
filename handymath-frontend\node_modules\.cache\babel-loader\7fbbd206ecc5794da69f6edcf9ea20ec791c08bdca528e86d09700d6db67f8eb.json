{"ast": null, "code": "export var invDocs = {\n  name: 'inv',\n  category: 'Matrix',\n  syntax: ['inv(x)'],\n  description: 'Calculate the inverse of a matrix',\n  examples: ['inv([1, 2; 3, 4])', 'inv(4)', '1 / 4'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};", "map": {"version": 3, "names": ["invDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/inv.js"], "sourcesContent": ["export var invDocs = {\n  name: 'inv',\n  category: 'Matrix',\n  syntax: ['inv(x)'],\n  description: 'Calculate the inverse of a matrix',\n  examples: ['inv([1, 2; 3, 4])', 'inv(4)', '1 / 4'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,mCAAmC;EAChDC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,OAAO,CAAC;EAClDC,OAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO;AAC5H,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}