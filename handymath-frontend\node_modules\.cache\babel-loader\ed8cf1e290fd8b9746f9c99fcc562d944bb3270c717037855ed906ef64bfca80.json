{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { AccessorNodeDependencies } from './dependenciesAccessorNode.generated.js';\nimport { ArrayNodeDependencies } from './dependenciesArrayNode.generated.js';\nimport { ConstantNodeDependencies } from './dependenciesConstantNode.generated.js';\nimport { FunctionNodeDependencies } from './dependenciesFunctionNode.generated.js';\nimport { IndexNodeDependencies } from './dependenciesIndexNode.generated.js';\nimport { ObjectNodeDependencies } from './dependenciesObjectNode.generated.js';\nimport { OperatorNodeDependencies } from './dependenciesOperatorNode.generated.js';\nimport { ParenthesisNodeDependencies } from './dependenciesParenthesisNode.generated.js';\nimport { SymbolNodeDependencies } from './dependenciesSymbolNode.generated.js';\nimport { equalDependencies } from './dependenciesEqual.generated.js';\nimport { parseDependencies } from './dependenciesParse.generated.js';\nimport { replacerDependencies } from './dependenciesReplacer.generated.js';\nimport { resolveDependencies } from './dependenciesResolve.generated.js';\nimport { simplifyConstantDependencies } from './dependenciesSimplifyConstant.generated.js';\nimport { simplifyCoreDependencies } from './dependenciesSimplifyCore.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSimplify } from '../../factoriesAny.js';\nexport var simplifyDependencies = {\n  AccessorNodeDependencies,\n  ArrayNodeDependencies,\n  ConstantNodeDependencies,\n  FunctionNodeDependencies,\n  IndexNodeDependencies,\n  ObjectNodeDependencies,\n  OperatorNodeDependencies,\n  ParenthesisNodeDependencies,\n  SymbolNodeDependencies,\n  equalDependencies,\n  parseDependencies,\n  replacerDependencies,\n  resolveDependencies,\n  simplifyConstantDependencies,\n  simplifyCoreDependencies,\n  typedDependencies,\n  createSimplify\n};", "map": {"version": 3, "names": ["AccessorNodeDependencies", "ArrayNodeDependencies", "ConstantNodeDependencies", "FunctionNodeDependencies", "IndexNodeDependencies", "ObjectNodeDependencies", "OperatorNodeDependencies", "ParenthesisNodeDependencies", "SymbolNodeDependencies", "equalDependencies", "parseDependencies", "replacerDependencies", "resolveDependencies", "simplifyConstantDependencies", "simplifyCoreDependencies", "typedDependencies", "createSimplify", "simplifyDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSimplify.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { AccessorNodeDependencies } from './dependenciesAccessorNode.generated.js';\nimport { ArrayNodeDependencies } from './dependenciesArrayNode.generated.js';\nimport { ConstantNodeDependencies } from './dependenciesConstantNode.generated.js';\nimport { FunctionNodeDependencies } from './dependenciesFunctionNode.generated.js';\nimport { IndexNodeDependencies } from './dependenciesIndexNode.generated.js';\nimport { ObjectNodeDependencies } from './dependenciesObjectNode.generated.js';\nimport { OperatorNodeDependencies } from './dependenciesOperatorNode.generated.js';\nimport { ParenthesisNodeDependencies } from './dependenciesParenthesisNode.generated.js';\nimport { SymbolNodeDependencies } from './dependenciesSymbolNode.generated.js';\nimport { equalDependencies } from './dependenciesEqual.generated.js';\nimport { parseDependencies } from './dependenciesParse.generated.js';\nimport { replacerDependencies } from './dependenciesReplacer.generated.js';\nimport { resolveDependencies } from './dependenciesResolve.generated.js';\nimport { simplifyConstantDependencies } from './dependenciesSimplifyConstant.generated.js';\nimport { simplifyCoreDependencies } from './dependenciesSimplifyCore.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSimplify } from '../../factoriesAny.js';\nexport var simplifyDependencies = {\n  AccessorNodeDependencies,\n  ArrayNodeDependencies,\n  ConstantNodeDependencies,\n  FunctionNodeDependencies,\n  IndexNodeDependencies,\n  ObjectNodeDependencies,\n  OperatorNodeDependencies,\n  ParenthesisNodeDependencies,\n  SymbolNodeDependencies,\n  equalDependencies,\n  parseDependencies,\n  replacerDependencies,\n  resolveDependencies,\n  simplifyConstantDependencies,\n  simplifyCoreDependencies,\n  typedDependencies,\n  createSimplify\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,4BAA4B,QAAQ,6CAA6C;AAC1F,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,cAAc,QAAQ,uBAAuB;AACtD,OAAO,IAAIC,oBAAoB,GAAG;EAChCjB,wBAAwB;EACxBC,qBAAqB;EACrBC,wBAAwB;EACxBC,wBAAwB;EACxBC,qBAAqB;EACrBC,sBAAsB;EACtBC,wBAAwB;EACxBC,2BAA2B;EAC3BC,sBAAsB;EACtBC,iBAAiB;EACjBC,iBAAiB;EACjBC,oBAAoB;EACpBC,mBAAmB;EACnBC,4BAA4B;EAC5BC,wBAAwB;EACxBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}