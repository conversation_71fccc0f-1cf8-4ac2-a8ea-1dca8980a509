{"ast": null, "code": "export var setPowersetDocs = {\n  name: 'setPowerset',\n  category: 'Set',\n  syntax: ['setPowerset(set)'],\n  description: 'Create the powerset of a (multi)set: the powerset contains very possible subsets of a (multi)set. A multi-dimension array will be converted to a single-dimension array before the operation.',\n  examples: ['setPowerset([1, 2, 3])'],\n  seealso: ['setCartesian']\n};", "map": {"version": 3, "names": ["setPowersetDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/set/setPowerset.js"], "sourcesContent": ["export var setPowersetDocs = {\n  name: 'setPowerset',\n  category: 'Set',\n  syntax: ['setPowerset(set)'],\n  description: 'Create the powerset of a (multi)set: the powerset contains very possible subsets of a (multi)set. A multi-dimension array will be converted to a single-dimension array before the operation.',\n  examples: ['setPowerset([1, 2, 3])'],\n  seealso: ['setCartesian']\n};"], "mappings": "AAAA,OAAO,IAAIA,eAAe,GAAG;EAC3BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,CAAC,kBAAkB,CAAC;EAC5BC,WAAW,EAAE,+LAA+L;EAC5MC,QAAQ,EAAE,CAAC,wBAAwB,CAAC;EACpCC,OAAO,EAAE,CAAC,cAAc;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}