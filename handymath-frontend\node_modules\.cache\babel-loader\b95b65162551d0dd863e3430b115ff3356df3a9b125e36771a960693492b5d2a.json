{"ast": null, "code": "'use strict';\n\n/**\n *\n * This class offers the possibility to calculate fractions.\n * You can pass a fraction in different formats. Either as array, as double, as string or as an integer.\n *\n * Array/Object form\n * [ 0 => <numerator>, 1 => <denominator> ]\n * { n => <numerator>, d => <denominator> }\n *\n * Integer form\n * - Single integer value as BigInt or Number\n *\n * Double form\n * - Single double value as Number\n *\n * String form\n * 123.456 - a simple double\n * 123/456 - a string fraction\n * 123.'456' - a double with repeating decimal places\n * 123.(456) - synonym\n * 123.45'6' - a double with repeating last place\n * 123.45(6) - synonym\n *\n * Example:\n * let f = new Fraction(\"9.4'31'\");\n * f.mul([-4, 3]).div(4.9);\n *\n */\n\n// Set Identity function to downgrade BigInt to Number if needed\nif (typeof BigInt === 'undefined') BigInt = function (n) {\n  if (isNaN(n)) throw new Error(\"\");\n  return n;\n};\nconst C_ZERO = BigInt(0);\nconst C_ONE = BigInt(1);\nconst C_TWO = BigInt(2);\nconst C_FIVE = BigInt(5);\nconst C_TEN = BigInt(10);\n\n// Maximum search depth for cyclic rational numbers. 2000 should be more than enough.\n// Example: 1/7 = 0.(142857) has 6 repeating decimal places.\n// If MAX_CYCLE_LEN gets reduced, long cycles will not be detected and toString() only gets the first 10 digits\nconst MAX_CYCLE_LEN = 2000;\n\n// Parsed data to avoid calling \"new\" all the time\nconst P = {\n  \"s\": C_ONE,\n  \"n\": C_ZERO,\n  \"d\": C_ONE\n};\nfunction assign(n, s) {\n  try {\n    n = BigInt(n);\n  } catch (e) {\n    throw InvalidParameter();\n  }\n  return n * s;\n}\nfunction trunc(x) {\n  return typeof x === 'bigint' ? x : Math.floor(x);\n}\n\n// Creates a new Fraction internally without the need of the bulky constructor\nfunction newFraction(n, d) {\n  if (d === C_ZERO) {\n    throw DivisionByZero();\n  }\n  const f = Object.create(Fraction.prototype);\n  f[\"s\"] = n < C_ZERO ? -C_ONE : C_ONE;\n  n = n < C_ZERO ? -n : n;\n  const a = gcd(n, d);\n  f[\"n\"] = n / a;\n  f[\"d\"] = d / a;\n  return f;\n}\nfunction factorize(num) {\n  const factors = {};\n  let n = num;\n  let i = C_TWO;\n  let s = C_FIVE - C_ONE;\n  while (s <= n) {\n    while (n % i === C_ZERO) {\n      n /= i;\n      factors[i] = (factors[i] || C_ZERO) + C_ONE;\n    }\n    s += C_ONE + C_TWO * i++;\n  }\n  if (n !== num) {\n    if (n > 1) factors[n] = (factors[n] || C_ZERO) + C_ONE;\n  } else {\n    factors[num] = (factors[num] || C_ZERO) + C_ONE;\n  }\n  return factors;\n}\nconst parse = function (p1, p2) {\n  let n = C_ZERO,\n    d = C_ONE,\n    s = C_ONE;\n  if (p1 === undefined || p1 === null) {// No argument\n    /* void */\n  } else if (p2 !== undefined) {\n    // Two arguments\n\n    if (typeof p1 === \"bigint\") {\n      n = p1;\n    } else if (isNaN(p1)) {\n      throw InvalidParameter();\n    } else if (p1 % 1 !== 0) {\n      throw NonIntegerParameter();\n    } else {\n      n = BigInt(p1);\n    }\n    if (typeof p2 === \"bigint\") {\n      d = p2;\n    } else if (isNaN(p2)) {\n      throw InvalidParameter();\n    } else if (p2 % 1 !== 0) {\n      throw NonIntegerParameter();\n    } else {\n      d = BigInt(p2);\n    }\n    s = n * d;\n  } else if (typeof p1 === \"object\") {\n    if (\"d\" in p1 && \"n\" in p1) {\n      n = BigInt(p1[\"n\"]);\n      d = BigInt(p1[\"d\"]);\n      if (\"s\" in p1) n *= BigInt(p1[\"s\"]);\n    } else if (0 in p1) {\n      n = BigInt(p1[0]);\n      if (1 in p1) d = BigInt(p1[1]);\n    } else if (typeof p1 === \"bigint\") {\n      n = p1;\n    } else {\n      throw InvalidParameter();\n    }\n    s = n * d;\n  } else if (typeof p1 === \"number\") {\n    if (isNaN(p1)) {\n      throw InvalidParameter();\n    }\n    if (p1 < 0) {\n      s = -C_ONE;\n      p1 = -p1;\n    }\n    if (p1 % 1 === 0) {\n      n = BigInt(p1);\n    } else {\n      let z = 1;\n      let A = 0,\n        B = 1;\n      let C = 1,\n        D = 1;\n      let N = 10000000;\n      if (p1 >= 1) {\n        z = 10 ** Math.floor(1 + Math.log10(p1));\n        p1 /= z;\n      }\n\n      // Using Farey Sequences\n\n      while (B <= N && D <= N) {\n        let M = (A + C) / (B + D);\n        if (p1 === M) {\n          if (B + D <= N) {\n            n = A + C;\n            d = B + D;\n          } else if (D > B) {\n            n = C;\n            d = D;\n          } else {\n            n = A;\n            d = B;\n          }\n          break;\n        } else {\n          if (p1 > M) {\n            A += C;\n            B += D;\n          } else {\n            C += A;\n            D += B;\n          }\n          if (B > N) {\n            n = C;\n            d = D;\n          } else {\n            n = A;\n            d = B;\n          }\n        }\n      }\n      n = BigInt(n) * BigInt(z);\n      d = BigInt(d);\n    }\n  } else if (typeof p1 === \"string\") {\n    let ndx = 0;\n    let v = C_ZERO,\n      w = C_ZERO,\n      x = C_ZERO,\n      y = C_ONE,\n      z = C_ONE;\n    let match = p1.replace(/_/g, '').match(/\\d+|./g);\n    if (match === null) throw InvalidParameter();\n    if (match[ndx] === '-') {\n      // Check for minus sign at the beginning\n      s = -C_ONE;\n      ndx++;\n    } else if (match[ndx] === '+') {\n      // Check for plus sign at the beginning\n      ndx++;\n    }\n    if (match.length === ndx + 1) {\n      // Check if it's just a simple number \"1234\"\n      w = assign(match[ndx++], s);\n    } else if (match[ndx + 1] === '.' || match[ndx] === '.') {\n      // Check if it's a decimal number\n\n      if (match[ndx] !== '.') {\n        // Handle 0.5 and .5\n        v = assign(match[ndx++], s);\n      }\n      ndx++;\n\n      // Check for decimal places\n      if (ndx + 1 === match.length || match[ndx + 1] === '(' && match[ndx + 3] === ')' || match[ndx + 1] === \"'\" && match[ndx + 3] === \"'\") {\n        w = assign(match[ndx], s);\n        y = C_TEN ** BigInt(match[ndx].length);\n        ndx++;\n      }\n\n      // Check for repeating places\n      if (match[ndx] === '(' && match[ndx + 2] === ')' || match[ndx] === \"'\" && match[ndx + 2] === \"'\") {\n        x = assign(match[ndx + 1], s);\n        z = C_TEN ** BigInt(match[ndx + 1].length) - C_ONE;\n        ndx += 3;\n      }\n    } else if (match[ndx + 1] === '/' || match[ndx + 1] === ':') {\n      // Check for a simple fraction \"123/456\" or \"123:456\"\n      w = assign(match[ndx], s);\n      y = assign(match[ndx + 2], C_ONE);\n      ndx += 3;\n    } else if (match[ndx + 3] === '/' && match[ndx + 1] === ' ') {\n      // Check for a complex fraction \"123 1/2\"\n      v = assign(match[ndx], s);\n      w = assign(match[ndx + 2], s);\n      y = assign(match[ndx + 4], C_ONE);\n      ndx += 5;\n    }\n    if (match.length <= ndx) {\n      // Check for more tokens on the stack\n      d = y * z;\n      s = /* void */\n      n = x + d * v + z * w;\n    } else {\n      throw InvalidParameter();\n    }\n  } else if (typeof p1 === \"bigint\") {\n    n = p1;\n    s = p1;\n    d = C_ONE;\n  } else {\n    throw InvalidParameter();\n  }\n  if (d === C_ZERO) {\n    throw DivisionByZero();\n  }\n  P[\"s\"] = s < C_ZERO ? -C_ONE : C_ONE;\n  P[\"n\"] = n < C_ZERO ? -n : n;\n  P[\"d\"] = d < C_ZERO ? -d : d;\n};\nfunction modpow(b, e, m) {\n  let r = C_ONE;\n  for (; e > C_ZERO; b = b * b % m, e >>= C_ONE) {\n    if (e & C_ONE) {\n      r = r * b % m;\n    }\n  }\n  return r;\n}\nfunction cycleLen(n, d) {\n  for (; d % C_TWO === C_ZERO; d /= C_TWO) {}\n  for (; d % C_FIVE === C_ZERO; d /= C_FIVE) {}\n  if (d === C_ONE)\n    // Catch non-cyclic numbers\n    return C_ZERO;\n\n  // If we would like to compute really large numbers quicker, we could make use of Fermat's little theorem:\n  // 10^(d-1) % d == 1\n  // However, we don't need such large numbers and MAX_CYCLE_LEN should be the capstone,\n  // as we want to translate the numbers to strings.\n\n  let rem = C_TEN % d;\n  let t = 1;\n  for (; rem !== C_ONE; t++) {\n    rem = rem * C_TEN % d;\n    if (t > MAX_CYCLE_LEN) return C_ZERO; // Returning 0 here means that we don't print it as a cyclic number. It's likely that the answer is `d-1`\n  }\n  return BigInt(t);\n}\nfunction cycleStart(n, d, len) {\n  let rem1 = C_ONE;\n  let rem2 = modpow(C_TEN, len, d);\n  for (let t = 0; t < 300; t++) {\n    // s < ~log10(Number.MAX_VALUE)\n    // Solve 10^s == 10^(s+t) (mod d)\n\n    if (rem1 === rem2) return BigInt(t);\n    rem1 = rem1 * C_TEN % d;\n    rem2 = rem2 * C_TEN % d;\n  }\n  return 0;\n}\nfunction gcd(a, b) {\n  if (!a) return b;\n  if (!b) return a;\n  while (1) {\n    a %= b;\n    if (!a) return b;\n    b %= a;\n    if (!b) return a;\n  }\n}\n\n/**\n * Module constructor\n *\n * @constructor\n * @param {number|Fraction=} a\n * @param {number=} b\n */\nfunction Fraction(a, b) {\n  parse(a, b);\n  if (this instanceof Fraction) {\n    a = gcd(P[\"d\"], P[\"n\"]); // Abuse a\n    this[\"s\"] = P[\"s\"];\n    this[\"n\"] = P[\"n\"] / a;\n    this[\"d\"] = P[\"d\"] / a;\n  } else {\n    return newFraction(P['s'] * P['n'], P['d']);\n  }\n}\nvar DivisionByZero = function () {\n  return new Error(\"Division by Zero\");\n};\nvar InvalidParameter = function () {\n  return new Error(\"Invalid argument\");\n};\nvar NonIntegerParameter = function () {\n  return new Error(\"Parameters must be integer\");\n};\nFraction.prototype = {\n  \"s\": C_ONE,\n  \"n\": C_ZERO,\n  \"d\": C_ONE,\n  /**\n   * Calculates the absolute value\n   *\n   * Ex: new Fraction(-4).abs() => 4\n   **/\n  \"abs\": function () {\n    return newFraction(this[\"n\"], this[\"d\"]);\n  },\n  /**\n   * Inverts the sign of the current fraction\n   *\n   * Ex: new Fraction(-4).neg() => 4\n   **/\n  \"neg\": function () {\n    return newFraction(-this[\"s\"] * this[\"n\"], this[\"d\"]);\n  },\n  /**\n   * Adds two rational numbers\n   *\n   * Ex: new Fraction({n: 2, d: 3}).add(\"14.9\") => 467 / 30\n   **/\n  \"add\": function (a, b) {\n    parse(a, b);\n    return newFraction(this[\"s\"] * this[\"n\"] * P[\"d\"] + P[\"s\"] * this[\"d\"] * P[\"n\"], this[\"d\"] * P[\"d\"]);\n  },\n  /**\n   * Subtracts two rational numbers\n   *\n   * Ex: new Fraction({n: 2, d: 3}).add(\"14.9\") => -427 / 30\n   **/\n  \"sub\": function (a, b) {\n    parse(a, b);\n    return newFraction(this[\"s\"] * this[\"n\"] * P[\"d\"] - P[\"s\"] * this[\"d\"] * P[\"n\"], this[\"d\"] * P[\"d\"]);\n  },\n  /**\n   * Multiplies two rational numbers\n   *\n   * Ex: new Fraction(\"-17.(345)\").mul(3) => 5776 / 111\n   **/\n  \"mul\": function (a, b) {\n    parse(a, b);\n    return newFraction(this[\"s\"] * P[\"s\"] * this[\"n\"] * P[\"n\"], this[\"d\"] * P[\"d\"]);\n  },\n  /**\n   * Divides two rational numbers\n   *\n   * Ex: new Fraction(\"-17.(345)\").inverse().div(3)\n   **/\n  \"div\": function (a, b) {\n    parse(a, b);\n    return newFraction(this[\"s\"] * P[\"s\"] * this[\"n\"] * P[\"d\"], this[\"d\"] * P[\"n\"]);\n  },\n  /**\n   * Clones the actual object\n   *\n   * Ex: new Fraction(\"-17.(345)\").clone()\n   **/\n  \"clone\": function () {\n    return newFraction(this['s'] * this['n'], this['d']);\n  },\n  /**\n   * Calculates the modulo of two rational numbers - a more precise fmod\n   *\n   * Ex: new Fraction('4.(3)').mod([7, 8]) => (13/3) % (7/8) = (5/6)\n   * Ex: new Fraction(20, 10).mod().equals(0) ? \"is Integer\"\n   **/\n  \"mod\": function (a, b) {\n    if (a === undefined) {\n      return newFraction(this[\"s\"] * this[\"n\"] % this[\"d\"], C_ONE);\n    }\n    parse(a, b);\n    if (C_ZERO === P[\"n\"] * this[\"d\"]) {\n      throw DivisionByZero();\n    }\n\n    /**\n     * I derived the rational modulo similar to the modulo for integers\n     *\n     * https://raw.org/book/analysis/rational-numbers/\n     *\n     *    n1/d1 = (n2/d2) * q + r, where 0 ≤ r < n2/d2\n     * => d2 * n1 = n2 * d1 * q + d1 * d2 * r\n     * => r = (d2 * n1 - n2 * d1 * q) / (d1 * d2)\n     *      = (d2 * n1 - n2 * d1 * floor((d2 * n1) / (n2 * d1))) / (d1 * d2)\n     *      = ((d2 * n1) % (n2 * d1)) / (d1 * d2)\n     */\n    return newFraction(this[\"s\"] * (P[\"d\"] * this[\"n\"]) % (P[\"n\"] * this[\"d\"]), P[\"d\"] * this[\"d\"]);\n  },\n  /**\n   * Calculates the fractional gcd of two rational numbers\n   *\n   * Ex: new Fraction(5,8).gcd(3,7) => 1/56\n   */\n  \"gcd\": function (a, b) {\n    parse(a, b);\n\n    // https://raw.org/book/analysis/rational-numbers/\n    // gcd(a / b, c / d) = gcd(a, c) / lcm(b, d)\n\n    return newFraction(gcd(P[\"n\"], this[\"n\"]) * gcd(P[\"d\"], this[\"d\"]), P[\"d\"] * this[\"d\"]);\n  },\n  /**\n   * Calculates the fractional lcm of two rational numbers\n   *\n   * Ex: new Fraction(5,8).lcm(3,7) => 15\n   */\n  \"lcm\": function (a, b) {\n    parse(a, b);\n\n    // https://raw.org/book/analysis/rational-numbers/\n    // lcm(a / b, c / d) = lcm(a, c) / gcd(b, d)\n\n    if (P[\"n\"] === C_ZERO && this[\"n\"] === C_ZERO) {\n      return newFraction(C_ZERO, C_ONE);\n    }\n    return newFraction(P[\"n\"] * this[\"n\"], gcd(P[\"n\"], this[\"n\"]) * gcd(P[\"d\"], this[\"d\"]));\n  },\n  /**\n   * Gets the inverse of the fraction, means numerator and denominator are exchanged\n   *\n   * Ex: new Fraction([-3, 4]).inverse() => -4 / 3\n   **/\n  \"inverse\": function () {\n    return newFraction(this[\"s\"] * this[\"d\"], this[\"n\"]);\n  },\n  /**\n   * Calculates the fraction to some integer exponent\n   *\n   * Ex: new Fraction(-1,2).pow(-3) => -8\n   */\n  \"pow\": function (a, b) {\n    parse(a, b);\n\n    // Trivial case when exp is an integer\n\n    if (P['d'] === C_ONE) {\n      if (P['s'] < C_ZERO) {\n        return newFraction((this['s'] * this[\"d\"]) ** P['n'], this[\"n\"] ** P['n']);\n      } else {\n        return newFraction((this['s'] * this[\"n\"]) ** P['n'], this[\"d\"] ** P['n']);\n      }\n    }\n\n    // Negative roots become complex\n    //     (-a/b)^(c/d) = x\n    // ⇔ (-1)^(c/d) * (a/b)^(c/d) = x\n    // ⇔ (cos(pi) + i*sin(pi))^(c/d) * (a/b)^(c/d) = x\n    // ⇔ (cos(c*pi/d) + i*sin(c*pi/d)) * (a/b)^(c/d) = x       # DeMoivre's formula\n    // From which follows that only for c=0 the root is non-complex\n    if (this['s'] < C_ZERO) return null;\n\n    // Now prime factor n and d\n    let N = factorize(this['n']);\n    let D = factorize(this['d']);\n\n    // Exponentiate and take root for n and d individually\n    let n = C_ONE;\n    let d = C_ONE;\n    for (let k in N) {\n      if (k === '1') continue;\n      if (k === '0') {\n        n = C_ZERO;\n        break;\n      }\n      N[k] *= P['n'];\n      if (N[k] % P['d'] === C_ZERO) {\n        N[k] /= P['d'];\n      } else return null;\n      n *= BigInt(k) ** N[k];\n    }\n    for (let k in D) {\n      if (k === '1') continue;\n      D[k] *= P['n'];\n      if (D[k] % P['d'] === C_ZERO) {\n        D[k] /= P['d'];\n      } else return null;\n      d *= BigInt(k) ** D[k];\n    }\n    if (P['s'] < C_ZERO) {\n      return newFraction(d, n);\n    }\n    return newFraction(n, d);\n  },\n  /**\n   * Calculates the logarithm of a fraction to a given rational base\n   *\n   * Ex: new Fraction(27, 8).log(9, 4) => 3/2\n   */\n  \"log\": function (a, b) {\n    parse(a, b);\n    if (this['s'] <= C_ZERO || P['s'] <= C_ZERO) return null;\n    const allPrimes = {};\n    const baseFactors = factorize(P['n']);\n    const T1 = factorize(P['d']);\n    const numberFactors = factorize(this['n']);\n    const T2 = factorize(this['d']);\n    for (const prime in T1) {\n      baseFactors[prime] = (baseFactors[prime] || C_ZERO) - T1[prime];\n    }\n    for (const prime in T2) {\n      numberFactors[prime] = (numberFactors[prime] || C_ZERO) - T2[prime];\n    }\n    for (const prime in baseFactors) {\n      if (prime === '1') continue;\n      allPrimes[prime] = true;\n    }\n    for (const prime in numberFactors) {\n      if (prime === '1') continue;\n      allPrimes[prime] = true;\n    }\n    let retN = null;\n    let retD = null;\n\n    // Iterate over all unique primes to determine if a consistent ratio exists\n    for (const prime in allPrimes) {\n      const baseExponent = baseFactors[prime] || C_ZERO;\n      const numberExponent = numberFactors[prime] || C_ZERO;\n      if (baseExponent === C_ZERO) {\n        if (numberExponent !== C_ZERO) {\n          return null; // Logarithm cannot be expressed as a rational number\n        }\n        continue; // Skip this prime since both exponents are zero\n      }\n\n      // Calculate the ratio of exponents for this prime\n      let curN = numberExponent;\n      let curD = baseExponent;\n\n      // Simplify the current ratio\n      const gcdValue = gcd(curN, curD);\n      curN /= gcdValue;\n      curD /= gcdValue;\n\n      // Check if this is the first ratio; otherwise, ensure ratios are consistent\n      if (retN === null && retD === null) {\n        retN = curN;\n        retD = curD;\n      } else if (curN * retD !== retN * curD) {\n        return null; // Ratios do not match, logarithm cannot be rational\n      }\n    }\n    return retN !== null && retD !== null ? newFraction(retN, retD) : null;\n  },\n  /**\n   * Check if two rational numbers are the same\n   *\n   * Ex: new Fraction(19.6).equals([98, 5]);\n   **/\n  \"equals\": function (a, b) {\n    parse(a, b);\n    return this[\"s\"] * this[\"n\"] * P[\"d\"] === P[\"s\"] * P[\"n\"] * this[\"d\"];\n  },\n  /**\n   * Check if this rational number is less than another\n   *\n   * Ex: new Fraction(19.6).lt([98, 5]);\n   **/\n  \"lt\": function (a, b) {\n    parse(a, b);\n    return this[\"s\"] * this[\"n\"] * P[\"d\"] < P[\"s\"] * P[\"n\"] * this[\"d\"];\n  },\n  /**\n   * Check if this rational number is less than or equal another\n   *\n   * Ex: new Fraction(19.6).lt([98, 5]);\n   **/\n  \"lte\": function (a, b) {\n    parse(a, b);\n    return this[\"s\"] * this[\"n\"] * P[\"d\"] <= P[\"s\"] * P[\"n\"] * this[\"d\"];\n  },\n  /**\n   * Check if this rational number is greater than another\n   *\n   * Ex: new Fraction(19.6).lt([98, 5]);\n   **/\n  \"gt\": function (a, b) {\n    parse(a, b);\n    return this[\"s\"] * this[\"n\"] * P[\"d\"] > P[\"s\"] * P[\"n\"] * this[\"d\"];\n  },\n  /**\n   * Check if this rational number is greater than or equal another\n   *\n   * Ex: new Fraction(19.6).lt([98, 5]);\n   **/\n  \"gte\": function (a, b) {\n    parse(a, b);\n    return this[\"s\"] * this[\"n\"] * P[\"d\"] >= P[\"s\"] * P[\"n\"] * this[\"d\"];\n  },\n  /**\n   * Compare two rational numbers\n   * < 0 iff this < that\n   * > 0 iff this > that\n   * = 0 iff this = that\n   *\n   * Ex: new Fraction(19.6).compare([98, 5]);\n   **/\n  \"compare\": function (a, b) {\n    parse(a, b);\n    let t = this[\"s\"] * this[\"n\"] * P[\"d\"] - P[\"s\"] * P[\"n\"] * this[\"d\"];\n    return (C_ZERO < t) - (t < C_ZERO);\n  },\n  /**\n   * Calculates the ceil of a rational number\n   *\n   * Ex: new Fraction('4.(3)').ceil() => (5 / 1)\n   **/\n  \"ceil\": function (places) {\n    places = C_TEN ** BigInt(places || 0);\n    return newFraction(trunc(this[\"s\"] * places * this[\"n\"] / this[\"d\"]) + (places * this[\"n\"] % this[\"d\"] > C_ZERO && this[\"s\"] >= C_ZERO ? C_ONE : C_ZERO), places);\n  },\n  /**\n   * Calculates the floor of a rational number\n   *\n   * Ex: new Fraction('4.(3)').floor() => (4 / 1)\n   **/\n  \"floor\": function (places) {\n    places = C_TEN ** BigInt(places || 0);\n    return newFraction(trunc(this[\"s\"] * places * this[\"n\"] / this[\"d\"]) - (places * this[\"n\"] % this[\"d\"] > C_ZERO && this[\"s\"] < C_ZERO ? C_ONE : C_ZERO), places);\n  },\n  /**\n   * Rounds a rational numbers\n   *\n   * Ex: new Fraction('4.(3)').round() => (4 / 1)\n   **/\n  \"round\": function (places) {\n    places = C_TEN ** BigInt(places || 0);\n\n    /* Derivation:\n     s >= 0:\n      round(n / d) = trunc(n / d) + (n % d) / d >= 0.5 ? 1 : 0\n                   = trunc(n / d) + 2(n % d) >= d ? 1 : 0\n    s < 0:\n      round(n / d) =-trunc(n / d) - (n % d) / d > 0.5 ? 1 : 0\n                   =-trunc(n / d) - 2(n % d) > d ? 1 : 0\n     =>:\n     round(s * n / d) = s * trunc(n / d) + s * (C + 2(n % d) > d ? 1 : 0)\n        where C = s >= 0 ? 1 : 0, to fix the >= for the positve case.\n    */\n\n    return newFraction(trunc(this[\"s\"] * places * this[\"n\"] / this[\"d\"]) + this[\"s\"] * ((this[\"s\"] >= C_ZERO ? C_ONE : C_ZERO) + C_TWO * (places * this[\"n\"] % this[\"d\"]) > this[\"d\"] ? C_ONE : C_ZERO), places);\n  },\n  /**\n    * Rounds a rational number to a multiple of another rational number\n    *\n    * Ex: new Fraction('0.9').roundTo(\"1/8\") => 7 / 8\n    **/\n  \"roundTo\": function (a, b) {\n    /*\n    k * x/y ≤ a/b < (k+1) * x/y\n    ⇔ k ≤ a/b / (x/y) < (k+1)\n    ⇔ k = floor(a/b * y/x)\n    ⇔ k = floor((a * y) / (b * x))\n    */\n\n    parse(a, b);\n    const n = this['n'] * P['d'];\n    const d = this['d'] * P['n'];\n    const r = n % d;\n\n    // round(n / d) = trunc(n / d) + 2(n % d) >= d ? 1 : 0\n    let k = trunc(n / d);\n    if (r + r >= d) {\n      k++;\n    }\n    return newFraction(this['s'] * k * P['n'], P['d']);\n  },\n  /**\n   * Check if two rational numbers are divisible\n   *\n   * Ex: new Fraction(19.6).divisible(1.5);\n   */\n  \"divisible\": function (a, b) {\n    parse(a, b);\n    return !(!(P[\"n\"] * this[\"d\"]) || this[\"n\"] * P[\"d\"] % (P[\"n\"] * this[\"d\"]));\n  },\n  /**\n   * Returns a decimal representation of the fraction\n   *\n   * Ex: new Fraction(\"100.'91823'\").valueOf() => 100.91823918239183\n   **/\n  'valueOf': function () {\n    // Best we can do so far\n    return Number(this[\"s\"] * this[\"n\"]) / Number(this[\"d\"]);\n  },\n  /**\n   * Creates a string representation of a fraction with all digits\n   *\n   * Ex: new Fraction(\"100.'91823'\").toString() => \"100.(91823)\"\n   **/\n  'toString': function (dec) {\n    let N = this[\"n\"];\n    let D = this[\"d\"];\n    dec = dec || 15; // 15 = decimal places when no repetition\n\n    let cycLen = cycleLen(N, D); // Cycle length\n    let cycOff = cycleStart(N, D, cycLen); // Cycle start\n\n    let str = this['s'] < C_ZERO ? \"-\" : \"\";\n\n    // Append integer part\n    str += trunc(N / D);\n    N %= D;\n    N *= C_TEN;\n    if (N) str += \".\";\n    if (cycLen) {\n      for (let i = cycOff; i--;) {\n        str += trunc(N / D);\n        N %= D;\n        N *= C_TEN;\n      }\n      str += \"(\";\n      for (let i = cycLen; i--;) {\n        str += trunc(N / D);\n        N %= D;\n        N *= C_TEN;\n      }\n      str += \")\";\n    } else {\n      for (let i = dec; N && i--;) {\n        str += trunc(N / D);\n        N %= D;\n        N *= C_TEN;\n      }\n    }\n    return str;\n  },\n  /**\n   * Returns a string-fraction representation of a Fraction object\n   *\n   * Ex: new Fraction(\"1.'3'\").toFraction() => \"4 1/3\"\n   **/\n  'toFraction': function (showMixed) {\n    let n = this[\"n\"];\n    let d = this[\"d\"];\n    let str = this['s'] < C_ZERO ? \"-\" : \"\";\n    if (d === C_ONE) {\n      str += n;\n    } else {\n      let whole = trunc(n / d);\n      if (showMixed && whole > C_ZERO) {\n        str += whole;\n        str += \" \";\n        n %= d;\n      }\n      str += n;\n      str += '/';\n      str += d;\n    }\n    return str;\n  },\n  /**\n   * Returns a latex representation of a Fraction object\n   *\n   * Ex: new Fraction(\"1.'3'\").toLatex() => \"\\frac{4}{3}\"\n   **/\n  'toLatex': function (showMixed) {\n    let n = this[\"n\"];\n    let d = this[\"d\"];\n    let str = this['s'] < C_ZERO ? \"-\" : \"\";\n    if (d === C_ONE) {\n      str += n;\n    } else {\n      let whole = trunc(n / d);\n      if (showMixed && whole > C_ZERO) {\n        str += whole;\n        n %= d;\n      }\n      str += \"\\\\frac{\";\n      str += n;\n      str += '}{';\n      str += d;\n      str += '}';\n    }\n    return str;\n  },\n  /**\n   * Returns an array of continued fraction elements\n   *\n   * Ex: new Fraction(\"7/8\").toContinued() => [0,1,7]\n   */\n  'toContinued': function () {\n    let a = this['n'];\n    let b = this['d'];\n    let res = [];\n    do {\n      res.push(trunc(a / b));\n      let t = a % b;\n      a = b;\n      b = t;\n    } while (a !== C_ONE);\n    return res;\n  },\n  \"simplify\": function (eps) {\n    const ieps = BigInt(1 / (eps || 0.001) | 0);\n    const thisABS = this['abs']();\n    const cont = thisABS['toContinued']();\n    for (let i = 1; i < cont.length; i++) {\n      let s = newFraction(cont[i - 1], C_ONE);\n      for (let k = i - 2; k >= 0; k--) {\n        s = s['inverse']()['add'](cont[k]);\n      }\n      let t = s['sub'](thisABS);\n      if (t['n'] * ieps < t['d']) {\n        // More robust than Math.abs(t.valueOf()) < eps\n        return s['mul'](this['s']);\n      }\n    }\n    return this;\n  }\n};\nexport { Fraction as default, Fraction };", "map": {"version": 3, "names": ["BigInt", "n", "isNaN", "Error", "C_ZERO", "C_ONE", "C_TWO", "C_FIVE", "C_TEN", "MAX_CYCLE_LEN", "P", "assign", "s", "e", "InvalidParameter", "trunc", "x", "Math", "floor", "newFraction", "d", "DivisionByZero", "f", "Object", "create", "Fraction", "prototype", "a", "gcd", "factorize", "num", "factors", "i", "parse", "p1", "p2", "undefined", "NonIntegerParameter", "z", "A", "B", "C", "D", "N", "log10", "M", "ndx", "v", "w", "y", "match", "replace", "length", "modpow", "b", "m", "r", "cycleLen", "rem", "t", "cycleStart", "len", "rem1", "rem2", "abs", "neg", "add", "sub", "mul", "div", "clone", "mod", "lcm", "inverse", "pow", "k", "log", "allPrimes", "baseFactors", "T1", "numberFactors", "T2", "prime", "retN", "retD", "baseExponent", "numberExponent", "curN", "curD", "gcdValue", "equals", "lt", "lte", "gt", "gte", "compare", "ceil", "places", "round", "roundTo", "divisible", "valueOf", "Number", "toString", "dec", "cycLen", "cycOff", "str", "toFraction", "showMixed", "whole", "toLatex", "toContinued", "res", "push", "simplify", "eps", "ieps", "thisABS", "cont", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/node_modules/fraction.js/dist/fraction.mjs"], "sourcesContent": ["'use strict';\n\n/**\n *\n * This class offers the possibility to calculate fractions.\n * You can pass a fraction in different formats. Either as array, as double, as string or as an integer.\n *\n * Array/Object form\n * [ 0 => <numerator>, 1 => <denominator> ]\n * { n => <numerator>, d => <denominator> }\n *\n * Integer form\n * - Single integer value as BigInt or Number\n *\n * Double form\n * - Single double value as Number\n *\n * String form\n * 123.456 - a simple double\n * 123/456 - a string fraction\n * 123.'456' - a double with repeating decimal places\n * 123.(456) - synonym\n * 123.45'6' - a double with repeating last place\n * 123.45(6) - synonym\n *\n * Example:\n * let f = new Fraction(\"9.4'31'\");\n * f.mul([-4, 3]).div(4.9);\n *\n */\n\n// Set Identity function to downgrade BigInt to Number if needed\nif (typeof BigInt === 'undefined') BigInt = function (n) { if (isNaN(n)) throw new Error(\"\"); return n; };\n\nconst C_ZERO = BigInt(0);\nconst C_ONE = BigInt(1);\nconst C_TWO = BigInt(2);\nconst C_FIVE = BigInt(5);\nconst C_TEN = BigInt(10);\n\n// Maximum search depth for cyclic rational numbers. 2000 should be more than enough.\n// Example: 1/7 = 0.(142857) has 6 repeating decimal places.\n// If MAX_CYCLE_LEN gets reduced, long cycles will not be detected and toString() only gets the first 10 digits\nconst MAX_CYCLE_LEN = 2000;\n\n// Parsed data to avoid calling \"new\" all the time\nconst P = {\n  \"s\": C_ONE,\n  \"n\": C_ZERO,\n  \"d\": C_ONE\n};\n\nfunction assign(n, s) {\n\n  try {\n    n = BigInt(n);\n  } catch (e) {\n    throw InvalidParameter();\n  }\n  return n * s;\n}\n\nfunction trunc(x) {\n  return typeof x === 'bigint' ? x : Math.floor(x);\n}\n\n// Creates a new Fraction internally without the need of the bulky constructor\nfunction newFraction(n, d) {\n\n  if (d === C_ZERO) {\n    throw DivisionByZero();\n  }\n\n  const f = Object.create(Fraction.prototype);\n  f[\"s\"] = n < C_ZERO ? -C_ONE : C_ONE;\n\n  n = n < C_ZERO ? -n : n;\n\n  const a = gcd(n, d);\n\n  f[\"n\"] = n / a;\n  f[\"d\"] = d / a;\n  return f;\n}\n\nfunction factorize(num) {\n\n  const factors = {};\n\n  let n = num;\n  let i = C_TWO;\n  let s = C_FIVE - C_ONE;\n\n  while (s <= n) {\n\n    while (n % i === C_ZERO) {\n      n /= i;\n      factors[i] = (factors[i] || C_ZERO) + C_ONE;\n    }\n    s += C_ONE + C_TWO * i++;\n  }\n\n  if (n !== num) {\n    if (n > 1)\n      factors[n] = (factors[n] || C_ZERO) + C_ONE;\n  } else {\n    factors[num] = (factors[num] || C_ZERO) + C_ONE;\n  }\n  return factors;\n}\n\nconst parse = function (p1, p2) {\n\n  let n = C_ZERO, d = C_ONE, s = C_ONE;\n\n  if (p1 === undefined || p1 === null) { // No argument\n    /* void */\n  } else if (p2 !== undefined) { // Two arguments\n\n    if (typeof p1 === \"bigint\") {\n      n = p1;\n    } else if (isNaN(p1)) {\n      throw InvalidParameter();\n    } else if (p1 % 1 !== 0) {\n      throw NonIntegerParameter();\n    } else {\n      n = BigInt(p1);\n    }\n\n    if (typeof p2 === \"bigint\") {\n      d = p2;\n    } else if (isNaN(p2)) {\n      throw InvalidParameter();\n    } else if (p2 % 1 !== 0) {\n      throw NonIntegerParameter();\n    } else {\n      d = BigInt(p2);\n    }\n\n    s = n * d;\n\n  } else if (typeof p1 === \"object\") {\n    if (\"d\" in p1 && \"n\" in p1) {\n      n = BigInt(p1[\"n\"]);\n      d = BigInt(p1[\"d\"]);\n      if (\"s\" in p1)\n        n *= BigInt(p1[\"s\"]);\n    } else if (0 in p1) {\n      n = BigInt(p1[0]);\n      if (1 in p1)\n        d = BigInt(p1[1]);\n    } else if (typeof p1 === \"bigint\") {\n      n = p1;\n    } else {\n      throw InvalidParameter();\n    }\n    s = n * d;\n  } else if (typeof p1 === \"number\") {\n\n    if (isNaN(p1)) {\n      throw InvalidParameter();\n    }\n\n    if (p1 < 0) {\n      s = -C_ONE;\n      p1 = -p1;\n    }\n\n    if (p1 % 1 === 0) {\n      n = BigInt(p1);\n    } else {\n\n      let z = 1;\n\n      let A = 0, B = 1;\n      let C = 1, D = 1;\n\n      let N = 10000000;\n\n      if (p1 >= 1) {\n        z = 10 ** Math.floor(1 + Math.log10(p1));\n        p1 /= z;\n      }\n\n      // Using Farey Sequences\n\n      while (B <= N && D <= N) {\n        let M = (A + C) / (B + D);\n\n        if (p1 === M) {\n          if (B + D <= N) {\n            n = A + C;\n            d = B + D;\n          } else if (D > B) {\n            n = C;\n            d = D;\n          } else {\n            n = A;\n            d = B;\n          }\n          break;\n\n        } else {\n\n          if (p1 > M) {\n            A += C;\n            B += D;\n          } else {\n            C += A;\n            D += B;\n          }\n\n          if (B > N) {\n            n = C;\n            d = D;\n          } else {\n            n = A;\n            d = B;\n          }\n        }\n      }\n      n = BigInt(n) * BigInt(z);\n      d = BigInt(d);\n    }\n\n  } else if (typeof p1 === \"string\") {\n\n    let ndx = 0;\n\n    let v = C_ZERO, w = C_ZERO, x = C_ZERO, y = C_ONE, z = C_ONE;\n\n    let match = p1.replace(/_/g, '').match(/\\d+|./g);\n\n    if (match === null)\n      throw InvalidParameter();\n\n    if (match[ndx] === '-') {// Check for minus sign at the beginning\n      s = -C_ONE;\n      ndx++;\n    } else if (match[ndx] === '+') {// Check for plus sign at the beginning\n      ndx++;\n    }\n\n    if (match.length === ndx + 1) { // Check if it's just a simple number \"1234\"\n      w = assign(match[ndx++], s);\n    } else if (match[ndx + 1] === '.' || match[ndx] === '.') { // Check if it's a decimal number\n\n      if (match[ndx] !== '.') { // Handle 0.5 and .5\n        v = assign(match[ndx++], s);\n      }\n      ndx++;\n\n      // Check for decimal places\n      if (ndx + 1 === match.length || match[ndx + 1] === '(' && match[ndx + 3] === ')' || match[ndx + 1] === \"'\" && match[ndx + 3] === \"'\") {\n        w = assign(match[ndx], s);\n        y = C_TEN ** BigInt(match[ndx].length);\n        ndx++;\n      }\n\n      // Check for repeating places\n      if (match[ndx] === '(' && match[ndx + 2] === ')' || match[ndx] === \"'\" && match[ndx + 2] === \"'\") {\n        x = assign(match[ndx + 1], s);\n        z = C_TEN ** BigInt(match[ndx + 1].length) - C_ONE;\n        ndx += 3;\n      }\n\n    } else if (match[ndx + 1] === '/' || match[ndx + 1] === ':') { // Check for a simple fraction \"123/456\" or \"123:456\"\n      w = assign(match[ndx], s);\n      y = assign(match[ndx + 2], C_ONE);\n      ndx += 3;\n    } else if (match[ndx + 3] === '/' && match[ndx + 1] === ' ') { // Check for a complex fraction \"123 1/2\"\n      v = assign(match[ndx], s);\n      w = assign(match[ndx + 2], s);\n      y = assign(match[ndx + 4], C_ONE);\n      ndx += 5;\n    }\n\n    if (match.length <= ndx) { // Check for more tokens on the stack\n      d = y * z;\n      s = /* void */\n        n = x + d * v + z * w;\n    } else {\n      throw InvalidParameter();\n    }\n\n  } else if (typeof p1 === \"bigint\") {\n    n = p1;\n    s = p1;\n    d = C_ONE;\n  } else {\n    throw InvalidParameter();\n  }\n\n  if (d === C_ZERO) {\n    throw DivisionByZero();\n  }\n\n  P[\"s\"] = s < C_ZERO ? -C_ONE : C_ONE;\n  P[\"n\"] = n < C_ZERO ? -n : n;\n  P[\"d\"] = d < C_ZERO ? -d : d;\n};\n\nfunction modpow(b, e, m) {\n\n  let r = C_ONE;\n  for (; e > C_ZERO; b = (b * b) % m, e >>= C_ONE) {\n\n    if (e & C_ONE) {\n      r = (r * b) % m;\n    }\n  }\n  return r;\n}\n\nfunction cycleLen(n, d) {\n\n  for (; d % C_TWO === C_ZERO;\n    d /= C_TWO) {\n  }\n\n  for (; d % C_FIVE === C_ZERO;\n    d /= C_FIVE) {\n  }\n\n  if (d === C_ONE) // Catch non-cyclic numbers\n    return C_ZERO;\n\n  // If we would like to compute really large numbers quicker, we could make use of Fermat's little theorem:\n  // 10^(d-1) % d == 1\n  // However, we don't need such large numbers and MAX_CYCLE_LEN should be the capstone,\n  // as we want to translate the numbers to strings.\n\n  let rem = C_TEN % d;\n  let t = 1;\n\n  for (; rem !== C_ONE; t++) {\n    rem = rem * C_TEN % d;\n\n    if (t > MAX_CYCLE_LEN)\n      return C_ZERO; // Returning 0 here means that we don't print it as a cyclic number. It's likely that the answer is `d-1`\n  }\n  return BigInt(t);\n}\n\nfunction cycleStart(n, d, len) {\n\n  let rem1 = C_ONE;\n  let rem2 = modpow(C_TEN, len, d);\n\n  for (let t = 0; t < 300; t++) { // s < ~log10(Number.MAX_VALUE)\n    // Solve 10^s == 10^(s+t) (mod d)\n\n    if (rem1 === rem2)\n      return BigInt(t);\n\n    rem1 = rem1 * C_TEN % d;\n    rem2 = rem2 * C_TEN % d;\n  }\n  return 0;\n}\n\nfunction gcd(a, b) {\n\n  if (!a)\n    return b;\n  if (!b)\n    return a;\n\n  while (1) {\n    a %= b;\n    if (!a)\n      return b;\n    b %= a;\n    if (!b)\n      return a;\n  }\n}\n\n/**\n * Module constructor\n *\n * @constructor\n * @param {number|Fraction=} a\n * @param {number=} b\n */\nfunction Fraction(a, b) {\n\n  parse(a, b);\n\n  if (this instanceof Fraction) {\n    a = gcd(P[\"d\"], P[\"n\"]); // Abuse a\n    this[\"s\"] = P[\"s\"];\n    this[\"n\"] = P[\"n\"] / a;\n    this[\"d\"] = P[\"d\"] / a;\n  } else {\n    return newFraction(P['s'] * P['n'], P['d']);\n  }\n}\n\nvar DivisionByZero = function () { return new Error(\"Division by Zero\"); };\nvar InvalidParameter = function () { return new Error(\"Invalid argument\"); };\nvar NonIntegerParameter = function () { return new Error(\"Parameters must be integer\"); };\n\nFraction.prototype = {\n\n  \"s\": C_ONE,\n  \"n\": C_ZERO,\n  \"d\": C_ONE,\n\n  /**\n   * Calculates the absolute value\n   *\n   * Ex: new Fraction(-4).abs() => 4\n   **/\n  \"abs\": function () {\n\n    return newFraction(this[\"n\"], this[\"d\"]);\n  },\n\n  /**\n   * Inverts the sign of the current fraction\n   *\n   * Ex: new Fraction(-4).neg() => 4\n   **/\n  \"neg\": function () {\n\n    return newFraction(-this[\"s\"] * this[\"n\"], this[\"d\"]);\n  },\n\n  /**\n   * Adds two rational numbers\n   *\n   * Ex: new Fraction({n: 2, d: 3}).add(\"14.9\") => 467 / 30\n   **/\n  \"add\": function (a, b) {\n\n    parse(a, b);\n    return newFraction(\n      this[\"s\"] * this[\"n\"] * P[\"d\"] + P[\"s\"] * this[\"d\"] * P[\"n\"],\n      this[\"d\"] * P[\"d\"]\n    );\n  },\n\n  /**\n   * Subtracts two rational numbers\n   *\n   * Ex: new Fraction({n: 2, d: 3}).add(\"14.9\") => -427 / 30\n   **/\n  \"sub\": function (a, b) {\n\n    parse(a, b);\n    return newFraction(\n      this[\"s\"] * this[\"n\"] * P[\"d\"] - P[\"s\"] * this[\"d\"] * P[\"n\"],\n      this[\"d\"] * P[\"d\"]\n    );\n  },\n\n  /**\n   * Multiplies two rational numbers\n   *\n   * Ex: new Fraction(\"-17.(345)\").mul(3) => 5776 / 111\n   **/\n  \"mul\": function (a, b) {\n\n    parse(a, b);\n    return newFraction(\n      this[\"s\"] * P[\"s\"] * this[\"n\"] * P[\"n\"],\n      this[\"d\"] * P[\"d\"]\n    );\n  },\n\n  /**\n   * Divides two rational numbers\n   *\n   * Ex: new Fraction(\"-17.(345)\").inverse().div(3)\n   **/\n  \"div\": function (a, b) {\n\n    parse(a, b);\n    return newFraction(\n      this[\"s\"] * P[\"s\"] * this[\"n\"] * P[\"d\"],\n      this[\"d\"] * P[\"n\"]\n    );\n  },\n\n  /**\n   * Clones the actual object\n   *\n   * Ex: new Fraction(\"-17.(345)\").clone()\n   **/\n  \"clone\": function () {\n    return newFraction(this['s'] * this['n'], this['d']);\n  },\n\n  /**\n   * Calculates the modulo of two rational numbers - a more precise fmod\n   *\n   * Ex: new Fraction('4.(3)').mod([7, 8]) => (13/3) % (7/8) = (5/6)\n   * Ex: new Fraction(20, 10).mod().equals(0) ? \"is Integer\"\n   **/\n  \"mod\": function (a, b) {\n\n    if (a === undefined) {\n      return newFraction(this[\"s\"] * this[\"n\"] % this[\"d\"], C_ONE);\n    }\n\n    parse(a, b);\n    if (C_ZERO === P[\"n\"] * this[\"d\"]) {\n      throw DivisionByZero();\n    }\n\n    /**\n     * I derived the rational modulo similar to the modulo for integers\n     *\n     * https://raw.org/book/analysis/rational-numbers/\n     *\n     *    n1/d1 = (n2/d2) * q + r, where 0 ≤ r < n2/d2\n     * => d2 * n1 = n2 * d1 * q + d1 * d2 * r\n     * => r = (d2 * n1 - n2 * d1 * q) / (d1 * d2)\n     *      = (d2 * n1 - n2 * d1 * floor((d2 * n1) / (n2 * d1))) / (d1 * d2)\n     *      = ((d2 * n1) % (n2 * d1)) / (d1 * d2)\n     */\n    return newFraction(\n      this[\"s\"] * (P[\"d\"] * this[\"n\"]) % (P[\"n\"] * this[\"d\"]),\n      P[\"d\"] * this[\"d\"]);\n  },\n\n  /**\n   * Calculates the fractional gcd of two rational numbers\n   *\n   * Ex: new Fraction(5,8).gcd(3,7) => 1/56\n   */\n  \"gcd\": function (a, b) {\n\n    parse(a, b);\n\n    // https://raw.org/book/analysis/rational-numbers/\n    // gcd(a / b, c / d) = gcd(a, c) / lcm(b, d)\n\n    return newFraction(gcd(P[\"n\"], this[\"n\"]) * gcd(P[\"d\"], this[\"d\"]), P[\"d\"] * this[\"d\"]);\n  },\n\n  /**\n   * Calculates the fractional lcm of two rational numbers\n   *\n   * Ex: new Fraction(5,8).lcm(3,7) => 15\n   */\n  \"lcm\": function (a, b) {\n\n    parse(a, b);\n\n    // https://raw.org/book/analysis/rational-numbers/\n    // lcm(a / b, c / d) = lcm(a, c) / gcd(b, d)\n\n    if (P[\"n\"] === C_ZERO && this[\"n\"] === C_ZERO) {\n      return newFraction(C_ZERO, C_ONE);\n    }\n    return newFraction(P[\"n\"] * this[\"n\"], gcd(P[\"n\"], this[\"n\"]) * gcd(P[\"d\"], this[\"d\"]));\n  },\n\n  /**\n   * Gets the inverse of the fraction, means numerator and denominator are exchanged\n   *\n   * Ex: new Fraction([-3, 4]).inverse() => -4 / 3\n   **/\n  \"inverse\": function () {\n    return newFraction(this[\"s\"] * this[\"d\"], this[\"n\"]);\n  },\n\n  /**\n   * Calculates the fraction to some integer exponent\n   *\n   * Ex: new Fraction(-1,2).pow(-3) => -8\n   */\n  \"pow\": function (a, b) {\n\n    parse(a, b);\n\n    // Trivial case when exp is an integer\n\n    if (P['d'] === C_ONE) {\n\n      if (P['s'] < C_ZERO) {\n        return newFraction((this['s'] * this[\"d\"]) ** P['n'], this[\"n\"] ** P['n']);\n      } else {\n        return newFraction((this['s'] * this[\"n\"]) ** P['n'], this[\"d\"] ** P['n']);\n      }\n    }\n\n    // Negative roots become complex\n    //     (-a/b)^(c/d) = x\n    // ⇔ (-1)^(c/d) * (a/b)^(c/d) = x\n    // ⇔ (cos(pi) + i*sin(pi))^(c/d) * (a/b)^(c/d) = x\n    // ⇔ (cos(c*pi/d) + i*sin(c*pi/d)) * (a/b)^(c/d) = x       # DeMoivre's formula\n    // From which follows that only for c=0 the root is non-complex\n    if (this['s'] < C_ZERO) return null;\n\n    // Now prime factor n and d\n    let N = factorize(this['n']);\n    let D = factorize(this['d']);\n\n    // Exponentiate and take root for n and d individually\n    let n = C_ONE;\n    let d = C_ONE;\n    for (let k in N) {\n      if (k === '1') continue;\n      if (k === '0') {\n        n = C_ZERO;\n        break;\n      }\n      N[k] *= P['n'];\n\n      if (N[k] % P['d'] === C_ZERO) {\n        N[k] /= P['d'];\n      } else return null;\n      n *= BigInt(k) ** N[k];\n    }\n\n    for (let k in D) {\n      if (k === '1') continue;\n      D[k] *= P['n'];\n\n      if (D[k] % P['d'] === C_ZERO) {\n        D[k] /= P['d'];\n      } else return null;\n      d *= BigInt(k) ** D[k];\n    }\n\n    if (P['s'] < C_ZERO) {\n      return newFraction(d, n);\n    }\n    return newFraction(n, d);\n  },\n\n  /**\n   * Calculates the logarithm of a fraction to a given rational base\n   *\n   * Ex: new Fraction(27, 8).log(9, 4) => 3/2\n   */\n  \"log\": function (a, b) {\n\n    parse(a, b);\n\n    if (this['s'] <= C_ZERO || P['s'] <= C_ZERO) return null;\n\n    const allPrimes = {};\n\n    const baseFactors = factorize(P['n']);\n    const T1 = factorize(P['d']);\n\n    const numberFactors = factorize(this['n']);\n    const T2 = factorize(this['d']);\n\n    for (const prime in T1) {\n      baseFactors[prime] = (baseFactors[prime] || C_ZERO) - T1[prime];\n    }\n    for (const prime in T2) {\n      numberFactors[prime] = (numberFactors[prime] || C_ZERO) - T2[prime];\n    }\n\n    for (const prime in baseFactors) {\n      if (prime === '1') continue;\n      allPrimes[prime] = true;\n    }\n    for (const prime in numberFactors) {\n      if (prime === '1') continue;\n      allPrimes[prime] = true;\n    }\n\n    let retN = null;\n    let retD = null;\n\n    // Iterate over all unique primes to determine if a consistent ratio exists\n    for (const prime in allPrimes) {\n\n      const baseExponent = baseFactors[prime] || C_ZERO;\n      const numberExponent = numberFactors[prime] || C_ZERO;\n\n      if (baseExponent === C_ZERO) {\n        if (numberExponent !== C_ZERO) {\n          return null; // Logarithm cannot be expressed as a rational number\n        }\n        continue; // Skip this prime since both exponents are zero\n      }\n\n      // Calculate the ratio of exponents for this prime\n      let curN = numberExponent;\n      let curD = baseExponent;\n\n      // Simplify the current ratio\n      const gcdValue = gcd(curN, curD);\n      curN /= gcdValue;\n      curD /= gcdValue;\n\n      // Check if this is the first ratio; otherwise, ensure ratios are consistent\n      if (retN === null && retD === null) {\n        retN = curN;\n        retD = curD;\n      } else if (curN * retD !== retN * curD) {\n        return null; // Ratios do not match, logarithm cannot be rational\n      }\n    }\n\n    return retN !== null && retD !== null\n      ? newFraction(retN, retD)\n      : null;\n  },\n\n  /**\n   * Check if two rational numbers are the same\n   *\n   * Ex: new Fraction(19.6).equals([98, 5]);\n   **/\n  \"equals\": function (a, b) {\n\n    parse(a, b);\n    return this[\"s\"] * this[\"n\"] * P[\"d\"] === P[\"s\"] * P[\"n\"] * this[\"d\"];\n  },\n\n  /**\n   * Check if this rational number is less than another\n   *\n   * Ex: new Fraction(19.6).lt([98, 5]);\n   **/\n  \"lt\": function (a, b) {\n\n    parse(a, b);\n    return this[\"s\"] * this[\"n\"] * P[\"d\"] < P[\"s\"] * P[\"n\"] * this[\"d\"];\n  },\n\n  /**\n   * Check if this rational number is less than or equal another\n   *\n   * Ex: new Fraction(19.6).lt([98, 5]);\n   **/\n  \"lte\": function (a, b) {\n\n    parse(a, b);\n    return this[\"s\"] * this[\"n\"] * P[\"d\"] <= P[\"s\"] * P[\"n\"] * this[\"d\"];\n  },\n\n  /**\n   * Check if this rational number is greater than another\n   *\n   * Ex: new Fraction(19.6).lt([98, 5]);\n   **/\n  \"gt\": function (a, b) {\n\n    parse(a, b);\n    return this[\"s\"] * this[\"n\"] * P[\"d\"] > P[\"s\"] * P[\"n\"] * this[\"d\"];\n  },\n\n  /**\n   * Check if this rational number is greater than or equal another\n   *\n   * Ex: new Fraction(19.6).lt([98, 5]);\n   **/\n  \"gte\": function (a, b) {\n\n    parse(a, b);\n    return this[\"s\"] * this[\"n\"] * P[\"d\"] >= P[\"s\"] * P[\"n\"] * this[\"d\"];\n  },\n\n  /**\n   * Compare two rational numbers\n   * < 0 iff this < that\n   * > 0 iff this > that\n   * = 0 iff this = that\n   *\n   * Ex: new Fraction(19.6).compare([98, 5]);\n   **/\n  \"compare\": function (a, b) {\n\n    parse(a, b);\n    let t = this[\"s\"] * this[\"n\"] * P[\"d\"] - P[\"s\"] * P[\"n\"] * this[\"d\"];\n\n    return (C_ZERO < t) - (t < C_ZERO);\n  },\n\n  /**\n   * Calculates the ceil of a rational number\n   *\n   * Ex: new Fraction('4.(3)').ceil() => (5 / 1)\n   **/\n  \"ceil\": function (places) {\n\n    places = C_TEN ** BigInt(places || 0);\n\n    return newFraction(trunc(this[\"s\"] * places * this[\"n\"] / this[\"d\"]) +\n      (places * this[\"n\"] % this[\"d\"] > C_ZERO && this[\"s\"] >= C_ZERO ? C_ONE : C_ZERO),\n      places);\n  },\n\n  /**\n   * Calculates the floor of a rational number\n   *\n   * Ex: new Fraction('4.(3)').floor() => (4 / 1)\n   **/\n  \"floor\": function (places) {\n\n    places = C_TEN ** BigInt(places || 0);\n\n    return newFraction(trunc(this[\"s\"] * places * this[\"n\"] / this[\"d\"]) -\n      (places * this[\"n\"] % this[\"d\"] > C_ZERO && this[\"s\"] < C_ZERO ? C_ONE : C_ZERO),\n      places);\n  },\n\n  /**\n   * Rounds a rational numbers\n   *\n   * Ex: new Fraction('4.(3)').round() => (4 / 1)\n   **/\n  \"round\": function (places) {\n\n    places = C_TEN ** BigInt(places || 0);\n\n    /* Derivation:\n\n    s >= 0:\n      round(n / d) = trunc(n / d) + (n % d) / d >= 0.5 ? 1 : 0\n                   = trunc(n / d) + 2(n % d) >= d ? 1 : 0\n    s < 0:\n      round(n / d) =-trunc(n / d) - (n % d) / d > 0.5 ? 1 : 0\n                   =-trunc(n / d) - 2(n % d) > d ? 1 : 0\n\n    =>:\n\n    round(s * n / d) = s * trunc(n / d) + s * (C + 2(n % d) > d ? 1 : 0)\n        where C = s >= 0 ? 1 : 0, to fix the >= for the positve case.\n    */\n\n    return newFraction(trunc(this[\"s\"] * places * this[\"n\"] / this[\"d\"]) +\n      this[\"s\"] * ((this[\"s\"] >= C_ZERO ? C_ONE : C_ZERO) + C_TWO * (places * this[\"n\"] % this[\"d\"]) > this[\"d\"] ? C_ONE : C_ZERO),\n      places);\n  },\n\n  /**\n    * Rounds a rational number to a multiple of another rational number\n    *\n    * Ex: new Fraction('0.9').roundTo(\"1/8\") => 7 / 8\n    **/\n  \"roundTo\": function (a, b) {\n\n    /*\n    k * x/y ≤ a/b < (k+1) * x/y\n    ⇔ k ≤ a/b / (x/y) < (k+1)\n    ⇔ k = floor(a/b * y/x)\n    ⇔ k = floor((a * y) / (b * x))\n    */\n\n    parse(a, b);\n\n    const n = this['n'] * P['d'];\n    const d = this['d'] * P['n'];\n    const r = n % d;\n\n    // round(n / d) = trunc(n / d) + 2(n % d) >= d ? 1 : 0\n    let k = trunc(n / d);\n    if (r + r >= d) {\n      k++;\n    }\n    return newFraction(this['s'] * k * P['n'], P['d']);\n  },\n\n  /**\n   * Check if two rational numbers are divisible\n   *\n   * Ex: new Fraction(19.6).divisible(1.5);\n   */\n  \"divisible\": function (a, b) {\n\n    parse(a, b);\n    return !(!(P[\"n\"] * this[\"d\"]) || ((this[\"n\"] * P[\"d\"]) % (P[\"n\"] * this[\"d\"])));\n  },\n\n  /**\n   * Returns a decimal representation of the fraction\n   *\n   * Ex: new Fraction(\"100.'91823'\").valueOf() => 100.91823918239183\n   **/\n  'valueOf': function () {\n    // Best we can do so far\n    return Number(this[\"s\"] * this[\"n\"]) / Number(this[\"d\"]);\n  },\n\n  /**\n   * Creates a string representation of a fraction with all digits\n   *\n   * Ex: new Fraction(\"100.'91823'\").toString() => \"100.(91823)\"\n   **/\n  'toString': function (dec) {\n\n    let N = this[\"n\"];\n    let D = this[\"d\"];\n\n    dec = dec || 15; // 15 = decimal places when no repetition\n\n    let cycLen = cycleLen(N, D); // Cycle length\n    let cycOff = cycleStart(N, D, cycLen); // Cycle start\n\n    let str = this['s'] < C_ZERO ? \"-\" : \"\";\n\n    // Append integer part\n    str += trunc(N / D);\n\n    N %= D;\n    N *= C_TEN;\n\n    if (N)\n      str += \".\";\n\n    if (cycLen) {\n\n      for (let i = cycOff; i--;) {\n        str += trunc(N / D);\n        N %= D;\n        N *= C_TEN;\n      }\n      str += \"(\";\n      for (let i = cycLen; i--;) {\n        str += trunc(N / D);\n        N %= D;\n        N *= C_TEN;\n      }\n      str += \")\";\n    } else {\n      for (let i = dec; N && i--;) {\n        str += trunc(N / D);\n        N %= D;\n        N *= C_TEN;\n      }\n    }\n    return str;\n  },\n\n  /**\n   * Returns a string-fraction representation of a Fraction object\n   *\n   * Ex: new Fraction(\"1.'3'\").toFraction() => \"4 1/3\"\n   **/\n  'toFraction': function (showMixed) {\n\n    let n = this[\"n\"];\n    let d = this[\"d\"];\n    let str = this['s'] < C_ZERO ? \"-\" : \"\";\n\n    if (d === C_ONE) {\n      str += n;\n    } else {\n      let whole = trunc(n / d);\n      if (showMixed && whole > C_ZERO) {\n        str += whole;\n        str += \" \";\n        n %= d;\n      }\n\n      str += n;\n      str += '/';\n      str += d;\n    }\n    return str;\n  },\n\n  /**\n   * Returns a latex representation of a Fraction object\n   *\n   * Ex: new Fraction(\"1.'3'\").toLatex() => \"\\frac{4}{3}\"\n   **/\n  'toLatex': function (showMixed) {\n\n    let n = this[\"n\"];\n    let d = this[\"d\"];\n    let str = this['s'] < C_ZERO ? \"-\" : \"\";\n\n    if (d === C_ONE) {\n      str += n;\n    } else {\n      let whole = trunc(n / d);\n      if (showMixed && whole > C_ZERO) {\n        str += whole;\n        n %= d;\n      }\n\n      str += \"\\\\frac{\";\n      str += n;\n      str += '}{';\n      str += d;\n      str += '}';\n    }\n    return str;\n  },\n\n  /**\n   * Returns an array of continued fraction elements\n   *\n   * Ex: new Fraction(\"7/8\").toContinued() => [0,1,7]\n   */\n  'toContinued': function () {\n\n    let a = this['n'];\n    let b = this['d'];\n    let res = [];\n\n    do {\n      res.push(trunc(a / b));\n      let t = a % b;\n      a = b;\n      b = t;\n    } while (a !== C_ONE);\n\n    return res;\n  },\n\n  \"simplify\": function (eps) {\n\n    const ieps = BigInt(1 / (eps || 0.001) | 0);\n\n    const thisABS = this['abs']();\n    const cont = thisABS['toContinued']();\n\n    for (let i = 1; i < cont.length; i++) {\n\n      let s = newFraction(cont[i - 1], C_ONE);\n      for (let k = i - 2; k >= 0; k--) {\n        s = s['inverse']()['add'](cont[k]);\n      }\n\n      let t = s['sub'](thisABS);\n      if (t['n'] * ieps < t['d']) { // More robust than Math.abs(t.valueOf()) < eps\n        return s['mul'](this['s']);\n      }\n    }\n    return this;\n  }\n};\nexport {\n  Fraction as default, Fraction\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI,OAAOA,MAAM,KAAK,WAAW,EAAEA,MAAM,GAAG,SAAAA,CAAUC,CAAC,EAAE;EAAE,IAAIC,KAAK,CAACD,CAAC,CAAC,EAAE,MAAM,IAAIE,KAAK,CAAC,EAAE,CAAC;EAAE,OAAOF,CAAC;AAAE,CAAC;AAEzG,MAAMG,MAAM,GAAGJ,MAAM,CAAC,CAAC,CAAC;AACxB,MAAMK,KAAK,GAAGL,MAAM,CAAC,CAAC,CAAC;AACvB,MAAMM,KAAK,GAAGN,MAAM,CAAC,CAAC,CAAC;AACvB,MAAMO,MAAM,GAAGP,MAAM,CAAC,CAAC,CAAC;AACxB,MAAMQ,KAAK,GAAGR,MAAM,CAAC,EAAE,CAAC;;AAExB;AACA;AACA;AACA,MAAMS,aAAa,GAAG,IAAI;;AAE1B;AACA,MAAMC,CAAC,GAAG;EACR,GAAG,EAAEL,KAAK;EACV,GAAG,EAAED,MAAM;EACX,GAAG,EAAEC;AACP,CAAC;AAED,SAASM,MAAMA,CAACV,CAAC,EAAEW,CAAC,EAAE;EAEpB,IAAI;IACFX,CAAC,GAAGD,MAAM,CAACC,CAAC,CAAC;EACf,CAAC,CAAC,OAAOY,CAAC,EAAE;IACV,MAAMC,gBAAgB,CAAC,CAAC;EAC1B;EACA,OAAOb,CAAC,GAAGW,CAAC;AACd;AAEA,SAASG,KAAKA,CAACC,CAAC,EAAE;EAChB,OAAO,OAAOA,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACF,CAAC,CAAC;AAClD;;AAEA;AACA,SAASG,WAAWA,CAAClB,CAAC,EAAEmB,CAAC,EAAE;EAEzB,IAAIA,CAAC,KAAKhB,MAAM,EAAE;IAChB,MAAMiB,cAAc,CAAC,CAAC;EACxB;EAEA,MAAMC,CAAC,GAAGC,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,SAAS,CAAC;EAC3CJ,CAAC,CAAC,GAAG,CAAC,GAAGrB,CAAC,GAAGG,MAAM,GAAG,CAACC,KAAK,GAAGA,KAAK;EAEpCJ,CAAC,GAAGA,CAAC,GAAGG,MAAM,GAAG,CAACH,CAAC,GAAGA,CAAC;EAEvB,MAAM0B,CAAC,GAAGC,GAAG,CAAC3B,CAAC,EAAEmB,CAAC,CAAC;EAEnBE,CAAC,CAAC,GAAG,CAAC,GAAGrB,CAAC,GAAG0B,CAAC;EACdL,CAAC,CAAC,GAAG,CAAC,GAAGF,CAAC,GAAGO,CAAC;EACd,OAAOL,CAAC;AACV;AAEA,SAASO,SAASA,CAACC,GAAG,EAAE;EAEtB,MAAMC,OAAO,GAAG,CAAC,CAAC;EAElB,IAAI9B,CAAC,GAAG6B,GAAG;EACX,IAAIE,CAAC,GAAG1B,KAAK;EACb,IAAIM,CAAC,GAAGL,MAAM,GAAGF,KAAK;EAEtB,OAAOO,CAAC,IAAIX,CAAC,EAAE;IAEb,OAAOA,CAAC,GAAG+B,CAAC,KAAK5B,MAAM,EAAE;MACvBH,CAAC,IAAI+B,CAAC;MACND,OAAO,CAACC,CAAC,CAAC,GAAG,CAACD,OAAO,CAACC,CAAC,CAAC,IAAI5B,MAAM,IAAIC,KAAK;IAC7C;IACAO,CAAC,IAAIP,KAAK,GAAGC,KAAK,GAAG0B,CAAC,EAAE;EAC1B;EAEA,IAAI/B,CAAC,KAAK6B,GAAG,EAAE;IACb,IAAI7B,CAAC,GAAG,CAAC,EACP8B,OAAO,CAAC9B,CAAC,CAAC,GAAG,CAAC8B,OAAO,CAAC9B,CAAC,CAAC,IAAIG,MAAM,IAAIC,KAAK;EAC/C,CAAC,MAAM;IACL0B,OAAO,CAACD,GAAG,CAAC,GAAG,CAACC,OAAO,CAACD,GAAG,CAAC,IAAI1B,MAAM,IAAIC,KAAK;EACjD;EACA,OAAO0B,OAAO;AAChB;AAEA,MAAME,KAAK,GAAG,SAAAA,CAAUC,EAAE,EAAEC,EAAE,EAAE;EAE9B,IAAIlC,CAAC,GAAGG,MAAM;IAAEgB,CAAC,GAAGf,KAAK;IAAEO,CAAC,GAAGP,KAAK;EAEpC,IAAI6B,EAAE,KAAKE,SAAS,IAAIF,EAAE,KAAK,IAAI,EAAE,CAAE;IACrC;EAAA,CACD,MAAM,IAAIC,EAAE,KAAKC,SAAS,EAAE;IAAE;;IAE7B,IAAI,OAAOF,EAAE,KAAK,QAAQ,EAAE;MAC1BjC,CAAC,GAAGiC,EAAE;IACR,CAAC,MAAM,IAAIhC,KAAK,CAACgC,EAAE,CAAC,EAAE;MACpB,MAAMpB,gBAAgB,CAAC,CAAC;IAC1B,CAAC,MAAM,IAAIoB,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE;MACvB,MAAMG,mBAAmB,CAAC,CAAC;IAC7B,CAAC,MAAM;MACLpC,CAAC,GAAGD,MAAM,CAACkC,EAAE,CAAC;IAChB;IAEA,IAAI,OAAOC,EAAE,KAAK,QAAQ,EAAE;MAC1Bf,CAAC,GAAGe,EAAE;IACR,CAAC,MAAM,IAAIjC,KAAK,CAACiC,EAAE,CAAC,EAAE;MACpB,MAAMrB,gBAAgB,CAAC,CAAC;IAC1B,CAAC,MAAM,IAAIqB,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE;MACvB,MAAME,mBAAmB,CAAC,CAAC;IAC7B,CAAC,MAAM;MACLjB,CAAC,GAAGpB,MAAM,CAACmC,EAAE,CAAC;IAChB;IAEAvB,CAAC,GAAGX,CAAC,GAAGmB,CAAC;EAEX,CAAC,MAAM,IAAI,OAAOc,EAAE,KAAK,QAAQ,EAAE;IACjC,IAAI,GAAG,IAAIA,EAAE,IAAI,GAAG,IAAIA,EAAE,EAAE;MAC1BjC,CAAC,GAAGD,MAAM,CAACkC,EAAE,CAAC,GAAG,CAAC,CAAC;MACnBd,CAAC,GAAGpB,MAAM,CAACkC,EAAE,CAAC,GAAG,CAAC,CAAC;MACnB,IAAI,GAAG,IAAIA,EAAE,EACXjC,CAAC,IAAID,MAAM,CAACkC,EAAE,CAAC,GAAG,CAAC,CAAC;IACxB,CAAC,MAAM,IAAI,CAAC,IAAIA,EAAE,EAAE;MAClBjC,CAAC,GAAGD,MAAM,CAACkC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,IAAI,CAAC,IAAIA,EAAE,EACTd,CAAC,GAAGpB,MAAM,CAACkC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,MAAM,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;MACjCjC,CAAC,GAAGiC,EAAE;IACR,CAAC,MAAM;MACL,MAAMpB,gBAAgB,CAAC,CAAC;IAC1B;IACAF,CAAC,GAAGX,CAAC,GAAGmB,CAAC;EACX,CAAC,MAAM,IAAI,OAAOc,EAAE,KAAK,QAAQ,EAAE;IAEjC,IAAIhC,KAAK,CAACgC,EAAE,CAAC,EAAE;MACb,MAAMpB,gBAAgB,CAAC,CAAC;IAC1B;IAEA,IAAIoB,EAAE,GAAG,CAAC,EAAE;MACVtB,CAAC,GAAG,CAACP,KAAK;MACV6B,EAAE,GAAG,CAACA,EAAE;IACV;IAEA,IAAIA,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE;MAChBjC,CAAC,GAAGD,MAAM,CAACkC,EAAE,CAAC;IAChB,CAAC,MAAM;MAEL,IAAII,CAAC,GAAG,CAAC;MAET,IAAIC,CAAC,GAAG,CAAC;QAAEC,CAAC,GAAG,CAAC;MAChB,IAAIC,CAAC,GAAG,CAAC;QAAEC,CAAC,GAAG,CAAC;MAEhB,IAAIC,CAAC,GAAG,QAAQ;MAEhB,IAAIT,EAAE,IAAI,CAAC,EAAE;QACXI,CAAC,GAAG,EAAE,IAAIrB,IAAI,CAACC,KAAK,CAAC,CAAC,GAAGD,IAAI,CAAC2B,KAAK,CAACV,EAAE,CAAC,CAAC;QACxCA,EAAE,IAAII,CAAC;MACT;;MAEA;;MAEA,OAAOE,CAAC,IAAIG,CAAC,IAAID,CAAC,IAAIC,CAAC,EAAE;QACvB,IAAIE,CAAC,GAAG,CAACN,CAAC,GAAGE,CAAC,KAAKD,CAAC,GAAGE,CAAC,CAAC;QAEzB,IAAIR,EAAE,KAAKW,CAAC,EAAE;UACZ,IAAIL,CAAC,GAAGE,CAAC,IAAIC,CAAC,EAAE;YACd1C,CAAC,GAAGsC,CAAC,GAAGE,CAAC;YACTrB,CAAC,GAAGoB,CAAC,GAAGE,CAAC;UACX,CAAC,MAAM,IAAIA,CAAC,GAAGF,CAAC,EAAE;YAChBvC,CAAC,GAAGwC,CAAC;YACLrB,CAAC,GAAGsB,CAAC;UACP,CAAC,MAAM;YACLzC,CAAC,GAAGsC,CAAC;YACLnB,CAAC,GAAGoB,CAAC;UACP;UACA;QAEF,CAAC,MAAM;UAEL,IAAIN,EAAE,GAAGW,CAAC,EAAE;YACVN,CAAC,IAAIE,CAAC;YACND,CAAC,IAAIE,CAAC;UACR,CAAC,MAAM;YACLD,CAAC,IAAIF,CAAC;YACNG,CAAC,IAAIF,CAAC;UACR;UAEA,IAAIA,CAAC,GAAGG,CAAC,EAAE;YACT1C,CAAC,GAAGwC,CAAC;YACLrB,CAAC,GAAGsB,CAAC;UACP,CAAC,MAAM;YACLzC,CAAC,GAAGsC,CAAC;YACLnB,CAAC,GAAGoB,CAAC;UACP;QACF;MACF;MACAvC,CAAC,GAAGD,MAAM,CAACC,CAAC,CAAC,GAAGD,MAAM,CAACsC,CAAC,CAAC;MACzBlB,CAAC,GAAGpB,MAAM,CAACoB,CAAC,CAAC;IACf;EAEF,CAAC,MAAM,IAAI,OAAOc,EAAE,KAAK,QAAQ,EAAE;IAEjC,IAAIY,GAAG,GAAG,CAAC;IAEX,IAAIC,CAAC,GAAG3C,MAAM;MAAE4C,CAAC,GAAG5C,MAAM;MAAEY,CAAC,GAAGZ,MAAM;MAAE6C,CAAC,GAAG5C,KAAK;MAAEiC,CAAC,GAAGjC,KAAK;IAE5D,IAAI6C,KAAK,GAAGhB,EAAE,CAACiB,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACD,KAAK,CAAC,QAAQ,CAAC;IAEhD,IAAIA,KAAK,KAAK,IAAI,EAChB,MAAMpC,gBAAgB,CAAC,CAAC;IAE1B,IAAIoC,KAAK,CAACJ,GAAG,CAAC,KAAK,GAAG,EAAE;MAAC;MACvBlC,CAAC,GAAG,CAACP,KAAK;MACVyC,GAAG,EAAE;IACP,CAAC,MAAM,IAAII,KAAK,CAACJ,GAAG,CAAC,KAAK,GAAG,EAAE;MAAC;MAC9BA,GAAG,EAAE;IACP;IAEA,IAAII,KAAK,CAACE,MAAM,KAAKN,GAAG,GAAG,CAAC,EAAE;MAAE;MAC9BE,CAAC,GAAGrC,MAAM,CAACuC,KAAK,CAACJ,GAAG,EAAE,CAAC,EAAElC,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIsC,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,IAAII,KAAK,CAACJ,GAAG,CAAC,KAAK,GAAG,EAAE;MAAE;;MAEzD,IAAII,KAAK,CAACJ,GAAG,CAAC,KAAK,GAAG,EAAE;QAAE;QACxBC,CAAC,GAAGpC,MAAM,CAACuC,KAAK,CAACJ,GAAG,EAAE,CAAC,EAAElC,CAAC,CAAC;MAC7B;MACAkC,GAAG,EAAE;;MAEL;MACA,IAAIA,GAAG,GAAG,CAAC,KAAKI,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,IAAII,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,IAAII,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,IAAII,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;QACpIE,CAAC,GAAGrC,MAAM,CAACuC,KAAK,CAACJ,GAAG,CAAC,EAAElC,CAAC,CAAC;QACzBqC,CAAC,GAAGzC,KAAK,IAAIR,MAAM,CAACkD,KAAK,CAACJ,GAAG,CAAC,CAACM,MAAM,CAAC;QACtCN,GAAG,EAAE;MACP;;MAEA;MACA,IAAII,KAAK,CAACJ,GAAG,CAAC,KAAK,GAAG,IAAII,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,IAAII,KAAK,CAACJ,GAAG,CAAC,KAAK,GAAG,IAAII,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;QAChG9B,CAAC,GAAGL,MAAM,CAACuC,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,EAAElC,CAAC,CAAC;QAC7B0B,CAAC,GAAG9B,KAAK,IAAIR,MAAM,CAACkD,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,CAACM,MAAM,CAAC,GAAG/C,KAAK;QAClDyC,GAAG,IAAI,CAAC;MACV;IAEF,CAAC,MAAM,IAAII,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,IAAII,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;MAAE;MAC7DE,CAAC,GAAGrC,MAAM,CAACuC,KAAK,CAACJ,GAAG,CAAC,EAAElC,CAAC,CAAC;MACzBqC,CAAC,GAAGtC,MAAM,CAACuC,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,EAAEzC,KAAK,CAAC;MACjCyC,GAAG,IAAI,CAAC;IACV,CAAC,MAAM,IAAII,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,IAAII,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;MAAE;MAC7DC,CAAC,GAAGpC,MAAM,CAACuC,KAAK,CAACJ,GAAG,CAAC,EAAElC,CAAC,CAAC;MACzBoC,CAAC,GAAGrC,MAAM,CAACuC,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,EAAElC,CAAC,CAAC;MAC7BqC,CAAC,GAAGtC,MAAM,CAACuC,KAAK,CAACJ,GAAG,GAAG,CAAC,CAAC,EAAEzC,KAAK,CAAC;MACjCyC,GAAG,IAAI,CAAC;IACV;IAEA,IAAII,KAAK,CAACE,MAAM,IAAIN,GAAG,EAAE;MAAE;MACzB1B,CAAC,GAAG6B,CAAC,GAAGX,CAAC;MACT1B,CAAC,GAAG;MACFX,CAAC,GAAGe,CAAC,GAAGI,CAAC,GAAG2B,CAAC,GAAGT,CAAC,GAAGU,CAAC;IACzB,CAAC,MAAM;MACL,MAAMlC,gBAAgB,CAAC,CAAC;IAC1B;EAEF,CAAC,MAAM,IAAI,OAAOoB,EAAE,KAAK,QAAQ,EAAE;IACjCjC,CAAC,GAAGiC,EAAE;IACNtB,CAAC,GAAGsB,EAAE;IACNd,CAAC,GAAGf,KAAK;EACX,CAAC,MAAM;IACL,MAAMS,gBAAgB,CAAC,CAAC;EAC1B;EAEA,IAAIM,CAAC,KAAKhB,MAAM,EAAE;IAChB,MAAMiB,cAAc,CAAC,CAAC;EACxB;EAEAX,CAAC,CAAC,GAAG,CAAC,GAAGE,CAAC,GAAGR,MAAM,GAAG,CAACC,KAAK,GAAGA,KAAK;EACpCK,CAAC,CAAC,GAAG,CAAC,GAAGT,CAAC,GAAGG,MAAM,GAAG,CAACH,CAAC,GAAGA,CAAC;EAC5BS,CAAC,CAAC,GAAG,CAAC,GAAGU,CAAC,GAAGhB,MAAM,GAAG,CAACgB,CAAC,GAAGA,CAAC;AAC9B,CAAC;AAED,SAASiC,MAAMA,CAACC,CAAC,EAAEzC,CAAC,EAAE0C,CAAC,EAAE;EAEvB,IAAIC,CAAC,GAAGnD,KAAK;EACb,OAAOQ,CAAC,GAAGT,MAAM,EAAEkD,CAAC,GAAIA,CAAC,GAAGA,CAAC,GAAIC,CAAC,EAAE1C,CAAC,KAAKR,KAAK,EAAE;IAE/C,IAAIQ,CAAC,GAAGR,KAAK,EAAE;MACbmD,CAAC,GAAIA,CAAC,GAAGF,CAAC,GAAIC,CAAC;IACjB;EACF;EACA,OAAOC,CAAC;AACV;AAEA,SAASC,QAAQA,CAACxD,CAAC,EAAEmB,CAAC,EAAE;EAEtB,OAAOA,CAAC,GAAGd,KAAK,KAAKF,MAAM,EACzBgB,CAAC,IAAId,KAAK,EAAE,CACd;EAEA,OAAOc,CAAC,GAAGb,MAAM,KAAKH,MAAM,EAC1BgB,CAAC,IAAIb,MAAM,EAAE,CACf;EAEA,IAAIa,CAAC,KAAKf,KAAK;IAAE;IACf,OAAOD,MAAM;;EAEf;EACA;EACA;EACA;;EAEA,IAAIsD,GAAG,GAAGlD,KAAK,GAAGY,CAAC;EACnB,IAAIuC,CAAC,GAAG,CAAC;EAET,OAAOD,GAAG,KAAKrD,KAAK,EAAEsD,CAAC,EAAE,EAAE;IACzBD,GAAG,GAAGA,GAAG,GAAGlD,KAAK,GAAGY,CAAC;IAErB,IAAIuC,CAAC,GAAGlD,aAAa,EACnB,OAAOL,MAAM,CAAC,CAAC;EACnB;EACA,OAAOJ,MAAM,CAAC2D,CAAC,CAAC;AAClB;AAEA,SAASC,UAAUA,CAAC3D,CAAC,EAAEmB,CAAC,EAAEyC,GAAG,EAAE;EAE7B,IAAIC,IAAI,GAAGzD,KAAK;EAChB,IAAI0D,IAAI,GAAGV,MAAM,CAAC7C,KAAK,EAAEqD,GAAG,EAAEzC,CAAC,CAAC;EAEhC,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;IAAE;IAC9B;;IAEA,IAAIG,IAAI,KAAKC,IAAI,EACf,OAAO/D,MAAM,CAAC2D,CAAC,CAAC;IAElBG,IAAI,GAAGA,IAAI,GAAGtD,KAAK,GAAGY,CAAC;IACvB2C,IAAI,GAAGA,IAAI,GAAGvD,KAAK,GAAGY,CAAC;EACzB;EACA,OAAO,CAAC;AACV;AAEA,SAASQ,GAAGA,CAACD,CAAC,EAAE2B,CAAC,EAAE;EAEjB,IAAI,CAAC3B,CAAC,EACJ,OAAO2B,CAAC;EACV,IAAI,CAACA,CAAC,EACJ,OAAO3B,CAAC;EAEV,OAAO,CAAC,EAAE;IACRA,CAAC,IAAI2B,CAAC;IACN,IAAI,CAAC3B,CAAC,EACJ,OAAO2B,CAAC;IACVA,CAAC,IAAI3B,CAAC;IACN,IAAI,CAAC2B,CAAC,EACJ,OAAO3B,CAAC;EACZ;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,QAAQA,CAACE,CAAC,EAAE2B,CAAC,EAAE;EAEtBrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;EAEX,IAAI,IAAI,YAAY7B,QAAQ,EAAE;IAC5BE,CAAC,GAAGC,GAAG,CAAClB,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzB,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC;IAClB,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,GAAGiB,CAAC;IACtB,IAAI,CAAC,GAAG,CAAC,GAAGjB,CAAC,CAAC,GAAG,CAAC,GAAGiB,CAAC;EACxB,CAAC,MAAM;IACL,OAAOR,WAAW,CAACT,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7C;AACF;AAEA,IAAIW,cAAc,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,IAAIlB,KAAK,CAAC,kBAAkB,CAAC;AAAE,CAAC;AAC1E,IAAIW,gBAAgB,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,IAAIX,KAAK,CAAC,kBAAkB,CAAC;AAAE,CAAC;AAC5E,IAAIkC,mBAAmB,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAO,IAAIlC,KAAK,CAAC,4BAA4B,CAAC;AAAE,CAAC;AAEzFsB,QAAQ,CAACC,SAAS,GAAG;EAEnB,GAAG,EAAErB,KAAK;EACV,GAAG,EAAED,MAAM;EACX,GAAG,EAAEC,KAAK;EAEV;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAA2D,CAAA,EAAY;IAEjB,OAAO7C,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;EAC1C,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAA8C,CAAA,EAAY;IAEjB,OAAO9C,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;EACvD,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAA+C,CAAUvC,CAAC,EAAE2B,CAAC,EAAE;IAErBrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;IACX,OAAOnC,WAAW,CAChB,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGT,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,EAC5D,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CACnB,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAyD,CAAUxC,CAAC,EAAE2B,CAAC,EAAE;IAErBrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;IACX,OAAOnC,WAAW,CAChB,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGT,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,EAC5D,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CACnB,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAA0D,CAAUzC,CAAC,EAAE2B,CAAC,EAAE;IAErBrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;IACX,OAAOnC,WAAW,CAChB,IAAI,CAAC,GAAG,CAAC,GAAGT,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,EACvC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CACnB,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAA2D,CAAU1C,CAAC,EAAE2B,CAAC,EAAE;IAErBrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;IACX,OAAOnC,WAAW,CAChB,IAAI,CAAC,GAAG,CAAC,GAAGT,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,EACvC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CACnB,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,OAAO,EAAE,SAAA4D,CAAA,EAAY;IACnB,OAAOnD,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;EACtD,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAoD,CAAU5C,CAAC,EAAE2B,CAAC,EAAE;IAErB,IAAI3B,CAAC,KAAKS,SAAS,EAAE;MACnB,OAAOjB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAEd,KAAK,CAAC;IAC9D;IAEA4B,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;IACX,IAAIlD,MAAM,KAAKM,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE;MACjC,MAAMW,cAAc,CAAC,CAAC;IACxB;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,OAAOF,WAAW,CAChB,IAAI,CAAC,GAAG,CAAC,IAAIT,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAIA,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EACvDA,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;EACvB,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAkB,CAAUD,CAAC,EAAE2B,CAAC,EAAE;IAErBrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;;IAEX;IACA;;IAEA,OAAOnC,WAAW,CAACS,GAAG,CAAClB,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGkB,GAAG,CAAClB,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;EACzF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAA8D,CAAU7C,CAAC,EAAE2B,CAAC,EAAE;IAErBrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;;IAEX;IACA;;IAEA,IAAI5C,CAAC,CAAC,GAAG,CAAC,KAAKN,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,KAAKA,MAAM,EAAE;MAC7C,OAAOe,WAAW,CAACf,MAAM,EAAEC,KAAK,CAAC;IACnC;IACA,OAAOc,WAAW,CAACT,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAEkB,GAAG,CAAClB,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGkB,GAAG,CAAClB,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EACzF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,SAAS,EAAE,SAAA+D,CAAA,EAAY;IACrB,OAAOtD,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;EACtD,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAuD,CAAU/C,CAAC,EAAE2B,CAAC,EAAE;IAErBrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;;IAEX;;IAEA,IAAI5C,CAAC,CAAC,GAAG,CAAC,KAAKL,KAAK,EAAE;MAEpB,IAAIK,CAAC,CAAC,GAAG,CAAC,GAAGN,MAAM,EAAE;QACnB,OAAOe,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAKT,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAIA,CAAC,CAAC,GAAG,CAAC,CAAC;MAC5E,CAAC,MAAM;QACL,OAAOS,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAKT,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAIA,CAAC,CAAC,GAAG,CAAC,CAAC;MAC5E;IACF;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC,GAAG,CAAC,GAAGN,MAAM,EAAE,OAAO,IAAI;;IAEnC;IACA,IAAIuC,CAAC,GAAGd,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5B,IAAIa,CAAC,GAAGb,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;IAE5B;IACA,IAAI5B,CAAC,GAAGI,KAAK;IACb,IAAIe,CAAC,GAAGf,KAAK;IACb,KAAK,IAAIsE,CAAC,IAAIhC,CAAC,EAAE;MACf,IAAIgC,CAAC,KAAK,GAAG,EAAE;MACf,IAAIA,CAAC,KAAK,GAAG,EAAE;QACb1E,CAAC,GAAGG,MAAM;QACV;MACF;MACAuC,CAAC,CAACgC,CAAC,CAAC,IAAIjE,CAAC,CAAC,GAAG,CAAC;MAEd,IAAIiC,CAAC,CAACgC,CAAC,CAAC,GAAGjE,CAAC,CAAC,GAAG,CAAC,KAAKN,MAAM,EAAE;QAC5BuC,CAAC,CAACgC,CAAC,CAAC,IAAIjE,CAAC,CAAC,GAAG,CAAC;MAChB,CAAC,MAAM,OAAO,IAAI;MAClBT,CAAC,IAAID,MAAM,CAAC2E,CAAC,CAAC,IAAIhC,CAAC,CAACgC,CAAC,CAAC;IACxB;IAEA,KAAK,IAAIA,CAAC,IAAIjC,CAAC,EAAE;MACf,IAAIiC,CAAC,KAAK,GAAG,EAAE;MACfjC,CAAC,CAACiC,CAAC,CAAC,IAAIjE,CAAC,CAAC,GAAG,CAAC;MAEd,IAAIgC,CAAC,CAACiC,CAAC,CAAC,GAAGjE,CAAC,CAAC,GAAG,CAAC,KAAKN,MAAM,EAAE;QAC5BsC,CAAC,CAACiC,CAAC,CAAC,IAAIjE,CAAC,CAAC,GAAG,CAAC;MAChB,CAAC,MAAM,OAAO,IAAI;MAClBU,CAAC,IAAIpB,MAAM,CAAC2E,CAAC,CAAC,IAAIjC,CAAC,CAACiC,CAAC,CAAC;IACxB;IAEA,IAAIjE,CAAC,CAAC,GAAG,CAAC,GAAGN,MAAM,EAAE;MACnB,OAAOe,WAAW,CAACC,CAAC,EAAEnB,CAAC,CAAC;IAC1B;IACA,OAAOkB,WAAW,CAAClB,CAAC,EAAEmB,CAAC,CAAC;EAC1B,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAwD,CAAUjD,CAAC,EAAE2B,CAAC,EAAE;IAErBrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;IAEX,IAAI,IAAI,CAAC,GAAG,CAAC,IAAIlD,MAAM,IAAIM,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,EAAE,OAAO,IAAI;IAExD,MAAMyE,SAAS,GAAG,CAAC,CAAC;IAEpB,MAAMC,WAAW,GAAGjD,SAAS,CAACnB,CAAC,CAAC,GAAG,CAAC,CAAC;IACrC,MAAMqE,EAAE,GAAGlD,SAAS,CAACnB,CAAC,CAAC,GAAG,CAAC,CAAC;IAE5B,MAAMsE,aAAa,GAAGnD,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1C,MAAMoD,EAAE,GAAGpD,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE/B,KAAK,MAAMqD,KAAK,IAAIH,EAAE,EAAE;MACtBD,WAAW,CAACI,KAAK,CAAC,GAAG,CAACJ,WAAW,CAACI,KAAK,CAAC,IAAI9E,MAAM,IAAI2E,EAAE,CAACG,KAAK,CAAC;IACjE;IACA,KAAK,MAAMA,KAAK,IAAID,EAAE,EAAE;MACtBD,aAAa,CAACE,KAAK,CAAC,GAAG,CAACF,aAAa,CAACE,KAAK,CAAC,IAAI9E,MAAM,IAAI6E,EAAE,CAACC,KAAK,CAAC;IACrE;IAEA,KAAK,MAAMA,KAAK,IAAIJ,WAAW,EAAE;MAC/B,IAAII,KAAK,KAAK,GAAG,EAAE;MACnBL,SAAS,CAACK,KAAK,CAAC,GAAG,IAAI;IACzB;IACA,KAAK,MAAMA,KAAK,IAAIF,aAAa,EAAE;MACjC,IAAIE,KAAK,KAAK,GAAG,EAAE;MACnBL,SAAS,CAACK,KAAK,CAAC,GAAG,IAAI;IACzB;IAEA,IAAIC,IAAI,GAAG,IAAI;IACf,IAAIC,IAAI,GAAG,IAAI;;IAEf;IACA,KAAK,MAAMF,KAAK,IAAIL,SAAS,EAAE;MAE7B,MAAMQ,YAAY,GAAGP,WAAW,CAACI,KAAK,CAAC,IAAI9E,MAAM;MACjD,MAAMkF,cAAc,GAAGN,aAAa,CAACE,KAAK,CAAC,IAAI9E,MAAM;MAErD,IAAIiF,YAAY,KAAKjF,MAAM,EAAE;QAC3B,IAAIkF,cAAc,KAAKlF,MAAM,EAAE;UAC7B,OAAO,IAAI,CAAC,CAAC;QACf;QACA,SAAS,CAAC;MACZ;;MAEA;MACA,IAAImF,IAAI,GAAGD,cAAc;MACzB,IAAIE,IAAI,GAAGH,YAAY;;MAEvB;MACA,MAAMI,QAAQ,GAAG7D,GAAG,CAAC2D,IAAI,EAAEC,IAAI,CAAC;MAChCD,IAAI,IAAIE,QAAQ;MAChBD,IAAI,IAAIC,QAAQ;;MAEhB;MACA,IAAIN,IAAI,KAAK,IAAI,IAAIC,IAAI,KAAK,IAAI,EAAE;QAClCD,IAAI,GAAGI,IAAI;QACXH,IAAI,GAAGI,IAAI;MACb,CAAC,MAAM,IAAID,IAAI,GAAGH,IAAI,KAAKD,IAAI,GAAGK,IAAI,EAAE;QACtC,OAAO,IAAI,CAAC,CAAC;MACf;IACF;IAEA,OAAOL,IAAI,KAAK,IAAI,IAAIC,IAAI,KAAK,IAAI,GACjCjE,WAAW,CAACgE,IAAI,EAAEC,IAAI,CAAC,GACvB,IAAI;EACV,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,QAAQ,EAAE,SAAAM,CAAU/D,CAAC,EAAE2B,CAAC,EAAE;IAExBrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;IACX,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG5C,CAAC,CAAC,GAAG,CAAC,KAAKA,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;EACvE,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,IAAI,EAAE,SAAAiF,CAAUhE,CAAC,EAAE2B,CAAC,EAAE;IAEpBrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;IACX,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG5C,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;EACrE,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAkF,CAAUjE,CAAC,EAAE2B,CAAC,EAAE;IAErBrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;IACX,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG5C,CAAC,CAAC,GAAG,CAAC,IAAIA,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;EACtE,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,IAAI,EAAE,SAAAmF,CAAUlE,CAAC,EAAE2B,CAAC,EAAE;IAEpBrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;IACX,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG5C,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;EACrE,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,KAAK,EAAE,SAAAoF,CAAUnE,CAAC,EAAE2B,CAAC,EAAE;IAErBrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;IACX,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG5C,CAAC,CAAC,GAAG,CAAC,IAAIA,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;EACtE,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS,EAAE,SAAAqF,CAAUpE,CAAC,EAAE2B,CAAC,EAAE;IAEzBrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;IACX,IAAIK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGjD,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpE,OAAO,CAACN,MAAM,GAAGuD,CAAC,KAAKA,CAAC,GAAGvD,MAAM,CAAC;EACpC,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAM,EAAE,SAAA4F,CAAUC,MAAM,EAAE;IAExBA,MAAM,GAAGzF,KAAK,IAAIR,MAAM,CAACiG,MAAM,IAAI,CAAC,CAAC;IAErC,OAAO9E,WAAW,CAACJ,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGkF,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IACjEA,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG7F,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,IAAIA,MAAM,GAAGC,KAAK,GAAGD,MAAM,CAAC,EACjF6F,MAAM,CAAC;EACX,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,OAAO,EAAE,SAAA/E,CAAU+E,MAAM,EAAE;IAEzBA,MAAM,GAAGzF,KAAK,IAAIR,MAAM,CAACiG,MAAM,IAAI,CAAC,CAAC;IAErC,OAAO9E,WAAW,CAACJ,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGkF,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IACjEA,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG7F,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,GAAGA,MAAM,GAAGC,KAAK,GAAGD,MAAM,CAAC,EAChF6F,MAAM,CAAC;EACX,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,OAAO,EAAE,SAAAC,CAAUD,MAAM,EAAE;IAEzBA,MAAM,GAAGzF,KAAK,IAAIR,MAAM,CAACiG,MAAM,IAAI,CAAC,CAAC;;IAErC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAKI,OAAO9E,WAAW,CAACJ,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGkF,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAClE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI7F,MAAM,GAAGC,KAAK,GAAGD,MAAM,IAAIE,KAAK,IAAI2F,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG5F,KAAK,GAAGD,MAAM,CAAC,EAC5H6F,MAAM,CAAC;EACX,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,SAAS,EAAE,SAAAE,CAAUxE,CAAC,EAAE2B,CAAC,EAAE;IAEzB;AACJ;AACA;AACA;AACA;AACA;;IAEIrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;IAEX,MAAMrD,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGS,CAAC,CAAC,GAAG,CAAC;IAC5B,MAAMU,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGV,CAAC,CAAC,GAAG,CAAC;IAC5B,MAAM8C,CAAC,GAAGvD,CAAC,GAAGmB,CAAC;;IAEf;IACA,IAAIuD,CAAC,GAAG5D,KAAK,CAACd,CAAC,GAAGmB,CAAC,CAAC;IACpB,IAAIoC,CAAC,GAAGA,CAAC,IAAIpC,CAAC,EAAE;MACduD,CAAC,EAAE;IACL;IACA,OAAOxD,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGwD,CAAC,GAAGjE,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,CAAC;EACpD,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,WAAW,EAAE,SAAA0F,CAAUzE,CAAC,EAAE2B,CAAC,EAAE;IAE3BrB,KAAK,CAACN,CAAC,EAAE2B,CAAC,CAAC;IACX,OAAO,EAAE,EAAE5C,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAM,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,IAAKA,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAE,CAAC;EAClF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,SAAS,EAAE,SAAA2F,CAAA,EAAY;IACrB;IACA,OAAOC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC1D,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,UAAU,EAAE,SAAAC,CAAUC,GAAG,EAAE;IAEzB,IAAI7D,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IACjB,IAAID,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IAEjB8D,GAAG,GAAGA,GAAG,IAAI,EAAE,CAAC,CAAC;;IAEjB,IAAIC,MAAM,GAAGhD,QAAQ,CAACd,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC;IAC7B,IAAIgE,MAAM,GAAG9C,UAAU,CAACjB,CAAC,EAAED,CAAC,EAAE+D,MAAM,CAAC,CAAC,CAAC;;IAEvC,IAAIE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGvG,MAAM,GAAG,GAAG,GAAG,EAAE;;IAEvC;IACAuG,GAAG,IAAI5F,KAAK,CAAC4B,CAAC,GAAGD,CAAC,CAAC;IAEnBC,CAAC,IAAID,CAAC;IACNC,CAAC,IAAInC,KAAK;IAEV,IAAImC,CAAC,EACHgE,GAAG,IAAI,GAAG;IAEZ,IAAIF,MAAM,EAAE;MAEV,KAAK,IAAIzE,CAAC,GAAG0E,MAAM,EAAE1E,CAAC,EAAE,GAAG;QACzB2E,GAAG,IAAI5F,KAAK,CAAC4B,CAAC,GAAGD,CAAC,CAAC;QACnBC,CAAC,IAAID,CAAC;QACNC,CAAC,IAAInC,KAAK;MACZ;MACAmG,GAAG,IAAI,GAAG;MACV,KAAK,IAAI3E,CAAC,GAAGyE,MAAM,EAAEzE,CAAC,EAAE,GAAG;QACzB2E,GAAG,IAAI5F,KAAK,CAAC4B,CAAC,GAAGD,CAAC,CAAC;QACnBC,CAAC,IAAID,CAAC;QACNC,CAAC,IAAInC,KAAK;MACZ;MACAmG,GAAG,IAAI,GAAG;IACZ,CAAC,MAAM;MACL,KAAK,IAAI3E,CAAC,GAAGwE,GAAG,EAAE7D,CAAC,IAAIX,CAAC,EAAE,GAAG;QAC3B2E,GAAG,IAAI5F,KAAK,CAAC4B,CAAC,GAAGD,CAAC,CAAC;QACnBC,CAAC,IAAID,CAAC;QACNC,CAAC,IAAInC,KAAK;MACZ;IACF;IACA,OAAOmG,GAAG;EACZ,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,YAAY,EAAE,SAAAC,CAAUC,SAAS,EAAE;IAEjC,IAAI5G,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IACjB,IAAImB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IACjB,IAAIuF,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGvG,MAAM,GAAG,GAAG,GAAG,EAAE;IAEvC,IAAIgB,CAAC,KAAKf,KAAK,EAAE;MACfsG,GAAG,IAAI1G,CAAC;IACV,CAAC,MAAM;MACL,IAAI6G,KAAK,GAAG/F,KAAK,CAACd,CAAC,GAAGmB,CAAC,CAAC;MACxB,IAAIyF,SAAS,IAAIC,KAAK,GAAG1G,MAAM,EAAE;QAC/BuG,GAAG,IAAIG,KAAK;QACZH,GAAG,IAAI,GAAG;QACV1G,CAAC,IAAImB,CAAC;MACR;MAEAuF,GAAG,IAAI1G,CAAC;MACR0G,GAAG,IAAI,GAAG;MACVA,GAAG,IAAIvF,CAAC;IACV;IACA,OAAOuF,GAAG;EACZ,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,SAAS,EAAE,SAAAI,CAAUF,SAAS,EAAE;IAE9B,IAAI5G,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IACjB,IAAImB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IACjB,IAAIuF,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGvG,MAAM,GAAG,GAAG,GAAG,EAAE;IAEvC,IAAIgB,CAAC,KAAKf,KAAK,EAAE;MACfsG,GAAG,IAAI1G,CAAC;IACV,CAAC,MAAM;MACL,IAAI6G,KAAK,GAAG/F,KAAK,CAACd,CAAC,GAAGmB,CAAC,CAAC;MACxB,IAAIyF,SAAS,IAAIC,KAAK,GAAG1G,MAAM,EAAE;QAC/BuG,GAAG,IAAIG,KAAK;QACZ7G,CAAC,IAAImB,CAAC;MACR;MAEAuF,GAAG,IAAI,SAAS;MAChBA,GAAG,IAAI1G,CAAC;MACR0G,GAAG,IAAI,IAAI;MACXA,GAAG,IAAIvF,CAAC;MACRuF,GAAG,IAAI,GAAG;IACZ;IACA,OAAOA,GAAG;EACZ,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,aAAa,EAAE,SAAAK,CAAA,EAAY;IAEzB,IAAIrF,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IACjB,IAAI2B,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IACjB,IAAI2D,GAAG,GAAG,EAAE;IAEZ,GAAG;MACDA,GAAG,CAACC,IAAI,CAACnG,KAAK,CAACY,CAAC,GAAG2B,CAAC,CAAC,CAAC;MACtB,IAAIK,CAAC,GAAGhC,CAAC,GAAG2B,CAAC;MACb3B,CAAC,GAAG2B,CAAC;MACLA,CAAC,GAAGK,CAAC;IACP,CAAC,QAAQhC,CAAC,KAAKtB,KAAK;IAEpB,OAAO4G,GAAG;EACZ,CAAC;EAED,UAAU,EAAE,SAAAE,CAAUC,GAAG,EAAE;IAEzB,MAAMC,IAAI,GAAGrH,MAAM,CAAC,CAAC,IAAIoH,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;IAE3C,MAAME,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC7B,MAAMC,IAAI,GAAGD,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;IAErC,KAAK,IAAItF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuF,IAAI,CAACnE,MAAM,EAAEpB,CAAC,EAAE,EAAE;MAEpC,IAAIpB,CAAC,GAAGO,WAAW,CAACoG,IAAI,CAACvF,CAAC,GAAG,CAAC,CAAC,EAAE3B,KAAK,CAAC;MACvC,KAAK,IAAIsE,CAAC,GAAG3C,CAAC,GAAG,CAAC,EAAE2C,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC/B/D,CAAC,GAAGA,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC2G,IAAI,CAAC5C,CAAC,CAAC,CAAC;MACpC;MAEA,IAAIhB,CAAC,GAAG/C,CAAC,CAAC,KAAK,CAAC,CAAC0G,OAAO,CAAC;MACzB,IAAI3D,CAAC,CAAC,GAAG,CAAC,GAAG0D,IAAI,GAAG1D,CAAC,CAAC,GAAG,CAAC,EAAE;QAAE;QAC5B,OAAO/C,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;MAC5B;IACF;IACA,OAAO,IAAI;EACb;AACF,CAAC;AACD,SACEa,QAAQ,IAAI+F,OAAO,EAAE/F,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}