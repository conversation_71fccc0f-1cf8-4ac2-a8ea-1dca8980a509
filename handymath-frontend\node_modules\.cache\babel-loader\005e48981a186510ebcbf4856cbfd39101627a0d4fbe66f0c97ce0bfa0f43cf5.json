{"ast": null, "code": "export var identityDocs = {\n  name: 'identity',\n  category: 'Matrix',\n  syntax: ['identity(n)', 'identity(m, n)', 'identity([m, n])'],\n  description: 'Returns the identity matrix with size m-by-n. The matrix has ones on the diagonal and zeros elsewhere.',\n  examples: ['identity(3)', 'identity(3, 5)', 'a = [1, 2, 3; 4, 5, 6]', 'identity(size(a))'],\n  seealso: ['concat', 'det', 'diag', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};", "map": {"version": 3, "names": ["identityDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/identity.js"], "sourcesContent": ["export var identityDocs = {\n  name: 'identity',\n  category: 'Matrix',\n  syntax: ['identity(n)', 'identity(m, n)', 'identity([m, n])'],\n  description: 'Returns the identity matrix with size m-by-n. The matrix has ones on the diagonal and zeros elsewhere.',\n  examples: ['identity(3)', 'identity(3, 5)', 'a = [1, 2, 3; 4, 5, 6]', 'identity(size(a))'],\n  seealso: ['concat', 'det', 'diag', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAG;EACxBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,aAAa,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;EAC7DC,WAAW,EAAE,wGAAwG;EACrHC,QAAQ,EAAE,CAAC,aAAa,EAAE,gBAAgB,EAAE,wBAAwB,EAAE,mBAAmB,CAAC;EAC1FC,OAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO;AACvH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}