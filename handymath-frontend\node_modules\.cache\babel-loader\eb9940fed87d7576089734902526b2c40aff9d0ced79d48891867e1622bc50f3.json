{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { zerosDependencies } from './dependenciesZeros.generated.js';\nimport { createPrintTransform } from '../../factoriesAny.js';\nexport var printTransformDependencies = {\n  addDependencies,\n  matrixDependencies,\n  typedDependencies,\n  zerosDependencies,\n  createPrintTransform\n};", "map": {"version": 3, "names": ["addDependencies", "matrixDependencies", "typedDependencies", "zerosDependencies", "createPrintTransform", "printTransformDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesPrintTransform.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { zerosDependencies } from './dependenciesZeros.generated.js';\nimport { createPrintTransform } from '../../factoriesAny.js';\nexport var printTransformDependencies = {\n  addDependencies,\n  matrixDependencies,\n  typedDependencies,\n  zerosDependencies,\n  createPrintTransform\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,QAAQ,gCAAgC;AAChE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,OAAO,IAAIC,0BAA0B,GAAG;EACtCL,eAAe;EACfC,kBAAkB;EAClBC,iBAAiB;EACjBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}