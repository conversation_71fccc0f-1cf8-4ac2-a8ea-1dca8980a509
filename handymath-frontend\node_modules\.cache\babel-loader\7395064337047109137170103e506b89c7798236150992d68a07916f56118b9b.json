{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\AdminUsers.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport api from '../services/api';\nimport { useNotifications } from '../components/NotificationSystem';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminUsers = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const navigate = useNavigate();\n  const {\n    addNotification\n  } = useNotifications();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [roleFilter, setRoleFilter] = useState('all');\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [newUser, setNewUser] = useState({\n    username: '',\n    email: '',\n    first_name: '',\n    last_name: '',\n    password: '',\n    role: 'student'\n  });\n\n  // Charger les utilisateurs depuis l'API\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      const response = await api.get('/admin/users/');\n      if (response.data && response.data.users) {\n        setUsers(response.data.users);\n        addNotification({\n          type: 'success',\n          title: 'Utilisateurs chargés',\n          message: `${response.data.users.length} utilisateurs trouvés`\n        });\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement des utilisateurs:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur de chargement',\n        message: 'Impossible de charger les utilisateurs'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    if (user && user.role !== 'admin') {\n      navigate('/');\n      return;\n    }\n    loadUsers();\n  }, [user, navigate]);\n\n  // Activer/désactiver un utilisateur\n  const toggleUserStatus = async userId => {\n    try {\n      const response = await api.post(`/admin/users/${userId}/toggle/`);\n      if (response.data.success) {\n        setUsers(users.map(u => u.id === userId ? {\n          ...u,\n          is_active: response.data.is_active\n        } : u));\n        addNotification({\n          type: 'success',\n          title: 'Statut modifié',\n          message: response.data.message\n        });\n      }\n    } catch (error) {\n      console.error('Erreur lors du changement de statut:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de modifier le statut de l\\'utilisateur'\n      });\n    }\n  };\n\n  // Supprimer un utilisateur\n  const deleteUser = async userId => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')) {\n      try {\n        await api.delete(`/users/${userId}/`);\n        setUsers(users.filter(u => u.id !== userId));\n        addNotification({\n          type: 'success',\n          title: 'Utilisateur supprimé',\n          message: 'L\\'utilisateur a été supprimé avec succès'\n        });\n      } catch (error) {\n        console.error('Erreur lors de la suppression:', error);\n        addNotification({\n          type: 'error',\n          title: 'Erreur',\n          message: 'Impossible de supprimer l\\'utilisateur'\n        });\n      }\n    }\n  };\n\n  // Ajouter un nouvel utilisateur\n  const addUser = async () => {\n    try {\n      const response = await api.post('/users/', newUser);\n      if (response.data) {\n        await loadUsers(); // Recharger la liste\n        setShowAddModal(false);\n        setNewUser({\n          username: '',\n          email: '',\n          first_name: '',\n          last_name: '',\n          password: '',\n          role: 'student'\n        });\n        addNotification({\n          type: 'success',\n          title: 'Utilisateur ajouté',\n          message: `L'utilisateur ${newUser.username} a été créé avec succès`\n        });\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Erreur lors de l\\'ajout:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Impossible de créer l\\'utilisateur'\n      });\n    }\n  };\n  const filteredUsers = users.filter(user => {\n    const matchesSearch = user.username.toLowerCase().includes(searchTerm.toLowerCase()) || user.email.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesRole = roleFilter === 'all' || user.role === roleFilter;\n    return matchesSearch && matchesRole;\n  });\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  if (!user || user.role !== 'admin') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Acc\\xE8s refus\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Vous devez \\xEAtre administrateur pour acc\\xE9der \\xE0 cette page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Chargement des utilisateurs...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(SimpleHeader, {\n      title: \"Gestion des utilisateurs\",\n      showBackButton: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/admin'),\n          className: \"mb-4 flex items-center text-primary-600 hover:text-primary-700 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mr-2\",\n            children: \"\\u2190\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 11\n          }, this), \"Retour au tableau de bord\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n              children: \"Gestion des utilisateurs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg text-gray-600 dark:text-gray-400\",\n              children: [filteredUsers.length, \" utilisateur(s) \\u2022 Administration HandyMath\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddModal(true),\n            className: \"px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1\",\n            children: \"\\u2795 Ajouter un utilisateur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Rechercher\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              placeholder: \"Nom d'utilisateur ou email...\",\n              className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Filtrer par r\\xF4le\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: roleFilter,\n              onChange: e => setRoleFilter(e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"Tous les r\\xF4les\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"admin\",\n                children: \"Administrateurs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"student\",\n                children: \"\\xC9tudiants\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50 dark:bg-gray-700\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                  children: \"Utilisateur\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                  children: \"R\\xF4le\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                  children: \"Statut\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                  children: \"Inscription\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                  children: \"Derni\\xE8re connexion\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\",\n              children: filteredUsers.map((user, index) => /*#__PURE__*/_jsxDEV(motion.tr, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: index * 0.1\n                },\n                className: \"hover:bg-gray-50 dark:hover:bg-gray-700\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-10 h-10 rounded-full flex items-center justify-center text-white text-sm font-semibold ${user.role === 'admin' ? 'bg-red-500' : 'bg-blue-500'}`,\n                      children: user.username.charAt(0).toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"ml-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                        children: user.username\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 307,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                        children: user.email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 310,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 306,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}`,\n                    children: user.role === 'admin' ? 'Administrateur' : 'Étudiant'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: user.is_active ? 'Actif' : 'Inactif'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\",\n                  children: formatDate(user.date_joined)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\",\n                  children: formatDate(user.last_activity)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleUserStatus(user.id),\n                    className: `px-3 py-1 rounded text-xs font-medium ${user.is_active ? 'bg-red-100 text-red-800 hover:bg-red-200' : 'bg-green-100 text-green-800 hover:bg-green-200'}`,\n                    children: user.is_active ? 'Désactiver' : 'Activer'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 21\n                  }, this), user.role !== 'admin' && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => deleteUser(user.id),\n                    className: \"px-3 py-1 bg-red-100 text-red-800 hover:bg-red-200 rounded text-xs font-medium\",\n                    children: \"Supprimer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this)]\n              }, user.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-blue-100 dark:bg-blue-900 rounded-full\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-blue-600\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                children: \"\\xC9tudiants\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-3xl font-bold text-blue-600\",\n                children: users.filter(u => u.role === 'student').length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-red-100 dark:bg-red-900 rounded-full\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-red-600\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                children: \"Administrateurs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-3xl font-bold text-red-600\",\n                children: users.filter(u => u.role === 'admin').length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-green-100 dark:bg-green-900 rounded-full\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-green-600\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                children: \"Utilisateurs actifs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-3xl font-bold text-green-600\",\n                children: users.filter(u => u.is_active).length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 7\n      }, this), showAddModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.9\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 w-full max-w-md mx-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-900 dark:text-white mb-4\",\n            children: \"Ajouter un nouvel utilisateur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                children: \"Nom d'utilisateur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: newUser.username,\n                onChange: e => setNewUser({\n                  ...newUser,\n                  username: e.target.value\n                }),\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                placeholder: \"nom_utilisateur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                value: newUser.email,\n                onChange: e => setNewUser({\n                  ...newUser,\n                  email: e.target.value\n                }),\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                placeholder: \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                  children: \"Pr\\xE9nom\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: newUser.first_name,\n                  onChange: e => setNewUser({\n                    ...newUser,\n                    first_name: e.target.value\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                  placeholder: \"Pr\\xE9nom\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                  children: \"Nom\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: newUser.last_name,\n                  onChange: e => setNewUser({\n                    ...newUser,\n                    last_name: e.target.value\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                  placeholder: \"Nom\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                children: \"Mot de passe\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                value: newUser.password,\n                onChange: e => setNewUser({\n                  ...newUser,\n                  password: e.target.value\n                }),\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                placeholder: \"Mot de passe\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                children: \"R\\xF4le\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: newUser.role,\n                onChange: e => setNewUser({\n                  ...newUser,\n                  role: e.target.value\n                }),\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"student\",\n                  children: \"\\xC9tudiant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"admin\",\n                  children: \"Administrateur\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-3 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowAddModal(false),\n              className: \"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\",\n              children: \"Annuler\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: addUser,\n              disabled: !newUser.username || !newUser.email || !newUser.password,\n              className: \"px-4 py-2 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white rounded-lg transition-colors\",\n              children: \"Cr\\xE9er l'utilisateur\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AdminUsers, \"I/RisVR3dBP9sysPiBAYleio1Ws=\", false, function () {\n  return [useAuth, useNavigate, useNotifications];\n});\n_c = AdminUsers;\nexport default AdminUsers;\nvar _c;\n$RefreshReg$(_c, \"AdminUsers\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useAuth", "useNavigate", "motion", "api", "useNotifications", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminUsers", "_s", "user", "navigate", "addNotification", "users", "setUsers", "loading", "setLoading", "searchTerm", "setSearchTerm", "<PERSON><PERSON><PERSON>er", "setRoleFilter", "showAddModal", "setShowAddModal", "newUser", "setNewUser", "username", "email", "first_name", "last_name", "password", "role", "loadUsers", "response", "get", "data", "type", "title", "message", "length", "error", "console", "toggleUserStatus", "userId", "post", "success", "map", "u", "id", "is_active", "deleteUser", "window", "confirm", "delete", "filter", "addUser", "_error$response", "_error$response$data", "filteredUsers", "matchesSearch", "toLowerCase", "includes", "matchesRole", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "SimpleHeader", "showBackButton", "onClick", "value", "onChange", "e", "target", "placeholder", "index", "tr", "initial", "opacity", "y", "animate", "transition", "delay", "char<PERSON>t", "toUpperCase", "date_joined", "last_activity", "fill", "viewBox", "d", "fillRule", "clipRule", "div", "scale", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/pages/AdminUsers.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport api from '../services/api';\nimport { useNotifications } from '../components/NotificationSystem';\nimport AdminNavbar from '../components/AdminNavbar';\n\ninterface User {\n  id: number;\n  username: string;\n  email: string;\n  first_name: string;\n  last_name: string;\n  role: string;\n  is_active: boolean;\n  is_staff: boolean;\n  date_joined: string;\n  last_activity: string;\n  equation_count: number;\n}\n\ninterface NewUser {\n  username: string;\n  email: string;\n  first_name: string;\n  last_name: string;\n  password: string;\n  role: string;\n}\n\nconst AdminUsers: React.FC = () => {\n  const { user } = useAuth();\n  const navigate = useNavigate();\n  const { addNotification } = useNotifications();\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [roleFilter, setRoleFilter] = useState('all');\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [newUser, setNewUser] = useState<NewUser>({\n    username: '',\n    email: '',\n    first_name: '',\n    last_name: '',\n    password: '',\n    role: 'student'\n  });\n\n  // Charger les utilisateurs depuis l'API\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      const response = await api.get('/admin/users/');\n      if (response.data && response.data.users) {\n        setUsers(response.data.users);\n        addNotification({\n          type: 'success',\n          title: 'Utilisateurs chargés',\n          message: `${response.data.users.length} utilisateurs trouvés`\n        });\n      }\n    } catch (error: any) {\n      console.error('Erreur lors du chargement des utilisateurs:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur de chargement',\n        message: 'Impossible de charger les utilisateurs'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    if (user && user.role !== 'admin') {\n      navigate('/');\n      return;\n    }\n\n    loadUsers();\n  }, [user, navigate]);\n\n  // Activer/désactiver un utilisateur\n  const toggleUserStatus = async (userId: number) => {\n    try {\n      const response = await api.post(`/admin/users/${userId}/toggle/`);\n      if (response.data.success) {\n        setUsers(users.map(u =>\n          u.id === userId ? { ...u, is_active: response.data.is_active } : u\n        ));\n        addNotification({\n          type: 'success',\n          title: 'Statut modifié',\n          message: response.data.message\n        });\n      }\n    } catch (error: any) {\n      console.error('Erreur lors du changement de statut:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de modifier le statut de l\\'utilisateur'\n      });\n    }\n  };\n\n  // Supprimer un utilisateur\n  const deleteUser = async (userId: number) => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')) {\n      try {\n        await api.delete(`/users/${userId}/`);\n        setUsers(users.filter(u => u.id !== userId));\n        addNotification({\n          type: 'success',\n          title: 'Utilisateur supprimé',\n          message: 'L\\'utilisateur a été supprimé avec succès'\n        });\n      } catch (error: any) {\n        console.error('Erreur lors de la suppression:', error);\n        addNotification({\n          type: 'error',\n          title: 'Erreur',\n          message: 'Impossible de supprimer l\\'utilisateur'\n        });\n      }\n    }\n  };\n\n  // Ajouter un nouvel utilisateur\n  const addUser = async () => {\n    try {\n      const response = await api.post('/users/', newUser);\n      if (response.data) {\n        await loadUsers(); // Recharger la liste\n        setShowAddModal(false);\n        setNewUser({\n          username: '',\n          email: '',\n          first_name: '',\n          last_name: '',\n          password: '',\n          role: 'student'\n        });\n        addNotification({\n          type: 'success',\n          title: 'Utilisateur ajouté',\n          message: `L'utilisateur ${newUser.username} a été créé avec succès`\n        });\n      }\n    } catch (error: any) {\n      console.error('Erreur lors de l\\'ajout:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: error.response?.data?.error || 'Impossible de créer l\\'utilisateur'\n      });\n    }\n  };\n\n  const filteredUsers = users.filter(user => {\n    const matchesSearch = user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         user.email.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesRole = roleFilter === 'all' || user.role === roleFilter;\n    return matchesSearch && matchesRole;\n  });\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  if (!user || user.role !== 'admin') {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Accès refusé</h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Vous devez être administrateur pour accéder à cette page.\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Chargement des utilisateurs...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <SimpleHeader title=\"Gestion des utilisateurs\" showBackButton />\n      <div className=\"container mx-auto px-4 py-8\">\n      <div className=\"mb-8\">\n        <button\n          onClick={() => navigate('/admin')}\n          className=\"mb-4 flex items-center text-primary-600 hover:text-primary-700 transition-colors\"\n        >\n          <span className=\"mr-2\">←</span>\n          Retour au tableau de bord\n        </button>\n\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              Gestion des utilisateurs\n            </h1>\n            <p className=\"text-lg text-gray-600 dark:text-gray-400\">\n              {filteredUsers.length} utilisateur(s) • Administration HandyMath\n            </p>\n          </div>\n          <button\n            onClick={() => setShowAddModal(true)}\n            className=\"px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1\"\n          >\n            ➕ Ajouter un utilisateur\n          </button>\n        </div>\n      </div>\n\n      <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Rechercher\n            </label>\n            <input\n              type=\"text\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              placeholder=\"Nom d'utilisateur ou email...\"\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Filtrer par rôle\n            </label>\n            <select\n              value={roleFilter}\n              onChange={(e) => setRoleFilter(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"all\">Tous les rôles</option>\n              <option value=\"admin\">Administrateurs</option>\n              <option value=\"student\">Étudiants</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Utilisateur\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Rôle\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Statut\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Inscription\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Dernière connexion\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n              {filteredUsers.map((user, index) => (\n                <motion.tr\n                  key={user.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                  className=\"hover:bg-gray-50 dark:hover:bg-gray-700\"\n                >\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white text-sm font-semibold ${\n                        user.role === 'admin' ? 'bg-red-500' : 'bg-blue-500'\n                      }`}>\n                        {user.username.charAt(0).toUpperCase()}\n                      </div>\n                      <div className=\"ml-4\">\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {user.username}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          {user.email}\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                      user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'\n                    }`}>\n                      {user.role === 'admin' ? 'Administrateur' : 'Étudiant'}\n                    </span>\n                  </td>\n\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                      user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                    }`}>\n                      {user.is_active ? 'Actif' : 'Inactif'}\n                    </span>\n                  </td>\n\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                    {formatDate(user.date_joined)}\n                  </td>\n\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                    {formatDate(user.last_activity)}\n                  </td>\n\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                    <button\n                      onClick={() => toggleUserStatus(user.id)}\n                      className={`px-3 py-1 rounded text-xs font-medium ${\n                        user.is_active\n                          ? 'bg-red-100 text-red-800 hover:bg-red-200'\n                          : 'bg-green-100 text-green-800 hover:bg-green-200'\n                      }`}\n                    >\n                      {user.is_active ? 'Désactiver' : 'Activer'}\n                    </button>\n\n                    {user.role !== 'admin' && (\n                      <button\n                        onClick={() => deleteUser(user.id)}\n                        className=\"px-3 py-1 bg-red-100 text-red-800 hover:bg-red-200 rounded text-xs font-medium\"\n                      >\n                        Supprimer\n                      </button>\n                    )}\n                  </td>\n                </motion.tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      <div className=\"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900 rounded-full\">\n              <svg className=\"w-6 h-6 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path d=\"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\" />\n              </svg>\n            </div>\n            <div className=\"ml-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                Étudiants\n              </h3>\n              <p className=\"text-3xl font-bold text-blue-600\">\n                {users.filter(u => u.role === 'student').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-red-100 dark:bg-red-900 rounded-full\">\n              <svg className=\"w-6 h-6 text-red-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <div className=\"ml-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                Administrateurs\n              </h3>\n              <p className=\"text-3xl font-bold text-red-600\">\n                {users.filter(u => u.role === 'admin').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900 rounded-full\">\n              <svg className=\"w-6 h-6 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <div className=\"ml-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                Utilisateurs actifs\n              </h3>\n              <p className=\"text-3xl font-bold text-green-600\">\n                {users.filter(u => u.is_active).length}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Modal d'ajout d'utilisateur */}\n      {showAddModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 w-full max-w-md mx-4\"\n          >\n            <h3 className=\"text-xl font-bold text-gray-900 dark:text-white mb-4\">\n              Ajouter un nouvel utilisateur\n            </h3>\n\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Nom d'utilisateur\n                </label>\n                <input\n                  type=\"text\"\n                  value={newUser.username}\n                  onChange={(e) => setNewUser({...newUser, username: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder=\"nom_utilisateur\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Email\n                </label>\n                <input\n                  type=\"email\"\n                  value={newUser.email}\n                  onChange={(e) => setNewUser({...newUser, email: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Prénom\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={newUser.first_name}\n                    onChange={(e) => setNewUser({...newUser, first_name: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder=\"Prénom\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Nom\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={newUser.last_name}\n                    onChange={(e) => setNewUser({...newUser, last_name: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder=\"Nom\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Mot de passe\n                </label>\n                <input\n                  type=\"password\"\n                  value={newUser.password}\n                  onChange={(e) => setNewUser({...newUser, password: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder=\"Mot de passe\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Rôle\n                </label>\n                <select\n                  value={newUser.role}\n                  onChange={(e) => setNewUser({...newUser, role: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"student\">Étudiant</option>\n                  <option value=\"admin\">Administrateur</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 mt-6\">\n              <button\n                onClick={() => setShowAddModal(false)}\n                className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\"\n              >\n                Annuler\n              </button>\n              <button\n                onClick={addUser}\n                disabled={!newUser.username || !newUser.email || !newUser.password}\n                className=\"px-4 py-2 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white rounded-lg transition-colors\"\n              >\n                Créer l'utilisateur\n              </button>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </div>\n    </>\n  );\n};\n\nexport default AdminUsers;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,GAAG,MAAM,iBAAiB;AACjC,SAASC,gBAAgB,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA0BpE,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY;EAAgB,CAAC,GAAGT,gBAAgB,CAAC,CAAC;EAC9C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAU;IAC9C2B,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMgB,QAAQ,GAAG,MAAM9B,GAAG,CAAC+B,GAAG,CAAC,eAAe,CAAC;MAC/C,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACrB,KAAK,EAAE;QACxCC,QAAQ,CAACkB,QAAQ,CAACE,IAAI,CAACrB,KAAK,CAAC;QAC7BD,eAAe,CAAC;UACduB,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,sBAAsB;UAC7BC,OAAO,EAAE,GAAGL,QAAQ,CAACE,IAAI,CAACrB,KAAK,CAACyB,MAAM;QACxC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE3B,eAAe,CAAC;QACduB,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,sBAAsB;QAC7BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDnB,SAAS,CAAC,MAAM;IACd,IAAIa,IAAI,IAAIA,IAAI,CAACoB,IAAI,KAAK,OAAO,EAAE;MACjCnB,QAAQ,CAAC,GAAG,CAAC;MACb;IACF;IAEAoB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACrB,IAAI,EAAEC,QAAQ,CAAC,CAAC;;EAEpB;EACA,MAAM8B,gBAAgB,GAAG,MAAOC,MAAc,IAAK;IACjD,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAM9B,GAAG,CAACyC,IAAI,CAAC,gBAAgBD,MAAM,UAAU,CAAC;MACjE,IAAIV,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzB9B,QAAQ,CAACD,KAAK,CAACgC,GAAG,CAACC,CAAC,IAClBA,CAAC,CAACC,EAAE,KAAKL,MAAM,GAAG;UAAE,GAAGI,CAAC;UAAEE,SAAS,EAAEhB,QAAQ,CAACE,IAAI,CAACc;QAAU,CAAC,GAAGF,CACnE,CAAC,CAAC;QACFlC,eAAe,CAAC;UACduB,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,gBAAgB;UACvBC,OAAO,EAAEL,QAAQ,CAACE,IAAI,CAACG;QACzB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D3B,eAAe,CAAC;QACduB,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,QAAQ;QACfC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMY,UAAU,GAAG,MAAOP,MAAc,IAAK;IAC3C,IAAIQ,MAAM,CAACC,OAAO,CAAC,sDAAsD,CAAC,EAAE;MAC1E,IAAI;QACF,MAAMjD,GAAG,CAACkD,MAAM,CAAC,UAAUV,MAAM,GAAG,CAAC;QACrC5B,QAAQ,CAACD,KAAK,CAACwC,MAAM,CAACP,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,MAAM,CAAC,CAAC;QAC5C9B,eAAe,CAAC;UACduB,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,sBAAsB;UAC7BC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOE,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD3B,eAAe,CAAC;UACduB,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAMiB,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAM9B,GAAG,CAACyC,IAAI,CAAC,SAAS,EAAEpB,OAAO,CAAC;MACnD,IAAIS,QAAQ,CAACE,IAAI,EAAE;QACjB,MAAMH,SAAS,CAAC,CAAC,CAAC,CAAC;QACnBT,eAAe,CAAC,KAAK,CAAC;QACtBE,UAAU,CAAC;UACTC,QAAQ,EAAE,EAAE;UACZC,KAAK,EAAE,EAAE;UACTC,UAAU,EAAE,EAAE;UACdC,SAAS,EAAE,EAAE;UACbC,QAAQ,EAAE,EAAE;UACZC,IAAI,EAAE;QACR,CAAC,CAAC;QACFlB,eAAe,CAAC;UACduB,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,oBAAoB;UAC3BC,OAAO,EAAE,iBAAiBd,OAAO,CAACE,QAAQ;QAC5C,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOc,KAAU,EAAE;MAAA,IAAAgB,eAAA,EAAAC,oBAAA;MACnBhB,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD3B,eAAe,CAAC;QACduB,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,QAAQ;QACfC,OAAO,EAAE,EAAAkB,eAAA,GAAAhB,KAAK,CAACP,QAAQ,cAAAuB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBrB,IAAI,cAAAsB,oBAAA,uBAApBA,oBAAA,CAAsBjB,KAAK,KAAI;MAC1C,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMkB,aAAa,GAAG5C,KAAK,CAACwC,MAAM,CAAC3C,IAAI,IAAI;IACzC,MAAMgD,aAAa,GAAGhD,IAAI,CAACe,QAAQ,CAACkC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3C,UAAU,CAAC0C,WAAW,CAAC,CAAC,CAAC,IAC/DjD,IAAI,CAACgB,KAAK,CAACiC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3C,UAAU,CAAC0C,WAAW,CAAC,CAAC,CAAC;IAChF,MAAME,WAAW,GAAG1C,UAAU,KAAK,KAAK,IAAIT,IAAI,CAACoB,IAAI,KAAKX,UAAU;IACpE,OAAOuC,aAAa,IAAIG,WAAW;EACrC,CAAC,CAAC;EAEF,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,IAAI,CAAC5D,IAAI,IAAIA,IAAI,CAACoB,IAAI,KAAK,OAAO,EAAE;IAClC,oBACEzB,OAAA;MAAKkE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CnE,OAAA;QAAKkE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BnE,OAAA;UAAIkE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzDvE,OAAA;UAAGkE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAEhD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI7D,OAAO,EAAE;IACX,oBACEV,OAAA;MAAKkE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CnE,OAAA;QAAKkE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCnE,OAAA;UAAKkE,SAAS,EAAC;QAAgF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGvE,OAAA;UAAGkE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEvE,OAAA,CAAAE,SAAA;IAAAiE,QAAA,gBACEnE,OAAA,CAACwE,YAAY;MAACzC,KAAK,EAAC,0BAA0B;MAAC0C,cAAc;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChEvE,OAAA;MAAKkE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC5CnE,OAAA;QAAKkE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBnE,OAAA;UACE0E,OAAO,EAAEA,CAAA,KAAMpE,QAAQ,CAAC,QAAQ,CAAE;UAClC4D,SAAS,EAAC,kFAAkF;UAAAC,QAAA,gBAE5FnE,OAAA;YAAMkE,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,6BAEjC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETvE,OAAA;UAAKkE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDnE,OAAA;YAAAmE,QAAA,gBACEnE,OAAA;cAAIkE,SAAS,EAAC,kDAAkD;cAAAC,QAAA,EAAC;YAEjE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLvE,OAAA;cAAGkE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,GACpDf,aAAa,CAACnB,MAAM,EAAC,iDACxB;YAAA;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNvE,OAAA;YACE0E,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAAC,IAAI,CAAE;YACrCiD,SAAS,EAAC,yJAAyJ;YAAAC,QAAA,EACpK;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvE,OAAA;QAAKkE,SAAS,EAAC,yDAAyD;QAAAC,QAAA,eACtEnE,OAAA;UAAKkE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDnE,OAAA;YAAAmE,QAAA,gBACEnE,OAAA;cAAOkE,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAEnF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvE,OAAA;cACE8B,IAAI,EAAC,MAAM;cACX6C,KAAK,EAAE/D,UAAW;cAClBgE,QAAQ,EAAGC,CAAC,IAAKhE,aAAa,CAACgE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CI,WAAW,EAAC,+BAA+B;cAC3Cb,SAAS,EAAC;YAAiI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5I,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENvE,OAAA;YAAAmE,QAAA,gBACEnE,OAAA;cAAOkE,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAEnF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvE,OAAA;cACE2E,KAAK,EAAE7D,UAAW;cAClB8D,QAAQ,EAAGC,CAAC,IAAK9D,aAAa,CAAC8D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CT,SAAS,EAAC,iIAAiI;cAAAC,QAAA,gBAE3InE,OAAA;gBAAQ2E,KAAK,EAAC,KAAK;gBAAAR,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3CvE,OAAA;gBAAQ2E,KAAK,EAAC,OAAO;gBAAAR,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9CvE,OAAA;gBAAQ2E,KAAK,EAAC,SAAS;gBAAAR,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvE,OAAA;QAAKkE,SAAS,EAAC,gEAAgE;QAAAC,QAAA,eAC7EnE,OAAA;UAAKkE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BnE,OAAA;YAAOkE,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACvBnE,OAAA;cAAOkE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eAC5CnE,OAAA;gBAAAmE,QAAA,gBACEnE,OAAA;kBAAIkE,SAAS,EAAC,mGAAmG;kBAAAC,QAAA,EAAC;gBAElH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLvE,OAAA;kBAAIkE,SAAS,EAAC,mGAAmG;kBAAAC,QAAA,EAAC;gBAElH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLvE,OAAA;kBAAIkE,SAAS,EAAC,mGAAmG;kBAAAC,QAAA,EAAC;gBAElH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLvE,OAAA;kBAAIkE,SAAS,EAAC,mGAAmG;kBAAAC,QAAA,EAAC;gBAElH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLvE,OAAA;kBAAIkE,SAAS,EAAC,mGAAmG;kBAAAC,QAAA,EAAC;gBAElH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLvE,OAAA;kBAAIkE,SAAS,EAAC,mGAAmG;kBAAAC,QAAA,EAAC;gBAElH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRvE,OAAA;cAAOkE,SAAS,EAAC,yEAAyE;cAAAC,QAAA,EACvFf,aAAa,CAACZ,GAAG,CAAC,CAACnC,IAAI,EAAE2E,KAAK,kBAC7BhF,OAAA,CAACJ,MAAM,CAACqF,EAAE;gBAERC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BE,UAAU,EAAE;kBAAEC,KAAK,EAAEP,KAAK,GAAG;gBAAI,CAAE;gBACnCd,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,gBAEnDnE,OAAA;kBAAIkE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzCnE,OAAA;oBAAKkE,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChCnE,OAAA;sBAAKkE,SAAS,EAAE,4FACd7D,IAAI,CAACoB,IAAI,KAAK,OAAO,GAAG,YAAY,GAAG,aAAa,EACnD;sBAAA0C,QAAA,EACA9D,IAAI,CAACe,QAAQ,CAACoE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;oBAAC;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACNvE,OAAA;sBAAKkE,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBnE,OAAA;wBAAKkE,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,EAC/D9D,IAAI,CAACe;sBAAQ;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACX,CAAC,eACNvE,OAAA;wBAAKkE,SAAS,EAAC,0CAA0C;wBAAAC,QAAA,EACtD9D,IAAI,CAACgB;sBAAK;wBAAA+C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACR,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAELvE,OAAA;kBAAIkE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzCnE,OAAA;oBAAMkE,SAAS,EAAE,4DACf7D,IAAI,CAACoB,IAAI,KAAK,OAAO,GAAG,yBAAyB,GAAG,2BAA2B,EAC9E;oBAAA0C,QAAA,EACA9D,IAAI,CAACoB,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG;kBAAU;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAELvE,OAAA;kBAAIkE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACzCnE,OAAA;oBAAMkE,SAAS,EAAE,4DACf7D,IAAI,CAACsC,SAAS,GAAG,6BAA6B,GAAG,yBAAyB,EACzE;oBAAAwB,QAAA,EACA9D,IAAI,CAACsC,SAAS,GAAG,OAAO,GAAG;kBAAS;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAELvE,OAAA;kBAAIkE,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,EACjFV,UAAU,CAACpD,IAAI,CAACqF,WAAW;gBAAC;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eAELvE,OAAA;kBAAIkE,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,EACjFV,UAAU,CAACpD,IAAI,CAACsF,aAAa;gBAAC;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,eAELvE,OAAA;kBAAIkE,SAAS,EAAC,2DAA2D;kBAAAC,QAAA,gBACvEnE,OAAA;oBACE0E,OAAO,EAAEA,CAAA,KAAMtC,gBAAgB,CAAC/B,IAAI,CAACqC,EAAE,CAAE;oBACzCwB,SAAS,EAAE,yCACT7D,IAAI,CAACsC,SAAS,GACV,0CAA0C,GAC1C,gDAAgD,EACnD;oBAAAwB,QAAA,EAEF9D,IAAI,CAACsC,SAAS,GAAG,YAAY,GAAG;kBAAS;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,EAERlE,IAAI,CAACoB,IAAI,KAAK,OAAO,iBACpBzB,OAAA;oBACE0E,OAAO,EAAEA,CAAA,KAAM9B,UAAU,CAACvC,IAAI,CAACqC,EAAE,CAAE;oBACnCwB,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAC3F;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GApEAlE,IAAI,CAACqC,EAAE;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqEH,CACZ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvE,OAAA;QAAKkE,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDnE,OAAA;UAAKkE,SAAS,EAAC,oDAAoD;UAAAC,QAAA,eACjEnE,OAAA;YAAKkE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCnE,OAAA;cAAKkE,SAAS,EAAC,+CAA+C;cAAAC,QAAA,eAC5DnE,OAAA;gBAAKkE,SAAS,EAAC,uBAAuB;gBAAC0B,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAA1B,QAAA,eAC5EnE,OAAA;kBAAM8F,CAAC,EAAC;gBAAkL;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvE,OAAA;cAAKkE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBnE,OAAA;gBAAIkE,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAC;cAEpE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLvE,OAAA;gBAAGkE,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC5C3D,KAAK,CAACwC,MAAM,CAACP,CAAC,IAAIA,CAAC,CAAChB,IAAI,KAAK,SAAS,CAAC,CAACQ;cAAM;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvE,OAAA;UAAKkE,SAAS,EAAC,oDAAoD;UAAAC,QAAA,eACjEnE,OAAA;YAAKkE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCnE,OAAA;cAAKkE,SAAS,EAAC,6CAA6C;cAAAC,QAAA,eAC1DnE,OAAA;gBAAKkE,SAAS,EAAC,sBAAsB;gBAAC0B,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAA1B,QAAA,eAC3EnE,OAAA;kBAAM+F,QAAQ,EAAC,SAAS;kBAACD,CAAC,EAAC,8JAA8J;kBAACE,QAAQ,EAAC;gBAAS;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5M;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvE,OAAA;cAAKkE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBnE,OAAA;gBAAIkE,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAC;cAEpE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLvE,OAAA;gBAAGkE,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC3C3D,KAAK,CAACwC,MAAM,CAACP,CAAC,IAAIA,CAAC,CAAChB,IAAI,KAAK,OAAO,CAAC,CAACQ;cAAM;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvE,OAAA;UAAKkE,SAAS,EAAC,oDAAoD;UAAAC,QAAA,eACjEnE,OAAA;YAAKkE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCnE,OAAA;cAAKkE,SAAS,EAAC,iDAAiD;cAAAC,QAAA,eAC9DnE,OAAA;gBAAKkE,SAAS,EAAC,wBAAwB;gBAAC0B,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAA1B,QAAA,eAC7EnE,OAAA;kBAAM+F,QAAQ,EAAC,SAAS;kBAACD,CAAC,EAAC,oHAAoH;kBAACE,QAAQ,EAAC;gBAAS;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvE,OAAA;cAAKkE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBnE,OAAA;gBAAIkE,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAC;cAEpE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLvE,OAAA;gBAAGkE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC7C3D,KAAK,CAACwC,MAAM,CAACP,CAAC,IAAIA,CAAC,CAACE,SAAS,CAAC,CAACV;cAAM;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLvD,YAAY,iBACXhB,OAAA;QAAKkE,SAAS,EAAC,4EAA4E;QAAAC,QAAA,eACzFnE,OAAA,CAACJ,MAAM,CAACqG,GAAG;UACTf,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEe,KAAK,EAAE;UAAI,CAAE;UACpCb,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEe,KAAK,EAAE;UAAE,CAAE;UAClChC,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBAEnFnE,OAAA;YAAIkE,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAAC;UAErE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELvE,OAAA;YAAKkE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBnE,OAAA;cAAAmE,QAAA,gBACEnE,OAAA;gBAAOkE,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBACE8B,IAAI,EAAC,MAAM;gBACX6C,KAAK,EAAEzD,OAAO,CAACE,QAAS;gBACxBwD,QAAQ,EAAGC,CAAC,IAAK1D,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEE,QAAQ,EAAEyD,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBACpET,SAAS,EAAC,iIAAiI;gBAC3Ia,WAAW,EAAC;cAAiB;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvE,OAAA;cAAAmE,QAAA,gBACEnE,OAAA;gBAAOkE,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBACE8B,IAAI,EAAC,OAAO;gBACZ6C,KAAK,EAAEzD,OAAO,CAACG,KAAM;gBACrBuD,QAAQ,EAAGC,CAAC,IAAK1D,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEG,KAAK,EAAEwD,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBACjET,SAAS,EAAC,iIAAiI;gBAC3Ia,WAAW,EAAC;cAAmB;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvE,OAAA;cAAKkE,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCnE,OAAA;gBAAAmE,QAAA,gBACEnE,OAAA;kBAAOkE,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,EAAC;gBAEnF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRvE,OAAA;kBACE8B,IAAI,EAAC,MAAM;kBACX6C,KAAK,EAAEzD,OAAO,CAACI,UAAW;kBAC1BsD,QAAQ,EAAGC,CAAC,IAAK1D,UAAU,CAAC;oBAAC,GAAGD,OAAO;oBAAEI,UAAU,EAAEuD,CAAC,CAACC,MAAM,CAACH;kBAAK,CAAC,CAAE;kBACtET,SAAS,EAAC,iIAAiI;kBAC3Ia,WAAW,EAAC;gBAAQ;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENvE,OAAA;gBAAAmE,QAAA,gBACEnE,OAAA;kBAAOkE,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,EAAC;gBAEnF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRvE,OAAA;kBACE8B,IAAI,EAAC,MAAM;kBACX6C,KAAK,EAAEzD,OAAO,CAACK,SAAU;kBACzBqD,QAAQ,EAAGC,CAAC,IAAK1D,UAAU,CAAC;oBAAC,GAAGD,OAAO;oBAAEK,SAAS,EAAEsD,CAAC,CAACC,MAAM,CAACH;kBAAK,CAAC,CAAE;kBACrET,SAAS,EAAC,iIAAiI;kBAC3Ia,WAAW,EAAC;gBAAK;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvE,OAAA;cAAAmE,QAAA,gBACEnE,OAAA;gBAAOkE,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBACE8B,IAAI,EAAC,UAAU;gBACf6C,KAAK,EAAEzD,OAAO,CAACM,QAAS;gBACxBoD,QAAQ,EAAGC,CAAC,IAAK1D,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEM,QAAQ,EAAEqD,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBACpET,SAAS,EAAC,iIAAiI;gBAC3Ia,WAAW,EAAC;cAAc;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvE,OAAA;cAAAmE,QAAA,gBACEnE,OAAA;gBAAOkE,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvE,OAAA;gBACE2E,KAAK,EAAEzD,OAAO,CAACO,IAAK;gBACpBmD,QAAQ,EAAGC,CAAC,IAAK1D,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEO,IAAI,EAAEoD,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBAChET,SAAS,EAAC,iIAAiI;gBAAAC,QAAA,gBAE3InE,OAAA;kBAAQ2E,KAAK,EAAC,SAAS;kBAAAR,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCvE,OAAA;kBAAQ2E,KAAK,EAAC,OAAO;kBAAAR,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvE,OAAA;YAAKkE,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CnE,OAAA;cACE0E,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAAC,KAAK,CAAE;cACtCiD,SAAS,EAAC,2GAA2G;cAAAC,QAAA,EACtH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvE,OAAA;cACE0E,OAAO,EAAEzB,OAAQ;cACjBkD,QAAQ,EAAE,CAACjF,OAAO,CAACE,QAAQ,IAAI,CAACF,OAAO,CAACG,KAAK,IAAI,CAACH,OAAO,CAACM,QAAS;cACnE0C,SAAS,EAAC,4GAA4G;cAAAC,QAAA,EACvH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAACnE,EAAA,CA7fID,UAAoB;EAAA,QACPT,OAAO,EACPC,WAAW,EACAG,gBAAgB;AAAA;AAAAsG,EAAA,GAHxCjG,UAAoB;AA+f1B,eAAeA,UAAU;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}