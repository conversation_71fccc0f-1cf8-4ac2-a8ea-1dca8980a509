{"ast": null, "code": "export var cscDocs = {\n  name: 'csc',\n  category: 'Trigonometry',\n  syntax: ['csc(x)'],\n  description: 'Compute the cosecant of x in radians. Defined as 1/sin(x)',\n  examples: ['csc(2)', '1 / sin(2)'],\n  seealso: ['sec', 'cot', 'sin']\n};", "map": {"version": 3, "names": ["cscDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/csc.js"], "sourcesContent": ["export var cscDocs = {\n  name: 'csc',\n  category: 'Trigonometry',\n  syntax: ['csc(x)'],\n  description: 'Compute the cosecant of x in radians. Defined as 1/sin(x)',\n  examples: ['csc(2)', '1 / sin(2)'],\n  seealso: ['sec', 'cot', 'sin']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,2DAA2D;EACxEC,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;EAClCC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}