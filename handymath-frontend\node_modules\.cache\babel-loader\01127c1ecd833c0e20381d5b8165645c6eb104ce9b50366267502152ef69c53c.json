{"ast": null, "code": "export var formatDocs = {\n  name: 'format',\n  category: 'Utils',\n  syntax: ['format(value)', 'format(value, precision)'],\n  description: 'Format a value of any type as string.',\n  examples: ['format(2.3)', 'format(3 - 4i)', 'format([])', 'format(pi, 3)'],\n  seealso: ['print']\n};", "map": {"version": 3, "names": ["formatDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/format.js"], "sourcesContent": ["export var formatDocs = {\n  name: 'format',\n  category: 'Utils',\n  syntax: ['format(value)', 'format(value, precision)'],\n  description: 'Format a value of any type as string.',\n  examples: ['format(2.3)', 'format(3 - 4i)', 'format([])', 'format(pi, 3)'],\n  seealso: ['print']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,eAAe,EAAE,0BAA0B,CAAC;EACrDC,WAAW,EAAE,uCAAuC;EACpDC,QAAQ,EAAE,CAAC,aAAa,EAAE,gBAAgB,EAAE,YAAY,EAAE,eAAe,CAAC;EAC1EC,OAAO,EAAE,CAAC,OAAO;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}