{"ast": null, "code": "export var largerEqDocs = {\n  name: 'largerEq',\n  category: 'Relational',\n  syntax: ['x >= y', 'largerEq(x, y)'],\n  description: 'Check if value x is larger or equal to y. Returns true if x is larger or equal to y, and false if not.',\n  examples: ['2 >= 1+1', '2 > 1+1', 'a = 3.2', 'b = 6-2.8', '(a >= b)'],\n  seealso: ['equal', 'unequal', 'smallerEq', 'smaller', 'compare']\n};", "map": {"version": 3, "names": ["largerEqDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/relational/largerEq.js"], "sourcesContent": ["export var largerEqDocs = {\n  name: 'largerEq',\n  category: 'Relational',\n  syntax: ['x >= y', 'largerEq(x, y)'],\n  description: 'Check if value x is larger or equal to y. Returns true if x is larger or equal to y, and false if not.',\n  examples: ['2 >= 1+1', '2 > 1+1', 'a = 3.2', 'b = 6-2.8', '(a >= b)'],\n  seealso: ['equal', 'unequal', 'smallerEq', 'smaller', 'compare']\n};"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAG;EACxBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,QAAQ,EAAE,gBAAgB,CAAC;EACpCC,WAAW,EAAE,wGAAwG;EACrHC,QAAQ,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,CAAC;EACrEC,OAAO,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS;AACjE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}