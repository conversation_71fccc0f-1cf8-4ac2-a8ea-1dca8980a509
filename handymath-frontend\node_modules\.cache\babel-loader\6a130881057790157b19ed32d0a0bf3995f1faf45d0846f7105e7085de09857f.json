{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createIsPrime } from '../../factoriesAny.js';\nexport var isPrimeDependencies = {\n  typedDependencies,\n  createIsPrime\n};", "map": {"version": 3, "names": ["typedDependencies", "createIsPrime", "isPrimeDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesIsPrime.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createIsPrime } from '../../factoriesAny.js';\nexport var isPrimeDependencies = {\n  typedDependencies,\n  createIsPrime\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAO,IAAIC,mBAAmB,GAAG;EAC/BF,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}