{"ast": null, "code": "export var prodDocs = {\n  name: 'prod',\n  category: 'Statistics',\n  syntax: ['prod(a, b, c, ...)', 'prod(A)'],\n  description: 'Compute the product of all values.',\n  examples: ['prod(2, 3, 4)', 'prod([2, 3, 4])', 'prod([2, 5; 4, 3])'],\n  seealso: ['max', 'mean', 'min', 'median', 'min', 'std', 'sum', 'variance']\n};", "map": {"version": 3, "names": ["prodDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/statistics/prod.js"], "sourcesContent": ["export var prodDocs = {\n  name: 'prod',\n  category: 'Statistics',\n  syntax: ['prod(a, b, c, ...)', 'prod(A)'],\n  description: 'Compute the product of all values.',\n  examples: ['prod(2, 3, 4)', 'prod([2, 3, 4])', 'prod([2, 5; 4, 3])'],\n  seealso: ['max', 'mean', 'min', 'median', 'min', 'std', 'sum', 'variance']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,oBAAoB,EAAE,SAAS,CAAC;EACzCC,WAAW,EAAE,oCAAoC;EACjDC,QAAQ,EAAE,CAAC,eAAe,EAAE,iBAAiB,EAAE,oBAAoB,CAAC;EACpEC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU;AAC3E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}