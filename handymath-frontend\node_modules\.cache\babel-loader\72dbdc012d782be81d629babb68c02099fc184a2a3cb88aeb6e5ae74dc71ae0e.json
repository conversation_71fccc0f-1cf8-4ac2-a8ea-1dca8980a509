{"ast": null, "code": "export var nthRootDocs = {\n  name: 'nthRoot',\n  category: 'Arithmetic',\n  syntax: ['nthRoot(a)', 'nthRoot(a, root)'],\n  description: 'Calculate the nth root of a value. ' + 'The principal nth root of a positive real number A, ' + 'is the positive real solution of the equation \"x^root = A\".',\n  examples: ['4 ^ 3', 'nthRoot(64, 3)', 'nthRoot(9, 2)', 'sqrt(9)'],\n  seealso: ['nthRoots', 'pow', 'sqrt']\n};", "map": {"version": 3, "names": ["nthRootDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/nthRoot.js"], "sourcesContent": ["export var nthRootDocs = {\n  name: 'nthRoot',\n  category: 'Arithmetic',\n  syntax: ['nthRoot(a)', 'nthRoot(a, root)'],\n  description: 'Calculate the nth root of a value. ' + 'The principal nth root of a positive real number A, ' + 'is the positive real solution of the equation \"x^root = A\".',\n  examples: ['4 ^ 3', 'nthRoot(64, 3)', 'nthRoot(9, 2)', 'sqrt(9)'],\n  seealso: ['nthRoots', 'pow', 'sqrt']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,YAAY,EAAE,kBAAkB,CAAC;EAC1CC,WAAW,EAAE,qCAAqC,GAAG,sDAAsD,GAAG,6DAA6D;EAC3KC,QAAQ,EAAE,CAAC,OAAO,EAAE,gBAAgB,EAAE,eAAe,EAAE,SAAS,CAAC;EACjEC,OAAO,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}