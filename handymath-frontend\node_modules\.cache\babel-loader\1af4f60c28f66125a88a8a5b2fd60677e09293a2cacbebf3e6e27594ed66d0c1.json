{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo01xDSid';\nvar dependencies = ['typed'];\nexport var createMatAlgo01xDSid = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix nonzero items and invokes the callback function f(Dij, Sij).\n   * Callback function invoked NNZ times (number of nonzero items in SparseMatrix).\n   *\n   *\n   *          ┌  f(Dij, Sij)  ; S(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  Dij          ; otherwise\n   *\n   *\n   * @param {Matrix}   denseMatrix       The DenseMatrix instance (D)\n   * @param {Matrix}   sparseMatrix      The SparseMatrix instance (S)\n   * @param {Function} callback          The f(Dij,Sij) operation to invoke, where Dij = DenseMatrix(i,j) and Sij = SparseMatrix(i,j)\n   * @param {boolean}  inverse           A true value indicates callback should be invoked f(Sij,Dij)\n   *\n   * @return {Matrix}                    DenseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97477571\n   */\n  return function algorithm1(denseMatrix, sparseMatrix, callback, inverse) {\n    // dense matrix arrays\n    var adata = denseMatrix._data;\n    var asize = denseMatrix._size;\n    var adt = denseMatrix._datatype || denseMatrix.getDataType();\n    // sparse matrix arrays\n    var bvalues = sparseMatrix._values;\n    var bindex = sparseMatrix._index;\n    var bptr = sparseMatrix._ptr;\n    var bsize = sparseMatrix._size;\n    var bdt = sparseMatrix._datatype || sparseMatrix._data === undefined ? sparseMatrix._datatype : sparseMatrix.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // sparse matrix cannot be a Pattern matrix\n    if (!bvalues) {\n      throw new Error('Cannot perform operation on Dense Matrix and Pattern Sparse Matrix');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // process data types\n    var dt = typeof adt === 'string' && adt !== 'mixed' && adt === bdt ? adt : undefined;\n    // callback function\n    var cf = dt ? typed.find(callback, [dt, dt]) : callback;\n\n    // vars\n    var i, j;\n\n    // result (DenseMatrix)\n    var cdata = [];\n    // initialize c\n    for (i = 0; i < rows; i++) {\n      cdata[i] = [];\n    }\n\n    // workspace\n    var x = [];\n    // marks indicating we have a value in x for a given column\n    var w = [];\n\n    // loop columns in b\n    for (j = 0; j < columns; j++) {\n      // column mark\n      var mark = j + 1;\n      // values in column j\n      for (var k0 = bptr[j], k1 = bptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        i = bindex[k];\n        // update workspace\n        x[i] = inverse ? cf(bvalues[k], adata[i][j]) : cf(adata[i][j], bvalues[k]);\n        // mark i as updated\n        w[i] = mark;\n      }\n      // loop rows\n      for (i = 0; i < rows; i++) {\n        // check row is in workspace\n        if (w[i] === mark) {\n          // c[i][j] was already calculated\n          cdata[i][j] = x[i];\n        } else {\n          // item does not exist in S\n          cdata[i][j] = adata[i][j];\n        }\n      }\n    }\n\n    // return dense matrix\n    return denseMatrix.createDenseMatrix({\n      data: cdata,\n      size: [rows, columns],\n      datatype: adt === denseMatrix._datatype && bdt === sparseMatrix._datatype ? dt : undefined\n    });\n  };\n});", "map": {"version": 3, "names": ["factory", "DimensionError", "name", "dependencies", "createMatAlgo01xDSid", "_ref", "typed", "algorithm1", "denseMatrix", "sparseMatrix", "callback", "inverse", "adata", "_data", "asize", "_size", "adt", "_datatype", "getDataType", "bvalues", "_values", "bindex", "_index", "bptr", "_ptr", "bsize", "bdt", "undefined", "length", "RangeError", "Error", "rows", "columns", "dt", "cf", "find", "i", "j", "cdata", "x", "w", "mark", "k0", "k1", "k", "createDenseMatrix", "data", "size", "datatype"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/utils/matAlgo01xDSid.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo01xDSid';\nvar dependencies = ['typed'];\nexport var createMatAlgo01xDSid = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix nonzero items and invokes the callback function f(Dij, Sij).\n   * Callback function invoked NNZ times (number of nonzero items in SparseMatrix).\n   *\n   *\n   *          ┌  f(Dij, Sij)  ; S(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  Dij          ; otherwise\n   *\n   *\n   * @param {Matrix}   denseMatrix       The DenseMatrix instance (D)\n   * @param {Matrix}   sparseMatrix      The SparseMatrix instance (S)\n   * @param {Function} callback          The f(Dij,Sij) operation to invoke, where Dij = DenseMatrix(i,j) and Sij = SparseMatrix(i,j)\n   * @param {boolean}  inverse           A true value indicates callback should be invoked f(Sij,Dij)\n   *\n   * @return {Matrix}                    DenseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97477571\n   */\n  return function algorithm1(denseMatrix, sparseMatrix, callback, inverse) {\n    // dense matrix arrays\n    var adata = denseMatrix._data;\n    var asize = denseMatrix._size;\n    var adt = denseMatrix._datatype || denseMatrix.getDataType();\n    // sparse matrix arrays\n    var bvalues = sparseMatrix._values;\n    var bindex = sparseMatrix._index;\n    var bptr = sparseMatrix._ptr;\n    var bsize = sparseMatrix._size;\n    var bdt = sparseMatrix._datatype || sparseMatrix._data === undefined ? sparseMatrix._datatype : sparseMatrix.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // sparse matrix cannot be a Pattern matrix\n    if (!bvalues) {\n      throw new Error('Cannot perform operation on Dense Matrix and Pattern Sparse Matrix');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // process data types\n    var dt = typeof adt === 'string' && adt !== 'mixed' && adt === bdt ? adt : undefined;\n    // callback function\n    var cf = dt ? typed.find(callback, [dt, dt]) : callback;\n\n    // vars\n    var i, j;\n\n    // result (DenseMatrix)\n    var cdata = [];\n    // initialize c\n    for (i = 0; i < rows; i++) {\n      cdata[i] = [];\n    }\n\n    // workspace\n    var x = [];\n    // marks indicating we have a value in x for a given column\n    var w = [];\n\n    // loop columns in b\n    for (j = 0; j < columns; j++) {\n      // column mark\n      var mark = j + 1;\n      // values in column j\n      for (var k0 = bptr[j], k1 = bptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        i = bindex[k];\n        // update workspace\n        x[i] = inverse ? cf(bvalues[k], adata[i][j]) : cf(adata[i][j], bvalues[k]);\n        // mark i as updated\n        w[i] = mark;\n      }\n      // loop rows\n      for (i = 0; i < rows; i++) {\n        // check row is in workspace\n        if (w[i] === mark) {\n          // c[i][j] was already calculated\n          cdata[i][j] = x[i];\n        } else {\n          // item does not exist in S\n          cdata[i][j] = adata[i][j];\n        }\n      }\n    }\n\n    // return dense matrix\n    return denseMatrix.createDenseMatrix({\n      data: cdata,\n      size: [rows, columns],\n      datatype: adt === denseMatrix._datatype && bdt === sparseMatrix._datatype ? dt : undefined\n    });\n  };\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,IAAIC,IAAI,GAAG,gBAAgB;AAC3B,IAAIC,YAAY,GAAG,CAAC,OAAO,CAAC;AAC5B,OAAO,IAAIC,oBAAoB,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACnF,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASE,UAAUA,CAACC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACvE;IACA,IAAIC,KAAK,GAAGJ,WAAW,CAACK,KAAK;IAC7B,IAAIC,KAAK,GAAGN,WAAW,CAACO,KAAK;IAC7B,IAAIC,GAAG,GAAGR,WAAW,CAACS,SAAS,IAAIT,WAAW,CAACU,WAAW,CAAC,CAAC;IAC5D;IACA,IAAIC,OAAO,GAAGV,YAAY,CAACW,OAAO;IAClC,IAAIC,MAAM,GAAGZ,YAAY,CAACa,MAAM;IAChC,IAAIC,IAAI,GAAGd,YAAY,CAACe,IAAI;IAC5B,IAAIC,KAAK,GAAGhB,YAAY,CAACM,KAAK;IAC9B,IAAIW,GAAG,GAAGjB,YAAY,CAACQ,SAAS,IAAIR,YAAY,CAACI,KAAK,KAAKc,SAAS,GAAGlB,YAAY,CAACQ,SAAS,GAAGR,YAAY,CAACS,WAAW,CAAC,CAAC;;IAE1H;IACA,IAAIJ,KAAK,CAACc,MAAM,KAAKH,KAAK,CAACG,MAAM,EAAE;MACjC,MAAM,IAAI3B,cAAc,CAACa,KAAK,CAACc,MAAM,EAAEH,KAAK,CAACG,MAAM,CAAC;IACtD;;IAEA;IACA,IAAId,KAAK,CAAC,CAAC,CAAC,KAAKW,KAAK,CAAC,CAAC,CAAC,IAAIX,KAAK,CAAC,CAAC,CAAC,KAAKW,KAAK,CAAC,CAAC,CAAC,EAAE;MAClD,MAAM,IAAII,UAAU,CAAC,gCAAgC,GAAGf,KAAK,GAAG,yBAAyB,GAAGW,KAAK,GAAG,GAAG,CAAC;IAC1G;;IAEA;IACA,IAAI,CAACN,OAAO,EAAE;MACZ,MAAM,IAAIW,KAAK,CAAC,oEAAoE,CAAC;IACvF;;IAEA;IACA,IAAIC,IAAI,GAAGjB,KAAK,CAAC,CAAC,CAAC;IACnB,IAAIkB,OAAO,GAAGlB,KAAK,CAAC,CAAC,CAAC;;IAEtB;IACA,IAAImB,EAAE,GAAG,OAAOjB,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,OAAO,IAAIA,GAAG,KAAKU,GAAG,GAAGV,GAAG,GAAGW,SAAS;IACpF;IACA,IAAIO,EAAE,GAAGD,EAAE,GAAG3B,KAAK,CAAC6B,IAAI,CAACzB,QAAQ,EAAE,CAACuB,EAAE,EAAEA,EAAE,CAAC,CAAC,GAAGvB,QAAQ;;IAEvD;IACA,IAAI0B,CAAC,EAAEC,CAAC;;IAER;IACA,IAAIC,KAAK,GAAG,EAAE;IACd;IACA,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,EAAEK,CAAC,EAAE,EAAE;MACzBE,KAAK,CAACF,CAAC,CAAC,GAAG,EAAE;IACf;;IAEA;IACA,IAAIG,CAAC,GAAG,EAAE;IACV;IACA,IAAIC,CAAC,GAAG,EAAE;;IAEV;IACA,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,OAAO,EAAEK,CAAC,EAAE,EAAE;MAC5B;MACA,IAAII,IAAI,GAAGJ,CAAC,GAAG,CAAC;MAChB;MACA,KAAK,IAAIK,EAAE,GAAGnB,IAAI,CAACc,CAAC,CAAC,EAAEM,EAAE,GAAGpB,IAAI,CAACc,CAAC,GAAG,CAAC,CAAC,EAAEO,CAAC,GAAGF,EAAE,EAAEE,CAAC,GAAGD,EAAE,EAAEC,CAAC,EAAE,EAAE;QAC5D;QACAR,CAAC,GAAGf,MAAM,CAACuB,CAAC,CAAC;QACb;QACAL,CAAC,CAACH,CAAC,CAAC,GAAGzB,OAAO,GAAGuB,EAAE,CAACf,OAAO,CAACyB,CAAC,CAAC,EAAEhC,KAAK,CAACwB,CAAC,CAAC,CAACC,CAAC,CAAC,CAAC,GAAGH,EAAE,CAACtB,KAAK,CAACwB,CAAC,CAAC,CAACC,CAAC,CAAC,EAAElB,OAAO,CAACyB,CAAC,CAAC,CAAC;QAC1E;QACAJ,CAAC,CAACJ,CAAC,CAAC,GAAGK,IAAI;MACb;MACA;MACA,KAAKL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,EAAEK,CAAC,EAAE,EAAE;QACzB;QACA,IAAII,CAAC,CAACJ,CAAC,CAAC,KAAKK,IAAI,EAAE;UACjB;UACAH,KAAK,CAACF,CAAC,CAAC,CAACC,CAAC,CAAC,GAAGE,CAAC,CAACH,CAAC,CAAC;QACpB,CAAC,MAAM;UACL;UACAE,KAAK,CAACF,CAAC,CAAC,CAACC,CAAC,CAAC,GAAGzB,KAAK,CAACwB,CAAC,CAAC,CAACC,CAAC,CAAC;QAC3B;MACF;IACF;;IAEA;IACA,OAAO7B,WAAW,CAACqC,iBAAiB,CAAC;MACnCC,IAAI,EAAER,KAAK;MACXS,IAAI,EAAE,CAAChB,IAAI,EAAEC,OAAO,CAAC;MACrBgB,QAAQ,EAAEhC,GAAG,KAAKR,WAAW,CAACS,SAAS,IAAIS,GAAG,KAAKjB,YAAY,CAACQ,SAAS,GAAGgB,EAAE,GAAGN;IACnF,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}