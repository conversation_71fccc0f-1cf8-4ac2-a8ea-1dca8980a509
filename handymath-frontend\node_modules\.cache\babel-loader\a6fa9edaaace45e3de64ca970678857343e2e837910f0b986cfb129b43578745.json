{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { compareDependencies } from './dependenciesCompare.generated.js';\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { partitionSelectDependencies } from './dependenciesPartitionSelect.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMedian } from '../../factoriesAny.js';\nexport var medianDependencies = {\n  addDependencies,\n  compareDependencies,\n  divideDependencies,\n  partitionSelectDependencies,\n  typedDependencies,\n  createMedian\n};", "map": {"version": 3, "names": ["addDependencies", "compareDependencies", "divideDependencies", "partitionSelectDependencies", "typedDependencies", "createMedian", "medianDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMedian.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { compareDependencies } from './dependenciesCompare.generated.js';\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { partitionSelectDependencies } from './dependenciesPartitionSelect.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMedian } from '../../factoriesAny.js';\nexport var medianDependencies = {\n  addDependencies,\n  compareDependencies,\n  divideDependencies,\n  partitionSelectDependencies,\n  typedDependencies,\n  createMedian\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,QAAQ,gCAAgC;AAChE,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAO,IAAIC,kBAAkB,GAAG;EAC9BN,eAAe;EACfC,mBAAmB;EACnBC,kBAAkB;EAClBC,2BAA2B;EAC3BC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}