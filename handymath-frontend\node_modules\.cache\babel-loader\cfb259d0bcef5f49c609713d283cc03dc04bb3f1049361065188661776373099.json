{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo03xDSf';\nvar dependencies = ['typed'];\nexport var createMatAlgo03xDSf = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix items and invokes the callback function f(Dij, Sij).\n   * Callback function invoked M*N times.\n   *\n   *\n   *          ┌  f(Dij, Sij)  ; S(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  f(Dij, 0)    ; otherwise\n   *\n   *\n   * @param {Matrix}   denseMatrix       The DenseMatrix instance (D)\n   * @param {Matrix}   sparseMatrix      The SparseMatrix instance (C)\n   * @param {Function} callback          The f(Dij,Sij) operation to invoke, where Dij = DenseMatrix(i,j) and Sij = SparseMatrix(i,j)\n   * @param {boolean}  inverse           A true value indicates callback should be invoked f(Sij,Dij)\n   *\n   * @return {Matrix}                    DenseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97477571\n   */\n  return function matAlgo03xDSf(denseMatrix, sparseMatrix, callback, inverse) {\n    // dense matrix arrays\n    var adata = denseMatrix._data;\n    var asize = denseMatrix._size;\n    var adt = denseMatrix._datatype || denseMatrix.getDataType();\n    // sparse matrix arrays\n    var bvalues = sparseMatrix._values;\n    var bindex = sparseMatrix._index;\n    var bptr = sparseMatrix._ptr;\n    var bsize = sparseMatrix._size;\n    var bdt = sparseMatrix._datatype || sparseMatrix._data === undefined ? sparseMatrix._datatype : sparseMatrix.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // sparse matrix cannot be a Pattern matrix\n    if (!bvalues) {\n      throw new Error('Cannot perform operation on Dense Matrix and Pattern Sparse Matrix');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      // datatype\n      dt = adt;\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result (DenseMatrix)\n    var cdata = [];\n\n    // initialize dense matrix\n    for (var z = 0; z < rows; z++) {\n      // initialize row\n      cdata[z] = [];\n    }\n\n    // workspace\n    var x = [];\n    // marks indicating we have a value in x for a given column\n    var w = [];\n\n    // loop columns in b\n    for (var j = 0; j < columns; j++) {\n      // column mark\n      var mark = j + 1;\n      // values in column j\n      for (var k0 = bptr[j], k1 = bptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        var i = bindex[k];\n        // update workspace\n        x[i] = inverse ? cf(bvalues[k], adata[i][j]) : cf(adata[i][j], bvalues[k]);\n        w[i] = mark;\n      }\n      // process workspace\n      for (var y = 0; y < rows; y++) {\n        // check we have a calculated value for current row\n        if (w[y] === mark) {\n          // use calculated value\n          cdata[y][j] = x[y];\n        } else {\n          // calculate value\n          cdata[y][j] = inverse ? cf(zero, adata[y][j]) : cf(adata[y][j], zero);\n        }\n      }\n    }\n\n    // return dense matrix\n    return denseMatrix.createDenseMatrix({\n      data: cdata,\n      size: [rows, columns],\n      datatype: adt === denseMatrix._datatype && bdt === sparseMatrix._datatype ? dt : undefined\n    });\n  };\n});", "map": {"version": 3, "names": ["factory", "DimensionError", "name", "dependencies", "createMatAlgo03xDSf", "_ref", "typed", "matAlgo03xDSf", "denseMatrix", "sparseMatrix", "callback", "inverse", "adata", "_data", "asize", "_size", "adt", "_datatype", "getDataType", "bvalues", "_values", "bindex", "_index", "bptr", "_ptr", "bsize", "bdt", "undefined", "length", "RangeError", "Error", "rows", "columns", "dt", "zero", "cf", "convert", "find", "cdata", "z", "x", "w", "j", "mark", "k0", "k1", "k", "i", "y", "createDenseMatrix", "data", "size", "datatype"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/utils/matAlgo03xDSf.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo03xDSf';\nvar dependencies = ['typed'];\nexport var createMatAlgo03xDSf = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix items and invokes the callback function f(Dij, Sij).\n   * Callback function invoked M*N times.\n   *\n   *\n   *          ┌  f(Dij, Sij)  ; S(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  f(Dij, 0)    ; otherwise\n   *\n   *\n   * @param {Matrix}   denseMatrix       The DenseMatrix instance (D)\n   * @param {Matrix}   sparseMatrix      The SparseMatrix instance (C)\n   * @param {Function} callback          The f(Dij,Sij) operation to invoke, where Dij = DenseMatrix(i,j) and Sij = SparseMatrix(i,j)\n   * @param {boolean}  inverse           A true value indicates callback should be invoked f(Sij,Dij)\n   *\n   * @return {Matrix}                    DenseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97477571\n   */\n  return function matAlgo03xDSf(denseMatrix, sparseMatrix, callback, inverse) {\n    // dense matrix arrays\n    var adata = denseMatrix._data;\n    var asize = denseMatrix._size;\n    var adt = denseMatrix._datatype || denseMatrix.getDataType();\n    // sparse matrix arrays\n    var bvalues = sparseMatrix._values;\n    var bindex = sparseMatrix._index;\n    var bptr = sparseMatrix._ptr;\n    var bsize = sparseMatrix._size;\n    var bdt = sparseMatrix._datatype || sparseMatrix._data === undefined ? sparseMatrix._datatype : sparseMatrix.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // sparse matrix cannot be a Pattern matrix\n    if (!bvalues) {\n      throw new Error('Cannot perform operation on Dense Matrix and Pattern Sparse Matrix');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      // datatype\n      dt = adt;\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result (DenseMatrix)\n    var cdata = [];\n\n    // initialize dense matrix\n    for (var z = 0; z < rows; z++) {\n      // initialize row\n      cdata[z] = [];\n    }\n\n    // workspace\n    var x = [];\n    // marks indicating we have a value in x for a given column\n    var w = [];\n\n    // loop columns in b\n    for (var j = 0; j < columns; j++) {\n      // column mark\n      var mark = j + 1;\n      // values in column j\n      for (var k0 = bptr[j], k1 = bptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        var i = bindex[k];\n        // update workspace\n        x[i] = inverse ? cf(bvalues[k], adata[i][j]) : cf(adata[i][j], bvalues[k]);\n        w[i] = mark;\n      }\n      // process workspace\n      for (var y = 0; y < rows; y++) {\n        // check we have a calculated value for current row\n        if (w[y] === mark) {\n          // use calculated value\n          cdata[y][j] = x[y];\n        } else {\n          // calculate value\n          cdata[y][j] = inverse ? cf(zero, adata[y][j]) : cf(adata[y][j], zero);\n        }\n      }\n    }\n\n    // return dense matrix\n    return denseMatrix.createDenseMatrix({\n      data: cdata,\n      size: [rows, columns],\n      datatype: adt === denseMatrix._datatype && bdt === sparseMatrix._datatype ? dt : undefined\n    });\n  };\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,IAAIC,IAAI,GAAG,eAAe;AAC1B,IAAIC,YAAY,GAAG,CAAC,OAAO,CAAC;AAC5B,OAAO,IAAIC,mBAAmB,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAClF,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASE,aAAaA,CAACC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IAC1E;IACA,IAAIC,KAAK,GAAGJ,WAAW,CAACK,KAAK;IAC7B,IAAIC,KAAK,GAAGN,WAAW,CAACO,KAAK;IAC7B,IAAIC,GAAG,GAAGR,WAAW,CAACS,SAAS,IAAIT,WAAW,CAACU,WAAW,CAAC,CAAC;IAC5D;IACA,IAAIC,OAAO,GAAGV,YAAY,CAACW,OAAO;IAClC,IAAIC,MAAM,GAAGZ,YAAY,CAACa,MAAM;IAChC,IAAIC,IAAI,GAAGd,YAAY,CAACe,IAAI;IAC5B,IAAIC,KAAK,GAAGhB,YAAY,CAACM,KAAK;IAC9B,IAAIW,GAAG,GAAGjB,YAAY,CAACQ,SAAS,IAAIR,YAAY,CAACI,KAAK,KAAKc,SAAS,GAAGlB,YAAY,CAACQ,SAAS,GAAGR,YAAY,CAACS,WAAW,CAAC,CAAC;;IAE1H;IACA,IAAIJ,KAAK,CAACc,MAAM,KAAKH,KAAK,CAACG,MAAM,EAAE;MACjC,MAAM,IAAI3B,cAAc,CAACa,KAAK,CAACc,MAAM,EAAEH,KAAK,CAACG,MAAM,CAAC;IACtD;;IAEA;IACA,IAAId,KAAK,CAAC,CAAC,CAAC,KAAKW,KAAK,CAAC,CAAC,CAAC,IAAIX,KAAK,CAAC,CAAC,CAAC,KAAKW,KAAK,CAAC,CAAC,CAAC,EAAE;MAClD,MAAM,IAAII,UAAU,CAAC,gCAAgC,GAAGf,KAAK,GAAG,yBAAyB,GAAGW,KAAK,GAAG,GAAG,CAAC;IAC1G;;IAEA;IACA,IAAI,CAACN,OAAO,EAAE;MACZ,MAAM,IAAIW,KAAK,CAAC,oEAAoE,CAAC;IACvF;;IAEA;IACA,IAAIC,IAAI,GAAGjB,KAAK,CAAC,CAAC,CAAC;IACnB,IAAIkB,OAAO,GAAGlB,KAAK,CAAC,CAAC,CAAC;;IAEtB;IACA,IAAImB,EAAE;IACN;IACA,IAAIC,IAAI,GAAG,CAAC;IACZ;IACA,IAAIC,EAAE,GAAGzB,QAAQ;;IAEjB;IACA,IAAI,OAAOM,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAKU,GAAG,IAAIV,GAAG,KAAK,OAAO,EAAE;MAC7D;MACAiB,EAAE,GAAGjB,GAAG;MACR;MACAkB,IAAI,GAAG5B,KAAK,CAAC8B,OAAO,CAAC,CAAC,EAAEH,EAAE,CAAC;MAC3B;MACAE,EAAE,GAAG7B,KAAK,CAAC+B,IAAI,CAAC3B,QAAQ,EAAE,CAACuB,EAAE,EAAEA,EAAE,CAAC,CAAC;IACrC;;IAEA;IACA,IAAIK,KAAK,GAAG,EAAE;;IAEd;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,EAAEQ,CAAC,EAAE,EAAE;MAC7B;MACAD,KAAK,CAACC,CAAC,CAAC,GAAG,EAAE;IACf;;IAEA;IACA,IAAIC,CAAC,GAAG,EAAE;IACV;IACA,IAAIC,CAAC,GAAG,EAAE;;IAEV;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,OAAO,EAAEU,CAAC,EAAE,EAAE;MAChC;MACA,IAAIC,IAAI,GAAGD,CAAC,GAAG,CAAC;MAChB;MACA,KAAK,IAAIE,EAAE,GAAGrB,IAAI,CAACmB,CAAC,CAAC,EAAEG,EAAE,GAAGtB,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,EAAEI,CAAC,GAAGF,EAAE,EAAEE,CAAC,GAAGD,EAAE,EAAEC,CAAC,EAAE,EAAE;QAC5D;QACA,IAAIC,CAAC,GAAG1B,MAAM,CAACyB,CAAC,CAAC;QACjB;QACAN,CAAC,CAACO,CAAC,CAAC,GAAGpC,OAAO,GAAGwB,EAAE,CAAChB,OAAO,CAAC2B,CAAC,CAAC,EAAElC,KAAK,CAACmC,CAAC,CAAC,CAACL,CAAC,CAAC,CAAC,GAAGP,EAAE,CAACvB,KAAK,CAACmC,CAAC,CAAC,CAACL,CAAC,CAAC,EAAEvB,OAAO,CAAC2B,CAAC,CAAC,CAAC;QAC1EL,CAAC,CAACM,CAAC,CAAC,GAAGJ,IAAI;MACb;MACA;MACA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,IAAI,EAAEiB,CAAC,EAAE,EAAE;QAC7B;QACA,IAAIP,CAAC,CAACO,CAAC,CAAC,KAAKL,IAAI,EAAE;UACjB;UACAL,KAAK,CAACU,CAAC,CAAC,CAACN,CAAC,CAAC,GAAGF,CAAC,CAACQ,CAAC,CAAC;QACpB,CAAC,MAAM;UACL;UACAV,KAAK,CAACU,CAAC,CAAC,CAACN,CAAC,CAAC,GAAG/B,OAAO,GAAGwB,EAAE,CAACD,IAAI,EAAEtB,KAAK,CAACoC,CAAC,CAAC,CAACN,CAAC,CAAC,CAAC,GAAGP,EAAE,CAACvB,KAAK,CAACoC,CAAC,CAAC,CAACN,CAAC,CAAC,EAAER,IAAI,CAAC;QACvE;MACF;IACF;;IAEA;IACA,OAAO1B,WAAW,CAACyC,iBAAiB,CAAC;MACnCC,IAAI,EAAEZ,KAAK;MACXa,IAAI,EAAE,CAACpB,IAAI,EAAEC,OAAO,CAAC;MACrBoB,QAAQ,EAAEpC,GAAG,KAAKR,WAAW,CAACS,SAAS,IAAIS,GAAG,KAAKjB,YAAY,CAACQ,SAAS,GAAGgB,EAAE,GAAGN;IACnF,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}