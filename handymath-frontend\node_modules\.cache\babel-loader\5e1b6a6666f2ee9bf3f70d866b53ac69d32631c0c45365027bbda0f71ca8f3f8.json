{"ast": null, "code": "export var multiplyDocs = {\n  name: 'multiply',\n  category: 'Operators',\n  syntax: ['x * y', 'multiply(x, y)'],\n  description: 'multiply two values.',\n  examples: ['a = 2.1 * 3.4', 'a / 3.4', '2 * 3 + 4', '2 * (3 + 4)', '3 * 2.1 km'],\n  seealso: ['divide']\n};", "map": {"version": 3, "names": ["multiplyDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/multiply.js"], "sourcesContent": ["export var multiplyDocs = {\n  name: 'multiply',\n  category: 'Operators',\n  syntax: ['x * y', 'multiply(x, y)'],\n  description: 'multiply two values.',\n  examples: ['a = 2.1 * 3.4', 'a / 3.4', '2 * 3 + 4', '2 * (3 + 4)', '3 * 2.1 km'],\n  seealso: ['divide']\n};"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAG;EACxBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,OAAO,EAAE,gBAAgB,CAAC;EACnCC,WAAW,EAAE,sBAAsB;EACnCC,QAAQ,EAAE,CAAC,eAAe,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC;EAChFC,OAAO,EAAE,CAAC,QAAQ;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}