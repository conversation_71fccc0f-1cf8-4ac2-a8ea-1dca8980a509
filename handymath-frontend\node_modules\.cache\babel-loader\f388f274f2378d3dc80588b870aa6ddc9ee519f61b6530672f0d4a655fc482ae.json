{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\CoursesPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport StudentNavbar from '../components/StudentNavbar';\nimport Pagination from '../components/Pagination';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CoursesPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [courses, setCourses] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    level: '',\n    featured: false\n  });\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(9);\n  const [totalItems, setTotalItems] = useState(0);\n\n  // Options de pagination pour les cours\n  const coursePaginationOptions = [6, 9, 12, 18];\n  useEffect(() => {\n    if (user) {\n      fetchCourses();\n    }\n  }, [user, filters, currentPage, itemsPerPage]);\n  const fetchCourses = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      if (filters.level) params.append('level', filters.level);\n      if (filters.featured) params.append('featured', 'true');\n\n      // Pagination parameters\n      params.append('page', currentPage.toString());\n      params.append('page_size', itemsPerPage.toString());\n      const response = await api.get(`/courses/?${params.toString()}`);\n      if (response.data) {\n        setCourses(response.data.courses || response.data.results || []);\n        setTotalItems(response.data.count || response.data.total || 0);\n      }\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      // Fallback avec des données de test si l'API n'est pas disponible\n      setCourses([{\n        id: 1,\n        title: 'Algèbre de base',\n        description: 'Apprenez les concepts fondamentaux de l\\'algèbre avec des exercices pratiques et des exemples concrets.',\n        level: 'beginner',\n        level_display: 'Débutant',\n        thumbnail: '📚',\n        estimated_duration: 180,\n        is_featured: true,\n        chapters_count: 4,\n        lessons_count: 12,\n        progress_percentage: 45,\n        is_accessible: true,\n        is_enrolled: true,\n        enrollment_date: '2024-01-15T10:30:00Z',\n        prerequisites: [],\n        created_at: '2024-01-15T10:30:00Z'\n      }, {\n        id: 2,\n        title: 'Géométrie euclidienne',\n        description: 'Découvrez les principes de la géométrie euclidienne et ses applications pratiques.',\n        level: 'intermediate',\n        level_display: 'Intermédiaire',\n        thumbnail: '📐',\n        estimated_duration: 240,\n        is_featured: false,\n        chapters_count: 6,\n        lessons_count: 18,\n        progress_percentage: 0,\n        is_accessible: true,\n        is_enrolled: false,\n        prerequisites: [{\n          id: 1,\n          title: 'Algèbre de base',\n          progress: 45\n        }],\n        created_at: '2024-01-16T14:20:00Z'\n      }, {\n        id: 3,\n        title: 'Calcul différentiel',\n        description: 'Maîtrisez les concepts du calcul différentiel et intégral.',\n        level: 'advanced',\n        level_display: 'Avancé',\n        thumbnail: '∫',\n        estimated_duration: 360,\n        is_featured: true,\n        chapters_count: 8,\n        lessons_count: 24,\n        progress_percentage: 0,\n        is_accessible: false,\n        is_enrolled: false,\n        prerequisites: [{\n          id: 1,\n          title: 'Algèbre de base',\n          progress: 45\n        }],\n        created_at: '2024-01-17T09:45:00Z'\n      }]);\n      setTotalItems(3); // Nombre total de cours de test\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Pagination handlers\n  const handlePageChange = page => {\n    setCurrentPage(page);\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  const handleItemsPerPageChange = newItemsPerPage => {\n    setItemsPerPage(newItemsPerPage);\n    setCurrentPage(1); // Reset to first page when changing items per page\n  };\n  const handleEnroll = async (courseId, courseTitle) => {\n    try {\n      setCourses(courses.map(course => course.id === courseId ? {\n        ...course,\n        is_enrolled: true,\n        enrollment_date: new Date().toISOString()\n      } : course));\n      alert(`Inscription réussie au cours \"${courseTitle}\" !`);\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      alert('Erreur lors de l\\'inscription');\n    }\n  };\n  const clearFilters = () => {\n    setFilters({\n      level: '',\n      featured: false\n    });\n    setCurrentPage(1); // Reset to first page when clearing filters\n  };\n  const formatDuration = minutes => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n  const getLevelColor = level => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n\n  // Calculate total pages\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Cours de Math\\xE9matiques\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Vous devez \\xEAtre connect\\xE9 pour acc\\xE9der aux cours.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(StudentNavbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 dark:text-white mb-4\",\n          children: \"Cours de Math\\xE9matiques\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"D\\xE9couvrez nos cours structur\\xE9s pour progresser \\xE0 votre rythme\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-4 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Niveau\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filters.level,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                level: e.target.value\n              })),\n              className: \"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tous les niveaux\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"beginner\",\n                children: \"D\\xE9butant\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"intermediate\",\n                children: \"Interm\\xE9diaire\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"advanced\",\n                children: \"Avanc\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              id: \"featured\",\n              checked: filters.featured,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                featured: e.target.checked\n              })),\n              className: \"mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"featured\",\n              className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n              children: \"Cours en vedette uniquement\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: clearFilters,\n            className: \"px-4 py-2 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors\",\n            children: \"Effacer les filtres\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 7\n      }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600 dark:text-gray-400\",\n          children: \"Chargement des cours...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), !loading && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\",\n          children: courses.map((course, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-32 bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-4xl text-white\",\n                children: course.thumbnail\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start justify-between mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                  children: course.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 21\n                }, this), course.is_featured && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"px-2 py-1 text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 rounded-full\",\n                  children: \"Vedette\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2\",\n                children: course.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500 dark:text-gray-400\",\n                    children: \"Niveau:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`,\n                    children: course.level_display\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500 dark:text-gray-400\",\n                    children: \"Dur\\xE9e:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900 dark:text-white\",\n                    children: formatDuration(course.estimated_duration)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500 dark:text-gray-400\",\n                    children: \"Le\\xE7ons:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900 dark:text-white\",\n                    children: course.lessons_count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this), course.is_enrolled && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-sm mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500 dark:text-gray-400\",\n                    children: \"Progression\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900 dark:text-white\",\n                    children: [course.progress_percentage, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                    style: {\n                      width: `${course.progress_percentage}%`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-2\",\n                children: course.is_enrolled ? /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"flex-1 bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors\",\n                  children: \"Continuer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 23\n                }, this) : course.is_accessible ? /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleEnroll(course.id, course.title),\n                  className: \"flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors\",\n                  children: \"S'inscrire\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                  disabled: true,\n                  className: \"flex-1 bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 font-medium py-2 px-4 rounded-md cursor-not-allowed\",\n                  children: \"Pr\\xE9requis manquants\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this)]\n          }, course.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), totalItems > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8\",\n          children: /*#__PURE__*/_jsxDEV(Pagination, {\n            currentPage: currentPage,\n            totalPages: totalPages,\n            totalItems: totalItems,\n            itemsPerPage: itemsPerPage,\n            onPageChange: handlePageChange,\n            onItemsPerPageChange: handleItemsPerPageChange,\n            showItemsPerPage: true,\n            showInfo: true,\n            itemsPerPageOptions: coursePaginationOptions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true);\n};\n_s(CoursesPage, \"V2W9XMTkyGr4HXpQWi0qIYBTwXw=\", false, function () {\n  return [useAuth];\n});\n_c = CoursesPage;\nexport default CoursesPage;\nvar _c;\n$RefreshReg$(_c, \"CoursesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useAuth", "StudentNavbar", "Pagination", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CoursesPage", "_s", "user", "courses", "setCourses", "loading", "setLoading", "filters", "setFilters", "level", "featured", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "totalItems", "setTotalItems", "coursePaginationOptions", "fetchCourses", "params", "URLSearchParams", "append", "toString", "response", "get", "data", "results", "count", "total", "error", "console", "id", "title", "description", "level_display", "thumbnail", "estimated_duration", "is_featured", "chapters_count", "lessons_count", "progress_percentage", "is_accessible", "is_enrolled", "enrollment_date", "prerequisites", "created_at", "progress", "handlePageChange", "page", "window", "scrollTo", "top", "behavior", "handleItemsPerPageChange", "newItemsPerPage", "handleEnroll", "courseId", "courseTitle", "map", "course", "Date", "toISOString", "alert", "clearFilters", "formatDuration", "minutes", "hours", "Math", "floor", "mins", "getLevelColor", "totalPages", "ceil", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "value", "onChange", "e", "prev", "target", "type", "checked", "htmlFor", "onClick", "index", "div", "initial", "opacity", "y", "animate", "transition", "delay", "style", "width", "disabled", "onPageChange", "onItemsPerPageChange", "showItemsPerPage", "showInfo", "itemsPerPageOptions", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/pages/CoursesPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport StudentNavbar from '../components/StudentNavbar';\nimport Pagination from '../components/Pagination';\nimport api from '../services/api';\n\ninterface Course {\n  id: number;\n  title: string;\n  description: string;\n  level: string;\n  level_display: string;\n  thumbnail: string;\n  estimated_duration: number;\n  is_featured: boolean;\n  chapters_count: number;\n  lessons_count: number;\n  progress_percentage: number;\n  is_accessible: boolean;\n  is_enrolled: boolean;\n  enrollment_date?: string;\n  prerequisites: Array<{\n    id: number;\n    title: string;\n    progress: number;\n  }>;\n  created_at: string;\n}\n\ninterface Filters {\n  level: string;\n  featured: boolean;\n}\n\nconst CoursesPage: React.FC = () => {\n  const { user } = useAuth();\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState<Filters>({\n    level: '',\n    featured: false\n  });\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(9);\n  const [totalItems, setTotalItems] = useState(0);\n  \n  // Options de pagination pour les cours\n  const coursePaginationOptions = [6, 9, 12, 18];\n\n  useEffect(() => {\n    if (user) {\n      fetchCourses();\n    }\n  }, [user, filters, currentPage, itemsPerPage]);\n\n  const fetchCourses = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      if (filters.level) params.append('level', filters.level);\n      if (filters.featured) params.append('featured', 'true');\n\n      // Pagination parameters\n      params.append('page', currentPage.toString());\n      params.append('page_size', itemsPerPage.toString());\n\n      const response = await api.get(`/courses/?${params.toString()}`);\n      if (response.data) {\n        setCourses(response.data.courses || response.data.results || []);\n        setTotalItems(response.data.count || response.data.total || 0);\n      }\n    } catch (error: any) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      // Fallback avec des données de test si l'API n'est pas disponible\n      setCourses([\n        {\n          id: 1,\n          title: 'Algèbre de base',\n          description: 'Apprenez les concepts fondamentaux de l\\'algèbre avec des exercices pratiques et des exemples concrets.',\n          level: 'beginner',\n          level_display: 'Débutant',\n          thumbnail: '📚',\n          estimated_duration: 180,\n          is_featured: true,\n          chapters_count: 4,\n          lessons_count: 12,\n          progress_percentage: 45,\n          is_accessible: true,\n          is_enrolled: true,\n          enrollment_date: '2024-01-15T10:30:00Z',\n          prerequisites: [],\n          created_at: '2024-01-15T10:30:00Z'\n        },\n        {\n          id: 2,\n          title: 'Géométrie euclidienne',\n          description: 'Découvrez les principes de la géométrie euclidienne et ses applications pratiques.',\n          level: 'intermediate',\n          level_display: 'Intermédiaire',\n          thumbnail: '📐',\n          estimated_duration: 240,\n          is_featured: false,\n          chapters_count: 6,\n          lessons_count: 18,\n          progress_percentage: 0,\n          is_accessible: true,\n          is_enrolled: false,\n          prerequisites: [\n            { id: 1, title: 'Algèbre de base', progress: 45 }\n          ],\n          created_at: '2024-01-16T14:20:00Z'\n        },\n        {\n          id: 3,\n          title: 'Calcul différentiel',\n          description: 'Maîtrisez les concepts du calcul différentiel et intégral.',\n          level: 'advanced',\n          level_display: 'Avancé',\n          thumbnail: '∫',\n          estimated_duration: 360,\n          is_featured: true,\n          chapters_count: 8,\n          lessons_count: 24,\n          progress_percentage: 0,\n          is_accessible: false,\n          is_enrolled: false,\n          prerequisites: [\n            { id: 1, title: 'Algèbre de base', progress: 45 }\n          ],\n          created_at: '2024-01-17T09:45:00Z'\n        }\n      ]);\n      setTotalItems(3); // Nombre total de cours de test\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Pagination handlers\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const handleItemsPerPageChange = (newItemsPerPage: number) => {\n    setItemsPerPage(newItemsPerPage);\n    setCurrentPage(1); // Reset to first page when changing items per page\n  };\n\n  const handleEnroll = async (courseId: number, courseTitle: string) => {\n    try {\n      setCourses(courses.map(course => \n        course.id === courseId \n          ? { ...course, is_enrolled: true, enrollment_date: new Date().toISOString() }\n          : course\n      ));\n      alert(`Inscription réussie au cours \"${courseTitle}\" !`);\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      alert('Erreur lors de l\\'inscription');\n    }\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      level: '',\n      featured: false\n    });\n    setCurrentPage(1); // Reset to first page when clearing filters\n  };\n\n  const formatDuration = (minutes: number) => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n\n  const getLevelColor = (level: string) => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n\n  // Calculate total pages\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\n\n  if (!user) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Cours de Mathématiques</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            Vous devez être connecté pour accéder aux cours.\n          </p>\n          <a\n            href=\"/login\"\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Se connecter\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n    <StudentNavbar />\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* En-tête */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">\n          Cours de Mathématiques\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-400\">\n          Découvrez nos cours structurés pour progresser à votre rythme\n        </p>\n      </div>\n\n      {/* Filtres */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8\">\n        <div className=\"flex flex-wrap gap-4 items-center\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Niveau\n            </label>\n            <select\n              value={filters.level}\n              onChange={(e) => setFilters(prev => ({ ...prev, level: e.target.value }))}\n              className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500\"\n            >\n              <option value=\"\">Tous les niveaux</option>\n              <option value=\"beginner\">Débutant</option>\n              <option value=\"intermediate\">Intermédiaire</option>\n              <option value=\"advanced\">Avancé</option>\n            </select>\n          </div>\n\n          <div className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              id=\"featured\"\n              checked={filters.featured}\n              onChange={(e) => setFilters(prev => ({ ...prev, featured: e.target.checked }))}\n              className=\"mr-2\"\n            />\n            <label htmlFor=\"featured\" className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              Cours en vedette uniquement\n            </label>\n          </div>\n\n          <button\n            onClick={clearFilters}\n            className=\"px-4 py-2 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors\"\n          >\n            Effacer les filtres\n          </button>\n        </div>\n      </div>\n\n      {/* Loading */}\n      {loading && (\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600 dark:text-gray-400\">Chargement des cours...</p>\n        </div>\n      )}\n\n      {/* Cours */}\n      {!loading && (\n        <>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\n            {courses.map((course, index) => (\n              <motion.div\n                key={course.id}\n                className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1 }}\n              >\n                {/* Thumbnail */}\n                <div className=\"h-32 bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center\">\n                  <span className=\"text-4xl text-white\">{course.thumbnail}</span>\n                </div>\n\n                <div className=\"p-6\">\n                  {/* En-tête du cours */}\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                      {course.title}\n                    </h3>\n                    {course.is_featured && (\n                      <span className=\"px-2 py-1 text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 rounded-full\">\n                        Vedette\n                      </span>\n                    )}\n                  </div>\n\n                  <p className=\"text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2\">\n                    {course.description}\n                  </p>\n\n                  {/* Métadonnées */}\n                  <div className=\"space-y-2 mb-4\">\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-gray-500 dark:text-gray-400\">Niveau:</span>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`}>\n                        {course.level_display}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-gray-500 dark:text-gray-400\">Durée:</span>\n                      <span className=\"text-gray-900 dark:text-white\">{formatDuration(course.estimated_duration)}</span>\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-gray-500 dark:text-gray-400\">Leçons:</span>\n                      <span className=\"text-gray-900 dark:text-white\">{course.lessons_count}</span>\n                    </div>\n                  </div>\n\n                  {/* Progression */}\n                  {course.is_enrolled && (\n                    <div className=\"mb-4\">\n                      <div className=\"flex items-center justify-between text-sm mb-1\">\n                        <span className=\"text-gray-500 dark:text-gray-400\">Progression</span>\n                        <span className=\"text-gray-900 dark:text-white\">{course.progress_percentage}%</span>\n                      </div>\n                      <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                        <div\n                          className=\"bg-primary-600 h-2 rounded-full transition-all duration-300\"\n                          style={{ width: `${course.progress_percentage}%` }}\n                        ></div>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Actions */}\n                  <div className=\"flex gap-2\">\n                    {course.is_enrolled ? (\n                      <button className=\"flex-1 bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors\">\n                        Continuer\n                      </button>\n                    ) : course.is_accessible ? (\n                      <button\n                        onClick={() => handleEnroll(course.id, course.title)}\n                        className=\"flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors\"\n                      >\n                        S'inscrire\n                      </button>\n                    ) : (\n                      <button\n                        disabled\n                        className=\"flex-1 bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 font-medium py-2 px-4 rounded-md cursor-not-allowed\"\n                      >\n                        Prérequis manquants\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Pagination */}\n          {totalItems > 0 && (\n            <div className=\"mt-8\">\n              <Pagination\n                currentPage={currentPage}\n                totalPages={totalPages}\n                totalItems={totalItems}\n                itemsPerPage={itemsPerPage}\n                onPageChange={handlePageChange}\n                onItemsPerPageChange={handleItemsPerPageChange}\n                showItemsPerPage={true}\n                showInfo={true}\n                itemsPerPageOptions={coursePaginationOptions}\n              />\n            </div>\n          )}\n        </>\n      )}\n    </div>\n    </>\n  );\n};\n\nexport default CoursesPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA8BlC,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAK,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAU;IAC9CoB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAM4B,uBAAuB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAE9C3B,SAAS,CAAC,MAAM;IACd,IAAIY,IAAI,EAAE;MACRgB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAChB,IAAI,EAAEK,OAAO,EAAEI,WAAW,EAAEE,YAAY,CAAC,CAAC;EAE9C,MAAMK,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMa,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpC,IAAIb,OAAO,CAACE,KAAK,EAAEU,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEd,OAAO,CAACE,KAAK,CAAC;MACxD,IAAIF,OAAO,CAACG,QAAQ,EAAES,MAAM,CAACE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC;;MAEvD;MACAF,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEV,WAAW,CAACW,QAAQ,CAAC,CAAC,CAAC;MAC7CH,MAAM,CAACE,MAAM,CAAC,WAAW,EAAER,YAAY,CAACS,QAAQ,CAAC,CAAC,CAAC;MAEnD,MAAMC,QAAQ,GAAG,MAAM5B,GAAG,CAAC6B,GAAG,CAAC,aAAaL,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;MAChE,IAAIC,QAAQ,CAACE,IAAI,EAAE;QACjBrB,UAAU,CAACmB,QAAQ,CAACE,IAAI,CAACtB,OAAO,IAAIoB,QAAQ,CAACE,IAAI,CAACC,OAAO,IAAI,EAAE,CAAC;QAChEV,aAAa,CAACO,QAAQ,CAACE,IAAI,CAACE,KAAK,IAAIJ,QAAQ,CAACE,IAAI,CAACG,KAAK,IAAI,CAAC,CAAC;MAChE;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE;MACAzB,UAAU,CAAC,CACT;QACE2B,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,iBAAiB;QACxBC,WAAW,EAAE,yGAAyG;QACtHxB,KAAK,EAAE,UAAU;QACjByB,aAAa,EAAE,UAAU;QACzBC,SAAS,EAAE,IAAI;QACfC,kBAAkB,EAAE,GAAG;QACvBC,WAAW,EAAE,IAAI;QACjBC,cAAc,EAAE,CAAC;QACjBC,aAAa,EAAE,EAAE;QACjBC,mBAAmB,EAAE,EAAE;QACvBC,aAAa,EAAE,IAAI;QACnBC,WAAW,EAAE,IAAI;QACjBC,eAAe,EAAE,sBAAsB;QACvCC,aAAa,EAAE,EAAE;QACjBC,UAAU,EAAE;MACd,CAAC,EACD;QACEd,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,uBAAuB;QAC9BC,WAAW,EAAE,oFAAoF;QACjGxB,KAAK,EAAE,cAAc;QACrByB,aAAa,EAAE,eAAe;QAC9BC,SAAS,EAAE,IAAI;QACfC,kBAAkB,EAAE,GAAG;QACvBC,WAAW,EAAE,KAAK;QAClBC,cAAc,EAAE,CAAC;QACjBC,aAAa,EAAE,EAAE;QACjBC,mBAAmB,EAAE,CAAC;QACtBC,aAAa,EAAE,IAAI;QACnBC,WAAW,EAAE,KAAK;QAClBE,aAAa,EAAE,CACb;UAAEb,EAAE,EAAE,CAAC;UAAEC,KAAK,EAAE,iBAAiB;UAAEc,QAAQ,EAAE;QAAG,CAAC,CAClD;QACDD,UAAU,EAAE;MACd,CAAC,EACD;QACEd,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,qBAAqB;QAC5BC,WAAW,EAAE,4DAA4D;QACzExB,KAAK,EAAE,UAAU;QACjByB,aAAa,EAAE,QAAQ;QACvBC,SAAS,EAAE,GAAG;QACdC,kBAAkB,EAAE,GAAG;QACvBC,WAAW,EAAE,IAAI;QACjBC,cAAc,EAAE,CAAC;QACjBC,aAAa,EAAE,EAAE;QACjBC,mBAAmB,EAAE,CAAC;QACtBC,aAAa,EAAE,KAAK;QACpBC,WAAW,EAAE,KAAK;QAClBE,aAAa,EAAE,CACb;UAAEb,EAAE,EAAE,CAAC;UAAEC,KAAK,EAAE,iBAAiB;UAAEc,QAAQ,EAAE;QAAG,CAAC,CAClD;QACDD,UAAU,EAAE;MACd,CAAC,CACF,CAAC;MACF7B,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyC,gBAAgB,GAAIC,IAAY,IAAK;IACzCpC,cAAc,CAACoC,IAAI,CAAC;IACpBC,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;EAED,MAAMC,wBAAwB,GAAIC,eAAuB,IAAK;IAC5DxC,eAAe,CAACwC,eAAe,CAAC;IAChC1C,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAM2C,YAAY,GAAG,MAAAA,CAAOC,QAAgB,EAAEC,WAAmB,KAAK;IACpE,IAAI;MACFrD,UAAU,CAACD,OAAO,CAACuD,GAAG,CAACC,MAAM,IAC3BA,MAAM,CAAC5B,EAAE,KAAKyB,QAAQ,GAClB;QAAE,GAAGG,MAAM;QAAEjB,WAAW,EAAE,IAAI;QAAEC,eAAe,EAAE,IAAIiB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAAE,CAAC,GAC3EF,MACN,CAAC,CAAC;MACFG,KAAK,CAAC,iCAAiCL,WAAW,KAAK,CAAC;IAC1D,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDiC,KAAK,CAAC,+BAA+B,CAAC;IACxC;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBvD,UAAU,CAAC;MACTC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAMoD,cAAc,GAAIC,OAAe,IAAK;IAC1C,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIG,IAAI,GAAG,CAAC,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE,EAAE;IACpD;IACA,OAAO,GAAGA,IAAI,KAAK;EACrB,CAAC;EAED,MAAMC,aAAa,GAAI7D,KAAa,IAAK;IACvC,QAAQA,KAAK;MACX,KAAK,UAAU;QACb,OAAO,mEAAmE;MAC5E,KAAK,cAAc;QACjB,OAAO,uEAAuE;MAChF,KAAK,UAAU;QACb,OAAO,2DAA2D;MACpE;QACE,OAAO,+DAA+D;IAC1E;EACF,CAAC;;EAED;EACA,MAAM8D,UAAU,GAAGJ,IAAI,CAACK,IAAI,CAACzD,UAAU,GAAGF,YAAY,CAAC;EAEvD,IAAI,CAACX,IAAI,EAAE;IACT,oBACEL,OAAA;MAAK4E,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C7E,OAAA;QAAK4E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7E,OAAA;UAAI4E,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnEjF,OAAA;UAAG4E,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJjF,OAAA;UACEkF,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEjF,OAAA,CAAAE,SAAA;IAAA2E,QAAA,gBACA7E,OAAA,CAACJ,aAAa;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjBjF,OAAA;MAAK4E,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1C7E,OAAA;QAAK4E,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB7E,OAAA;UAAI4E,SAAS,EAAC,uDAAuD;UAAAC,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLjF,OAAA;UAAG4E,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAEhD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNjF,OAAA;QAAK4E,SAAS,EAAC,qGAAqG;QAAAC,QAAA,eAClH7E,OAAA;UAAK4E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD7E,OAAA;YAAA6E,QAAA,gBACE7E,OAAA;cAAO4E,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAEnF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjF,OAAA;cACEmF,KAAK,EAAEzE,OAAO,CAACE,KAAM;cACrBwE,QAAQ,EAAGC,CAAC,IAAK1E,UAAU,CAAC2E,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE1E,KAAK,EAAEyE,CAAC,CAACE,MAAM,CAACJ;cAAM,CAAC,CAAC,CAAE;cAC1EP,SAAS,EAAC,iLAAiL;cAAAC,QAAA,gBAE3L7E,OAAA;gBAAQmF,KAAK,EAAC,EAAE;gBAAAN,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1CjF,OAAA;gBAAQmF,KAAK,EAAC,UAAU;gBAAAN,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1CjF,OAAA;gBAAQmF,KAAK,EAAC,cAAc;gBAAAN,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnDjF,OAAA;gBAAQmF,KAAK,EAAC,UAAU;gBAAAN,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENjF,OAAA;YAAK4E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC7E,OAAA;cACEwF,IAAI,EAAC,UAAU;cACftD,EAAE,EAAC,UAAU;cACbuD,OAAO,EAAE/E,OAAO,CAACG,QAAS;cAC1BuE,QAAQ,EAAGC,CAAC,IAAK1E,UAAU,CAAC2E,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEzE,QAAQ,EAAEwE,CAAC,CAACE,MAAM,CAACE;cAAQ,CAAC,CAAC,CAAE;cAC/Eb,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACFjF,OAAA;cAAO0F,OAAO,EAAC,UAAU;cAACd,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAAC;YAE3F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENjF,OAAA;YACE2F,OAAO,EAAEzB,YAAa;YACtBU,SAAS,EAAC,uJAAuJ;YAAAC,QAAA,EAClK;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLzE,OAAO,iBACNR,OAAA;QAAK4E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC7E,OAAA;UAAK4E,SAAS,EAAC;QAA2E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjGjF,OAAA;UAAG4E,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CACN,EAGA,CAACzE,OAAO,iBACPR,OAAA,CAAAE,SAAA;QAAA2E,QAAA,gBACE7E,OAAA;UAAK4E,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EACvEvE,OAAO,CAACuD,GAAG,CAAC,CAACC,MAAM,EAAE8B,KAAK,kBACzB5F,OAAA,CAACN,MAAM,CAACmG,GAAG;YAETjB,SAAS,EAAC,8IAA8I;YACxJkB,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAEP,KAAK,GAAG;YAAI,CAAE;YAAAf,QAAA,gBAGnC7E,OAAA;cAAK4E,SAAS,EAAC,2FAA2F;cAAAC,QAAA,eACxG7E,OAAA;gBAAM4E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEf,MAAM,CAACxB;cAAS;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eAENjF,OAAA;cAAK4E,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAElB7E,OAAA;gBAAK4E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD7E,OAAA;kBAAI4E,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAChEf,MAAM,CAAC3B;gBAAK;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,EACJnB,MAAM,CAACtB,WAAW,iBACjBxC,OAAA;kBAAM4E,SAAS,EAAC,sGAAsG;kBAAAC,QAAA,EAAC;gBAEvH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENjF,OAAA;gBAAG4E,SAAS,EAAC,4DAA4D;gBAAAC,QAAA,EACtEf,MAAM,CAAC1B;cAAW;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eAGJjF,OAAA;gBAAK4E,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B7E,OAAA;kBAAK4E,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,gBACxD7E,OAAA;oBAAM4E,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjEjF,OAAA;oBAAM4E,SAAS,EAAE,8CAA8CH,aAAa,CAACX,MAAM,CAAClD,KAAK,CAAC,EAAG;oBAAAiE,QAAA,EAC1Ff,MAAM,CAACzB;kBAAa;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNjF,OAAA;kBAAK4E,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,gBACxD7E,OAAA;oBAAM4E,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChEjF,OAAA;oBAAM4E,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EAAEV,cAAc,CAACL,MAAM,CAACvB,kBAAkB;kBAAC;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC,eACNjF,OAAA;kBAAK4E,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,gBACxD7E,OAAA;oBAAM4E,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjEjF,OAAA;oBAAM4E,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EAAEf,MAAM,CAACpB;kBAAa;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLnB,MAAM,CAACjB,WAAW,iBACjB7C,OAAA;gBAAK4E,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB7E,OAAA;kBAAK4E,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,gBAC7D7E,OAAA;oBAAM4E,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrEjF,OAAA;oBAAM4E,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,GAAEf,MAAM,CAACnB,mBAAmB,EAAC,GAAC;kBAAA;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACNjF,OAAA;kBAAK4E,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,eACnE7E,OAAA;oBACE4E,SAAS,EAAC,6DAA6D;oBACvEwB,KAAK,EAAE;sBAAEC,KAAK,EAAE,GAAGvC,MAAM,CAACnB,mBAAmB;oBAAI;kBAAE;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAGDjF,OAAA;gBAAK4E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EACxBf,MAAM,CAACjB,WAAW,gBACjB7C,OAAA;kBAAQ4E,SAAS,EAAC,0GAA0G;kBAAAC,QAAA,EAAC;gBAE7H;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,GACPnB,MAAM,CAAClB,aAAa,gBACtB5C,OAAA;kBACE2F,OAAO,EAAEA,CAAA,KAAMjC,YAAY,CAACI,MAAM,CAAC5B,EAAE,EAAE4B,MAAM,CAAC3B,KAAK,CAAE;kBACrDyC,SAAS,EAAC,sGAAsG;kBAAAC,QAAA,EACjH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,gBAETjF,OAAA;kBACEsG,QAAQ;kBACR1B,SAAS,EAAC,0HAA0H;kBAAAC,QAAA,EACrI;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cACT;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GApFDnB,MAAM,CAAC5B,EAAE;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqFJ,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGL/D,UAAU,GAAG,CAAC,iBACblB,OAAA;UAAK4E,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB7E,OAAA,CAACH,UAAU;YACTiB,WAAW,EAAEA,WAAY;YACzB4D,UAAU,EAAEA,UAAW;YACvBxD,UAAU,EAAEA,UAAW;YACvBF,YAAY,EAAEA,YAAa;YAC3BuF,YAAY,EAAErD,gBAAiB;YAC/BsD,oBAAoB,EAAEhD,wBAAyB;YAC/CiD,gBAAgB,EAAE,IAAK;YACvBC,QAAQ,EAAE,IAAK;YACfC,mBAAmB,EAAEvF;UAAwB;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA,eACD,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAC7E,EAAA,CA1WID,WAAqB;EAAA,QACRR,OAAO;AAAA;AAAAiH,EAAA,GADpBzG,WAAqB;AA4W3B,eAAeA,WAAW;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}