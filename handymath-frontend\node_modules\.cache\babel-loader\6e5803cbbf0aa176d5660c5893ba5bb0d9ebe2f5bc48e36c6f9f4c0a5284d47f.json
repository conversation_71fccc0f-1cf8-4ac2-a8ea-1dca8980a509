{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo02xDS0';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createMatAlgo02xDS0 = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix nonzero items and invokes the callback function f(Dij, Sij).\n   * Callback function invoked NNZ times (number of nonzero items in SparseMatrix).\n   *\n   *\n   *          ┌  f(Dij, Sij)  ; S(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  0            ; otherwise\n   *\n   *\n   * @param {Matrix}   denseMatrix       The DenseMatrix instance (D)\n   * @param {Matrix}   sparseMatrix      The SparseMatrix instance (S)\n   * @param {Function} callback          The f(Dij,Sij) operation to invoke, where Dij = DenseMatrix(i,j) and Sij = SparseMatrix(i,j)\n   * @param {boolean}  inverse           A true value indicates callback should be invoked f(Sij,Dij)\n   *\n   * @return {Matrix}                    SparseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97477571\n   */\n  return function matAlgo02xDS0(denseMatrix, sparseMatrix, callback, inverse) {\n    // dense matrix arrays\n    var adata = denseMatrix._data;\n    var asize = denseMatrix._size;\n    var adt = denseMatrix._datatype || denseMatrix.getDataType();\n    // sparse matrix arrays\n    var bvalues = sparseMatrix._values;\n    var bindex = sparseMatrix._index;\n    var bptr = sparseMatrix._ptr;\n    var bsize = sparseMatrix._size;\n    var bdt = sparseMatrix._datatype || sparseMatrix._data === undefined ? sparseMatrix._datatype : sparseMatrix.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // sparse matrix cannot be a Pattern matrix\n    if (!bvalues) {\n      throw new Error('Cannot perform operation on Dense Matrix and Pattern Sparse Matrix');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      // datatype\n      dt = adt;\n      // find signature that matches (dt, dt)\n      eq = typed.find(equalScalar, [dt, dt]);\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result (SparseMatrix)\n    var cvalues = [];\n    var cindex = [];\n    var cptr = [];\n\n    // loop columns in b\n    for (var j = 0; j < columns; j++) {\n      // update cptr\n      cptr[j] = cindex.length;\n      // values in column j\n      for (var k0 = bptr[j], k1 = bptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        var i = bindex[k];\n        // update C(i,j)\n        var cij = inverse ? cf(bvalues[k], adata[i][j]) : cf(adata[i][j], bvalues[k]);\n        // check for nonzero\n        if (!eq(cij, zero)) {\n          // push i & v\n          cindex.push(i);\n          cvalues.push(cij);\n        }\n      }\n    }\n    // update cptr\n    cptr[columns] = cindex.length;\n\n    // return sparse matrix\n    return sparseMatrix.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === denseMatrix._datatype && bdt === sparseMatrix._datatype ? dt : undefined\n    });\n  };\n});", "map": {"version": 3, "names": ["factory", "DimensionError", "name", "dependencies", "createMatAlgo02xDS0", "_ref", "typed", "equalScalar", "matAlgo02xDS0", "denseMatrix", "sparseMatrix", "callback", "inverse", "adata", "_data", "asize", "_size", "adt", "_datatype", "getDataType", "bvalues", "_values", "bindex", "_index", "bptr", "_ptr", "bsize", "bdt", "undefined", "length", "RangeError", "Error", "rows", "columns", "dt", "eq", "zero", "cf", "find", "convert", "cvalues", "cindex", "cptr", "j", "k0", "k1", "k", "i", "cij", "push", "createSparseMatrix", "values", "index", "ptr", "size", "datatype"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/utils/matAlgo02xDS0.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo02xDS0';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createMatAlgo02xDS0 = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix nonzero items and invokes the callback function f(Dij, Sij).\n   * Callback function invoked NNZ times (number of nonzero items in SparseMatrix).\n   *\n   *\n   *          ┌  f(Dij, Sij)  ; S(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  0            ; otherwise\n   *\n   *\n   * @param {Matrix}   denseMatrix       The DenseMatrix instance (D)\n   * @param {Matrix}   sparseMatrix      The SparseMatrix instance (S)\n   * @param {Function} callback          The f(Dij,Sij) operation to invoke, where Dij = DenseMatrix(i,j) and Sij = SparseMatrix(i,j)\n   * @param {boolean}  inverse           A true value indicates callback should be invoked f(Sij,Dij)\n   *\n   * @return {Matrix}                    SparseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97477571\n   */\n  return function matAlgo02xDS0(denseMatrix, sparseMatrix, callback, inverse) {\n    // dense matrix arrays\n    var adata = denseMatrix._data;\n    var asize = denseMatrix._size;\n    var adt = denseMatrix._datatype || denseMatrix.getDataType();\n    // sparse matrix arrays\n    var bvalues = sparseMatrix._values;\n    var bindex = sparseMatrix._index;\n    var bptr = sparseMatrix._ptr;\n    var bsize = sparseMatrix._size;\n    var bdt = sparseMatrix._datatype || sparseMatrix._data === undefined ? sparseMatrix._datatype : sparseMatrix.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // sparse matrix cannot be a Pattern matrix\n    if (!bvalues) {\n      throw new Error('Cannot perform operation on Dense Matrix and Pattern Sparse Matrix');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      // datatype\n      dt = adt;\n      // find signature that matches (dt, dt)\n      eq = typed.find(equalScalar, [dt, dt]);\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result (SparseMatrix)\n    var cvalues = [];\n    var cindex = [];\n    var cptr = [];\n\n    // loop columns in b\n    for (var j = 0; j < columns; j++) {\n      // update cptr\n      cptr[j] = cindex.length;\n      // values in column j\n      for (var k0 = bptr[j], k1 = bptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        var i = bindex[k];\n        // update C(i,j)\n        var cij = inverse ? cf(bvalues[k], adata[i][j]) : cf(adata[i][j], bvalues[k]);\n        // check for nonzero\n        if (!eq(cij, zero)) {\n          // push i & v\n          cindex.push(i);\n          cvalues.push(cij);\n        }\n      }\n    }\n    // update cptr\n    cptr[columns] = cindex.length;\n\n    // return sparse matrix\n    return sparseMatrix.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === denseMatrix._datatype && bdt === sparseMatrix._datatype ? dt : undefined\n    });\n  };\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,IAAIC,IAAI,GAAG,eAAe;AAC1B,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC;AAC3C,OAAO,IAAIC,mBAAmB,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAClF,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASG,aAAaA,CAACC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IAC1E;IACA,IAAIC,KAAK,GAAGJ,WAAW,CAACK,KAAK;IAC7B,IAAIC,KAAK,GAAGN,WAAW,CAACO,KAAK;IAC7B,IAAIC,GAAG,GAAGR,WAAW,CAACS,SAAS,IAAIT,WAAW,CAACU,WAAW,CAAC,CAAC;IAC5D;IACA,IAAIC,OAAO,GAAGV,YAAY,CAACW,OAAO;IAClC,IAAIC,MAAM,GAAGZ,YAAY,CAACa,MAAM;IAChC,IAAIC,IAAI,GAAGd,YAAY,CAACe,IAAI;IAC5B,IAAIC,KAAK,GAAGhB,YAAY,CAACM,KAAK;IAC9B,IAAIW,GAAG,GAAGjB,YAAY,CAACQ,SAAS,IAAIR,YAAY,CAACI,KAAK,KAAKc,SAAS,GAAGlB,YAAY,CAACQ,SAAS,GAAGR,YAAY,CAACS,WAAW,CAAC,CAAC;;IAE1H;IACA,IAAIJ,KAAK,CAACc,MAAM,KAAKH,KAAK,CAACG,MAAM,EAAE;MACjC,MAAM,IAAI5B,cAAc,CAACc,KAAK,CAACc,MAAM,EAAEH,KAAK,CAACG,MAAM,CAAC;IACtD;;IAEA;IACA,IAAId,KAAK,CAAC,CAAC,CAAC,KAAKW,KAAK,CAAC,CAAC,CAAC,IAAIX,KAAK,CAAC,CAAC,CAAC,KAAKW,KAAK,CAAC,CAAC,CAAC,EAAE;MAClD,MAAM,IAAII,UAAU,CAAC,gCAAgC,GAAGf,KAAK,GAAG,yBAAyB,GAAGW,KAAK,GAAG,GAAG,CAAC;IAC1G;;IAEA;IACA,IAAI,CAACN,OAAO,EAAE;MACZ,MAAM,IAAIW,KAAK,CAAC,oEAAoE,CAAC;IACvF;;IAEA;IACA,IAAIC,IAAI,GAAGjB,KAAK,CAAC,CAAC,CAAC;IACnB,IAAIkB,OAAO,GAAGlB,KAAK,CAAC,CAAC,CAAC;;IAEtB;IACA,IAAImB,EAAE;IACN;IACA,IAAIC,EAAE,GAAG5B,WAAW;IACpB;IACA,IAAI6B,IAAI,GAAG,CAAC;IACZ;IACA,IAAIC,EAAE,GAAG1B,QAAQ;;IAEjB;IACA,IAAI,OAAOM,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAKU,GAAG,IAAIV,GAAG,KAAK,OAAO,EAAE;MAC7D;MACAiB,EAAE,GAAGjB,GAAG;MACR;MACAkB,EAAE,GAAG7B,KAAK,CAACgC,IAAI,CAAC/B,WAAW,EAAE,CAAC2B,EAAE,EAAEA,EAAE,CAAC,CAAC;MACtC;MACAE,IAAI,GAAG9B,KAAK,CAACiC,OAAO,CAAC,CAAC,EAAEL,EAAE,CAAC;MAC3B;MACAG,EAAE,GAAG/B,KAAK,CAACgC,IAAI,CAAC3B,QAAQ,EAAE,CAACuB,EAAE,EAAEA,EAAE,CAAC,CAAC;IACrC;;IAEA;IACA,IAAIM,OAAO,GAAG,EAAE;IAChB,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIC,IAAI,GAAG,EAAE;;IAEb;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,OAAO,EAAEU,CAAC,EAAE,EAAE;MAChC;MACAD,IAAI,CAACC,CAAC,CAAC,GAAGF,MAAM,CAACZ,MAAM;MACvB;MACA,KAAK,IAAIe,EAAE,GAAGpB,IAAI,CAACmB,CAAC,CAAC,EAAEE,EAAE,GAAGrB,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,EAAEG,CAAC,GAAGF,EAAE,EAAEE,CAAC,GAAGD,EAAE,EAAEC,CAAC,EAAE,EAAE;QAC5D;QACA,IAAIC,CAAC,GAAGzB,MAAM,CAACwB,CAAC,CAAC;QACjB;QACA,IAAIE,GAAG,GAAGpC,OAAO,GAAGyB,EAAE,CAACjB,OAAO,CAAC0B,CAAC,CAAC,EAAEjC,KAAK,CAACkC,CAAC,CAAC,CAACJ,CAAC,CAAC,CAAC,GAAGN,EAAE,CAACxB,KAAK,CAACkC,CAAC,CAAC,CAACJ,CAAC,CAAC,EAAEvB,OAAO,CAAC0B,CAAC,CAAC,CAAC;QAC7E;QACA,IAAI,CAACX,EAAE,CAACa,GAAG,EAAEZ,IAAI,CAAC,EAAE;UAClB;UACAK,MAAM,CAACQ,IAAI,CAACF,CAAC,CAAC;UACdP,OAAO,CAACS,IAAI,CAACD,GAAG,CAAC;QACnB;MACF;IACF;IACA;IACAN,IAAI,CAACT,OAAO,CAAC,GAAGQ,MAAM,CAACZ,MAAM;;IAE7B;IACA,OAAOnB,YAAY,CAACwC,kBAAkB,CAAC;MACrCC,MAAM,EAAEX,OAAO;MACfY,KAAK,EAAEX,MAAM;MACbY,GAAG,EAAEX,IAAI;MACTY,IAAI,EAAE,CAACtB,IAAI,EAAEC,OAAO,CAAC;MACrBsB,QAAQ,EAAEtC,GAAG,KAAKR,WAAW,CAACS,SAAS,IAAIS,GAAG,KAAKjB,YAAY,CAACQ,SAAS,GAAGgB,EAAE,GAAGN;IACnF,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}