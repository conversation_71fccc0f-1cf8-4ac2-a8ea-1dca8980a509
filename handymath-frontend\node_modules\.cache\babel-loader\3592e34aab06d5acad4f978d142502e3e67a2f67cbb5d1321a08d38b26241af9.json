{"ast": null, "code": "export var cbrtDocs = {\n  name: 'cbrt',\n  category: 'Arithmetic',\n  syntax: ['cbrt(x)', 'cbrt(x, allRoots)'],\n  description: 'Compute the cubic root value. If x = y * y * y, then y is the cubic root of x. When `x` is a number or complex number, an optional second argument `allRoots` can be provided to return all three cubic roots. If not provided, the principal root is returned',\n  examples: ['cbrt(64)', 'cube(4)', 'cbrt(-8)', 'cbrt(2 + 3i)', 'cbrt(8i)', 'cbrt(8i, true)', 'cbrt(27 m^3)'],\n  seealso: ['square', 'sqrt', 'cube', 'multiply']\n};", "map": {"version": 3, "names": ["cbrtDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/cbrt.js"], "sourcesContent": ["export var cbrtDocs = {\n  name: 'cbrt',\n  category: 'Arithmetic',\n  syntax: ['cbrt(x)', 'cbrt(x, allRoots)'],\n  description: 'Compute the cubic root value. If x = y * y * y, then y is the cubic root of x. When `x` is a number or complex number, an optional second argument `allRoots` can be provided to return all three cubic roots. If not provided, the principal root is returned',\n  examples: ['cbrt(64)', 'cube(4)', 'cbrt(-8)', 'cbrt(2 + 3i)', 'cbrt(8i)', 'cbrt(8i, true)', 'cbrt(27 m^3)'],\n  seealso: ['square', 'sqrt', 'cube', 'multiply']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,SAAS,EAAE,mBAAmB,CAAC;EACxCC,WAAW,EAAE,gQAAgQ;EAC7QC,QAAQ,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,gBAAgB,EAAE,cAAc,CAAC;EAC3GC,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU;AAChD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}