{"ast": null, "code": "export var randomDocs = {\n  name: 'random',\n  category: 'Probability',\n  syntax: ['random()', 'random(max)', 'random(min, max)', 'random(size)', 'random(size, max)', 'random(size, min, max)'],\n  description: 'Return a random number.',\n  examples: ['random()', 'random(10, 20)', 'random([2, 3])'],\n  seealso: ['pickRandom', 'randomInt']\n};", "map": {"version": 3, "names": ["randomDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/probability/random.js"], "sourcesContent": ["export var randomDocs = {\n  name: 'random',\n  category: 'Probability',\n  syntax: ['random()', 'random(max)', 'random(min, max)', 'random(size)', 'random(size, max)', 'random(size, min, max)'],\n  description: 'Return a random number.',\n  examples: ['random()', 'random(10, 20)', 'random([2, 3])'],\n  seealso: ['pickRandom', 'randomInt']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,kBAAkB,EAAE,cAAc,EAAE,mBAAmB,EAAE,wBAAwB,CAAC;EACtHC,WAAW,EAAE,yBAAyB;EACtCC,QAAQ,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;EAC1DC,OAAO,EAAE,CAAC,YAAY,EAAE,WAAW;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}