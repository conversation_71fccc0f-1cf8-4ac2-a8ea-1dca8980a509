{"ast": null, "code": "import seedrandom from 'seedrandom';\nvar singletonRandom = /* #__PURE__ */seedrandom(Date.now());\nexport function createRng(randomSeed) {\n  var random;\n\n  // create a new random generator with given seed\n  function setSeed(seed) {\n    random = seed === null ? singletonRandom : seedrandom(String(seed));\n  }\n\n  // initialize a seeded pseudo random number generator with config's random seed\n  setSeed(randomSeed);\n\n  // wrapper function so the rng can be updated via generator\n  function rng() {\n    return random();\n  }\n  return rng;\n}", "map": {"version": 3, "names": ["seedrandom", "singletonRandom", "Date", "now", "createRng", "randomSeed", "random", "setSeed", "seed", "String", "rng"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/probability/util/seededRNG.js"], "sourcesContent": ["import seedrandom from 'seedrandom';\nvar singletonRandom = /* #__PURE__ */seedrandom(Date.now());\nexport function createRng(randomSeed) {\n  var random;\n\n  // create a new random generator with given seed\n  function setSeed(seed) {\n    random = seed === null ? singletonRandom : seedrandom(String(seed));\n  }\n\n  // initialize a seeded pseudo random number generator with config's random seed\n  setSeed(randomSeed);\n\n  // wrapper function so the rng can be updated via generator\n  function rng() {\n    return random();\n  }\n  return rng;\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,IAAIC,eAAe,GAAG,eAAeD,UAAU,CAACE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;AAC3D,OAAO,SAASC,SAASA,CAACC,UAAU,EAAE;EACpC,IAAIC,MAAM;;EAEV;EACA,SAASC,OAAOA,CAACC,IAAI,EAAE;IACrBF,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGP,eAAe,GAAGD,UAAU,CAACS,MAAM,CAACD,IAAI,CAAC,CAAC;EACrE;;EAEA;EACAD,OAAO,CAACF,UAAU,CAAC;;EAEnB;EACA,SAASK,GAAGA,CAAA,EAAG;IACb,OAAOJ,MAAM,CAAC,CAAC;EACjB;EACA,OAAOI,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}