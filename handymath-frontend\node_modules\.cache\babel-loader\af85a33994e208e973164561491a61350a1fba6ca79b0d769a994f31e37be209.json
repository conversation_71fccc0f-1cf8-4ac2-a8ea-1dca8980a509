{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { piDependencies } from './dependenciesPi.generated.js';\nimport { createUppercasePi } from '../../factoriesAny.js';\nexport var PIDependencies = {\n  piDependencies,\n  createUppercasePi\n};", "map": {"version": 3, "names": ["piDependencies", "createUppercasePi", "PIDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesUppercasePi.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { piDependencies } from './dependenciesPi.generated.js';\nimport { createUppercasePi } from '../../factoriesAny.js';\nexport var PIDependencies = {\n  piDependencies,\n  createUppercasePi\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,OAAO,IAAIC,cAAc,GAAG;EAC1BF,cAAc;EACdC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}