{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nvar name = 'matAlgo10xSids';\nvar dependencies = ['typed', 'DenseMatrix'];\nexport var createMatAlgo10xSids = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    DenseMatrix\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix S nonzero items and invokes the callback function f(Sij, b).\n   * Callback function invoked NZ times (number of nonzero items in S).\n   *\n   *\n   *          ┌  f(Sij, b)  ; S(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  b          ; otherwise\n   *\n   *\n   * @param {Matrix}   s                 The SparseMatrix instance (S)\n   * @param {Scalar}   b                 The Scalar value\n   * @param {Function} callback          The f(Aij,b) operation to invoke\n   * @param {boolean}  inverse           A true value indicates callback should be invoked f(b,Sij)\n   *\n   * @return {Matrix}                    DenseMatrix (C)\n   *\n   * https://github.com/josdejong/mathjs/pull/346#issuecomment-97626813\n   */\n  return function matAlgo10xSids(s, b, callback, inverse) {\n    // sparse matrix arrays\n    var avalues = s._values;\n    var aindex = s._index;\n    var aptr = s._ptr;\n    var asize = s._size;\n    var adt = s._datatype;\n\n    // sparse matrix cannot be a Pattern matrix\n    if (!avalues) {\n      throw new Error('Cannot perform operation on Pattern Sparse Matrix and Scalar value');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string') {\n      // datatype\n      dt = adt;\n      // convert b to the same datatype\n      b = typed.convert(b, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays\n    var cdata = [];\n\n    // workspaces\n    var x = [];\n    // marks indicating we have a value in x for a given column\n    var w = [];\n\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // columns mark\n      var mark = j + 1;\n      // values in j\n      for (var k0 = aptr[j], k1 = aptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        var r = aindex[k];\n        // update workspace\n        x[r] = avalues[k];\n        w[r] = mark;\n      }\n      // loop rows\n      for (var i = 0; i < rows; i++) {\n        // initialize C on first column\n        if (j === 0) {\n          // create row array\n          cdata[i] = [];\n        }\n        // check sparse matrix has a value @ i,j\n        if (w[i] === mark) {\n          // invoke callback, update C\n          cdata[i][j] = inverse ? cf(b, x[i]) : cf(x[i], b);\n        } else {\n          // dense matrix value @ i, j\n          cdata[i][j] = b;\n        }\n      }\n    }\n\n    // return dense matrix\n    return new DenseMatrix({\n      data: cdata,\n      size: [rows, columns],\n      datatype: dt\n    });\n  };\n});", "map": {"version": 3, "names": ["factory", "name", "dependencies", "createMatAlgo10xSids", "_ref", "typed", "DenseMatrix", "matAlgo10xSids", "s", "b", "callback", "inverse", "avalues", "_values", "aindex", "_index", "aptr", "_ptr", "asize", "_size", "adt", "_datatype", "Error", "rows", "columns", "dt", "cf", "convert", "find", "cdata", "x", "w", "j", "mark", "k0", "k1", "k", "r", "i", "data", "size", "datatype"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/utils/matAlgo10xSids.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nvar name = 'matAlgo10xSids';\nvar dependencies = ['typed', 'DenseMatrix'];\nexport var createMatAlgo10xSids = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    DenseMatrix\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix S nonzero items and invokes the callback function f(Sij, b).\n   * Callback function invoked NZ times (number of nonzero items in S).\n   *\n   *\n   *          ┌  f(Sij, b)  ; S(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  b          ; otherwise\n   *\n   *\n   * @param {Matrix}   s                 The SparseMatrix instance (S)\n   * @param {Scalar}   b                 The Scalar value\n   * @param {Function} callback          The f(Aij,b) operation to invoke\n   * @param {boolean}  inverse           A true value indicates callback should be invoked f(b,Sij)\n   *\n   * @return {Matrix}                    DenseMatrix (C)\n   *\n   * https://github.com/josdejong/mathjs/pull/346#issuecomment-97626813\n   */\n  return function matAlgo10xSids(s, b, callback, inverse) {\n    // sparse matrix arrays\n    var avalues = s._values;\n    var aindex = s._index;\n    var aptr = s._ptr;\n    var asize = s._size;\n    var adt = s._datatype;\n\n    // sparse matrix cannot be a Pattern matrix\n    if (!avalues) {\n      throw new Error('Cannot perform operation on Pattern Sparse Matrix and Scalar value');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string') {\n      // datatype\n      dt = adt;\n      // convert b to the same datatype\n      b = typed.convert(b, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays\n    var cdata = [];\n\n    // workspaces\n    var x = [];\n    // marks indicating we have a value in x for a given column\n    var w = [];\n\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // columns mark\n      var mark = j + 1;\n      // values in j\n      for (var k0 = aptr[j], k1 = aptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        var r = aindex[k];\n        // update workspace\n        x[r] = avalues[k];\n        w[r] = mark;\n      }\n      // loop rows\n      for (var i = 0; i < rows; i++) {\n        // initialize C on first column\n        if (j === 0) {\n          // create row array\n          cdata[i] = [];\n        }\n        // check sparse matrix has a value @ i,j\n        if (w[i] === mark) {\n          // invoke callback, update C\n          cdata[i][j] = inverse ? cf(b, x[i]) : cf(x[i], b);\n        } else {\n          // dense matrix value @ i, j\n          cdata[i][j] = b;\n        }\n      }\n    }\n\n    // return dense matrix\n    return new DenseMatrix({\n      data: cdata,\n      size: [rows, columns],\n      datatype: dt\n    });\n  };\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,IAAIC,IAAI,GAAG,gBAAgB;AAC3B,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC;AAC3C,OAAO,IAAIC,oBAAoB,GAAG,eAAeH,OAAO,CAACC,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACnF,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASG,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACtD;IACA,IAAIC,OAAO,GAAGJ,CAAC,CAACK,OAAO;IACvB,IAAIC,MAAM,GAAGN,CAAC,CAACO,MAAM;IACrB,IAAIC,IAAI,GAAGR,CAAC,CAACS,IAAI;IACjB,IAAIC,KAAK,GAAGV,CAAC,CAACW,KAAK;IACnB,IAAIC,GAAG,GAAGZ,CAAC,CAACa,SAAS;;IAErB;IACA,IAAI,CAACT,OAAO,EAAE;MACZ,MAAM,IAAIU,KAAK,CAAC,oEAAoE,CAAC;IACvF;;IAEA;IACA,IAAIC,IAAI,GAAGL,KAAK,CAAC,CAAC,CAAC;IACnB,IAAIM,OAAO,GAAGN,KAAK,CAAC,CAAC,CAAC;;IAEtB;IACA,IAAIO,EAAE;IACN;IACA,IAAIC,EAAE,GAAGhB,QAAQ;;IAEjB;IACA,IAAI,OAAOU,GAAG,KAAK,QAAQ,EAAE;MAC3B;MACAK,EAAE,GAAGL,GAAG;MACR;MACAX,CAAC,GAAGJ,KAAK,CAACsB,OAAO,CAAClB,CAAC,EAAEgB,EAAE,CAAC;MACxB;MACAC,EAAE,GAAGrB,KAAK,CAACuB,IAAI,CAAClB,QAAQ,EAAE,CAACe,EAAE,EAAEA,EAAE,CAAC,CAAC;IACrC;;IAEA;IACA,IAAII,KAAK,GAAG,EAAE;;IAEd;IACA,IAAIC,CAAC,GAAG,EAAE;IACV;IACA,IAAIC,CAAC,GAAG,EAAE;;IAEV;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,OAAO,EAAEQ,CAAC,EAAE,EAAE;MAChC;MACA,IAAIC,IAAI,GAAGD,CAAC,GAAG,CAAC;MAChB;MACA,KAAK,IAAIE,EAAE,GAAGlB,IAAI,CAACgB,CAAC,CAAC,EAAEG,EAAE,GAAGnB,IAAI,CAACgB,CAAC,GAAG,CAAC,CAAC,EAAEI,CAAC,GAAGF,EAAE,EAAEE,CAAC,GAAGD,EAAE,EAAEC,CAAC,EAAE,EAAE;QAC5D;QACA,IAAIC,CAAC,GAAGvB,MAAM,CAACsB,CAAC,CAAC;QACjB;QACAN,CAAC,CAACO,CAAC,CAAC,GAAGzB,OAAO,CAACwB,CAAC,CAAC;QACjBL,CAAC,CAACM,CAAC,CAAC,GAAGJ,IAAI;MACb;MACA;MACA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,IAAI,EAAEe,CAAC,EAAE,EAAE;QAC7B;QACA,IAAIN,CAAC,KAAK,CAAC,EAAE;UACX;UACAH,KAAK,CAACS,CAAC,CAAC,GAAG,EAAE;QACf;QACA;QACA,IAAIP,CAAC,CAACO,CAAC,CAAC,KAAKL,IAAI,EAAE;UACjB;UACAJ,KAAK,CAACS,CAAC,CAAC,CAACN,CAAC,CAAC,GAAGrB,OAAO,GAAGe,EAAE,CAACjB,CAAC,EAAEqB,CAAC,CAACQ,CAAC,CAAC,CAAC,GAAGZ,EAAE,CAACI,CAAC,CAACQ,CAAC,CAAC,EAAE7B,CAAC,CAAC;QACnD,CAAC,MAAM;UACL;UACAoB,KAAK,CAACS,CAAC,CAAC,CAACN,CAAC,CAAC,GAAGvB,CAAC;QACjB;MACF;IACF;;IAEA;IACA,OAAO,IAAIH,WAAW,CAAC;MACrBiC,IAAI,EAAEV,KAAK;MACXW,IAAI,EAAE,CAACjB,IAAI,EAAEC,OAAO,CAAC;MACrBiB,QAAQ,EAAEhB;IACZ,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}