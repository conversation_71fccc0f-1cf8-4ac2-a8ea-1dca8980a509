{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';\nimport { logDependencies } from './dependenciesLog.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createLog1p } from '../../factoriesAny.js';\nexport var log1pDependencies = {\n  ComplexDependencies,\n  divideScalarDependencies,\n  logDependencies,\n  typedDependencies,\n  createLog1p\n};", "map": {"version": 3, "names": ["ComplexDependencies", "divideScalarDependencies", "logDependencies", "typedDependencies", "createLog1p", "log1pDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesLog1p.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';\nimport { logDependencies } from './dependenciesLog.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createLog1p } from '../../factoriesAny.js';\nexport var log1pDependencies = {\n  ComplexDependencies,\n  divideScalarDependencies,\n  logDependencies,\n  typedDependencies,\n  createLog1p\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,WAAW,QAAQ,uBAAuB;AACnD,OAAO,IAAIC,iBAAiB,GAAG;EAC7BL,mBAAmB;EACnBC,wBAAwB;EACxBC,eAAe;EACfC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}