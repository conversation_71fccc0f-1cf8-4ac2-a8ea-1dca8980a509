{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createMolarMassC12 } from '../../factoriesAny.js';\nexport var molarMassC12Dependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createMolarMassC12\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createMolarMassC12", "molarMassC12Dependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMolarMassC12.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createMolarMassC12 } from '../../factoriesAny.js';\nexport var molarMassC12Dependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createMolarMassC12\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,OAAO,IAAIC,wBAAwB,GAAG;EACpCH,qBAAqB;EACrBC,gBAAgB;EAChBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}