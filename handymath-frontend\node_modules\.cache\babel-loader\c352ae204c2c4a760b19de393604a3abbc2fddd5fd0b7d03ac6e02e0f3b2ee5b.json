{"ast": null, "code": "export var setDistinctDocs = {\n  name: 'setDistinct',\n  category: 'Set',\n  syntax: ['setDistinct(set)'],\n  description: 'Collect the distinct elements of a multiset. A multi-dimension array will be converted to a single-dimension array before the operation.',\n  examples: ['setDistinct([1, 1, 1, 2, 2, 3])'],\n  seealso: ['setMultiplicity']\n};", "map": {"version": 3, "names": ["setDistinctDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/set/setDistinct.js"], "sourcesContent": ["export var setDistinctDocs = {\n  name: 'setDistinct',\n  category: 'Set',\n  syntax: ['setDistinct(set)'],\n  description: 'Collect the distinct elements of a multiset. A multi-dimension array will be converted to a single-dimension array before the operation.',\n  examples: ['setDistinct([1, 1, 1, 2, 2, 3])'],\n  seealso: ['setMultiplicity']\n};"], "mappings": "AAAA,OAAO,IAAIA,eAAe,GAAG;EAC3BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,CAAC,kBAAkB,CAAC;EAC5BC,WAAW,EAAE,0IAA0I;EACvJC,QAAQ,EAAE,CAAC,iCAAiC,CAAC;EAC7CC,OAAO,EAAE,CAAC,iBAAiB;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}