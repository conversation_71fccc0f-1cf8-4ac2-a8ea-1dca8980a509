{"ast": null, "code": "export var dotDivideDocs = {\n  name: 'dotDivide',\n  category: 'Operators',\n  syntax: ['x ./ y', 'dotDivide(x, y)'],\n  description: 'Divide two values element wise.',\n  examples: ['a = [1, 2, 3; 4, 5, 6]', 'b = [2, 1, 1; 3, 2, 5]', 'a ./ b'],\n  seealso: ['multiply', 'dotMultiply', 'divide']\n};", "map": {"version": 3, "names": ["dotDivideDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/dotDivide.js"], "sourcesContent": ["export var dotDivideDocs = {\n  name: 'dotDivide',\n  category: 'Operators',\n  syntax: ['x ./ y', 'dotDivide(x, y)'],\n  description: 'Divide two values element wise.',\n  examples: ['a = [1, 2, 3; 4, 5, 6]', 'b = [2, 1, 1; 3, 2, 5]', 'a ./ b'],\n  seealso: ['multiply', 'dotMultiply', 'divide']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;EACrCC,WAAW,EAAE,iCAAiC;EAC9CC,QAAQ,EAAE,CAAC,wBAAwB,EAAE,wBAAwB,EAAE,QAAQ,CAAC;EACxEC,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,QAAQ;AAC/C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}