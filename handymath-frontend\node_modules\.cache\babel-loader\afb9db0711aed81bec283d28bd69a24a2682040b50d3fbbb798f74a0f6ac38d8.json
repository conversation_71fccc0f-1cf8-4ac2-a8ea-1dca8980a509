{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMean } from '../../factoriesAny.js';\nexport var meanDependencies = {\n  addDependencies,\n  divideDependencies,\n  typedDependencies,\n  createMean\n};", "map": {"version": 3, "names": ["addDependencies", "divideDependencies", "typedDependencies", "createMean", "meanDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMean.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMean } from '../../factoriesAny.js';\nexport var meanDependencies = {\n  addDependencies,\n  divideDependencies,\n  typedDependencies,\n  createMean\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,QAAQ,gCAAgC;AAChE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAO,IAAIC,gBAAgB,GAAG;EAC5BJ,eAAe;EACfC,kBAAkB;EAClBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}