{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\ExercisesPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport api from '../services/api';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExercisesPage = () => {\n  _s();\n  var _availableFilters$dif, _availableFilters$typ;\n  const {\n    user\n  } = useAuth();\n  const {\n    addNotification\n  } = useNotifications();\n\n  // Helper functions for notifications\n  const showSuccess = (title, message) => {\n    addNotification({\n      type: 'success',\n      title,\n      message\n    });\n  };\n  const showError = (title, message) => {\n    addNotification({\n      type: 'error',\n      title,\n      message\n    });\n  };\n  const showWarning = (title, message) => {\n    addNotification({\n      type: 'warning',\n      title,\n      message\n    });\n  };\n\n  // State\n  const [exercises, setExercises] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    difficulty: '',\n    type: '',\n    course: ''\n  });\n  const [availableFilters, setAvailableFilters] = useState({});\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(12);\n  const [totalItems, setTotalItems] = useState(0);\n\n  // Effects\n  useEffect(() => {\n    if (user) {\n      fetchExercises();\n    }\n  }, [user, filters]);\n\n  // API Functions\n  const fetchExercises = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      if (filters.difficulty) params.append('difficulty', filters.difficulty);\n      if (filters.type) params.append('type', filters.type);\n      if (filters.course) params.append('course', filters.course);\n      const response = await api.get(`/exercises/?${params.toString()}`);\n      if (response.data) {\n        setExercises(response.data.exercises || []);\n        setAvailableFilters(response.data.filters || {});\n      }\n    } catch (error) {\n      console.error('Erreur lors de la récupération des exercices:', error);\n      showError('Erreur', 'Impossible de charger les exercices');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Event Handlers\n  const handleFilterChange = (filterType, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n  };\n  const clearFilters = () => {\n    setFilters({\n      difficulty: '',\n      type: '',\n      course: ''\n    });\n  };\n\n  // Utility Functions\n  const getDifficultyColor = difficulty => {\n    switch (difficulty) {\n      case 'easy':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'medium':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'hard':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n  const getTypeIcon = type => {\n    switch (type) {\n      case 'equation':\n        return '📐';\n      case 'multiple_choice':\n        return '📝';\n      case 'calculation':\n        return '🧮';\n      case 'proof':\n        return '📋';\n      default:\n        return '📚';\n    }\n  };\n  const getStatusIcon = exercise => {\n    if (!exercise.user_attempt) return '🆕';\n    if (exercise.user_attempt.completed) {\n      return exercise.user_attempt.is_correct ? '✅' : '❌';\n    }\n    return '⏳';\n  };\n  const getStatusText = exercise => {\n    if (!exercise.user_attempt) return 'Nouveau';\n    if (exercise.user_attempt.completed) {\n      return exercise.user_attempt.is_correct ? 'Réussi' : 'Échoué';\n    }\n    return 'En cours';\n  };\n\n  // Loading and Error States\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Exercices Interactifs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Vous devez \\xEAtre connect\\xE9 pour acc\\xE9der aux exercices.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Main Render\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n      className: \"text-4xl font-bold mb-8 text-center text-primary-600\",\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: \"Exercices Interactifs\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold mb-4 flex items-center\",\n        children: \"\\uD83D\\uDD0D Filtres\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n            children: \"Difficult\\xE9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filters.difficulty,\n            onChange: e => handleFilterChange('difficulty', e.target.value),\n            className: \"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Toutes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), (_availableFilters$dif = availableFilters.difficulties) === null || _availableFilters$dif === void 0 ? void 0 : _availableFilters$dif.map(([value, label]) => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: value,\n              children: label\n            }, value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n            children: \"Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filters.type,\n            onChange: e => handleFilterChange('type', e.target.value),\n            className: \"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Tous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), (_availableFilters$typ = availableFilters.types) === null || _availableFilters$typ === void 0 ? void 0 : _availableFilters$typ.map(([value, label]) => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: value,\n              children: label\n            }, value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-end\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: clearFilters,\n            className: \"w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\",\n            children: \"Effacer filtres\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-600 dark:text-gray-400\",\n        children: [exercises.length, \" exercice\", exercises.length !== 1 ? 's' : '', \" trouv\\xE9\", exercises.length !== 1 ? 's' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 dark:text-gray-400\",\n        children: \"Chargement des exercices...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 9\n    }, this), !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: exercises.map((exercise, index) => {\n        var _exercise$user_attemp, _exercise$user_attemp2, _exercise$user_attemp3, _exercise$user_attemp4;\n        return /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: index * 0.1\n          },\n          whileHover: {\n            scale: 1.02\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl mr-3\",\n                  children: getTypeIcon(exercise.exercise_type)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 dark:text-white line-clamp-2\",\n                    children: exercise.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                    children: exercise.course.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xl\",\n                children: getStatusIcon(exercise)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 dark:text-gray-300 text-sm mb-4 line-clamp-3\",\n              children: exercise.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-2 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exercise.difficulty)}`,\n                children: exercise.difficulty_display\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300\",\n                children: exercise.type_display\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4 text-sm mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1\",\n                  children: \"\\uD83C\\uDFC6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [exercise.points, \" pts\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1\",\n                  children: \"\\u23F1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [Math.floor(exercise.time_limit / 60), \"min\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Statut:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `font-medium ${(_exercise$user_attemp = exercise.user_attempt) !== null && _exercise$user_attemp !== void 0 && _exercise$user_attemp.completed ? exercise.user_attempt.is_correct ? 'text-green-600' : 'text-red-600' : 'text-blue-600'}`,\n                  children: getStatusText(exercise)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this), ((_exercise$user_attemp2 = exercise.user_attempt) === null || _exercise$user_attemp2 === void 0 ? void 0 : _exercise$user_attemp2.completed) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 text-xs text-gray-600 dark:text-gray-400\",\n                children: [\"Points obtenus: \", exercise.user_attempt.points_earned, \"/\", exercise.points]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: `/exercises/${exercise.id}`,\n              className: `block w-full text-center py-3 px-4 rounded-lg font-medium transition-colors ${(_exercise$user_attemp3 = exercise.user_attempt) !== null && _exercise$user_attemp3 !== void 0 && _exercise$user_attemp3.completed ? 'bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300' : 'bg-primary-600 hover:bg-primary-700 text-white'}`,\n              children: (_exercise$user_attemp4 = exercise.user_attempt) !== null && _exercise$user_attemp4 !== void 0 && _exercise$user_attemp4.completed ? 'Revoir' : 'Commencer'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this)\n        }, exercise.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 9\n    }, this), !loading && exercises.length === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"text-center py-12\",\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      transition: {\n        delay: 0.3\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-6xl mb-4\",\n        children: \"\\uD83D\\uDCDA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold mb-2\",\n        children: \"Aucun exercice trouv\\xE9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 dark:text-gray-400 mb-4\",\n        children: \"Essayez de modifier vos filtres ou revenez plus tard.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: clearFilters,\n        className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors\",\n        children: \"Effacer les filtres\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 5\n  }, this);\n};\n_s(ExercisesPage, \"BnqML7GneqR8lpbMrbO2NVKNj4Q=\", false, function () {\n  return [useAuth, useNotifications];\n});\n_c = ExercisesPage;\nexport default ExercisesPage;\nvar _c;\n$RefreshReg$(_c, \"ExercisesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useAuth", "useNotifications", "api", "jsxDEV", "_jsxDEV", "ExercisesPage", "_s", "_availableFilters$dif", "_availableFilters$typ", "user", "addNotification", "showSuccess", "title", "message", "type", "showError", "showWarning", "exercises", "setExercises", "loading", "setLoading", "filters", "setFilters", "difficulty", "course", "availableFilters", "setAvailableFilters", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "totalItems", "setTotalItems", "fetchExercises", "params", "URLSearchParams", "append", "response", "get", "toString", "data", "error", "console", "handleFilterChange", "filterType", "value", "prev", "clearFilters", "getDifficultyColor", "getTypeIcon", "getStatusIcon", "exercise", "user_attempt", "completed", "is_correct", "getStatusText", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "h1", "initial", "opacity", "y", "animate", "div", "transition", "delay", "onChange", "e", "target", "difficulties", "map", "label", "types", "onClick", "length", "index", "_exercise$user_attemp", "_exercise$user_attemp2", "_exercise$user_attemp3", "_exercise$user_attemp4", "whileHover", "scale", "exercise_type", "description", "difficulty_display", "type_display", "points", "Math", "floor", "time_limit", "points_earned", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/pages/ExercisesPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport StudentNavbar from '../components/StudentNavbar';\nimport Pagination from '../components/Pagination';\nimport api from '../services/api';\n\n// Types\ninterface Exercise {\n  id: number;\n  title: string;\n  description: string;\n  course: {\n    id: number;\n    title: string;\n    level: string;\n  };\n  difficulty: string;\n  difficulty_display: string;\n  exercise_type: string;\n  type_display: string;\n  points: number;\n  time_limit: number;\n  user_attempt?: {\n    completed: boolean;\n    is_correct: boolean;\n    points_earned: number;\n    time_taken: number;\n  } | null;\n  created_at: string;\n}\n\ninterface Filters {\n  difficulty: string;\n  type: string;\n  course: string;\n}\n\nconst ExercisesPage: React.FC = () => {\n  const { user } = useAuth();\n  const { addNotification } = useNotifications();\n\n  // Helper functions for notifications\n  const showSuccess = (title: string, message: string) => {\n    addNotification({ type: 'success', title, message });\n  };\n  const showError = (title: string, message: string) => {\n    addNotification({ type: 'error', title, message });\n  };\n  const showWarning = (title: string, message: string) => {\n    addNotification({ type: 'warning', title, message });\n  };\n\n  // State\n  const [exercises, setExercises] = useState<Exercise[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState<Filters>({\n    difficulty: '',\n    type: '',\n    course: ''\n  });\n  const [availableFilters, setAvailableFilters] = useState<any>({});\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(12);\n  const [totalItems, setTotalItems] = useState(0);\n\n  // Effects\n  useEffect(() => {\n    if (user) {\n      fetchExercises();\n    }\n  }, [user, filters]);\n\n  // API Functions\n  const fetchExercises = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      if (filters.difficulty) params.append('difficulty', filters.difficulty);\n      if (filters.type) params.append('type', filters.type);\n      if (filters.course) params.append('course', filters.course);\n\n      const response = await api.get(`/exercises/?${params.toString()}`);\n      if (response.data) {\n        setExercises(response.data.exercises || []);\n        setAvailableFilters(response.data.filters || {});\n      }\n    } catch (error: any) {\n      console.error('Erreur lors de la récupération des exercices:', error);\n      showError('Erreur', 'Impossible de charger les exercices');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Event Handlers\n  const handleFilterChange = (filterType: keyof Filters, value: string) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      difficulty: '',\n      type: '',\n      course: ''\n    });\n  };\n\n  // Utility Functions\n  const getDifficultyColor = (difficulty: string) => {\n    switch (difficulty) {\n      case 'easy':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'medium':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'hard':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'equation':\n        return '📐';\n      case 'multiple_choice':\n        return '📝';\n      case 'calculation':\n        return '🧮';\n      case 'proof':\n        return '📋';\n      default:\n        return '📚';\n    }\n  };\n\n  const getStatusIcon = (exercise: Exercise) => {\n    if (!exercise.user_attempt) return '🆕';\n    if (exercise.user_attempt.completed) {\n      return exercise.user_attempt.is_correct ? '✅' : '❌';\n    }\n    return '⏳';\n  };\n\n  const getStatusText = (exercise: Exercise) => {\n    if (!exercise.user_attempt) return 'Nouveau';\n    if (exercise.user_attempt.completed) {\n      return exercise.user_attempt.is_correct ? 'Réussi' : 'Échoué';\n    }\n    return 'En cours';\n  };\n\n  // Loading and Error States\n  if (!user) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Exercices Interactifs</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            Vous devez être connecté pour accéder aux exercices.\n          </p>\n          <a\n            href=\"/login\"\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Se connecter\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  // Main Render\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <motion.h1\n        className=\"text-4xl font-bold mb-8 text-center text-primary-600\"\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        Exercices Interactifs\n      </motion.h1>\n\n      {/* Filtres */}\n      <motion.div\n        className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1 }}\n      >\n        <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n          🔍 Filtres\n        </h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n          {/* Filtre difficulté */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Difficulté\n            </label>\n            <select\n              value={filters.difficulty}\n              onChange={(e) => handleFilterChange('difficulty', e.target.value)}\n              className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"\">Toutes</option>\n              {availableFilters.difficulties?.map(([value, label]: [string, string]) => (\n                <option key={value} value={value}>{label}</option>\n              ))}\n            </select>\n          </div>\n\n          {/* Filtre type */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Type\n            </label>\n            <select\n              value={filters.type}\n              onChange={(e) => handleFilterChange('type', e.target.value)}\n              className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"\">Tous</option>\n              {availableFilters.types?.map(([value, label]: [string, string]) => (\n                <option key={value} value={value}>{label}</option>\n              ))}\n            </select>\n          </div>\n\n          {/* Bouton effacer */}\n          <div className=\"flex items-end\">\n            <button\n              onClick={clearFilters}\n              className=\"w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n            >\n              Effacer filtres\n            </button>\n          </div>\n        </div>\n        <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n          {exercises.length} exercice{exercises.length !== 1 ? 's' : ''} trouvé{exercises.length !== 1 ? 's' : ''}\n        </div>\n      </motion.div>\n\n      {/* État de chargement */}\n      {loading && (\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Chargement des exercices...</p>\n        </div>\n      )}\n\n      {/* Liste des exercices */}\n      {!loading && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {exercises.map((exercise, index) => (\n            <motion.div\n              key={exercise.id}\n              className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n              whileHover={{ scale: 1.02 }}\n            >\n              <div className=\"p-6\">\n                {/* En-tête de l'exercice */}\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-2xl mr-3\">{getTypeIcon(exercise.exercise_type)}</span>\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white line-clamp-2\">\n                        {exercise.title}\n                      </h3>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {exercise.course.title}\n                      </p>\n                    </div>\n                  </div>\n                  <span className=\"text-xl\">{getStatusIcon(exercise)}</span>\n                </div>\n\n                {/* Description */}\n                <p className=\"text-gray-700 dark:text-gray-300 text-sm mb-4 line-clamp-3\">\n                  {exercise.description}\n                </p>\n\n                {/* Badges */}\n                <div className=\"flex flex-wrap gap-2 mb-4\">\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exercise.difficulty)}`}>\n                    {exercise.difficulty_display}\n                  </span>\n                  <span className=\"px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300\">\n                    {exercise.type_display}\n                  </span>\n                </div>\n\n                {/* Informations */}\n                <div className=\"grid grid-cols-2 gap-4 text-sm mb-4\">\n                  <div className=\"flex items-center\">\n                    <span className=\"mr-1\">🏆</span>\n                    <span>{exercise.points} pts</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span className=\"mr-1\">⏱</span>\n                    <span>{Math.floor(exercise.time_limit / 60)}min</span>\n                  </div>\n                </div>\n\n                {/* Statut et résultats */}\n                <div className=\"mb-4\">\n                  <div className=\"flex items-center justify-between text-sm\">\n                    <span className=\"font-medium\">Statut:</span>\n                    <span className={`font-medium ${\n                      exercise.user_attempt?.completed\n                        ? exercise.user_attempt.is_correct\n                          ? 'text-green-600'\n                          : 'text-red-600'\n                        : 'text-blue-600'\n                    }`}>\n                      {getStatusText(exercise)}\n                    </span>\n                  </div>\n                  {exercise.user_attempt?.completed && (\n                    <div className=\"mt-2 text-xs text-gray-600 dark:text-gray-400\">\n                      Points obtenus: {exercise.user_attempt.points_earned}/{exercise.points}\n                    </div>\n                  )}\n                </div>\n\n                {/* Bouton d'action */}\n                <a\n                  href={`/exercises/${exercise.id}`}\n                  className={`block w-full text-center py-3 px-4 rounded-lg font-medium transition-colors ${\n                    exercise.user_attempt?.completed\n                      ? 'bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300'\n                      : 'bg-primary-600 hover:bg-primary-700 text-white'\n                  }`}\n                >\n                  {exercise.user_attempt?.completed ? 'Revoir' : 'Commencer'}\n                </a>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      )}\n\n      {/* Message si aucun exercice */}\n      {!loading && exercises.length === 0 && (\n        <motion.div\n          className=\"text-center py-12\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.3 }}\n        >\n          <div className=\"text-6xl mb-4\">📚</div>\n          <h3 className=\"text-xl font-semibold mb-2\">Aucun exercice trouvé</h3>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n            Essayez de modifier vos filtres ou revenez plus tard.\n          </p>\n          <button\n            onClick={clearFilters}\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors\"\n          >\n            Effacer les filtres\n          </button>\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\nexport default ExercisesPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,gBAAgB,QAAQ,kCAAkC;AAGnE,OAAOC,GAAG,MAAM,iBAAiB;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AA+BA,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EACpC,MAAM;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEU;EAAgB,CAAC,GAAGT,gBAAgB,CAAC,CAAC;;EAE9C;EACA,MAAMU,WAAW,GAAGA,CAACC,KAAa,EAAEC,OAAe,KAAK;IACtDH,eAAe,CAAC;MAAEI,IAAI,EAAE,SAAS;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACtD,CAAC;EACD,MAAME,SAAS,GAAGA,CAACH,KAAa,EAAEC,OAAe,KAAK;IACpDH,eAAe,CAAC;MAAEI,IAAI,EAAE,OAAO;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACpD,CAAC;EACD,MAAMG,WAAW,GAAGA,CAACJ,KAAa,EAAEC,OAAe,KAAK;IACtDH,eAAe,CAAC;MAAEI,IAAI,EAAE,SAAS;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACtD,CAAC;;EAED;EACA,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAU;IAC9C0B,UAAU,EAAE,EAAE;IACdT,IAAI,EAAE,EAAE;IACRU,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAAM,CAAC,CAAC,CAAC;;EAEjE;EACA,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACAC,SAAS,CAAC,MAAM;IACd,IAAIW,IAAI,EAAE;MACRwB,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACxB,IAAI,EAAEY,OAAO,CAAC,CAAC;;EAEnB;EACA,MAAMY,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMc,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpC,IAAId,OAAO,CAACE,UAAU,EAAEW,MAAM,CAACE,MAAM,CAAC,YAAY,EAAEf,OAAO,CAACE,UAAU,CAAC;MACvE,IAAIF,OAAO,CAACP,IAAI,EAAEoB,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEf,OAAO,CAACP,IAAI,CAAC;MACrD,IAAIO,OAAO,CAACG,MAAM,EAAEU,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEf,OAAO,CAACG,MAAM,CAAC;MAE3D,MAAMa,QAAQ,GAAG,MAAMnC,GAAG,CAACoC,GAAG,CAAC,eAAeJ,MAAM,CAACK,QAAQ,CAAC,CAAC,EAAE,CAAC;MAClE,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACjBtB,YAAY,CAACmB,QAAQ,CAACG,IAAI,CAACvB,SAAS,IAAI,EAAE,CAAC;QAC3CS,mBAAmB,CAACW,QAAQ,CAACG,IAAI,CAACnB,OAAO,IAAI,CAAC,CAAC,CAAC;MAClD;IACF,CAAC,CAAC,OAAOoB,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE1B,SAAS,CAAC,QAAQ,EAAE,qCAAqC,CAAC;IAC5D,CAAC,SAAS;MACRK,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuB,kBAAkB,GAAGA,CAACC,UAAyB,EAAEC,KAAa,KAAK;IACvEvB,UAAU,CAACwB,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACF,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzBzB,UAAU,CAAC;MACTC,UAAU,EAAE,EAAE;MACdT,IAAI,EAAE,EAAE;MACRU,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMwB,kBAAkB,GAAIzB,UAAkB,IAAK;IACjD,QAAQA,UAAU;MAChB,KAAK,MAAM;QACT,OAAO,mEAAmE;MAC5E,KAAK,QAAQ;QACX,OAAO,uEAAuE;MAChF,KAAK,MAAM;QACT,OAAO,2DAA2D;MACpE;QACE,OAAO,+DAA+D;IAC1E;EACF,CAAC;EAED,MAAM0B,WAAW,GAAInC,IAAY,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,UAAU;QACb,OAAO,IAAI;MACb,KAAK,iBAAiB;QACpB,OAAO,IAAI;MACb,KAAK,aAAa;QAChB,OAAO,IAAI;MACb,KAAK,OAAO;QACV,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMoC,aAAa,GAAIC,QAAkB,IAAK;IAC5C,IAAI,CAACA,QAAQ,CAACC,YAAY,EAAE,OAAO,IAAI;IACvC,IAAID,QAAQ,CAACC,YAAY,CAACC,SAAS,EAAE;MACnC,OAAOF,QAAQ,CAACC,YAAY,CAACE,UAAU,GAAG,GAAG,GAAG,GAAG;IACrD;IACA,OAAO,GAAG;EACZ,CAAC;EAED,MAAMC,aAAa,GAAIJ,QAAkB,IAAK;IAC5C,IAAI,CAACA,QAAQ,CAACC,YAAY,EAAE,OAAO,SAAS;IAC5C,IAAID,QAAQ,CAACC,YAAY,CAACC,SAAS,EAAE;MACnC,OAAOF,QAAQ,CAACC,YAAY,CAACE,UAAU,GAAG,QAAQ,GAAG,QAAQ;IAC/D;IACA,OAAO,UAAU;EACnB,CAAC;;EAED;EACA,IAAI,CAAC7C,IAAI,EAAE;IACT,oBACEL,OAAA;MAAKoD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CrD,OAAA;QAAKoD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrD,OAAA;UAAIoD,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClEzD,OAAA;UAAGoD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJzD,OAAA;UACE0D,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACEzD,OAAA;IAAKoD,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1CrD,OAAA,CAACL,MAAM,CAACgE,EAAE;MACRP,SAAS,EAAC,sDAAsD;MAChEQ,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAT,QAAA,EAC/B;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAW,CAAC,eAGZzD,OAAA,CAACL,MAAM,CAACqE,GAAG;MACTZ,SAAS,EAAC,yDAAyD;MACnEQ,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BG,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAb,QAAA,gBAE3BrD,OAAA;QAAIoD,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAE7D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLzD,OAAA;QAAKoD,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBAEzDrD,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAOoD,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAAC;UAEnF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRzD,OAAA;YACEyC,KAAK,EAAExB,OAAO,CAACE,UAAW;YAC1BgD,QAAQ,EAAGC,CAAC,IAAK7B,kBAAkB,CAAC,YAAY,EAAE6B,CAAC,CAACC,MAAM,CAAC5B,KAAK,CAAE;YAClEW,SAAS,EAAC,2HAA2H;YAAAC,QAAA,gBAErIrD,OAAA;cAAQyC,KAAK,EAAC,EAAE;cAAAY,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,GAAAtD,qBAAA,GAC/BkB,gBAAgB,CAACiD,YAAY,cAAAnE,qBAAA,uBAA7BA,qBAAA,CAA+BoE,GAAG,CAAC,CAAC,CAAC9B,KAAK,EAAE+B,KAAK,CAAmB,kBACnExE,OAAA;cAAoByC,KAAK,EAAEA,KAAM;cAAAY,QAAA,EAAEmB;YAAK,GAA3B/B,KAAK;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA+B,CAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNzD,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAOoD,SAAS,EAAC,iEAAiE;YAAAC,QAAA,EAAC;UAEnF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRzD,OAAA;YACEyC,KAAK,EAAExB,OAAO,CAACP,IAAK;YACpByD,QAAQ,EAAGC,CAAC,IAAK7B,kBAAkB,CAAC,MAAM,EAAE6B,CAAC,CAACC,MAAM,CAAC5B,KAAK,CAAE;YAC5DW,SAAS,EAAC,2HAA2H;YAAAC,QAAA,gBAErIrD,OAAA;cAAQyC,KAAK,EAAC,EAAE;cAAAY,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,GAAArD,qBAAA,GAC7BiB,gBAAgB,CAACoD,KAAK,cAAArE,qBAAA,uBAAtBA,qBAAA,CAAwBmE,GAAG,CAAC,CAAC,CAAC9B,KAAK,EAAE+B,KAAK,CAAmB,kBAC5DxE,OAAA;cAAoByC,KAAK,EAAEA,KAAM;cAAAY,QAAA,EAAEmB;YAAK,GAA3B/B,KAAK;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA+B,CAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNzD,OAAA;UAAKoD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BrD,OAAA;YACE0E,OAAO,EAAE/B,YAAa;YACtBS,SAAS,EAAC,oGAAoG;YAAAC,QAAA,EAC/G;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNzD,OAAA;QAAKoD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,GACtDxC,SAAS,CAAC8D,MAAM,EAAC,WAAS,EAAC9D,SAAS,CAAC8D,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,YAAO,EAAC9D,SAAS,CAAC8D,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGZ1C,OAAO,iBACNf,OAAA;MAAKoD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCrD,OAAA;QAAKoD,SAAS,EAAC;MAAgF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtGzD,OAAA;QAAGoD,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5E,CACN,EAGA,CAAC1C,OAAO,iBACPf,OAAA;MAAKoD,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClExC,SAAS,CAAC0D,GAAG,CAAC,CAACxB,QAAQ,EAAE6B,KAAK;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QAAA,oBAC7BhF,OAAA,CAACL,MAAM,CAACqE,GAAG;UAETZ,SAAS,EAAC,4GAA4G;UACtHQ,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BG,UAAU,EAAE;YAAEC,KAAK,EAAEU,KAAK,GAAG;UAAI,CAAE;UACnCK,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAAA7B,QAAA,eAE5BrD,OAAA;YAAKoD,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAElBrD,OAAA;cAAKoD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDrD,OAAA;gBAAKoD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCrD,OAAA;kBAAMoD,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAER,WAAW,CAACE,QAAQ,CAACoC,aAAa;gBAAC;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5EzD,OAAA;kBAAAqD,QAAA,gBACErD,OAAA;oBAAIoD,SAAS,EAAC,kEAAkE;oBAAAC,QAAA,EAC7EN,QAAQ,CAACvC;kBAAK;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eACLzD,OAAA;oBAAGoD,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EACpDN,QAAQ,CAAC3B,MAAM,CAACZ;kBAAK;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBAAMoD,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAEP,aAAa,CAACC,QAAQ;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eAGNzD,OAAA;cAAGoD,SAAS,EAAC,4DAA4D;cAAAC,QAAA,EACtEN,QAAQ,CAACqC;YAAW;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eAGJzD,OAAA;cAAKoD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCrD,OAAA;gBAAMoD,SAAS,EAAE,8CAA8CR,kBAAkB,CAACG,QAAQ,CAAC5B,UAAU,CAAC,EAAG;gBAAAkC,QAAA,EACtGN,QAAQ,CAACsC;cAAkB;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACPzD,OAAA;gBAAMoD,SAAS,EAAC,0GAA0G;gBAAAC,QAAA,EACvHN,QAAQ,CAACuC;cAAY;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNzD,OAAA;cAAKoD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,gBAClDrD,OAAA;gBAAKoD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCrD,OAAA;kBAAMoD,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChCzD,OAAA;kBAAAqD,QAAA,GAAON,QAAQ,CAACwC,MAAM,EAAC,MAAI;gBAAA;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACNzD,OAAA;gBAAKoD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCrD,OAAA;kBAAMoD,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/BzD,OAAA;kBAAAqD,QAAA,GAAOmC,IAAI,CAACC,KAAK,CAAC1C,QAAQ,CAAC2C,UAAU,GAAG,EAAE,CAAC,EAAC,KAAG;gBAAA;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNzD,OAAA;cAAKoD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBrD,OAAA;gBAAKoD,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,gBACxDrD,OAAA;kBAAMoD,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5CzD,OAAA;kBAAMoD,SAAS,EAAE,eACf,CAAAyB,qBAAA,GAAA9B,QAAQ,CAACC,YAAY,cAAA6B,qBAAA,eAArBA,qBAAA,CAAuB5B,SAAS,GAC5BF,QAAQ,CAACC,YAAY,CAACE,UAAU,GAC9B,gBAAgB,GAChB,cAAc,GAChB,eAAe,EAClB;kBAAAG,QAAA,EACAF,aAAa,CAACJ,QAAQ;gBAAC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EACL,EAAAqB,sBAAA,GAAA/B,QAAQ,CAACC,YAAY,cAAA8B,sBAAA,uBAArBA,sBAAA,CAAuB7B,SAAS,kBAC/BjD,OAAA;gBAAKoD,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,GAAC,kBAC7C,EAACN,QAAQ,CAACC,YAAY,CAAC2C,aAAa,EAAC,GAAC,EAAC5C,QAAQ,CAACwC,MAAM;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNzD,OAAA;cACE0D,IAAI,EAAE,cAAcX,QAAQ,CAAC6C,EAAE,EAAG;cAClCxC,SAAS,EAAE,+EACT,CAAA2B,sBAAA,GAAAhC,QAAQ,CAACC,YAAY,cAAA+B,sBAAA,eAArBA,sBAAA,CAAuB9B,SAAS,GAC5B,wGAAwG,GACxG,gDAAgD,EACnD;cAAAI,QAAA,EAEF,CAAA2B,sBAAA,GAAAjC,QAAQ,CAACC,YAAY,cAAAgC,sBAAA,eAArBA,sBAAA,CAAuB/B,SAAS,GAAG,QAAQ,GAAG;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GAnFDV,QAAQ,CAAC6C,EAAE;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoFN,CAAC;MAAA,CACd;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGA,CAAC1C,OAAO,IAAIF,SAAS,CAAC8D,MAAM,KAAK,CAAC,iBACjC3E,OAAA,CAACL,MAAM,CAACqE,GAAG;MACTZ,SAAS,EAAC,mBAAmB;MAC7BQ,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBE,OAAO,EAAE;QAAEF,OAAO,EAAE;MAAE,CAAE;MACxBI,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAAAb,QAAA,gBAE3BrD,OAAA;QAAKoD,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvCzD,OAAA;QAAIoD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrEzD,OAAA;QAAGoD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAErD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJzD,OAAA;QACE0E,OAAO,EAAE/B,YAAa;QACtBS,SAAS,EAAC,mGAAmG;QAAAC,QAAA,EAC9G;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvD,EAAA,CAhVID,aAAuB;EAAA,QACVL,OAAO,EACIC,gBAAgB;AAAA;AAAAgG,EAAA,GAFxC5F,aAAuB;AAkV7B,eAAeA,aAAa;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}