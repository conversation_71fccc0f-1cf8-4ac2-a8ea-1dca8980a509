{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { SparseMatrixDependencies } from './dependenciesSparseMatrixClass.generated.js';\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { cosDependencies } from './dependenciesCos.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { normDependencies } from './dependenciesNorm.generated.js';\nimport { sinDependencies } from './dependenciesSin.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { unaryMinusDependencies } from './dependenciesUnaryMinus.generated.js';\nimport { createRotationMatrix } from '../../factoriesAny.js';\nexport var rotationMatrixDependencies = {\n  BigNumberDependencies,\n  DenseMatrixDependencies,\n  SparseMatrixDependencies,\n  addScalarDependencies,\n  cosDependencies,\n  matrixDependencies,\n  multiplyScalarDependencies,\n  normDependencies,\n  sinDependencies,\n  typedDependencies,\n  unaryMinusDependencies,\n  createRotationMatrix\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "DenseMatrixDependencies", "SparseMatrixDependencies", "addScalarDependencies", "cosDependencies", "matrixDependencies", "multiplyScalarDependencies", "normDependencies", "sinDependencies", "typedDependencies", "unaryMinusDependencies", "createRotationMatrix", "rotationMatrixDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesRotationMatrix.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { SparseMatrixDependencies } from './dependenciesSparseMatrixClass.generated.js';\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { cosDependencies } from './dependenciesCos.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { normDependencies } from './dependenciesNorm.generated.js';\nimport { sinDependencies } from './dependenciesSin.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { unaryMinusDependencies } from './dependenciesUnaryMinus.generated.js';\nimport { createRotationMatrix } from '../../factoriesAny.js';\nexport var rotationMatrixDependencies = {\n  BigNumberDependencies,\n  DenseMatrixDependencies,\n  SparseMatrixDependencies,\n  addScalarDependencies,\n  cosDependencies,\n  matrixDependencies,\n  multiplyScalarDependencies,\n  normDependencies,\n  sinDependencies,\n  typedDependencies,\n  unaryMinusDependencies,\n  createRotationMatrix\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,wBAAwB,QAAQ,8CAA8C;AACvF,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,0BAA0B,QAAQ,2CAA2C;AACtF,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,OAAO,IAAIC,0BAA0B,GAAG;EACtCZ,qBAAqB;EACrBC,uBAAuB;EACvBC,wBAAwB;EACxBC,qBAAqB;EACrBC,eAAe;EACfC,kBAAkB;EAClBC,0BAA0B;EAC1BC,gBAAgB;EAChBC,eAAe;EACfC,iBAAiB;EACjBC,sBAAsB;EACtBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}