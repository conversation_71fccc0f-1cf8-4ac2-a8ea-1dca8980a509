{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { compareDependencies } from './dependenciesCompare.generated.js';\nimport { isNaNDependencies } from './dependenciesIsNaN.generated.js';\nimport { isNumericDependencies } from './dependenciesIsNumeric.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createPartitionSelect } from '../../factoriesAny.js';\nexport var partitionSelectDependencies = {\n  compareDependencies,\n  isNaNDependencies,\n  isNumericDependencies,\n  typedDependencies,\n  createPartitionSelect\n};", "map": {"version": 3, "names": ["compareDependencies", "isNaNDependencies", "isNumericDependencies", "typedDependencies", "createPartitionSelect", "partitionSelectDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesPartitionSelect.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { compareDependencies } from './dependenciesCompare.generated.js';\nimport { isNaNDependencies } from './dependenciesIsNaN.generated.js';\nimport { isNumericDependencies } from './dependenciesIsNumeric.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createPartitionSelect } from '../../factoriesAny.js';\nexport var partitionSelectDependencies = {\n  compareDependencies,\n  isNaNDependencies,\n  isNumericDependencies,\n  typedDependencies,\n  createPartitionSelect\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,OAAO,IAAIC,2BAA2B,GAAG;EACvCL,mBAAmB;EACnBC,iBAAiB;EACjBC,qBAAqB;EACrBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}