{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { IndexDependencies } from './dependenciesIndexClass.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { rangeDependencies } from './dependenciesRange.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createRowTransform } from '../../factoriesAny.js';\nexport var rowTransformDependencies = {\n  IndexDependencies,\n  matrixDependencies,\n  rangeDependencies,\n  typedDependencies,\n  createRowTransform\n};", "map": {"version": 3, "names": ["IndexDependencies", "matrixDependencies", "rangeDependencies", "typedDependencies", "createRowTransform", "rowTransformDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesRowTransform.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { IndexDependencies } from './dependenciesIndexClass.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { rangeDependencies } from './dependenciesRange.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createRowTransform } from '../../factoriesAny.js';\nexport var rowTransformDependencies = {\n  IndexDependencies,\n  matrixDependencies,\n  rangeDependencies,\n  typedDependencies,\n  createRowTransform\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,uCAAuC;AACzE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,OAAO,IAAIC,wBAAwB,GAAG;EACpCL,iBAAiB;EACjBC,kBAAkB;EAClBC,iBAAiB;EACjBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}