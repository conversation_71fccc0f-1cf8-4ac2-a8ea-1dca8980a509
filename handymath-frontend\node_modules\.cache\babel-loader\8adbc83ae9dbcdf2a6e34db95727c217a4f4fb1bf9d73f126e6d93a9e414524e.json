{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo08xS0Sid';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createMatAlgo08xS0Sid = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix A and SparseMatrix B nonzero items and invokes the callback function f(Aij, Bij).\n   * Callback function invoked MAX(NNZA, NNZB) times\n   *\n   *\n   *          ┌  f(Aij, Bij)  ; A(i,j) !== 0 && B(i,j) !== 0\n   * C(i,j) = ┤  A(i,j)       ; A(i,j) !== 0 && B(i,j) === 0\n   *          └  0            ; otherwise\n   *\n   *\n   * @param {Matrix}   a                 The SparseMatrix instance (A)\n   * @param {Matrix}   b                 The SparseMatrix instance (B)\n   * @param {Function} callback          The f(Aij,Bij) operation to invoke\n   *\n   * @return {Matrix}                    SparseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97620294\n   */\n  return function matAlgo08xS0Sid(a, b, callback) {\n    // sparse matrix arrays\n    var avalues = a._values;\n    var aindex = a._index;\n    var aptr = a._ptr;\n    var asize = a._size;\n    var adt = a._datatype || a._data === undefined ? a._datatype : a.getDataType();\n    // sparse matrix arrays\n    var bvalues = b._values;\n    var bindex = b._index;\n    var bptr = b._ptr;\n    var bsize = b._size;\n    var bdt = b._datatype || b._data === undefined ? b._datatype : b.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // sparse matrix cannot be a Pattern matrix\n    if (!avalues || !bvalues) {\n      throw new Error('Cannot perform operation on Pattern Sparse Matrices');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      // datatype\n      dt = adt;\n      // find signature that matches (dt, dt)\n      eq = typed.find(equalScalar, [dt, dt]);\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays\n    var cvalues = [];\n    var cindex = [];\n    var cptr = [];\n\n    // workspace\n    var x = [];\n    // marks indicating we have a value in x for a given column\n    var w = [];\n\n    // vars\n    var k, k0, k1, i;\n\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // update cptr\n      cptr[j] = cindex.length;\n      // columns mark\n      var mark = j + 1;\n      // loop values in a\n      for (k0 = aptr[j], k1 = aptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        i = aindex[k];\n        // mark workspace\n        w[i] = mark;\n        // set value\n        x[i] = avalues[k];\n        // add index\n        cindex.push(i);\n      }\n      // loop values in b\n      for (k0 = bptr[j], k1 = bptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        i = bindex[k];\n        // check value exists in workspace\n        if (w[i] === mark) {\n          // evaluate callback\n          x[i] = cf(x[i], bvalues[k]);\n        }\n      }\n      // initialize first index in j\n      k = cptr[j];\n      // loop index in j\n      while (k < cindex.length) {\n        // row\n        i = cindex[k];\n        // value @ i\n        var v = x[i];\n        // check for zero value\n        if (!eq(v, zero)) {\n          // push value\n          cvalues.push(v);\n          // increment pointer\n          k++;\n        } else {\n          // remove value @ i, do not increment pointer\n          cindex.splice(k, 1);\n        }\n      }\n    }\n    // update cptr\n    cptr[columns] = cindex.length;\n\n    // return sparse matrix\n    return a.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === a._datatype && bdt === b._datatype ? dt : undefined\n    });\n  };\n});", "map": {"version": 3, "names": ["factory", "DimensionError", "name", "dependencies", "createMatAlgo08xS0Sid", "_ref", "typed", "equalScalar", "matAlgo08xS0Sid", "a", "b", "callback", "avalues", "_values", "aindex", "_index", "aptr", "_ptr", "asize", "_size", "adt", "_datatype", "_data", "undefined", "getDataType", "bvalues", "bindex", "bptr", "bsize", "bdt", "length", "RangeError", "Error", "rows", "columns", "dt", "eq", "zero", "cf", "find", "convert", "cvalues", "cindex", "cptr", "x", "w", "k", "k0", "k1", "i", "j", "mark", "push", "v", "splice", "createSparseMatrix", "values", "index", "ptr", "size", "datatype"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/utils/matAlgo08xS0Sid.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo08xS0Sid';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createMatAlgo08xS0Sid = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix A and SparseMatrix B nonzero items and invokes the callback function f(Aij, Bij).\n   * Callback function invoked MAX(NNZA, NNZB) times\n   *\n   *\n   *          ┌  f(Aij, Bij)  ; A(i,j) !== 0 && B(i,j) !== 0\n   * C(i,j) = ┤  A(i,j)       ; A(i,j) !== 0 && B(i,j) === 0\n   *          └  0            ; otherwise\n   *\n   *\n   * @param {Matrix}   a                 The SparseMatrix instance (A)\n   * @param {Matrix}   b                 The SparseMatrix instance (B)\n   * @param {Function} callback          The f(Aij,Bij) operation to invoke\n   *\n   * @return {Matrix}                    SparseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97620294\n   */\n  return function matAlgo08xS0Sid(a, b, callback) {\n    // sparse matrix arrays\n    var avalues = a._values;\n    var aindex = a._index;\n    var aptr = a._ptr;\n    var asize = a._size;\n    var adt = a._datatype || a._data === undefined ? a._datatype : a.getDataType();\n    // sparse matrix arrays\n    var bvalues = b._values;\n    var bindex = b._index;\n    var bptr = b._ptr;\n    var bsize = b._size;\n    var bdt = b._datatype || b._data === undefined ? b._datatype : b.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // sparse matrix cannot be a Pattern matrix\n    if (!avalues || !bvalues) {\n      throw new Error('Cannot perform operation on Pattern Sparse Matrices');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      // datatype\n      dt = adt;\n      // find signature that matches (dt, dt)\n      eq = typed.find(equalScalar, [dt, dt]);\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays\n    var cvalues = [];\n    var cindex = [];\n    var cptr = [];\n\n    // workspace\n    var x = [];\n    // marks indicating we have a value in x for a given column\n    var w = [];\n\n    // vars\n    var k, k0, k1, i;\n\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // update cptr\n      cptr[j] = cindex.length;\n      // columns mark\n      var mark = j + 1;\n      // loop values in a\n      for (k0 = aptr[j], k1 = aptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        i = aindex[k];\n        // mark workspace\n        w[i] = mark;\n        // set value\n        x[i] = avalues[k];\n        // add index\n        cindex.push(i);\n      }\n      // loop values in b\n      for (k0 = bptr[j], k1 = bptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        i = bindex[k];\n        // check value exists in workspace\n        if (w[i] === mark) {\n          // evaluate callback\n          x[i] = cf(x[i], bvalues[k]);\n        }\n      }\n      // initialize first index in j\n      k = cptr[j];\n      // loop index in j\n      while (k < cindex.length) {\n        // row\n        i = cindex[k];\n        // value @ i\n        var v = x[i];\n        // check for zero value\n        if (!eq(v, zero)) {\n          // push value\n          cvalues.push(v);\n          // increment pointer\n          k++;\n        } else {\n          // remove value @ i, do not increment pointer\n          cindex.splice(k, 1);\n        }\n      }\n    }\n    // update cptr\n    cptr[columns] = cindex.length;\n\n    // return sparse matrix\n    return a.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === a._datatype && bdt === b._datatype ? dt : undefined\n    });\n  };\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,IAAIC,IAAI,GAAG,iBAAiB;AAC5B,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC;AAC3C,OAAO,IAAIC,qBAAqB,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACpF,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASG,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAE;IAC9C;IACA,IAAIC,OAAO,GAAGH,CAAC,CAACI,OAAO;IACvB,IAAIC,MAAM,GAAGL,CAAC,CAACM,MAAM;IACrB,IAAIC,IAAI,GAAGP,CAAC,CAACQ,IAAI;IACjB,IAAIC,KAAK,GAAGT,CAAC,CAACU,KAAK;IACnB,IAAIC,GAAG,GAAGX,CAAC,CAACY,SAAS,IAAIZ,CAAC,CAACa,KAAK,KAAKC,SAAS,GAAGd,CAAC,CAACY,SAAS,GAAGZ,CAAC,CAACe,WAAW,CAAC,CAAC;IAC9E;IACA,IAAIC,OAAO,GAAGf,CAAC,CAACG,OAAO;IACvB,IAAIa,MAAM,GAAGhB,CAAC,CAACK,MAAM;IACrB,IAAIY,IAAI,GAAGjB,CAAC,CAACO,IAAI;IACjB,IAAIW,KAAK,GAAGlB,CAAC,CAACS,KAAK;IACnB,IAAIU,GAAG,GAAGnB,CAAC,CAACW,SAAS,IAAIX,CAAC,CAACY,KAAK,KAAKC,SAAS,GAAGb,CAAC,CAACW,SAAS,GAAGX,CAAC,CAACc,WAAW,CAAC,CAAC;;IAE9E;IACA,IAAIN,KAAK,CAACY,MAAM,KAAKF,KAAK,CAACE,MAAM,EAAE;MACjC,MAAM,IAAI7B,cAAc,CAACiB,KAAK,CAACY,MAAM,EAAEF,KAAK,CAACE,MAAM,CAAC;IACtD;;IAEA;IACA,IAAIZ,KAAK,CAAC,CAAC,CAAC,KAAKU,KAAK,CAAC,CAAC,CAAC,IAAIV,KAAK,CAAC,CAAC,CAAC,KAAKU,KAAK,CAAC,CAAC,CAAC,EAAE;MAClD,MAAM,IAAIG,UAAU,CAAC,gCAAgC,GAAGb,KAAK,GAAG,yBAAyB,GAAGU,KAAK,GAAG,GAAG,CAAC;IAC1G;;IAEA;IACA,IAAI,CAAChB,OAAO,IAAI,CAACa,OAAO,EAAE;MACxB,MAAM,IAAIO,KAAK,CAAC,qDAAqD,CAAC;IACxE;;IAEA;IACA,IAAIC,IAAI,GAAGf,KAAK,CAAC,CAAC,CAAC;IACnB,IAAIgB,OAAO,GAAGhB,KAAK,CAAC,CAAC,CAAC;;IAEtB;IACA,IAAIiB,EAAE;IACN;IACA,IAAIC,EAAE,GAAG7B,WAAW;IACpB;IACA,IAAI8B,IAAI,GAAG,CAAC;IACZ;IACA,IAAIC,EAAE,GAAG3B,QAAQ;;IAEjB;IACA,IAAI,OAAOS,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAKS,GAAG,IAAIT,GAAG,KAAK,OAAO,EAAE;MAC7D;MACAe,EAAE,GAAGf,GAAG;MACR;MACAgB,EAAE,GAAG9B,KAAK,CAACiC,IAAI,CAAChC,WAAW,EAAE,CAAC4B,EAAE,EAAEA,EAAE,CAAC,CAAC;MACtC;MACAE,IAAI,GAAG/B,KAAK,CAACkC,OAAO,CAAC,CAAC,EAAEL,EAAE,CAAC;MAC3B;MACAG,EAAE,GAAGhC,KAAK,CAACiC,IAAI,CAAC5B,QAAQ,EAAE,CAACwB,EAAE,EAAEA,EAAE,CAAC,CAAC;IACrC;;IAEA;IACA,IAAIM,OAAO,GAAG,EAAE;IAChB,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIC,IAAI,GAAG,EAAE;;IAEb;IACA,IAAIC,CAAC,GAAG,EAAE;IACV;IACA,IAAIC,CAAC,GAAG,EAAE;;IAEV;IACA,IAAIC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC;;IAEhB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,OAAO,EAAEgB,CAAC,EAAE,EAAE;MAChC;MACAP,IAAI,CAACO,CAAC,CAAC,GAAGR,MAAM,CAACZ,MAAM;MACvB;MACA,IAAIqB,IAAI,GAAGD,CAAC,GAAG,CAAC;MAChB;MACA,KAAKH,EAAE,GAAG/B,IAAI,CAACkC,CAAC,CAAC,EAAEF,EAAE,GAAGhC,IAAI,CAACkC,CAAC,GAAG,CAAC,CAAC,EAAEJ,CAAC,GAAGC,EAAE,EAAED,CAAC,GAAGE,EAAE,EAAEF,CAAC,EAAE,EAAE;QACxD;QACAG,CAAC,GAAGnC,MAAM,CAACgC,CAAC,CAAC;QACb;QACAD,CAAC,CAACI,CAAC,CAAC,GAAGE,IAAI;QACX;QACAP,CAAC,CAACK,CAAC,CAAC,GAAGrC,OAAO,CAACkC,CAAC,CAAC;QACjB;QACAJ,MAAM,CAACU,IAAI,CAACH,CAAC,CAAC;MAChB;MACA;MACA,KAAKF,EAAE,GAAGpB,IAAI,CAACuB,CAAC,CAAC,EAAEF,EAAE,GAAGrB,IAAI,CAACuB,CAAC,GAAG,CAAC,CAAC,EAAEJ,CAAC,GAAGC,EAAE,EAAED,CAAC,GAAGE,EAAE,EAAEF,CAAC,EAAE,EAAE;QACxD;QACAG,CAAC,GAAGvB,MAAM,CAACoB,CAAC,CAAC;QACb;QACA,IAAID,CAAC,CAACI,CAAC,CAAC,KAAKE,IAAI,EAAE;UACjB;UACAP,CAAC,CAACK,CAAC,CAAC,GAAGX,EAAE,CAACM,CAAC,CAACK,CAAC,CAAC,EAAExB,OAAO,CAACqB,CAAC,CAAC,CAAC;QAC7B;MACF;MACA;MACAA,CAAC,GAAGH,IAAI,CAACO,CAAC,CAAC;MACX;MACA,OAAOJ,CAAC,GAAGJ,MAAM,CAACZ,MAAM,EAAE;QACxB;QACAmB,CAAC,GAAGP,MAAM,CAACI,CAAC,CAAC;QACb;QACA,IAAIO,CAAC,GAAGT,CAAC,CAACK,CAAC,CAAC;QACZ;QACA,IAAI,CAACb,EAAE,CAACiB,CAAC,EAAEhB,IAAI,CAAC,EAAE;UAChB;UACAI,OAAO,CAACW,IAAI,CAACC,CAAC,CAAC;UACf;UACAP,CAAC,EAAE;QACL,CAAC,MAAM;UACL;UACAJ,MAAM,CAACY,MAAM,CAACR,CAAC,EAAE,CAAC,CAAC;QACrB;MACF;IACF;IACA;IACAH,IAAI,CAACT,OAAO,CAAC,GAAGQ,MAAM,CAACZ,MAAM;;IAE7B;IACA,OAAOrB,CAAC,CAAC8C,kBAAkB,CAAC;MAC1BC,MAAM,EAAEf,OAAO;MACfgB,KAAK,EAAEf,MAAM;MACbgB,GAAG,EAAEf,IAAI;MACTgB,IAAI,EAAE,CAAC1B,IAAI,EAAEC,OAAO,CAAC;MACrB0B,QAAQ,EAAExC,GAAG,KAAKX,CAAC,CAACY,SAAS,IAAIQ,GAAG,KAAKnB,CAAC,CAACW,SAAS,GAAGc,EAAE,GAAGZ;IAC9D,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}