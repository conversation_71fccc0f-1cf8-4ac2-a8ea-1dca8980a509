{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\components\\\\SimpleHeader.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport Logo from './Logo';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleHeader = ({\n  showBackButton = false,\n  title\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const {\n    theme,\n    toggleTheme\n  } = useTheme();\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n  const handleBack = () => {\n    navigate(-1);\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [showBackButton && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleBack,\n            className: \"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\",\n            title: \"Retour\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 19l-7-7 7-7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Logo, {\n            linkTo: \"/\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), title && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-500 dark:text-gray-400\",\n            children: \"\\u2022\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 15\n          }, this), title && /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg font-medium text-gray-900 dark:text-white\",\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center space-x-1\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700\",\n              title: \"Accueil\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-4 h-4 mr-1\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 70,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this), \"Accueil\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), user && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/profile\",\n                className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700\",\n                title: \"Mon profil\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4 mr-1\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 83,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 21\n                }, this), \"Profil\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/settings\",\n                className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700\",\n                title: \"Param\\xE8tres\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4 mr-1\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 94,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 95,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 21\n                }, this), \"Param\\xE8tres\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/about\",\n              className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700\",\n              title: \"\\xC0 propos\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-4 h-4 mr-1\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), \"\\xC0 propos\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: toggleTheme,\n            className: \"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\",\n            title: theme === 'light' ? 'Mode sombre' : 'Mode clair',\n            children: theme === 'light' ? /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold ${user.role === 'admin' ? 'bg-red-500' : 'bg-blue-500'}`,\n                children: user.username.charAt(0).toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden sm:block\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                  children: user.prenom || user.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `text-xs ${user.role === 'admin' ? 'text-red-600' : 'text-blue-600'}`,\n                  children: user.role === 'admin' ? 'Admin' : 'Étudiant'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogout,\n              className: \"px-3 py-1 text-sm bg-red-100 hover:bg-red-200 text-red-800 rounded-md transition-colors\",\n              children: \"D\\xE9connexion\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) :\n          /*#__PURE__*/\n          /* Boutons de connexion pour utilisateurs non connectés */\n          _jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors\",\n              children: \"Se connecter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"px-3 py-2 text-sm font-medium bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors\",\n              children: \"S'inscrire\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleHeader, \"LeOYZx9H8Hh1b2j3gslKg5GT1fE=\", false, function () {\n  return [useNavigate, useAuth, useTheme];\n});\n_c = SimpleHeader;\nexport default SimpleHeader;\nvar _c;\n$RefreshReg$(_c, \"SimpleHeader\");", "map": {"version": 3, "names": ["React", "Link", "useNavigate", "useAuth", "useTheme", "Logo", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleHeader", "showBackButton", "title", "_s", "navigate", "user", "logout", "theme", "toggleTheme", "handleLogout", "handleBack", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "linkTo", "to", "type", "role", "username", "char<PERSON>t", "toUpperCase", "prenom", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/components/SimpleHeader.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport Logo from './Logo';\n\ninterface SimpleHeaderProps {\n  showBackButton?: boolean;\n  title?: string;\n}\n\nconst SimpleHeader: React.FC<SimpleHeaderProps> = ({\n  showBackButton = false,\n  title\n}) => {\n  const navigate = useNavigate();\n  const { user, logout } = useAuth();\n  const { theme, toggleTheme } = useTheme();\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  const handleBack = () => {\n    navigate(-1);\n  };\n\n  return (\n    <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* <PERSON><PERSON><PERSON> gauche */}\n          <div className=\"flex items-center space-x-4\">\n            {showBackButton && (\n              <button\n                onClick={handleBack}\n                className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\n                title=\"Retour\"\n              >\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n                </svg>\n              </button>\n            )}\n\n            <Logo linkTo=\"/\" />\n\n            {title && (\n              <span className=\"text-gray-500 dark:text-gray-400\">•</span>\n            )}\n\n            {title && (\n              <h1 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {title}\n              </h1>\n            )}\n          </div>\n\n          {/* Côté droit */}\n          <div className=\"flex items-center space-x-3\">\n            {/* Navigation rapide */}\n            <div className=\"hidden md:flex items-center space-x-1\">\n              <Link\n                to=\"/\"\n                className=\"flex items-center px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700\"\n                title=\"Accueil\"\n              >\n                <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n                </svg>\n                Accueil\n              </Link>\n\n              {user && (\n                <>\n                  <Link\n                    to=\"/profile\"\n                    className=\"flex items-center px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700\"\n                    title=\"Mon profil\"\n                  >\n                    <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                    </svg>\n                    Profil\n                  </Link>\n\n                  <Link\n                    to=\"/settings\"\n                    className=\"flex items-center px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700\"\n                    title=\"Paramètres\"\n                  >\n                    <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                    </svg>\n                    Paramètres\n                  </Link>\n                </>\n              )}\n\n              <Link\n                to=\"/about\"\n                className=\"flex items-center px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700\"\n                title=\"À propos\"\n              >\n                <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                À propos\n              </Link>\n            </div>\n\n            {/* Bouton thème */}\n            <button\n              type=\"button\"\n              onClick={toggleTheme}\n              className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\n              title={theme === 'light' ? 'Mode sombre' : 'Mode clair'}\n            >\n              {theme === 'light' ? (\n                <svg className=\"w-5 h-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\" />\n                </svg>\n              ) : (\n                <svg className=\"w-5 h-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\" />\n                </svg>\n              )}\n            </button>\n\n            {/* Informations utilisateur OU boutons de connexion */}\n            {user ? (\n              <>\n                <div className=\"flex items-center space-x-2\">\n                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold ${\n                    user.role === 'admin' ? 'bg-red-500' : 'bg-blue-500'\n                  }`}>\n                    {user.username.charAt(0).toUpperCase()}\n                  </div>\n                  <div className=\"hidden sm:block\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {user.prenom || user.username}\n                    </div>\n                    <div className={`text-xs ${\n                      user.role === 'admin' ? 'text-red-600' : 'text-blue-600'\n                    }`}>\n                      {user.role === 'admin' ? 'Admin' : 'Étudiant'}\n                    </div>\n                  </div>\n                </div>\n\n                <button\n                  onClick={handleLogout}\n                  className=\"px-3 py-1 text-sm bg-red-100 hover:bg-red-200 text-red-800 rounded-md transition-colors\"\n                >\n                  Déconnexion\n                </button>\n              </>\n            ) : (\n              /* Boutons de connexion pour utilisateurs non connectés */\n              <>\n                <Link\n                  to=\"/login\"\n                  className=\"px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors\"\n                >\n                  Se connecter\n                </Link>\n                <Link\n                  to=\"/register\"\n                  className=\"px-3 py-2 text-sm font-medium bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors\"\n                >\n                  S'inscrire\n                </Link>\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default SimpleHeader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,IAAI,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAO1B,MAAMC,YAAyC,GAAGA,CAAC;EACjDC,cAAc,GAAG,KAAK;EACtBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEa,IAAI;IAAEC;EAAO,CAAC,GAAGb,OAAO,CAAC,CAAC;EAClC,MAAM;IAAEc,KAAK;IAAEC;EAAY,CAAC,GAAGd,QAAQ,CAAC,CAAC;EAEzC,MAAMe,YAAY,GAAGA,CAAA,KAAM;IACzBH,MAAM,CAAC,CAAC;IACRF,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvBN,QAAQ,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;EAED,oBACEP,OAAA;IAAQc,SAAS,EAAC,mFAAmF;IAAAC,QAAA,eACnGf,OAAA;MAAKc,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrDf,OAAA;QAAKc,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDf,OAAA;UAAKc,SAAS,EAAC,6BAA6B;UAAAC,QAAA,GACzCX,cAAc,iBACbJ,OAAA;YACEgB,OAAO,EAAEH,UAAW;YACpBC,SAAS,EAAC,qGAAqG;YAC/GT,KAAK,EAAC,QAAQ;YAAAU,QAAA,eAEdf,OAAA;cAAKc,SAAS,EAAC,SAAS;cAACG,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC5Ef,OAAA;gBAAMoB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CACT,eAED3B,OAAA,CAACF,IAAI;YAAC8B,MAAM,EAAC;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAElBtB,KAAK,iBACJL,OAAA;YAAMc,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC3D,EAEAtB,KAAK,iBACJL,OAAA;YAAIc,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAC9DV;UAAK;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACL;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN3B,OAAA;UAAKc,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAE1Cf,OAAA;YAAKc,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDf,OAAA,CAACN,IAAI;cACHmC,EAAE,EAAC,GAAG;cACNf,SAAS,EAAC,2MAA2M;cACrNT,KAAK,EAAC,SAAS;cAAAU,QAAA,gBAEff,OAAA;gBAAKc,SAAS,EAAC,cAAc;gBAACG,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAJ,QAAA,eACjFf,OAAA;kBAAMoB,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAkJ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvN,CAAC,WAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAENnB,IAAI,iBACHR,OAAA,CAAAE,SAAA;cAAAa,QAAA,gBACEf,OAAA,CAACN,IAAI;gBACHmC,EAAE,EAAC,UAAU;gBACbf,SAAS,EAAC,2MAA2M;gBACrNT,KAAK,EAAC,YAAY;gBAAAU,QAAA,gBAElBf,OAAA;kBAAKc,SAAS,EAAC,cAAc;kBAACG,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eACjFf,OAAA;oBAAMoB,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAqE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1I,CAAC,UAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAEP3B,OAAA,CAACN,IAAI;gBACHmC,EAAE,EAAC,WAAW;gBACdf,SAAS,EAAC,2MAA2M;gBACrNT,KAAK,EAAC,eAAY;gBAAAU,QAAA,gBAElBf,OAAA;kBAAKc,SAAS,EAAC,cAAc;kBAACG,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,gBACjFf,OAAA;oBAAMoB,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAqe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7iB3B,OAAA;oBAAMoB,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAkC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG,CAAC,iBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACP,CACH,eAED3B,OAAA,CAACN,IAAI;cACHmC,EAAE,EAAC,QAAQ;cACXf,SAAS,EAAC,2MAA2M;cACrNT,KAAK,EAAC,aAAU;cAAAU,QAAA,gBAEhBf,OAAA;gBAAKc,SAAS,EAAC,cAAc;gBAACG,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAJ,QAAA,eACjFf,OAAA;kBAAMoB,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAA2D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChI,CAAC,eAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGN3B,OAAA;YACE8B,IAAI,EAAC,QAAQ;YACbd,OAAO,EAAEL,WAAY;YACrBG,SAAS,EAAC,qGAAqG;YAC/GT,KAAK,EAAEK,KAAK,KAAK,OAAO,GAAG,aAAa,GAAG,YAAa;YAAAK,QAAA,EAEvDL,KAAK,KAAK,OAAO,gBAChBV,OAAA;cAAKc,SAAS,EAAC,SAAS;cAACG,IAAI,EAAC,MAAM;cAACE,OAAO,EAAC,WAAW;cAACD,MAAM,EAAC,cAAc;cAAAH,QAAA,eAC5Ef,OAAA;gBAAMoB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAuF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5J,CAAC,gBAEN3B,OAAA;cAAKc,SAAS,EAAC,SAAS;cAACG,IAAI,EAAC,MAAM;cAACE,OAAO,EAAC,WAAW;cAACD,MAAM,EAAC,cAAc;cAAAH,QAAA,eAC5Ef,OAAA;gBAAMoB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAuJ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5N;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,EAGRnB,IAAI,gBACHR,OAAA,CAAAE,SAAA;YAAAa,QAAA,gBACEf,OAAA;cAAKc,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1Cf,OAAA;gBAAKc,SAAS,EAAE,0FACdN,IAAI,CAACuB,IAAI,KAAK,OAAO,GAAG,YAAY,GAAG,aAAa,EACnD;gBAAAhB,QAAA,EACAP,IAAI,CAACwB,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;cAAC;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACN3B,OAAA;gBAAKc,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9Bf,OAAA;kBAAKc,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC/DP,IAAI,CAAC2B,MAAM,IAAI3B,IAAI,CAACwB;gBAAQ;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACN3B,OAAA;kBAAKc,SAAS,EAAE,WACdN,IAAI,CAACuB,IAAI,KAAK,OAAO,GAAG,cAAc,GAAG,eAAe,EACvD;kBAAAhB,QAAA,EACAP,IAAI,CAACuB,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG;gBAAU;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3B,OAAA;cACEgB,OAAO,EAAEJ,YAAa;cACtBE,SAAS,EAAC,yFAAyF;cAAAC,QAAA,EACpG;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC;UAAA;UAEH;UACA3B,OAAA,CAAAE,SAAA;YAAAa,QAAA,gBACEf,OAAA,CAACN,IAAI;cACHmC,EAAE,EAAC,QAAQ;cACXf,SAAS,EAAC,qIAAqI;cAAAC,QAAA,EAChJ;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP3B,OAAA,CAACN,IAAI;cACHmC,EAAE,EAAC,WAAW;cACdf,SAAS,EAAC,2GAA2G;cAAAC,QAAA,EACtH;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACrB,EAAA,CA1KIH,YAAyC;EAAA,QAI5BR,WAAW,EACHC,OAAO,EACDC,QAAQ;AAAA;AAAAuC,EAAA,GANnCjC,YAAyC;AA4K/C,eAAeA,YAAY;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}