{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { bignumberDependencies } from './dependenciesBignumber.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { isPositiveDependencies } from './dependenciesIsPositive.generated.js';\nimport { largerDependencies } from './dependenciesLarger.generated.js';\nimport { largerEqDependencies } from './dependenciesLargerEq.generated.js';\nimport { smallerDependencies } from './dependenciesSmaller.generated.js';\nimport { smallerEqDependencies } from './dependenciesSmallerEq.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createRange } from '../../factoriesAny.js';\nexport var rangeDependencies = {\n  bignumberDependencies,\n  matrixDependencies,\n  addDependencies,\n  isPositiveDependencies,\n  largerDependencies,\n  largerEqDependencies,\n  smallerDependencies,\n  smallerEqDependencies,\n  typedDependencies,\n  createRange\n};", "map": {"version": 3, "names": ["bignumberDependencies", "matrixDependencies", "addDependencies", "isPositiveDependencies", "largerDependencies", "largerEqDependencies", "smallerDependencies", "smallerEqDependencies", "typedDependencies", "createRange", "rangeDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesRange.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { bignumberDependencies } from './dependenciesBignumber.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { isPositiveDependencies } from './dependenciesIsPositive.generated.js';\nimport { largerDependencies } from './dependenciesLarger.generated.js';\nimport { largerEqDependencies } from './dependenciesLargerEq.generated.js';\nimport { smallerDependencies } from './dependenciesSmaller.generated.js';\nimport { smallerEqDependencies } from './dependenciesSmallerEq.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createRange } from '../../factoriesAny.js';\nexport var rangeDependencies = {\n  bignumberDependencies,\n  matrixDependencies,\n  addDependencies,\n  isPositiveDependencies,\n  largerDependencies,\n  largerEqDependencies,\n  smallerDependencies,\n  smallerEqDependencies,\n  typedDependencies,\n  createRange\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,WAAW,QAAQ,uBAAuB;AACnD,OAAO,IAAIC,iBAAiB,GAAG;EAC7BV,qBAAqB;EACrBC,kBAAkB;EAClBC,eAAe;EACfC,sBAAsB;EACtBC,kBAAkB;EAClBC,oBAAoB;EACpBC,mBAAmB;EACnBC,qBAAqB;EACrBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}