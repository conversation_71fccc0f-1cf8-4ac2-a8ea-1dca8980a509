{"ast": null, "code": "export var tanDocs = {\n  name: 'tan',\n  category: 'Trigonometry',\n  syntax: ['tan(x)'],\n  description: 'Compute the tangent of x in radians.',\n  examples: ['tan(0.5)', 'sin(0.5) / cos(0.5)', 'tan(pi / 4)', 'tan(45 deg)'],\n  seealso: ['atan', 'sin', 'cos']\n};", "map": {"version": 3, "names": ["tanDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/tan.js"], "sourcesContent": ["export var tanDocs = {\n  name: 'tan',\n  category: 'Trigonometry',\n  syntax: ['tan(x)'],\n  description: 'Compute the tangent of x in radians.',\n  examples: ['tan(0.5)', 'sin(0.5) / cos(0.5)', 'tan(pi / 4)', 'tan(45 deg)'],\n  seealso: ['atan', 'sin', 'cos']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,sCAAsC;EACnDC,QAAQ,EAAE,CAAC,UAAU,EAAE,qBAAqB,EAAE,aAAa,EAAE,aAAa,CAAC;EAC3EC,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}