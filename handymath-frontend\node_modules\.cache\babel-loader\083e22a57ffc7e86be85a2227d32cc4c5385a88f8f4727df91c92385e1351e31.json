{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csPermute } from './csPermute.js';\nimport { csPost } from './csPost.js';\nimport { csEtree } from './csEtree.js';\nimport { createCsAmd } from './csAmd.js';\nimport { createCsCounts } from './csCounts.js';\nimport { factory } from '../../../utils/factory.js';\nvar name = 'csSqr';\nvar dependencies = ['add', 'multiply', 'transpose'];\nexport var createCsSqr = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    add,\n    multiply,\n    transpose\n  } = _ref;\n  var csAmd = createCsAmd({\n    add,\n    multiply,\n    transpose\n  });\n  var csCounts = createCsCounts({\n    transpose\n  });\n\n  /**\n   * Symbolic ordering and analysis for QR and LU decompositions.\n   *\n   * @param {Number}  order           The ordering strategy (see csAmd for more details)\n   * @param {Matrix}  a               The A matrix\n   * @param {boolean} qr              Symbolic ordering and analysis for QR decomposition (true) or\n   *                                  symbolic ordering and analysis for LU decomposition (false)\n   *\n   * @return {Object}                 The Symbolic ordering and analysis for matrix A\n   */\n  return function csSqr(order, a, qr) {\n    // a arrays\n    var aptr = a._ptr;\n    var asize = a._size;\n    // columns\n    var n = asize[1];\n    // vars\n    var k;\n    // symbolic analysis result\n    var s = {};\n    // fill-reducing ordering\n    s.q = csAmd(order, a);\n    // validate results\n    if (order && !s.q) {\n      return null;\n    }\n    // QR symbolic analysis\n    if (qr) {\n      // apply permutations if needed\n      var c = order ? csPermute(a, null, s.q, 0) : a;\n      // etree of C'*C, where C=A(:,q)\n      s.parent = csEtree(c, 1);\n      // post order elimination tree\n      var post = csPost(s.parent, n);\n      // col counts chol(C'*C)\n      s.cp = csCounts(c, s.parent, post, 1);\n      // check we have everything needed to calculate number of nonzero elements\n      if (c && s.parent && s.cp && _vcount(c, s)) {\n        // calculate number of nonzero elements\n        for (s.unz = 0, k = 0; k < n; k++) {\n          s.unz += s.cp[k];\n        }\n      }\n    } else {\n      // for LU factorization only, guess nnz(L) and nnz(U)\n      s.unz = 4 * aptr[n] + n;\n      s.lnz = s.unz;\n    }\n    // return result S\n    return s;\n  };\n\n  /**\n   * Compute nnz(V) = s.lnz, s.pinv, s.leftmost, s.m2 from A and s.parent\n   */\n  function _vcount(a, s) {\n    // a arrays\n    var aptr = a._ptr;\n    var aindex = a._index;\n    var asize = a._size;\n    // rows & columns\n    var m = asize[0];\n    var n = asize[1];\n    // initialize s arrays\n    s.pinv = []; // (m + n)\n    s.leftmost = []; // (m)\n    // vars\n    var parent = s.parent;\n    var pinv = s.pinv;\n    var leftmost = s.leftmost;\n    // workspace, next: first m entries, head: next n entries, tail: next n entries, nque: next n entries\n    var w = []; // (m + 3 * n)\n    var next = 0;\n    var head = m;\n    var tail = m + n;\n    var nque = m + 2 * n;\n    // vars\n    var i, k, p, p0, p1;\n    // initialize w\n    for (k = 0; k < n; k++) {\n      // queue k is empty\n      w[head + k] = -1;\n      w[tail + k] = -1;\n      w[nque + k] = 0;\n    }\n    // initialize row arrays\n    for (i = 0; i < m; i++) {\n      leftmost[i] = -1;\n    }\n    // loop columns backwards\n    for (k = n - 1; k >= 0; k--) {\n      // values & index for column k\n      for (p0 = aptr[k], p1 = aptr[k + 1], p = p0; p < p1; p++) {\n        // leftmost[i] = min(find(A(i,:)))\n        leftmost[aindex[p]] = k;\n      }\n    }\n    // scan rows in reverse order\n    for (i = m - 1; i >= 0; i--) {\n      // row i is not yet ordered\n      pinv[i] = -1;\n      k = leftmost[i];\n      // check row i is empty\n      if (k === -1) {\n        continue;\n      }\n      // first row in queue k\n      if (w[nque + k]++ === 0) {\n        w[tail + k] = i;\n      }\n      // put i at head of queue k\n      w[next + i] = w[head + k];\n      w[head + k] = i;\n    }\n    s.lnz = 0;\n    s.m2 = m;\n    // find row permutation and nnz(V)\n    for (k = 0; k < n; k++) {\n      // remove row i from queue k\n      i = w[head + k];\n      // count V(k,k) as nonzero\n      s.lnz++;\n      // add a fictitious row\n      if (i < 0) {\n        i = s.m2++;\n      }\n      // associate row i with V(:,k)\n      pinv[i] = k;\n      // skip if V(k+1:m,k) is empty\n      if (--nque[k] <= 0) {\n        continue;\n      }\n      // nque[k] is nnz (V(k+1:m,k))\n      s.lnz += w[nque + k];\n      // move all rows to parent of k\n      var pa = parent[k];\n      if (pa !== -1) {\n        if (w[nque + pa] === 0) {\n          w[tail + pa] = w[tail + k];\n        }\n        w[next + w[tail + k]] = w[head + pa];\n        w[head + pa] = w[next + i];\n        w[nque + pa] += w[nque + k];\n      }\n    }\n    for (i = 0; i < m; i++) {\n      if (pinv[i] < 0) {\n        pinv[i] = k++;\n      }\n    }\n    return true;\n  }\n});", "map": {"version": 3, "names": ["csPermute", "csPost", "csEtree", "createCsAmd", "createCsCounts", "factory", "name", "dependencies", "createCsSqr", "_ref", "add", "multiply", "transpose", "csAmd", "csCounts", "csSqr", "order", "a", "qr", "aptr", "_ptr", "asize", "_size", "n", "k", "s", "q", "c", "parent", "post", "cp", "_vcount", "unz", "lnz", "aindex", "_index", "m", "pinv", "leftmost", "w", "next", "head", "tail", "nque", "i", "p", "p0", "p1", "m2", "pa"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csSqr.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csPermute } from './csPermute.js';\nimport { csPost } from './csPost.js';\nimport { csEtree } from './csEtree.js';\nimport { createCsAmd } from './csAmd.js';\nimport { createCsCounts } from './csCounts.js';\nimport { factory } from '../../../utils/factory.js';\nvar name = 'csSqr';\nvar dependencies = ['add', 'multiply', 'transpose'];\nexport var createCsSqr = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    add,\n    multiply,\n    transpose\n  } = _ref;\n  var csAmd = createCsAmd({\n    add,\n    multiply,\n    transpose\n  });\n  var csCounts = createCsCounts({\n    transpose\n  });\n\n  /**\n   * Symbolic ordering and analysis for QR and LU decompositions.\n   *\n   * @param {Number}  order           The ordering strategy (see csAmd for more details)\n   * @param {Matrix}  a               The A matrix\n   * @param {boolean} qr              Symbolic ordering and analysis for QR decomposition (true) or\n   *                                  symbolic ordering and analysis for LU decomposition (false)\n   *\n   * @return {Object}                 The Symbolic ordering and analysis for matrix A\n   */\n  return function csSqr(order, a, qr) {\n    // a arrays\n    var aptr = a._ptr;\n    var asize = a._size;\n    // columns\n    var n = asize[1];\n    // vars\n    var k;\n    // symbolic analysis result\n    var s = {};\n    // fill-reducing ordering\n    s.q = csAmd(order, a);\n    // validate results\n    if (order && !s.q) {\n      return null;\n    }\n    // QR symbolic analysis\n    if (qr) {\n      // apply permutations if needed\n      var c = order ? csPermute(a, null, s.q, 0) : a;\n      // etree of C'*C, where C=A(:,q)\n      s.parent = csEtree(c, 1);\n      // post order elimination tree\n      var post = csPost(s.parent, n);\n      // col counts chol(C'*C)\n      s.cp = csCounts(c, s.parent, post, 1);\n      // check we have everything needed to calculate number of nonzero elements\n      if (c && s.parent && s.cp && _vcount(c, s)) {\n        // calculate number of nonzero elements\n        for (s.unz = 0, k = 0; k < n; k++) {\n          s.unz += s.cp[k];\n        }\n      }\n    } else {\n      // for LU factorization only, guess nnz(L) and nnz(U)\n      s.unz = 4 * aptr[n] + n;\n      s.lnz = s.unz;\n    }\n    // return result S\n    return s;\n  };\n\n  /**\n   * Compute nnz(V) = s.lnz, s.pinv, s.leftmost, s.m2 from A and s.parent\n   */\n  function _vcount(a, s) {\n    // a arrays\n    var aptr = a._ptr;\n    var aindex = a._index;\n    var asize = a._size;\n    // rows & columns\n    var m = asize[0];\n    var n = asize[1];\n    // initialize s arrays\n    s.pinv = []; // (m + n)\n    s.leftmost = []; // (m)\n    // vars\n    var parent = s.parent;\n    var pinv = s.pinv;\n    var leftmost = s.leftmost;\n    // workspace, next: first m entries, head: next n entries, tail: next n entries, nque: next n entries\n    var w = []; // (m + 3 * n)\n    var next = 0;\n    var head = m;\n    var tail = m + n;\n    var nque = m + 2 * n;\n    // vars\n    var i, k, p, p0, p1;\n    // initialize w\n    for (k = 0; k < n; k++) {\n      // queue k is empty\n      w[head + k] = -1;\n      w[tail + k] = -1;\n      w[nque + k] = 0;\n    }\n    // initialize row arrays\n    for (i = 0; i < m; i++) {\n      leftmost[i] = -1;\n    }\n    // loop columns backwards\n    for (k = n - 1; k >= 0; k--) {\n      // values & index for column k\n      for (p0 = aptr[k], p1 = aptr[k + 1], p = p0; p < p1; p++) {\n        // leftmost[i] = min(find(A(i,:)))\n        leftmost[aindex[p]] = k;\n      }\n    }\n    // scan rows in reverse order\n    for (i = m - 1; i >= 0; i--) {\n      // row i is not yet ordered\n      pinv[i] = -1;\n      k = leftmost[i];\n      // check row i is empty\n      if (k === -1) {\n        continue;\n      }\n      // first row in queue k\n      if (w[nque + k]++ === 0) {\n        w[tail + k] = i;\n      }\n      // put i at head of queue k\n      w[next + i] = w[head + k];\n      w[head + k] = i;\n    }\n    s.lnz = 0;\n    s.m2 = m;\n    // find row permutation and nnz(V)\n    for (k = 0; k < n; k++) {\n      // remove row i from queue k\n      i = w[head + k];\n      // count V(k,k) as nonzero\n      s.lnz++;\n      // add a fictitious row\n      if (i < 0) {\n        i = s.m2++;\n      }\n      // associate row i with V(:,k)\n      pinv[i] = k;\n      // skip if V(k+1:m,k) is empty\n      if (--nque[k] <= 0) {\n        continue;\n      }\n      // nque[k] is nnz (V(k+1:m,k))\n      s.lnz += w[nque + k];\n      // move all rows to parent of k\n      var pa = parent[k];\n      if (pa !== -1) {\n        if (w[nque + pa] === 0) {\n          w[tail + pa] = w[tail + k];\n        }\n        w[next + w[tail + k]] = w[head + pa];\n        w[head + pa] = w[next + i];\n        w[nque + pa] += w[nque + k];\n      }\n    }\n    for (i = 0; i < m; i++) {\n      if (pinv[i] < 0) {\n        pinv[i] = k++;\n      }\n    }\n    return true;\n  }\n});"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,WAAW,QAAQ,YAAY;AACxC,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,IAAIC,IAAI,GAAG,OAAO;AAClB,IAAIC,YAAY,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,WAAW,CAAC;AACnD,OAAO,IAAIC,WAAW,GAAG,eAAeH,OAAO,CAACC,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAC1E,IAAI;IACFC,GAAG;IACHC,QAAQ;IACRC;EACF,CAAC,GAAGH,IAAI;EACR,IAAII,KAAK,GAAGV,WAAW,CAAC;IACtBO,GAAG;IACHC,QAAQ;IACRC;EACF,CAAC,CAAC;EACF,IAAIE,QAAQ,GAAGV,cAAc,CAAC;IAC5BQ;EACF,CAAC,CAAC;;EAEF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASG,KAAKA,CAACC,KAAK,EAAEC,CAAC,EAAEC,EAAE,EAAE;IAClC;IACA,IAAIC,IAAI,GAAGF,CAAC,CAACG,IAAI;IACjB,IAAIC,KAAK,GAAGJ,CAAC,CAACK,KAAK;IACnB;IACA,IAAIC,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC;IAChB;IACA,IAAIG,CAAC;IACL;IACA,IAAIC,CAAC,GAAG,CAAC,CAAC;IACV;IACAA,CAAC,CAACC,CAAC,GAAGb,KAAK,CAACG,KAAK,EAAEC,CAAC,CAAC;IACrB;IACA,IAAID,KAAK,IAAI,CAACS,CAAC,CAACC,CAAC,EAAE;MACjB,OAAO,IAAI;IACb;IACA;IACA,IAAIR,EAAE,EAAE;MACN;MACA,IAAIS,CAAC,GAAGX,KAAK,GAAGhB,SAAS,CAACiB,CAAC,EAAE,IAAI,EAAEQ,CAAC,CAACC,CAAC,EAAE,CAAC,CAAC,GAAGT,CAAC;MAC9C;MACAQ,CAAC,CAACG,MAAM,GAAG1B,OAAO,CAACyB,CAAC,EAAE,CAAC,CAAC;MACxB;MACA,IAAIE,IAAI,GAAG5B,MAAM,CAACwB,CAAC,CAACG,MAAM,EAAEL,CAAC,CAAC;MAC9B;MACAE,CAAC,CAACK,EAAE,GAAGhB,QAAQ,CAACa,CAAC,EAAEF,CAAC,CAACG,MAAM,EAAEC,IAAI,EAAE,CAAC,CAAC;MACrC;MACA,IAAIF,CAAC,IAAIF,CAAC,CAACG,MAAM,IAAIH,CAAC,CAACK,EAAE,IAAIC,OAAO,CAACJ,CAAC,EAAEF,CAAC,CAAC,EAAE;QAC1C;QACA,KAAKA,CAAC,CAACO,GAAG,GAAG,CAAC,EAAER,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,EAAEC,CAAC,EAAE,EAAE;UACjCC,CAAC,CAACO,GAAG,IAAIP,CAAC,CAACK,EAAE,CAACN,CAAC,CAAC;QAClB;MACF;IACF,CAAC,MAAM;MACL;MACAC,CAAC,CAACO,GAAG,GAAG,CAAC,GAAGb,IAAI,CAACI,CAAC,CAAC,GAAGA,CAAC;MACvBE,CAAC,CAACQ,GAAG,GAAGR,CAAC,CAACO,GAAG;IACf;IACA;IACA,OAAOP,CAAC;EACV,CAAC;;EAED;AACF;AACA;EACE,SAASM,OAAOA,CAACd,CAAC,EAAEQ,CAAC,EAAE;IACrB;IACA,IAAIN,IAAI,GAAGF,CAAC,CAACG,IAAI;IACjB,IAAIc,MAAM,GAAGjB,CAAC,CAACkB,MAAM;IACrB,IAAId,KAAK,GAAGJ,CAAC,CAACK,KAAK;IACnB;IACA,IAAIc,CAAC,GAAGf,KAAK,CAAC,CAAC,CAAC;IAChB,IAAIE,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC;IAChB;IACAI,CAAC,CAACY,IAAI,GAAG,EAAE,CAAC,CAAC;IACbZ,CAAC,CAACa,QAAQ,GAAG,EAAE,CAAC,CAAC;IACjB;IACA,IAAIV,MAAM,GAAGH,CAAC,CAACG,MAAM;IACrB,IAAIS,IAAI,GAAGZ,CAAC,CAACY,IAAI;IACjB,IAAIC,QAAQ,GAAGb,CAAC,CAACa,QAAQ;IACzB;IACA,IAAIC,CAAC,GAAG,EAAE,CAAC,CAAC;IACZ,IAAIC,IAAI,GAAG,CAAC;IACZ,IAAIC,IAAI,GAAGL,CAAC;IACZ,IAAIM,IAAI,GAAGN,CAAC,GAAGb,CAAC;IAChB,IAAIoB,IAAI,GAAGP,CAAC,GAAG,CAAC,GAAGb,CAAC;IACpB;IACA,IAAIqB,CAAC,EAAEpB,CAAC,EAAEqB,CAAC,EAAEC,EAAE,EAAEC,EAAE;IACnB;IACA,KAAKvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,EAAEC,CAAC,EAAE,EAAE;MACtB;MACAe,CAAC,CAACE,IAAI,GAAGjB,CAAC,CAAC,GAAG,CAAC,CAAC;MAChBe,CAAC,CAACG,IAAI,GAAGlB,CAAC,CAAC,GAAG,CAAC,CAAC;MAChBe,CAAC,CAACI,IAAI,GAAGnB,CAAC,CAAC,GAAG,CAAC;IACjB;IACA;IACA,KAAKoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,CAAC,EAAEQ,CAAC,EAAE,EAAE;MACtBN,QAAQ,CAACM,CAAC,CAAC,GAAG,CAAC,CAAC;IAClB;IACA;IACA,KAAKpB,CAAC,GAAGD,CAAC,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3B;MACA,KAAKsB,EAAE,GAAG3B,IAAI,CAACK,CAAC,CAAC,EAAEuB,EAAE,GAAG5B,IAAI,CAACK,CAAC,GAAG,CAAC,CAAC,EAAEqB,CAAC,GAAGC,EAAE,EAAED,CAAC,GAAGE,EAAE,EAAEF,CAAC,EAAE,EAAE;QACxD;QACAP,QAAQ,CAACJ,MAAM,CAACW,CAAC,CAAC,CAAC,GAAGrB,CAAC;MACzB;IACF;IACA;IACA,KAAKoB,CAAC,GAAGR,CAAC,GAAG,CAAC,EAAEQ,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3B;MACAP,IAAI,CAACO,CAAC,CAAC,GAAG,CAAC,CAAC;MACZpB,CAAC,GAAGc,QAAQ,CAACM,CAAC,CAAC;MACf;MACA,IAAIpB,CAAC,KAAK,CAAC,CAAC,EAAE;QACZ;MACF;MACA;MACA,IAAIe,CAAC,CAACI,IAAI,GAAGnB,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE;QACvBe,CAAC,CAACG,IAAI,GAAGlB,CAAC,CAAC,GAAGoB,CAAC;MACjB;MACA;MACAL,CAAC,CAACC,IAAI,GAAGI,CAAC,CAAC,GAAGL,CAAC,CAACE,IAAI,GAAGjB,CAAC,CAAC;MACzBe,CAAC,CAACE,IAAI,GAAGjB,CAAC,CAAC,GAAGoB,CAAC;IACjB;IACAnB,CAAC,CAACQ,GAAG,GAAG,CAAC;IACTR,CAAC,CAACuB,EAAE,GAAGZ,CAAC;IACR;IACA,KAAKZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,EAAEC,CAAC,EAAE,EAAE;MACtB;MACAoB,CAAC,GAAGL,CAAC,CAACE,IAAI,GAAGjB,CAAC,CAAC;MACf;MACAC,CAAC,CAACQ,GAAG,EAAE;MACP;MACA,IAAIW,CAAC,GAAG,CAAC,EAAE;QACTA,CAAC,GAAGnB,CAAC,CAACuB,EAAE,EAAE;MACZ;MACA;MACAX,IAAI,CAACO,CAAC,CAAC,GAAGpB,CAAC;MACX;MACA,IAAI,EAAEmB,IAAI,CAACnB,CAAC,CAAC,IAAI,CAAC,EAAE;QAClB;MACF;MACA;MACAC,CAAC,CAACQ,GAAG,IAAIM,CAAC,CAACI,IAAI,GAAGnB,CAAC,CAAC;MACpB;MACA,IAAIyB,EAAE,GAAGrB,MAAM,CAACJ,CAAC,CAAC;MAClB,IAAIyB,EAAE,KAAK,CAAC,CAAC,EAAE;QACb,IAAIV,CAAC,CAACI,IAAI,GAAGM,EAAE,CAAC,KAAK,CAAC,EAAE;UACtBV,CAAC,CAACG,IAAI,GAAGO,EAAE,CAAC,GAAGV,CAAC,CAACG,IAAI,GAAGlB,CAAC,CAAC;QAC5B;QACAe,CAAC,CAACC,IAAI,GAAGD,CAAC,CAACG,IAAI,GAAGlB,CAAC,CAAC,CAAC,GAAGe,CAAC,CAACE,IAAI,GAAGQ,EAAE,CAAC;QACpCV,CAAC,CAACE,IAAI,GAAGQ,EAAE,CAAC,GAAGV,CAAC,CAACC,IAAI,GAAGI,CAAC,CAAC;QAC1BL,CAAC,CAACI,IAAI,GAAGM,EAAE,CAAC,IAAIV,CAAC,CAACI,IAAI,GAAGnB,CAAC,CAAC;MAC7B;IACF;IACA,KAAKoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,CAAC,EAAEQ,CAAC,EAAE,EAAE;MACtB,IAAIP,IAAI,CAACO,CAAC,CAAC,GAAG,CAAC,EAAE;QACfP,IAAI,CAACO,CAAC,CAAC,GAAGpB,CAAC,EAAE;MACf;IACF;IACA,OAAO,IAAI;EACb;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}