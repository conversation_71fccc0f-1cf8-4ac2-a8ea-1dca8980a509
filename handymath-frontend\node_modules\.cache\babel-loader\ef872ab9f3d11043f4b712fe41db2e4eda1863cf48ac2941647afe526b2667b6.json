{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { bignumberDependencies } from './dependenciesBignumber.generated.js';\nimport { fractionDependencies } from './dependenciesFraction.generated.js';\nimport { AccessorNodeDependencies } from './dependenciesAccessorNode.generated.js';\nimport { ArrayNodeDependencies } from './dependenciesArrayNode.generated.js';\nimport { ConstantNodeDependencies } from './dependenciesConstantNode.generated.js';\nimport { FunctionNodeDependencies } from './dependenciesFunctionNode.generated.js';\nimport { IndexNodeDependencies } from './dependenciesIndexNode.generated.js';\nimport { ObjectNodeDependencies } from './dependenciesObjectNode.generated.js';\nimport { OperatorNodeDependencies } from './dependenciesOperatorNode.generated.js';\nimport { SymbolNodeDependencies } from './dependenciesSymbolNode.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSimplifyConstant } from '../../factoriesAny.js';\nexport var simplifyConstantDependencies = {\n  bignumberDependencies,\n  fractionDependencies,\n  AccessorNodeDependencies,\n  ArrayNodeDependencies,\n  ConstantNodeDependencies,\n  FunctionNodeDependencies,\n  IndexNodeDependencies,\n  ObjectNodeDependencies,\n  OperatorNodeDependencies,\n  SymbolNodeDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createSimplifyConstant\n};", "map": {"version": 3, "names": ["bignumberDependencies", "fractionDependencies", "AccessorNodeDependencies", "ArrayNodeDependencies", "ConstantNodeDependencies", "FunctionNodeDependencies", "IndexNodeDependencies", "ObjectNodeDependencies", "OperatorNodeDependencies", "SymbolNodeDependencies", "matrixDependencies", "typedDependencies", "createSimplifyConstant", "simplifyConstantDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSimplifyConstant.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { bignumberDependencies } from './dependenciesBignumber.generated.js';\nimport { fractionDependencies } from './dependenciesFraction.generated.js';\nimport { AccessorNodeDependencies } from './dependenciesAccessorNode.generated.js';\nimport { ArrayNodeDependencies } from './dependenciesArrayNode.generated.js';\nimport { ConstantNodeDependencies } from './dependenciesConstantNode.generated.js';\nimport { FunctionNodeDependencies } from './dependenciesFunctionNode.generated.js';\nimport { IndexNodeDependencies } from './dependenciesIndexNode.generated.js';\nimport { ObjectNodeDependencies } from './dependenciesObjectNode.generated.js';\nimport { OperatorNodeDependencies } from './dependenciesOperatorNode.generated.js';\nimport { SymbolNodeDependencies } from './dependenciesSymbolNode.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSimplifyConstant } from '../../factoriesAny.js';\nexport var simplifyConstantDependencies = {\n  bignumberDependencies,\n  fractionDependencies,\n  AccessorNodeDependencies,\n  ArrayNodeDependencies,\n  ConstantNodeDependencies,\n  FunctionNodeDependencies,\n  IndexNodeDependencies,\n  ObjectNodeDependencies,\n  OperatorNodeDependencies,\n  SymbolNodeDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createSimplifyConstant\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,sBAAsB,QAAQ,uBAAuB;AAC9D,OAAO,IAAIC,4BAA4B,GAAG;EACxCb,qBAAqB;EACrBC,oBAAoB;EACpBC,wBAAwB;EACxBC,qBAAqB;EACrBC,wBAAwB;EACxBC,wBAAwB;EACxBC,qBAAqB;EACrBC,sBAAsB;EACtBC,wBAAwB;EACxBC,sBAAsB;EACtBC,kBAAkB;EAClBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}