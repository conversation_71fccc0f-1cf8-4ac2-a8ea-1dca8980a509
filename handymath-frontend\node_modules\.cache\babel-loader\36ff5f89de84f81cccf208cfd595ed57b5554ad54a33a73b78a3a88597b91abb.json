{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\CoursesPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport StudentNavbar from '../components/StudentNavbar';\nimport Pagination from '../components/Pagination';\nimport api from '../services/api';\nimport { MagnifyingGlassIcon, FunnelIcon, BookOpenIcon, ClockIcon, AcademicCapIcon, StarIcon, CheckCircleIcon, LockClosedIcon, ChartBarIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CoursesPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [courses, setCourses] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    level: '',\n    featured: false,\n    search: '',\n    sortBy: 'title',\n    sortOrder: 'asc'\n  });\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(9);\n  const [totalItems, setTotalItems] = useState(0);\n\n  // Options de pagination pour les cours\n  const coursePaginationOptions = [6, 9, 12, 18];\n  useEffect(() => {\n    if (user) {\n      fetchCourses();\n    }\n  }, [user, filters, currentPage, itemsPerPage]);\n  const fetchCourses = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n\n      // Filtres\n      if (filters.level) params.append('level', filters.level);\n      if (filters.featured) params.append('featured', 'true');\n      if (filters.search) params.append('search', filters.search);\n\n      // Tri\n      params.append('ordering', filters.sortOrder === 'desc' ? `-${filters.sortBy}` : filters.sortBy);\n\n      // Pagination parameters\n      params.append('page', currentPage.toString());\n      params.append('page_size', itemsPerPage.toString());\n      const response = await api.get(`/courses/?${params.toString()}`);\n      if (response.data) {\n        setCourses(response.data.courses || response.data.results || []);\n        // Utiliser les nouvelles données de pagination du backend\n        if (response.data.pagination) {\n          setTotalItems(response.data.pagination.total_count);\n        } else {\n          setTotalItems(response.data.count || response.data.total || 0);\n        }\n      }\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      // Fallback avec des données de test si l'API n'est pas disponible\n      setCourses([{\n        id: 1,\n        title: 'Algèbre de base',\n        description: 'Apprenez les concepts fondamentaux de l\\'algèbre avec des exercices pratiques et des exemples concrets.',\n        level: 'beginner',\n        level_display: 'Débutant',\n        thumbnail: '📚',\n        estimated_duration: 180,\n        is_featured: true,\n        chapters_count: 4,\n        lessons_count: 12,\n        progress_percentage: 45,\n        is_accessible: true,\n        is_enrolled: true,\n        enrollment_date: '2024-01-15T10:30:00Z',\n        prerequisites: [],\n        created_at: '2024-01-15T10:30:00Z'\n      }, {\n        id: 2,\n        title: 'Géométrie euclidienne',\n        description: 'Découvrez les principes de la géométrie euclidienne et ses applications pratiques.',\n        level: 'intermediate',\n        level_display: 'Intermédiaire',\n        thumbnail: '📐',\n        estimated_duration: 240,\n        is_featured: false,\n        chapters_count: 6,\n        lessons_count: 18,\n        progress_percentage: 0,\n        is_accessible: true,\n        is_enrolled: false,\n        prerequisites: [{\n          id: 1,\n          title: 'Algèbre de base',\n          progress: 45\n        }],\n        created_at: '2024-01-16T14:20:00Z'\n      }, {\n        id: 3,\n        title: 'Calcul différentiel',\n        description: 'Maîtrisez les concepts du calcul différentiel et intégral.',\n        level: 'advanced',\n        level_display: 'Avancé',\n        thumbnail: '∫',\n        estimated_duration: 360,\n        is_featured: true,\n        chapters_count: 8,\n        lessons_count: 24,\n        progress_percentage: 0,\n        is_accessible: false,\n        is_enrolled: false,\n        prerequisites: [{\n          id: 1,\n          title: 'Algèbre de base',\n          progress: 45\n        }],\n        created_at: '2024-01-17T09:45:00Z'\n      }]);\n      setTotalItems(3); // Nombre total de cours de test\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Pagination handlers\n  const handlePageChange = page => {\n    setCurrentPage(page);\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  const handleItemsPerPageChange = newItemsPerPage => {\n    setItemsPerPage(newItemsPerPage);\n    setCurrentPage(1); // Reset to first page when changing items per page\n  };\n  const handleEnroll = async (courseId, courseTitle) => {\n    try {\n      setCourses(courses.map(course => course.id === courseId ? {\n        ...course,\n        is_enrolled: true,\n        enrollment_date: new Date().toISOString()\n      } : course));\n      alert(`Inscription réussie au cours \"${courseTitle}\" !`);\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      alert('Erreur lors de l\\'inscription');\n    }\n  };\n  const clearFilters = () => {\n    setFilters({\n      level: '',\n      featured: false,\n      search: '',\n      sortBy: 'title',\n      sortOrder: 'asc'\n    });\n    setCurrentPage(1); // Reset to first page when clearing filters\n  };\n  const formatDuration = minutes => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n  const getLevelColor = level => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n\n  // Calculate total pages\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Cours de Math\\xE9matiques\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Vous devez \\xEAtre connect\\xE9 pour acc\\xE9der aux cours.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(StudentNavbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-5xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-4\",\n              children: \"Cours de Math\\xE9matiques\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\",\n              children: \"D\\xE9couvrez nos cours structur\\xE9s et interactifs pour ma\\xEEtriser les math\\xE9matiques \\xE0 votre rythme\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.2\n          },\n          className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6 mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n              className: \"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Rechercher un cours...\",\n              value: filters.search,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                search: e.target.value\n              })),\n              className: \"w-full pl-12 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-4 gap-4 items-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(AcademicCapIcon, {\n                  className: \"inline h-4 w-4 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 17\n                }, this), \"Niveau\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.level,\n                onChange: e => setFilters(prev => ({\n                  ...prev,\n                  level: e.target.value\n                })),\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Tous les niveaux\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"beginner\",\n                  children: \"D\\xE9butant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"intermediate\",\n                  children: \"Interm\\xE9diaire\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"advanced\",\n                  children: \"Avanc\\xE9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(FunnelIcon, {\n                  className: \"inline h-4 w-4 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 17\n                }, this), \"Trier par\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.sortBy,\n                onChange: e => setFilters(prev => ({\n                  ...prev,\n                  sortBy: e.target.value\n                })),\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"title\",\n                  children: \"Titre\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"level\",\n                  children: \"Niveau\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"duration\",\n                  children: \"Dur\\xE9e\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"created_at\",\n                  children: \"Date de cr\\xE9ation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: \"Ordre\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filters.sortOrder,\n                onChange: e => setFilters(prev => ({\n                  ...prev,\n                  sortOrder: e.target.value\n                })),\n                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"asc\",\n                  children: \"Croissant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"desc\",\n                  children: \"D\\xE9croissant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: filters.featured,\n                  onChange: e => setFilters(prev => ({\n                    ...prev,\n                    featured: e.target.checked\n                  })),\n                  className: \"mr-2 rounded text-blue-600 focus:ring-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(StarIcon, {\n                  className: \"h-4 w-4 text-yellow-500 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-700 dark:text-gray-300\",\n                  children: \"Vedettes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: clearFilters,\n                className: \"px-4 py-2 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors\",\n                children: \"Effacer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 9\n        }, this), loading && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          className: \"text-center py-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 rounded-full h-16 w-16 border-4 border-transparent border-r-indigo-600 mx-auto animate-spin\",\n              style: {\n                animationDirection: 'reverse',\n                animationDuration: '1.5s'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-6 text-lg text-gray-600 dark:text-gray-400\",\n            children: \"Chargement des cours...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), !loading && courses.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.4\n          },\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-4 text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(BookOpenIcon, {\n                className: \"h-8 w-8 mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm opacity-90\",\n                  children: \"Total des cours\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold\",\n                  children: totalItems\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-4 text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                className: \"h-8 w-8 mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm opacity-90\",\n                  children: \"Cours inscrits\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold\",\n                  children: courses.filter(c => c.is_enrolled).length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-4 text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(ChartBarIcon, {\n                className: \"h-8 w-8 mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm opacity-90\",\n                  children: \"Progression moyenne\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-bold\",\n                  children: [Math.round(courses.filter(c => c.is_enrolled).reduce((acc, c) => acc + c.progress_percentage, 0) / Math.max(courses.filter(c => c.is_enrolled).length, 1)), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this), !loading && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 mb-8\",\n            children: courses.map((course, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"group bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-2xl hover:-translate-y-2 transition-all duration-300\",\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: index * 0.1,\n                duration: 0.5\n              },\n              whileHover: {\n                scale: 1.02\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative h-48 bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 flex items-center justify-center overflow-hidden\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-black bg-opacity-20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-6xl text-white z-10 drop-shadow-lg\",\n                  children: course.thumbnail\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this), course.is_featured && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-4 right-4 bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(StarIcon, {\n                    className: \"h-3 w-3 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 25\n                  }, this), \"Vedette\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-4 left-4\",\n                  children: course.is_enrolled ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                      className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 27\n                    }, this), \"Inscrit\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 25\n                  }, this) : course.is_accessible ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-bold\",\n                    children: \"Disponible\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-500 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(LockClosedIcon, {\n                      className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 27\n                    }, this), \"Verrouill\\xE9\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 transition-colors\",\n                    children: course.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600 dark:text-gray-400 text-sm leading-relaxed line-clamp-2\",\n                    children: course.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center text-sm text-gray-500 dark:text-gray-400\",\n                      children: [/*#__PURE__*/_jsxDEV(AcademicCapIcon, {\n                        className: \"h-4 w-4 mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 483,\n                        columnNumber: 27\n                      }, this), \"Niveau\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 482,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-3 py-1 rounded-full text-xs font-bold ${getLevelColor(course.level)}`,\n                      children: course.level_display\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center text-sm text-gray-500 dark:text-gray-400\",\n                      children: [/*#__PURE__*/_jsxDEV(ClockIcon, {\n                        className: \"h-4 w-4 mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 493,\n                        columnNumber: 27\n                      }, this), \"Dur\\xE9e\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                      children: formatDuration(course.estimated_duration)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 496,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center text-sm text-gray-500 dark:text-gray-400\",\n                      children: [/*#__PURE__*/_jsxDEV(BookOpenIcon, {\n                        className: \"h-4 w-4 mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 503,\n                        columnNumber: 27\n                      }, this), \"Le\\xE7ons\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 502,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                      children: course.lessons_count\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 21\n                }, this), course.is_enrolled && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between text-sm mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600 dark:text-gray-400 font-medium\",\n                      children: \"Progression\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-blue-600 dark:text-blue-400 font-bold\",\n                      children: [course.progress_percentage, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 517,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden\",\n                    children: /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-3 rounded-full\",\n                      initial: {\n                        width: 0\n                      },\n                      animate: {\n                        width: `${course.progress_percentage}%`\n                      },\n                      transition: {\n                        duration: 1,\n                        delay: index * 0.1 + 0.5\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 520,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-2\",\n                  children: course.is_enrolled ? /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"flex-1 bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors\",\n                    children: \"Continuer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 23\n                  }, this) : course.is_accessible ? /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEnroll(course.id, course.title),\n                    className: \"flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors\",\n                    children: \"S'inscrire\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                    disabled: true,\n                    className: \"flex-1 bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 font-medium py-2 px-4 rounded-md cursor-not-allowed\",\n                    children: \"Pr\\xE9requis manquants\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 544,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 19\n              }, this)]\n            }, course.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this), totalItems > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-8\",\n            children: /*#__PURE__*/_jsxDEV(Pagination, {\n              currentPage: currentPage,\n              totalPages: totalPages,\n              totalItems: totalItems,\n              itemsPerPage: itemsPerPage,\n              onPageChange: handlePageChange,\n              onItemsPerPageChange: handleItemsPerPageChange,\n              showItemsPerPage: true,\n              showInfo: true,\n              itemsPerPageOptions: coursePaginationOptions\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true);\n};\n_s(CoursesPage, \"NLRjCLvH8JWo2CnCaUKWm9ycxLM=\", false, function () {\n  return [useAuth];\n});\n_c = CoursesPage;\nexport default CoursesPage;\nvar _c;\n$RefreshReg$(_c, \"CoursesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useAuth", "StudentNavbar", "Pagination", "api", "MagnifyingGlassIcon", "FunnelIcon", "BookOpenIcon", "ClockIcon", "AcademicCapIcon", "StarIcon", "CheckCircleIcon", "LockClosedIcon", "ChartBarIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CoursesPage", "_s", "user", "courses", "setCourses", "loading", "setLoading", "filters", "setFilters", "level", "featured", "search", "sortBy", "sortOrder", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "totalItems", "setTotalItems", "coursePaginationOptions", "fetchCourses", "params", "URLSearchParams", "append", "toString", "response", "get", "data", "results", "pagination", "total_count", "count", "total", "error", "console", "id", "title", "description", "level_display", "thumbnail", "estimated_duration", "is_featured", "chapters_count", "lessons_count", "progress_percentage", "is_accessible", "is_enrolled", "enrollment_date", "prerequisites", "created_at", "progress", "handlePageChange", "page", "window", "scrollTo", "top", "behavior", "handleItemsPerPageChange", "newItemsPerPage", "handleEnroll", "courseId", "courseTitle", "map", "course", "Date", "toISOString", "alert", "clearFilters", "formatDuration", "minutes", "hours", "Math", "floor", "mins", "getLevelColor", "totalPages", "ceil", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "div", "initial", "opacity", "y", "animate", "transition", "duration", "delay", "type", "placeholder", "value", "onChange", "e", "prev", "target", "checked", "onClick", "style", "animationDirection", "animationDuration", "length", "filter", "c", "round", "reduce", "acc", "max", "index", "whileHover", "scale", "width", "disabled", "onPageChange", "onItemsPerPageChange", "showItemsPerPage", "showInfo", "itemsPerPageOptions", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/pages/CoursesPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport StudentNavbar from '../components/StudentNavbar';\nimport Pagination from '../components/Pagination';\nimport api from '../services/api';\nimport {\n  MagnifyingGlassIcon,\n  FunnelIcon,\n  BookOpenIcon,\n  ClockIcon,\n  AcademicCapIcon,\n  StarIcon,\n  CheckCircleIcon,\n  PlayIcon,\n  LockClosedIcon,\n  ChartBarIcon\n} from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\n\ninterface Course {\n  id: number;\n  title: string;\n  description: string;\n  level: string;\n  level_display: string;\n  thumbnail: string;\n  estimated_duration: number;\n  is_featured: boolean;\n  chapters_count: number;\n  lessons_count: number;\n  progress_percentage: number;\n  is_accessible: boolean;\n  is_enrolled: boolean;\n  enrollment_date?: string;\n  prerequisites: Array<{\n    id: number;\n    title: string;\n    progress: number;\n  }>;\n  created_at: string;\n}\n\ninterface Filters {\n  level: string;\n  featured: boolean;\n  search: string;\n  sortBy: 'title' | 'level' | 'duration' | 'created_at';\n  sortOrder: 'asc' | 'desc';\n}\n\nconst CoursesPage: React.FC = () => {\n  const { user } = useAuth();\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState<Filters>({\n    level: '',\n    featured: false,\n    search: '',\n    sortBy: 'title',\n    sortOrder: 'asc'\n  });\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(9);\n  const [totalItems, setTotalItems] = useState(0);\n  \n  // Options de pagination pour les cours\n  const coursePaginationOptions = [6, 9, 12, 18];\n\n  useEffect(() => {\n    if (user) {\n      fetchCourses();\n    }\n  }, [user, filters, currentPage, itemsPerPage]);\n\n  const fetchCourses = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n\n      // Filtres\n      if (filters.level) params.append('level', filters.level);\n      if (filters.featured) params.append('featured', 'true');\n      if (filters.search) params.append('search', filters.search);\n\n      // Tri\n      params.append('ordering', filters.sortOrder === 'desc' ? `-${filters.sortBy}` : filters.sortBy);\n\n      // Pagination parameters\n      params.append('page', currentPage.toString());\n      params.append('page_size', itemsPerPage.toString());\n\n      const response = await api.get(`/courses/?${params.toString()}`);\n      if (response.data) {\n        setCourses(response.data.courses || response.data.results || []);\n        // Utiliser les nouvelles données de pagination du backend\n        if (response.data.pagination) {\n          setTotalItems(response.data.pagination.total_count);\n        } else {\n          setTotalItems(response.data.count || response.data.total || 0);\n        }\n      }\n    } catch (error: any) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      // Fallback avec des données de test si l'API n'est pas disponible\n      setCourses([\n        {\n          id: 1,\n          title: 'Algèbre de base',\n          description: 'Apprenez les concepts fondamentaux de l\\'algèbre avec des exercices pratiques et des exemples concrets.',\n          level: 'beginner',\n          level_display: 'Débutant',\n          thumbnail: '📚',\n          estimated_duration: 180,\n          is_featured: true,\n          chapters_count: 4,\n          lessons_count: 12,\n          progress_percentage: 45,\n          is_accessible: true,\n          is_enrolled: true,\n          enrollment_date: '2024-01-15T10:30:00Z',\n          prerequisites: [],\n          created_at: '2024-01-15T10:30:00Z'\n        },\n        {\n          id: 2,\n          title: 'Géométrie euclidienne',\n          description: 'Découvrez les principes de la géométrie euclidienne et ses applications pratiques.',\n          level: 'intermediate',\n          level_display: 'Intermédiaire',\n          thumbnail: '📐',\n          estimated_duration: 240,\n          is_featured: false,\n          chapters_count: 6,\n          lessons_count: 18,\n          progress_percentage: 0,\n          is_accessible: true,\n          is_enrolled: false,\n          prerequisites: [\n            { id: 1, title: 'Algèbre de base', progress: 45 }\n          ],\n          created_at: '2024-01-16T14:20:00Z'\n        },\n        {\n          id: 3,\n          title: 'Calcul différentiel',\n          description: 'Maîtrisez les concepts du calcul différentiel et intégral.',\n          level: 'advanced',\n          level_display: 'Avancé',\n          thumbnail: '∫',\n          estimated_duration: 360,\n          is_featured: true,\n          chapters_count: 8,\n          lessons_count: 24,\n          progress_percentage: 0,\n          is_accessible: false,\n          is_enrolled: false,\n          prerequisites: [\n            { id: 1, title: 'Algèbre de base', progress: 45 }\n          ],\n          created_at: '2024-01-17T09:45:00Z'\n        }\n      ]);\n      setTotalItems(3); // Nombre total de cours de test\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Pagination handlers\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const handleItemsPerPageChange = (newItemsPerPage: number) => {\n    setItemsPerPage(newItemsPerPage);\n    setCurrentPage(1); // Reset to first page when changing items per page\n  };\n\n  const handleEnroll = async (courseId: number, courseTitle: string) => {\n    try {\n      setCourses(courses.map(course => \n        course.id === courseId \n          ? { ...course, is_enrolled: true, enrollment_date: new Date().toISOString() }\n          : course\n      ));\n      alert(`Inscription réussie au cours \"${courseTitle}\" !`);\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      alert('Erreur lors de l\\'inscription');\n    }\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      level: '',\n      featured: false,\n      search: '',\n      sortBy: 'title',\n      sortOrder: 'asc'\n    });\n    setCurrentPage(1); // Reset to first page when clearing filters\n  };\n\n  const formatDuration = (minutes: number) => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n\n  const getLevelColor = (level: string) => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n\n  // Calculate total pages\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\n\n  if (!user) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Cours de Mathématiques</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            Vous devez être connecté pour accéder aux cours.\n          </p>\n          <a\n            href=\"/login\"\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Se connecter\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n    <StudentNavbar />\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* En-tête Hero */}\n        <div className=\"text-center mb-12\">\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n          >\n            <h1 className=\"text-5xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-4\">\n              Cours de Mathématiques\n            </h1>\n            <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n              Découvrez nos cours structurés et interactifs pour maîtriser les mathématiques à votre rythme\n            </p>\n          </motion.div>\n        </div>\n\n        {/* Barre de recherche et filtres */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6 mb-8\"\n        >\n          {/* Barre de recherche */}\n          <div className=\"relative mb-6\">\n            <MagnifyingGlassIcon className=\"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Rechercher un cours...\"\n              value={filters.search}\n              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}\n              className=\"w-full pl-12 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\"\n            />\n          </div>\n\n          {/* Filtres et tri */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 items-end\">\n            {/* Niveau */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                <AcademicCapIcon className=\"inline h-4 w-4 mr-1\" />\n                Niveau\n              </label>\n              <select\n                value={filters.level}\n                onChange={(e) => setFilters(prev => ({ ...prev, level: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"\">Tous les niveaux</option>\n                <option value=\"beginner\">Débutant</option>\n                <option value=\"intermediate\">Intermédiaire</option>\n                <option value=\"advanced\">Avancé</option>\n              </select>\n            </div>\n\n            {/* Tri */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                <FunnelIcon className=\"inline h-4 w-4 mr-1\" />\n                Trier par\n              </label>\n              <select\n                value={filters.sortBy}\n                onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as any }))}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"title\">Titre</option>\n                <option value=\"level\">Niveau</option>\n                <option value=\"duration\">Durée</option>\n                <option value=\"created_at\">Date de création</option>\n              </select>\n            </div>\n\n            {/* Ordre */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Ordre\n              </label>\n              <select\n                value={filters.sortOrder}\n                onChange={(e) => setFilters(prev => ({ ...prev, sortOrder: e.target.value as any }))}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"asc\">Croissant</option>\n                <option value=\"desc\">Décroissant</option>\n              </select>\n            </div>\n\n            {/* Actions */}\n            <div className=\"flex gap-2\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={filters.featured}\n                  onChange={(e) => setFilters(prev => ({ ...prev, featured: e.target.checked }))}\n                  className=\"mr-2 rounded text-blue-600 focus:ring-blue-500\"\n                />\n                <StarIcon className=\"h-4 w-4 text-yellow-500 mr-1\" />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">Vedettes</span>\n              </label>\n              <button\n                onClick={clearFilters}\n                className=\"px-4 py-2 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors\"\n              >\n                Effacer\n              </button>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Loading */}\n        {loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"text-center py-16\"\n          >\n            <div className=\"relative\">\n              <div className=\"animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto\"></div>\n              <div className=\"absolute inset-0 rounded-full h-16 w-16 border-4 border-transparent border-r-indigo-600 mx-auto animate-spin\" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>\n            </div>\n            <p className=\"mt-6 text-lg text-gray-600 dark:text-gray-400\">Chargement des cours...</p>\n          </motion.div>\n        )}\n\n        {/* Statistiques rapides */}\n        {!loading && courses.length > 0 && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8\"\n          >\n            <div className=\"bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-4 text-white\">\n              <div className=\"flex items-center\">\n                <BookOpenIcon className=\"h-8 w-8 mr-3\" />\n                <div>\n                  <p className=\"text-sm opacity-90\">Total des cours</p>\n                  <p className=\"text-2xl font-bold\">{totalItems}</p>\n                </div>\n              </div>\n            </div>\n            <div className=\"bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-4 text-white\">\n              <div className=\"flex items-center\">\n                <CheckCircleIcon className=\"h-8 w-8 mr-3\" />\n                <div>\n                  <p className=\"text-sm opacity-90\">Cours inscrits</p>\n                  <p className=\"text-2xl font-bold\">{courses.filter(c => c.is_enrolled).length}</p>\n                </div>\n              </div>\n            </div>\n            <div className=\"bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-4 text-white\">\n              <div className=\"flex items-center\">\n                <ChartBarIcon className=\"h-8 w-8 mr-3\" />\n                <div>\n                  <p className=\"text-sm opacity-90\">Progression moyenne</p>\n                  <p className=\"text-2xl font-bold\">\n                    {Math.round(courses.filter(c => c.is_enrolled).reduce((acc, c) => acc + c.progress_percentage, 0) / Math.max(courses.filter(c => c.is_enrolled).length, 1))}%\n                  </p>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Cours */}\n        {!loading && (\n          <>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 mb-8\">\n              {courses.map((course, index) => (\n                <motion.div\n                  key={course.id}\n                  className=\"group bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-2xl hover:-translate-y-2 transition-all duration-300\"\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1, duration: 0.5 }}\n                  whileHover={{ scale: 1.02 }}\n                >\n                  {/* Thumbnail avec overlay */}\n                  <div className=\"relative h-48 bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 flex items-center justify-center overflow-hidden\">\n                    <div className=\"absolute inset-0 bg-black bg-opacity-20\"></div>\n                    <span className=\"text-6xl text-white z-10 drop-shadow-lg\">{course.thumbnail}</span>\n\n                    {/* Badge vedette */}\n                    {course.is_featured && (\n                      <div className=\"absolute top-4 right-4 bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold flex items-center\">\n                        <StarIcon className=\"h-3 w-3 mr-1\" />\n                        Vedette\n                      </div>\n                    )}\n\n                    {/* Badge statut */}\n                    <div className=\"absolute top-4 left-4\">\n                      {course.is_enrolled ? (\n                        <div className=\"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center\">\n                          <CheckCircleIcon className=\"h-3 w-3 mr-1\" />\n                          Inscrit\n                        </div>\n                      ) : course.is_accessible ? (\n                        <div className=\"bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-bold\">\n                          Disponible\n                        </div>\n                      ) : (\n                        <div className=\"bg-gray-500 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center\">\n                          <LockClosedIcon className=\"h-3 w-3 mr-1\" />\n                          Verrouillé\n                        </div>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"p-6\">\n                    {/* En-tête du cours */}\n                    <div className=\"mb-4\">\n                      <h3 className=\"text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 transition-colors\">\n                        {course.title}\n                      </h3>\n                      <p className=\"text-gray-600 dark:text-gray-400 text-sm leading-relaxed line-clamp-2\">\n                        {course.description}\n                      </p>\n                    </div>\n\n                    {/* Métadonnées avec icônes */}\n                    <div className=\"space-y-3 mb-6\">\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center text-sm text-gray-500 dark:text-gray-400\">\n                          <AcademicCapIcon className=\"h-4 w-4 mr-2\" />\n                          Niveau\n                        </div>\n                        <span className={`px-3 py-1 rounded-full text-xs font-bold ${getLevelColor(course.level)}`}>\n                          {course.level_display}\n                        </span>\n                      </div>\n\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center text-sm text-gray-500 dark:text-gray-400\">\n                          <ClockIcon className=\"h-4 w-4 mr-2\" />\n                          Durée\n                        </div>\n                        <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {formatDuration(course.estimated_duration)}\n                        </span>\n                      </div>\n\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center text-sm text-gray-500 dark:text-gray-400\">\n                          <BookOpenIcon className=\"h-4 w-4 mr-2\" />\n                          Leçons\n                        </div>\n                        <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {course.lessons_count}\n                        </span>\n                      </div>\n                    </div>\n\n                    {/* Progression avec animation */}\n                    {course.is_enrolled && (\n                      <div className=\"mb-6\">\n                        <div className=\"flex items-center justify-between text-sm mb-2\">\n                          <span className=\"text-gray-600 dark:text-gray-400 font-medium\">Progression</span>\n                          <span className=\"text-blue-600 dark:text-blue-400 font-bold\">{course.progress_percentage}%</span>\n                        </div>\n                        <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden\">\n                          <motion.div\n                            className=\"bg-gradient-to-r from-blue-500 to-indigo-500 h-3 rounded-full\"\n                            initial={{ width: 0 }}\n                            animate={{ width: `${course.progress_percentage}%` }}\n                            transition={{ duration: 1, delay: index * 0.1 + 0.5 }}\n                          />\n                        </div>\n                      </div>\n                    )}\n\n                  {/* Actions */}\n                  <div className=\"flex gap-2\">\n                    {course.is_enrolled ? (\n                      <button className=\"flex-1 bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors\">\n                        Continuer\n                      </button>\n                    ) : course.is_accessible ? (\n                      <button\n                        onClick={() => handleEnroll(course.id, course.title)}\n                        className=\"flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors\"\n                      >\n                        S'inscrire\n                      </button>\n                    ) : (\n                      <button\n                        disabled\n                        className=\"flex-1 bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 font-medium py-2 px-4 rounded-md cursor-not-allowed\"\n                      >\n                        Prérequis manquants\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Pagination */}\n          {totalItems > 0 && (\n            <div className=\"mt-8\">\n              <Pagination\n                currentPage={currentPage}\n                totalPages={totalPages}\n                totalItems={totalItems}\n                itemsPerPage={itemsPerPage}\n                onPageChange={handlePageChange}\n                onItemsPerPageChange={handleItemsPerPageChange}\n                showItemsPerPage={true}\n                showInfo={true}\n                itemsPerPageOptions={coursePaginationOptions}\n              />\n            </div>\n          )}\n        </>\n      )}\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default CoursesPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAyB,eAAe;AACvD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,GAAG,MAAM,iBAAiB;AACjC,SACEC,mBAAmB,EACnBC,UAAU,EACVC,YAAY,EACZC,SAAS,EACTC,eAAe,EACfC,QAAQ,EACRC,eAAe,EAEfC,cAAc,EACdC,YAAY,QACP,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAkCrC,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAK,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAU;IAC9C6B,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAMwC,uBAAuB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAE9CvC,SAAS,CAAC,MAAM;IACd,IAAIqB,IAAI,EAAE;MACRmB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACnB,IAAI,EAAEK,OAAO,EAAEO,WAAW,EAAEE,YAAY,CAAC,CAAC;EAE9C,MAAMK,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMgB,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;;MAEpC;MACA,IAAIhB,OAAO,CAACE,KAAK,EAAEa,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEjB,OAAO,CAACE,KAAK,CAAC;MACxD,IAAIF,OAAO,CAACG,QAAQ,EAAEY,MAAM,CAACE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC;MACvD,IAAIjB,OAAO,CAACI,MAAM,EAAEW,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEjB,OAAO,CAACI,MAAM,CAAC;;MAE3D;MACAW,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEjB,OAAO,CAACM,SAAS,KAAK,MAAM,GAAG,IAAIN,OAAO,CAACK,MAAM,EAAE,GAAGL,OAAO,CAACK,MAAM,CAAC;;MAE/F;MACAU,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEV,WAAW,CAACW,QAAQ,CAAC,CAAC,CAAC;MAC7CH,MAAM,CAACE,MAAM,CAAC,WAAW,EAAER,YAAY,CAACS,QAAQ,CAAC,CAAC,CAAC;MAEnD,MAAMC,QAAQ,GAAG,MAAMxC,GAAG,CAACyC,GAAG,CAAC,aAAaL,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;MAChE,IAAIC,QAAQ,CAACE,IAAI,EAAE;QACjBxB,UAAU,CAACsB,QAAQ,CAACE,IAAI,CAACzB,OAAO,IAAIuB,QAAQ,CAACE,IAAI,CAACC,OAAO,IAAI,EAAE,CAAC;QAChE;QACA,IAAIH,QAAQ,CAACE,IAAI,CAACE,UAAU,EAAE;UAC5BX,aAAa,CAACO,QAAQ,CAACE,IAAI,CAACE,UAAU,CAACC,WAAW,CAAC;QACrD,CAAC,MAAM;UACLZ,aAAa,CAACO,QAAQ,CAACE,IAAI,CAACI,KAAK,IAAIN,QAAQ,CAACE,IAAI,CAACK,KAAK,IAAI,CAAC,CAAC;QAChE;MACF;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE;MACA9B,UAAU,CAAC,CACT;QACEgC,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,iBAAiB;QACxBC,WAAW,EAAE,yGAAyG;QACtH7B,KAAK,EAAE,UAAU;QACjB8B,aAAa,EAAE,UAAU;QACzBC,SAAS,EAAE,IAAI;QACfC,kBAAkB,EAAE,GAAG;QACvBC,WAAW,EAAE,IAAI;QACjBC,cAAc,EAAE,CAAC;QACjBC,aAAa,EAAE,EAAE;QACjBC,mBAAmB,EAAE,EAAE;QACvBC,aAAa,EAAE,IAAI;QACnBC,WAAW,EAAE,IAAI;QACjBC,eAAe,EAAE,sBAAsB;QACvCC,aAAa,EAAE,EAAE;QACjBC,UAAU,EAAE;MACd,CAAC,EACD;QACEd,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,uBAAuB;QAC9BC,WAAW,EAAE,oFAAoF;QACjG7B,KAAK,EAAE,cAAc;QACrB8B,aAAa,EAAE,eAAe;QAC9BC,SAAS,EAAE,IAAI;QACfC,kBAAkB,EAAE,GAAG;QACvBC,WAAW,EAAE,KAAK;QAClBC,cAAc,EAAE,CAAC;QACjBC,aAAa,EAAE,EAAE;QACjBC,mBAAmB,EAAE,CAAC;QACtBC,aAAa,EAAE,IAAI;QACnBC,WAAW,EAAE,KAAK;QAClBE,aAAa,EAAE,CACb;UAAEb,EAAE,EAAE,CAAC;UAAEC,KAAK,EAAE,iBAAiB;UAAEc,QAAQ,EAAE;QAAG,CAAC,CAClD;QACDD,UAAU,EAAE;MACd,CAAC,EACD;QACEd,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,qBAAqB;QAC5BC,WAAW,EAAE,4DAA4D;QACzE7B,KAAK,EAAE,UAAU;QACjB8B,aAAa,EAAE,QAAQ;QACvBC,SAAS,EAAE,GAAG;QACdC,kBAAkB,EAAE,GAAG;QACvBC,WAAW,EAAE,IAAI;QACjBC,cAAc,EAAE,CAAC;QACjBC,aAAa,EAAE,EAAE;QACjBC,mBAAmB,EAAE,CAAC;QACtBC,aAAa,EAAE,KAAK;QACpBC,WAAW,EAAE,KAAK;QAClBE,aAAa,EAAE,CACb;UAAEb,EAAE,EAAE,CAAC;UAAEC,KAAK,EAAE,iBAAiB;UAAEc,QAAQ,EAAE;QAAG,CAAC,CAClD;QACDD,UAAU,EAAE;MACd,CAAC,CACF,CAAC;MACF/B,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8C,gBAAgB,GAAIC,IAAY,IAAK;IACzCtC,cAAc,CAACsC,IAAI,CAAC;IACpBC,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;EAED,MAAMC,wBAAwB,GAAIC,eAAuB,IAAK;IAC5D1C,eAAe,CAAC0C,eAAe,CAAC;IAChC5C,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAM6C,YAAY,GAAG,MAAAA,CAAOC,QAAgB,EAAEC,WAAmB,KAAK;IACpE,IAAI;MACF1D,UAAU,CAACD,OAAO,CAAC4D,GAAG,CAACC,MAAM,IAC3BA,MAAM,CAAC5B,EAAE,KAAKyB,QAAQ,GAClB;QAAE,GAAGG,MAAM;QAAEjB,WAAW,EAAE,IAAI;QAAEC,eAAe,EAAE,IAAIiB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAAE,CAAC,GAC3EF,MACN,CAAC,CAAC;MACFG,KAAK,CAAC,iCAAiCL,WAAW,KAAK,CAAC;IAC1D,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDiC,KAAK,CAAC,+BAA+B,CAAC;IACxC;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB5D,UAAU,CAAC;MACTC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAMsD,cAAc,GAAIC,OAAe,IAAK;IAC1C,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIG,IAAI,GAAG,CAAC,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE,EAAE;IACpD;IACA,OAAO,GAAGA,IAAI,KAAK;EACrB,CAAC;EAED,MAAMC,aAAa,GAAIlE,KAAa,IAAK;IACvC,QAAQA,KAAK;MACX,KAAK,UAAU;QACb,OAAO,mEAAmE;MAC5E,KAAK,cAAc;QACjB,OAAO,uEAAuE;MAChF,KAAK,UAAU;QACb,OAAO,2DAA2D;MACpE;QACE,OAAO,+DAA+D;IAC1E;EACF,CAAC;;EAED;EACA,MAAMmE,UAAU,GAAGJ,IAAI,CAACK,IAAI,CAAC3D,UAAU,GAAGF,YAAY,CAAC;EAEvD,IAAI,CAACd,IAAI,EAAE;IACT,oBACEL,OAAA;MAAKiF,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1ClF,OAAA;QAAKiF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlF,OAAA;UAAIiF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnEtF,OAAA;UAAGiF,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJtF,OAAA;UACEuF,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtF,OAAA,CAAAE,SAAA;IAAAgF,QAAA,gBACAlF,OAAA,CAACb,aAAa;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjBtF,OAAA;MAAKiF,SAAS,EAAC,0HAA0H;MAAAC,QAAA,eACvIlF,OAAA;QAAKiF,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1ClF,OAAA;UAAKiF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChClF,OAAA,CAACf,MAAM,CAACuG,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAZ,QAAA,gBAE9BlF,OAAA;cAAIiF,SAAS,EAAC,oGAAoG;cAAAC,QAAA,EAAC;YAEnH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtF,OAAA;cAAGiF,SAAS,EAAC,4DAA4D;cAAAC,QAAA,EAAC;YAE1E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNtF,OAAA,CAACf,MAAM,CAACuG,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1Cd,SAAS,EAAC,sGAAsG;UAAAC,QAAA,gBAGhHlF,OAAA;YAAKiF,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlF,OAAA,CAACV,mBAAmB;cAAC2F,SAAS,EAAC;YAA0E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5GtF,OAAA;cACEgG,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,wBAAwB;cACpCC,KAAK,EAAExF,OAAO,CAACI,MAAO;cACtBqF,QAAQ,EAAGC,CAAC,IAAKzF,UAAU,CAAC0F,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEvF,MAAM,EAAEsF,CAAC,CAACE,MAAM,CAACJ;cAAM,CAAC,CAAC,CAAE;cAC3EjB,SAAS,EAAC;YAA0P;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNtF,OAAA;YAAKiF,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAE9DlF,OAAA;cAAAkF,QAAA,gBACElF,OAAA;gBAAOiF,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChFlF,OAAA,CAACN,eAAe;kBAACuF,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAErD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtF,OAAA;gBACEkG,KAAK,EAAExF,OAAO,CAACE,KAAM;gBACrBuF,QAAQ,EAAGC,CAAC,IAAKzF,UAAU,CAAC0F,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEzF,KAAK,EAAEwF,CAAC,CAACE,MAAM,CAACJ;gBAAM,CAAC,CAAC,CAAE;gBAC1EjB,SAAS,EAAC,qLAAqL;gBAAAC,QAAA,gBAE/LlF,OAAA;kBAAQkG,KAAK,EAAC,EAAE;kBAAAhB,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CtF,OAAA;kBAAQkG,KAAK,EAAC,UAAU;kBAAAhB,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CtF,OAAA;kBAAQkG,KAAK,EAAC,cAAc;kBAAAhB,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnDtF,OAAA;kBAAQkG,KAAK,EAAC,UAAU;kBAAAhB,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNtF,OAAA;cAAAkF,QAAA,gBACElF,OAAA;gBAAOiF,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,gBAChFlF,OAAA,CAACT,UAAU;kBAAC0F,SAAS,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAEhD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtF,OAAA;gBACEkG,KAAK,EAAExF,OAAO,CAACK,MAAO;gBACtBoF,QAAQ,EAAGC,CAAC,IAAKzF,UAAU,CAAC0F,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEtF,MAAM,EAAEqF,CAAC,CAACE,MAAM,CAACJ;gBAAa,CAAC,CAAC,CAAE;gBAClFjB,SAAS,EAAC,qLAAqL;gBAAAC,QAAA,gBAE/LlF,OAAA;kBAAQkG,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCtF,OAAA;kBAAQkG,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCtF,OAAA;kBAAQkG,KAAK,EAAC,UAAU;kBAAAhB,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCtF,OAAA;kBAAQkG,KAAK,EAAC,YAAY;kBAAAhB,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNtF,OAAA;cAAAkF,QAAA,gBACElF,OAAA;gBAAOiF,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtF,OAAA;gBACEkG,KAAK,EAAExF,OAAO,CAACM,SAAU;gBACzBmF,QAAQ,EAAGC,CAAC,IAAKzF,UAAU,CAAC0F,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAErF,SAAS,EAAEoF,CAAC,CAACE,MAAM,CAACJ;gBAAa,CAAC,CAAC,CAAE;gBACrFjB,SAAS,EAAC,qLAAqL;gBAAAC,QAAA,gBAE/LlF,OAAA;kBAAQkG,KAAK,EAAC,KAAK;kBAAAhB,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtF,OAAA;kBAAQkG,KAAK,EAAC,MAAM;kBAAAhB,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNtF,OAAA;cAAKiF,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlF,OAAA;gBAAOiF,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAClClF,OAAA;kBACEgG,IAAI,EAAC,UAAU;kBACfO,OAAO,EAAE7F,OAAO,CAACG,QAAS;kBAC1BsF,QAAQ,EAAGC,CAAC,IAAKzF,UAAU,CAAC0F,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAExF,QAAQ,EAAEuF,CAAC,CAACE,MAAM,CAACC;kBAAQ,CAAC,CAAC,CAAE;kBAC/EtB,SAAS,EAAC;gBAAgD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACFtF,OAAA,CAACL,QAAQ;kBAACsF,SAAS,EAAC;gBAA8B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrDtF,OAAA;kBAAMiF,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACRtF,OAAA;gBACEwG,OAAO,EAAEjC,YAAa;gBACtBU,SAAS,EAAC,uJAAuJ;gBAAAC,QAAA,EAClK;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZ9E,OAAO,iBACNR,OAAA,CAACf,MAAM,CAACuG,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBT,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BlF,OAAA;YAAKiF,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBlF,OAAA;cAAKiF,SAAS,EAAC;YAAwF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9GtF,OAAA;cAAKiF,SAAS,EAAC,8GAA8G;cAACwB,KAAK,EAAE;gBAAEC,kBAAkB,EAAE,SAAS;gBAAEC,iBAAiB,EAAE;cAAO;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtM,CAAC,eACNtF,OAAA;YAAGiF,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CACb,EAGA,CAAC9E,OAAO,IAAIF,OAAO,CAACsG,MAAM,GAAG,CAAC,iBAC7B5G,OAAA,CAACf,MAAM,CAACuG,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1Cd,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBAEtDlF,OAAA;YAAKiF,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnFlF,OAAA;cAAKiF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClF,OAAA,CAACR,YAAY;gBAACyF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCtF,OAAA;gBAAAkF,QAAA,gBACElF,OAAA;kBAAGiF,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACrDtF,OAAA;kBAAGiF,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAE7D;gBAAU;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtF,OAAA;YAAKiF,SAAS,EAAC,wEAAwE;YAAAC,QAAA,eACrFlF,OAAA;cAAKiF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClF,OAAA,CAACJ,eAAe;gBAACqF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5CtF,OAAA;gBAAAkF,QAAA,gBACElF,OAAA;kBAAGiF,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACpDtF,OAAA;kBAAGiF,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAE5E,OAAO,CAACuG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5D,WAAW,CAAC,CAAC0D;gBAAM;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtF,OAAA;YAAKiF,SAAS,EAAC,0EAA0E;YAAAC,QAAA,eACvFlF,OAAA;cAAKiF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClF,OAAA,CAACF,YAAY;gBAACmF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCtF,OAAA;gBAAAkF,QAAA,gBACElF,OAAA;kBAAGiF,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACzDtF,OAAA;kBAAGiF,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,GAC9BP,IAAI,CAACoC,KAAK,CAACzG,OAAO,CAACuG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5D,WAAW,CAAC,CAAC8D,MAAM,CAAC,CAACC,GAAG,EAAEH,CAAC,KAAKG,GAAG,GAAGH,CAAC,CAAC9D,mBAAmB,EAAE,CAAC,CAAC,GAAG2B,IAAI,CAACuC,GAAG,CAAC5G,OAAO,CAACuG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5D,WAAW,CAAC,CAAC0D,MAAM,EAAE,CAAC,CAAC,CAAC,EAAC,GAC9J;gBAAA;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb,EAGA,CAAC9E,OAAO,iBACPR,OAAA,CAAAE,SAAA;UAAAgF,QAAA,gBACElF,OAAA;YAAKiF,SAAS,EAAC,2DAA2D;YAAAC,QAAA,EACvE5E,OAAO,CAAC4D,GAAG,CAAC,CAACC,MAAM,EAAEgD,KAAK,kBACzBnH,OAAA,CAACf,MAAM,CAACuG,GAAG;cAETP,SAAS,EAAC,qLAAqL;cAC/LQ,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEE,KAAK,EAAEoB,KAAK,GAAG,GAAG;gBAAErB,QAAQ,EAAE;cAAI,CAAE;cAClDsB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAAAnC,QAAA,gBAG5BlF,OAAA;gBAAKiF,SAAS,EAAC,6HAA6H;gBAAAC,QAAA,gBAC1IlF,OAAA;kBAAKiF,SAAS,EAAC;gBAAyC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/DtF,OAAA;kBAAMiF,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAEf,MAAM,CAACxB;gBAAS;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAGlFnB,MAAM,CAACtB,WAAW,iBACjB7C,OAAA;kBAAKiF,SAAS,EAAC,iHAAiH;kBAAAC,QAAA,gBAC9HlF,OAAA,CAACL,QAAQ;oBAACsF,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAEvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN,eAGDtF,OAAA;kBAAKiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACnCf,MAAM,CAACjB,WAAW,gBACjBlD,OAAA;oBAAKiF,SAAS,EAAC,oFAAoF;oBAAAC,QAAA,gBACjGlF,OAAA,CAACJ,eAAe;sBAACqF,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,WAE9C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,GACJnB,MAAM,CAAClB,aAAa,gBACtBjD,OAAA;oBAAKiF,SAAS,EAAC,iEAAiE;oBAAAC,QAAA,EAAC;kBAEjF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,gBAENtF,OAAA;oBAAKiF,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,gBAChGlF,OAAA,CAACH,cAAc;sBAACoF,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,iBAE7C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtF,OAAA;gBAAKiF,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAElBlF,OAAA;kBAAKiF,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBlF,OAAA;oBAAIiF,SAAS,EAAC,kGAAkG;oBAAAC,QAAA,EAC7Gf,MAAM,CAAC3B;kBAAK;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACLtF,OAAA;oBAAGiF,SAAS,EAAC,uEAAuE;oBAAAC,QAAA,EACjFf,MAAM,CAAC1B;kBAAW;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAGNtF,OAAA;kBAAKiF,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BlF,OAAA;oBAAKiF,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDlF,OAAA;sBAAKiF,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,gBACzElF,OAAA,CAACN,eAAe;wBAACuF,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,UAE9C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtF,OAAA;sBAAMiF,SAAS,EAAE,4CAA4CH,aAAa,CAACX,MAAM,CAACvD,KAAK,CAAC,EAAG;sBAAAsE,QAAA,EACxFf,MAAM,CAACzB;oBAAa;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eAENtF,OAAA;oBAAKiF,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDlF,OAAA;sBAAKiF,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,gBACzElF,OAAA,CAACP,SAAS;wBAACwF,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,YAExC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtF,OAAA;sBAAMiF,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,EAChEV,cAAc,CAACL,MAAM,CAACvB,kBAAkB;oBAAC;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eAENtF,OAAA;oBAAKiF,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDlF,OAAA;sBAAKiF,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,gBACzElF,OAAA,CAACR,YAAY;wBAACyF,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,aAE3C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtF,OAAA;sBAAMiF,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,EAChEf,MAAM,CAACpB;oBAAa;sBAAAoC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLnB,MAAM,CAACjB,WAAW,iBACjBlD,OAAA;kBAAKiF,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBlF,OAAA;oBAAKiF,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,gBAC7DlF,OAAA;sBAAMiF,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjFtF,OAAA;sBAAMiF,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,GAAEf,MAAM,CAACnB,mBAAmB,EAAC,GAAC;oBAAA;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F,CAAC,eACNtF,OAAA;oBAAKiF,SAAS,EAAC,sEAAsE;oBAAAC,QAAA,eACnFlF,OAAA,CAACf,MAAM,CAACuG,GAAG;sBACTP,SAAS,EAAC,+DAA+D;sBACzEQ,OAAO,EAAE;wBAAE6B,KAAK,EAAE;sBAAE,CAAE;sBACtB1B,OAAO,EAAE;wBAAE0B,KAAK,EAAE,GAAGnD,MAAM,CAACnB,mBAAmB;sBAAI,CAAE;sBACrD6C,UAAU,EAAE;wBAAEC,QAAQ,EAAE,CAAC;wBAAEC,KAAK,EAAEoB,KAAK,GAAG,GAAG,GAAG;sBAAI;oBAAE;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAGHtF,OAAA;kBAAKiF,SAAS,EAAC,YAAY;kBAAAC,QAAA,EACxBf,MAAM,CAACjB,WAAW,gBACjBlD,OAAA;oBAAQiF,SAAS,EAAC,0GAA0G;oBAAAC,QAAA,EAAC;kBAE7H;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,GACPnB,MAAM,CAAClB,aAAa,gBACtBjD,OAAA;oBACEwG,OAAO,EAAEA,CAAA,KAAMzC,YAAY,CAACI,MAAM,CAAC5B,EAAE,EAAE4B,MAAM,CAAC3B,KAAK,CAAE;oBACrDyC,SAAS,EAAC,sGAAsG;oBAAAC,QAAA,EACjH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,gBAETtF,OAAA;oBACEuH,QAAQ;oBACRtC,SAAS,EAAC,0HAA0H;oBAAAC,QAAA,EACrI;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBACT;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA5HCnB,MAAM,CAAC5B,EAAE;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6HN,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAGLjE,UAAU,GAAG,CAAC,iBACbrB,OAAA;YAAKiF,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBlF,OAAA,CAACZ,UAAU;cACT6B,WAAW,EAAEA,WAAY;cACzB8D,UAAU,EAAEA,UAAW;cACvB1D,UAAU,EAAEA,UAAW;cACvBF,YAAY,EAAEA,YAAa;cAC3BqG,YAAY,EAAEjE,gBAAiB;cAC/BkE,oBAAoB,EAAE5D,wBAAyB;cAC/C6D,gBAAgB,EAAE,IAAK;cACvBC,QAAQ,EAAE,IAAK;cACfC,mBAAmB,EAAErG;YAAwB;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA,eACD,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAClF,EAAA,CA/gBID,WAAqB;EAAA,QACRjB,OAAO;AAAA;AAAA2I,EAAA,GADpB1H,WAAqB;AAihB3B,eAAeA,WAAW;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}