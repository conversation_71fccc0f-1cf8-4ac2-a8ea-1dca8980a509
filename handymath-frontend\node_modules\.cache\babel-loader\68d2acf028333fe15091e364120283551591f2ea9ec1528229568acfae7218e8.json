{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createString } from '../../factoriesAny.js';\nexport var stringDependencies = {\n  typedDependencies,\n  createString\n};", "map": {"version": 3, "names": ["typedDependencies", "createString", "stringDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesString.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createString } from '../../factoriesAny.js';\nexport var stringDependencies = {\n  typedDependencies,\n  createString\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAO,IAAIC,kBAAkB,GAAG;EAC9BF,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}