{"ast": null, "code": "export var compositionDocs = {\n  name: 'composition',\n  category: 'Combinatorics',\n  syntax: ['composition(n, k)'],\n  description: 'The composition counts of n into k parts. composition only takes integer arguments. The following condition must be enforced: k <= n.',\n  examples: ['composition(5, 3)'],\n  seealso: ['combinations']\n};", "map": {"version": 3, "names": ["compositionDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/combinatorics/composition.js"], "sourcesContent": ["export var compositionDocs = {\n  name: 'composition',\n  category: 'Combinatorics',\n  syntax: ['composition(n, k)'],\n  description: 'The composition counts of n into k parts. composition only takes integer arguments. The following condition must be enforced: k <= n.',\n  examples: ['composition(5, 3)'],\n  seealso: ['combinations']\n};"], "mappings": "AAAA,OAAO,IAAIA,eAAe,GAAG;EAC3BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,CAAC,mBAAmB,CAAC;EAC7BC,WAAW,EAAE,uIAAuI;EACpJC,QAAQ,EAAE,CAAC,mBAAmB,CAAC;EAC/BC,OAAO,EAAE,CAAC,cAAc;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}