{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createNull } from '../../factoriesAny.js';\nexport var nullDependencies = {\n  createNull\n};", "map": {"version": 3, "names": ["createNull", "nullDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesNull.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createNull } from '../../factoriesAny.js';\nexport var nullDependencies = {\n  createNull\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,UAAU,QAAQ,uBAAuB;AAClD,OAAO,IAAIC,gBAAgB,GAAG;EAC5BD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}