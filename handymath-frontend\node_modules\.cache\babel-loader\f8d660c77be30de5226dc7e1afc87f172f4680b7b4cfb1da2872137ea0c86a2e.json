{"ast": null, "code": "/* eslint-disable no-loss-of-precision */\n\nimport { isInteger } from '../../utils/number.js';\nimport { product } from '../../utils/product.js';\nexport function gammaNumber(n) {\n  var x;\n  if (isInteger(n)) {\n    if (n <= 0) {\n      return isFinite(n) ? Infinity : NaN;\n    }\n    if (n > 171) {\n      return Infinity; // Will overflow\n    }\n    return product(1, n - 1);\n  }\n  if (n < 0.5) {\n    return Math.PI / (Math.sin(Math.PI * n) * gammaNumber(1 - n));\n  }\n  if (n >= 171.35) {\n    return Infinity; // will overflow\n  }\n  if (n > 85.0) {\n    // Extended Stirling Approx\n    var twoN = n * n;\n    var threeN = twoN * n;\n    var fourN = threeN * n;\n    var fiveN = fourN * n;\n    return Math.sqrt(2 * Math.PI / n) * Math.pow(n / Math.E, n) * (1 + 1 / (12 * n) + 1 / (288 * twoN) - 139 / (51840 * threeN) - 571 / (2488320 * fourN) + 163879 / (209018880 * fiveN) + 5246819 / (75246796800 * fiveN * n));\n  }\n  --n;\n  x = gammaP[0];\n  for (var i = 1; i < gammaP.length; ++i) {\n    x += gammaP[i] / (n + i);\n  }\n  var t = n + gammaG + 0.5;\n  return Math.sqrt(2 * Math.PI) * Math.pow(t, n + 0.5) * Math.exp(-t) * x;\n}\ngammaNumber.signature = 'number';\n\n// TODO: comment on the variables g and p\n\nexport var gammaG = 4.7421875;\nexport var gammaP = [0.99999999999999709182, 57.156235665862923517, -59.597960355475491248, 14.136097974741747174, -0.49191381609762019978, 0.33994649984811888699e-4, 0.46523628927048575665e-4, -0.98374475304879564677e-4, 0.15808870322491248884e-3, -0.21026444172410488319e-3, 0.21743961811521264320e-3, -0.16431810653676389022e-3, 0.84418223983852743293e-4, -0.26190838401581408670e-4, 0.36899182659531622704e-5];\n\n// lgamma implementation ref: https://mrob.com/pub/ries/lanczos-gamma.html#code\n\n// log(2 * pi) / 2\nexport var lnSqrt2PI = 0.91893853320467274178;\nexport var lgammaG = 5; // Lanczos parameter \"g\"\nexport var lgammaN = 7; // Range of coefficients \"n\"\n\nexport var lgammaSeries = [1.000000000190015, 76.18009172947146, -86.50532032941677, 24.01409824083091, -1.231739572450155, 0.1208650973866179e-2, -0.5395239384953e-5];\nexport function lgammaNumber(n) {\n  if (n < 0) return NaN;\n  if (n === 0) return Infinity;\n  if (!isFinite(n)) return n;\n  if (n < 0.5) {\n    // Use Euler's reflection formula:\n    // gamma(z) = PI / (sin(PI * z) * gamma(1 - z))\n    return Math.log(Math.PI / Math.sin(Math.PI * n)) - lgammaNumber(1 - n);\n  }\n\n  // Compute the logarithm of the Gamma function using the Lanczos method\n\n  n = n - 1;\n  var base = n + lgammaG + 0.5; // Base of the Lanczos exponential\n  var sum = lgammaSeries[0];\n\n  // We start with the terms that have the smallest coefficients and largest denominator\n  for (var i = lgammaN - 1; i >= 1; i--) {\n    sum += lgammaSeries[i] / (n + i);\n  }\n  return lnSqrt2PI + (n + 0.5) * Math.log(base) - base + Math.log(sum);\n}\nlgammaNumber.signature = 'number';", "map": {"version": 3, "names": ["isInteger", "product", "gammaNumber", "n", "x", "isFinite", "Infinity", "NaN", "Math", "PI", "sin", "twoN", "threeN", "fourN", "fiveN", "sqrt", "pow", "E", "gammaP", "i", "length", "t", "gammaG", "exp", "signature", "lnSqrt2PI", "lgammaG", "lgammaN", "lgammaSeries", "lgammaNumber", "log", "base", "sum"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/plain/number/probability.js"], "sourcesContent": ["/* eslint-disable no-loss-of-precision */\n\nimport { isInteger } from '../../utils/number.js';\nimport { product } from '../../utils/product.js';\nexport function gammaNumber(n) {\n  var x;\n  if (isInteger(n)) {\n    if (n <= 0) {\n      return isFinite(n) ? Infinity : NaN;\n    }\n    if (n > 171) {\n      return Infinity; // Will overflow\n    }\n    return product(1, n - 1);\n  }\n  if (n < 0.5) {\n    return Math.PI / (Math.sin(Math.PI * n) * gammaNumber(1 - n));\n  }\n  if (n >= 171.35) {\n    return Infinity; // will overflow\n  }\n  if (n > 85.0) {\n    // Extended Stirling Approx\n    var twoN = n * n;\n    var threeN = twoN * n;\n    var fourN = threeN * n;\n    var fiveN = fourN * n;\n    return Math.sqrt(2 * Math.PI / n) * Math.pow(n / Math.E, n) * (1 + 1 / (12 * n) + 1 / (288 * twoN) - 139 / (51840 * threeN) - 571 / (2488320 * fourN) + 163879 / (209018880 * fiveN) + 5246819 / (75246796800 * fiveN * n));\n  }\n  --n;\n  x = gammaP[0];\n  for (var i = 1; i < gammaP.length; ++i) {\n    x += gammaP[i] / (n + i);\n  }\n  var t = n + gammaG + 0.5;\n  return Math.sqrt(2 * Math.PI) * Math.pow(t, n + 0.5) * Math.exp(-t) * x;\n}\ngammaNumber.signature = 'number';\n\n// TODO: comment on the variables g and p\n\nexport var gammaG = 4.7421875;\nexport var gammaP = [0.99999999999999709182, 57.156235665862923517, -59.597960355475491248, 14.136097974741747174, -0.49191381609762019978, 0.33994649984811888699e-4, 0.46523628927048575665e-4, -0.98374475304879564677e-4, 0.15808870322491248884e-3, -0.21026444172410488319e-3, 0.21743961811521264320e-3, -0.16431810653676389022e-3, 0.84418223983852743293e-4, -0.26190838401581408670e-4, 0.36899182659531622704e-5];\n\n// lgamma implementation ref: https://mrob.com/pub/ries/lanczos-gamma.html#code\n\n// log(2 * pi) / 2\nexport var lnSqrt2PI = 0.91893853320467274178;\nexport var lgammaG = 5; // Lanczos parameter \"g\"\nexport var lgammaN = 7; // Range of coefficients \"n\"\n\nexport var lgammaSeries = [1.000000000190015, 76.18009172947146, -86.50532032941677, 24.01409824083091, -1.231739572450155, 0.1208650973866179e-2, -0.5395239384953e-5];\nexport function lgammaNumber(n) {\n  if (n < 0) return NaN;\n  if (n === 0) return Infinity;\n  if (!isFinite(n)) return n;\n  if (n < 0.5) {\n    // Use Euler's reflection formula:\n    // gamma(z) = PI / (sin(PI * z) * gamma(1 - z))\n    return Math.log(Math.PI / Math.sin(Math.PI * n)) - lgammaNumber(1 - n);\n  }\n\n  // Compute the logarithm of the Gamma function using the Lanczos method\n\n  n = n - 1;\n  var base = n + lgammaG + 0.5; // Base of the Lanczos exponential\n  var sum = lgammaSeries[0];\n\n  // We start with the terms that have the smallest coefficients and largest denominator\n  for (var i = lgammaN - 1; i >= 1; i--) {\n    sum += lgammaSeries[i] / (n + i);\n  }\n  return lnSqrt2PI + (n + 0.5) * Math.log(base) - base + Math.log(sum);\n}\nlgammaNumber.signature = 'number';"], "mappings": "AAAA;;AAEA,SAASA,SAAS,QAAQ,uBAAuB;AACjD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,SAASC,WAAWA,CAACC,CAAC,EAAE;EAC7B,IAAIC,CAAC;EACL,IAAIJ,SAAS,CAACG,CAAC,CAAC,EAAE;IAChB,IAAIA,CAAC,IAAI,CAAC,EAAE;MACV,OAAOE,QAAQ,CAACF,CAAC,CAAC,GAAGG,QAAQ,GAAGC,GAAG;IACrC;IACA,IAAIJ,CAAC,GAAG,GAAG,EAAE;MACX,OAAOG,QAAQ,CAAC,CAAC;IACnB;IACA,OAAOL,OAAO,CAAC,CAAC,EAAEE,CAAC,GAAG,CAAC,CAAC;EAC1B;EACA,IAAIA,CAAC,GAAG,GAAG,EAAE;IACX,OAAOK,IAAI,CAACC,EAAE,IAAID,IAAI,CAACE,GAAG,CAACF,IAAI,CAACC,EAAE,GAAGN,CAAC,CAAC,GAAGD,WAAW,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC;EAC/D;EACA,IAAIA,CAAC,IAAI,MAAM,EAAE;IACf,OAAOG,QAAQ,CAAC,CAAC;EACnB;EACA,IAAIH,CAAC,GAAG,IAAI,EAAE;IACZ;IACA,IAAIQ,IAAI,GAAGR,CAAC,GAAGA,CAAC;IAChB,IAAIS,MAAM,GAAGD,IAAI,GAAGR,CAAC;IACrB,IAAIU,KAAK,GAAGD,MAAM,GAAGT,CAAC;IACtB,IAAIW,KAAK,GAAGD,KAAK,GAAGV,CAAC;IACrB,OAAOK,IAAI,CAACO,IAAI,CAAC,CAAC,GAAGP,IAAI,CAACC,EAAE,GAAGN,CAAC,CAAC,GAAGK,IAAI,CAACQ,GAAG,CAACb,CAAC,GAAGK,IAAI,CAACS,CAAC,EAAEd,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAGA,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,GAAGQ,IAAI,CAAC,GAAG,GAAG,IAAI,KAAK,GAAGC,MAAM,CAAC,GAAG,GAAG,IAAI,OAAO,GAAGC,KAAK,CAAC,GAAG,MAAM,IAAI,SAAS,GAAGC,KAAK,CAAC,GAAG,OAAO,IAAI,WAAW,GAAGA,KAAK,GAAGX,CAAC,CAAC,CAAC;EAC7N;EACA,EAAEA,CAAC;EACHC,CAAC,GAAGc,MAAM,CAAC,CAAC,CAAC;EACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACE,MAAM,EAAE,EAAED,CAAC,EAAE;IACtCf,CAAC,IAAIc,MAAM,CAACC,CAAC,CAAC,IAAIhB,CAAC,GAAGgB,CAAC,CAAC;EAC1B;EACA,IAAIE,CAAC,GAAGlB,CAAC,GAAGmB,MAAM,GAAG,GAAG;EACxB,OAAOd,IAAI,CAACO,IAAI,CAAC,CAAC,GAAGP,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACQ,GAAG,CAACK,CAAC,EAAElB,CAAC,GAAG,GAAG,CAAC,GAAGK,IAAI,CAACe,GAAG,CAAC,CAACF,CAAC,CAAC,GAAGjB,CAAC;AACzE;AACAF,WAAW,CAACsB,SAAS,GAAG,QAAQ;;AAEhC;;AAEA,OAAO,IAAIF,MAAM,GAAG,SAAS;AAC7B,OAAO,IAAIJ,MAAM,GAAG,CAAC,sBAAsB,EAAE,qBAAqB,EAAE,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,CAAC,sBAAsB,EAAE,yBAAyB,EAAE,yBAAyB,EAAE,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,CAAC,yBAAyB,EAAE,yBAAyB,EAAE,CAAC,yBAAyB,EAAE,yBAAyB,CAAC;;AAE7Z;;AAEA;AACA,OAAO,IAAIO,SAAS,GAAG,sBAAsB;AAC7C,OAAO,IAAIC,OAAO,GAAG,CAAC,CAAC,CAAC;AACxB,OAAO,IAAIC,OAAO,GAAG,CAAC,CAAC,CAAC;;AAExB,OAAO,IAAIC,YAAY,GAAG,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,CAAC,iBAAiB,EAAE,qBAAqB,EAAE,CAAC,kBAAkB,CAAC;AACvK,OAAO,SAASC,YAAYA,CAAC1B,CAAC,EAAE;EAC9B,IAAIA,CAAC,GAAG,CAAC,EAAE,OAAOI,GAAG;EACrB,IAAIJ,CAAC,KAAK,CAAC,EAAE,OAAOG,QAAQ;EAC5B,IAAI,CAACD,QAAQ,CAACF,CAAC,CAAC,EAAE,OAAOA,CAAC;EAC1B,IAAIA,CAAC,GAAG,GAAG,EAAE;IACX;IACA;IACA,OAAOK,IAAI,CAACsB,GAAG,CAACtB,IAAI,CAACC,EAAE,GAAGD,IAAI,CAACE,GAAG,CAACF,IAAI,CAACC,EAAE,GAAGN,CAAC,CAAC,CAAC,GAAG0B,YAAY,CAAC,CAAC,GAAG1B,CAAC,CAAC;EACxE;;EAEA;;EAEAA,CAAC,GAAGA,CAAC,GAAG,CAAC;EACT,IAAI4B,IAAI,GAAG5B,CAAC,GAAGuB,OAAO,GAAG,GAAG,CAAC,CAAC;EAC9B,IAAIM,GAAG,GAAGJ,YAAY,CAAC,CAAC,CAAC;;EAEzB;EACA,KAAK,IAAIT,CAAC,GAAGQ,OAAO,GAAG,CAAC,EAAER,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACrCa,GAAG,IAAIJ,YAAY,CAACT,CAAC,CAAC,IAAIhB,CAAC,GAAGgB,CAAC,CAAC;EAClC;EACA,OAAOM,SAAS,GAAG,CAACtB,CAAC,GAAG,GAAG,IAAIK,IAAI,CAACsB,GAAG,CAACC,IAAI,CAAC,GAAGA,IAAI,GAAGvB,IAAI,CAACsB,GAAG,CAACE,GAAG,CAAC;AACtE;AACAH,YAAY,CAACL,SAAS,GAAG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}