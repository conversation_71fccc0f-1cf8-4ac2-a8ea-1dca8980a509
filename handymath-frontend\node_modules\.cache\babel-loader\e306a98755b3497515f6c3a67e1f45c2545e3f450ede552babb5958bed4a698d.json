{"ast": null, "code": "export var printDocs = {\n  name: 'print',\n  category: 'Utils',\n  syntax: ['print(template, values)', 'print(template, values, precision)'],\n  description: 'Interpolate values into a string template.',\n  examples: ['print(\"<PERSON> is $age years old\", {age: 5})', 'print(\"The value of pi is $pi\", {pi: pi}, 3)', 'print(\"Hello, $user.name!\", {user: {name: \"<PERSON>\"}})', 'print(\"Values: $1, $2, $3\", [6, 9, 4])'],\n  seealso: ['format']\n};", "map": {"version": 3, "names": ["printDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/print.js"], "sourcesContent": ["export var printDocs = {\n  name: 'print',\n  category: 'Utils',\n  syntax: ['print(template, values)', 'print(template, values, precision)'],\n  description: 'Interpolate values into a string template.',\n  examples: ['print(\"<PERSON> is $age years old\", {age: 5})', 'print(\"The value of pi is $pi\", {pi: pi}, 3)', 'print(\"Hello, $user.name!\", {user: {name: \"<PERSON>\"}})', 'print(\"Values: $1, $2, $3\", [6, 9, 4])'],\n  seealso: ['format']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,yBAAyB,EAAE,oCAAoC,CAAC;EACzEC,WAAW,EAAE,4CAA4C;EACzDC,QAAQ,EAAE,CAAC,2CAA2C,EAAE,8CAA8C,EAAE,qDAAqD,EAAE,wCAAwC,CAAC;EACxMC,OAAO,EAAE,CAAC,QAAQ;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}