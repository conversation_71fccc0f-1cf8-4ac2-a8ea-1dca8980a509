{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * Keeps entries in the matrix when the callback function returns true, removes the entry otherwise\n *\n * @param {Matrix}   a              The sparse matrix\n * @param {function} callback       The callback function, function will be invoked with the following args:\n *                                    - The entry row\n *                                    - The entry column\n *                                    - The entry value\n *                                    - The state parameter\n * @param {any}      other          The state\n *\n * @return                          The number of nonzero elements in the matrix\n */\nexport function csFkeep(a, callback, other) {\n  // a arrays\n  var avalues = a._values;\n  var aindex = a._index;\n  var aptr = a._ptr;\n  var asize = a._size;\n  // columns\n  var n = asize[1];\n  // nonzero items\n  var nz = 0;\n  // loop columns\n  for (var j = 0; j < n; j++) {\n    // get current location of col j\n    var p = aptr[j];\n    // record new location of col j\n    aptr[j] = nz;\n    for (; p < aptr[j + 1]; p++) {\n      // check we need to keep this item\n      if (callback(aindex[p], j, avalues ? avalues[p] : 1, other)) {\n        // keep A(i,j)\n        aindex[nz] = aindex[p];\n        // check we need to process values (pattern only)\n        if (avalues) {\n          avalues[nz] = avalues[p];\n        }\n        // increment nonzero items\n        nz++;\n      }\n    }\n  }\n  // finalize A\n  aptr[n] = nz;\n  // trim arrays\n  aindex.splice(nz, aindex.length - nz);\n  // check we need to process values (pattern only)\n  if (avalues) {\n    avalues.splice(nz, avalues.length - nz);\n  }\n  // return number of nonzero items\n  return nz;\n}", "map": {"version": 3, "names": ["csFkeep", "a", "callback", "other", "avalues", "_values", "aindex", "_index", "aptr", "_ptr", "asize", "_size", "n", "nz", "j", "p", "splice", "length"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csFkeep.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * Keeps entries in the matrix when the callback function returns true, removes the entry otherwise\n *\n * @param {Matrix}   a              The sparse matrix\n * @param {function} callback       The callback function, function will be invoked with the following args:\n *                                    - The entry row\n *                                    - The entry column\n *                                    - The entry value\n *                                    - The state parameter\n * @param {any}      other          The state\n *\n * @return                          The number of nonzero elements in the matrix\n */\nexport function csFkeep(a, callback, other) {\n  // a arrays\n  var avalues = a._values;\n  var aindex = a._index;\n  var aptr = a._ptr;\n  var asize = a._size;\n  // columns\n  var n = asize[1];\n  // nonzero items\n  var nz = 0;\n  // loop columns\n  for (var j = 0; j < n; j++) {\n    // get current location of col j\n    var p = aptr[j];\n    // record new location of col j\n    aptr[j] = nz;\n    for (; p < aptr[j + 1]; p++) {\n      // check we need to keep this item\n      if (callback(aindex[p], j, avalues ? avalues[p] : 1, other)) {\n        // keep A(i,j)\n        aindex[nz] = aindex[p];\n        // check we need to process values (pattern only)\n        if (avalues) {\n          avalues[nz] = avalues[p];\n        }\n        // increment nonzero items\n        nz++;\n      }\n    }\n  }\n  // finalize A\n  aptr[n] = nz;\n  // trim arrays\n  aindex.splice(nz, aindex.length - nz);\n  // check we need to process values (pattern only)\n  if (avalues) {\n    avalues.splice(nz, avalues.length - nz);\n  }\n  // return number of nonzero items\n  return nz;\n}"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAACC,CAAC,EAAEC,QAAQ,EAAEC,KAAK,EAAE;EAC1C;EACA,IAAIC,OAAO,GAAGH,CAAC,CAACI,OAAO;EACvB,IAAIC,MAAM,GAAGL,CAAC,CAACM,MAAM;EACrB,IAAIC,IAAI,GAAGP,CAAC,CAACQ,IAAI;EACjB,IAAIC,KAAK,GAAGT,CAAC,CAACU,KAAK;EACnB;EACA,IAAIC,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC;EAChB;EACA,IAAIG,EAAE,GAAG,CAAC;EACV;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAEE,CAAC,EAAE,EAAE;IAC1B;IACA,IAAIC,CAAC,GAAGP,IAAI,CAACM,CAAC,CAAC;IACf;IACAN,IAAI,CAACM,CAAC,CAAC,GAAGD,EAAE;IACZ,OAAOE,CAAC,GAAGP,IAAI,CAACM,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,EAAE,EAAE;MAC3B;MACA,IAAIb,QAAQ,CAACI,MAAM,CAACS,CAAC,CAAC,EAAED,CAAC,EAAEV,OAAO,GAAGA,OAAO,CAACW,CAAC,CAAC,GAAG,CAAC,EAAEZ,KAAK,CAAC,EAAE;QAC3D;QACAG,MAAM,CAACO,EAAE,CAAC,GAAGP,MAAM,CAACS,CAAC,CAAC;QACtB;QACA,IAAIX,OAAO,EAAE;UACXA,OAAO,CAACS,EAAE,CAAC,GAAGT,OAAO,CAACW,CAAC,CAAC;QAC1B;QACA;QACAF,EAAE,EAAE;MACN;IACF;EACF;EACA;EACAL,IAAI,CAACI,CAAC,CAAC,GAAGC,EAAE;EACZ;EACAP,MAAM,CAACU,MAAM,CAACH,EAAE,EAAEP,MAAM,CAACW,MAAM,GAAGJ,EAAE,CAAC;EACrC;EACA,IAAIT,OAAO,EAAE;IACXA,OAAO,CAACY,MAAM,CAACH,EAAE,EAAET,OAAO,CAACa,MAAM,GAAGJ,EAAE,CAAC;EACzC;EACA;EACA,OAAOA,EAAE;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}