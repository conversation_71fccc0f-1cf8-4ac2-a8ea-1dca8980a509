{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { IndexDependencies } from './dependenciesIndexClass.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { rangeDependencies } from './dependenciesRange.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createRow } from '../../factoriesAny.js';\nexport var rowDependencies = {\n  IndexDependencies,\n  matrixDependencies,\n  rangeDependencies,\n  typedDependencies,\n  createRow\n};", "map": {"version": 3, "names": ["IndexDependencies", "matrixDependencies", "rangeDependencies", "typedDependencies", "createRow", "rowDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesRow.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { IndexDependencies } from './dependenciesIndexClass.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { rangeDependencies } from './dependenciesRange.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createRow } from '../../factoriesAny.js';\nexport var rowDependencies = {\n  IndexDependencies,\n  matrixDependencies,\n  rangeDependencies,\n  typedDependencies,\n  createRow\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,uCAAuC;AACzE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAO,IAAIC,eAAe,GAAG;EAC3BL,iBAAiB;EACjBC,kBAAkB;EAClBC,iBAAiB;EACjBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}