{"ast": null, "code": "export var cothDocs = {\n  name: 'coth',\n  category: 'Trigonometry',\n  syntax: ['coth(x)'],\n  description: 'Compute the hyperbolic cotangent of x in radians.',\n  examples: ['coth(2)', '1 / tanh(2)'],\n  seealso: ['sech', 'csch', 'tanh']\n};", "map": {"version": 3, "names": ["cothDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/coth.js"], "sourcesContent": ["export var cothDocs = {\n  name: 'coth',\n  category: 'Trigonometry',\n  syntax: ['coth(x)'],\n  description: 'Compute the hyperbolic cotangent of x in radians.',\n  examples: ['coth(2)', '1 / tanh(2)'],\n  seealso: ['sech', 'csch', 'tanh']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,mDAAmD;EAChEC,QAAQ,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;EACpCC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}