{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { isNaNDependencies } from './dependenciesIsNaN.generated.js';\nimport { largerDependencies } from './dependenciesLarger.generated.js';\nimport { numericDependencies } from './dependenciesNumeric.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMaxTransform } from '../../factoriesAny.js';\nexport var maxTransformDependencies = {\n  isNaNDependencies,\n  largerDependencies,\n  numericDependencies,\n  typedDependencies,\n  createMaxTransform\n};", "map": {"version": 3, "names": ["isNaNDependencies", "largerDependencies", "numericDependencies", "typedDependencies", "createMaxTransform", "maxTransformDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMaxTransform.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { isNaNDependencies } from './dependenciesIsNaN.generated.js';\nimport { largerDependencies } from './dependenciesLarger.generated.js';\nimport { numericDependencies } from './dependenciesNumeric.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMaxTransform } from '../../factoriesAny.js';\nexport var maxTransformDependencies = {\n  isNaNDependencies,\n  largerDependencies,\n  numericDependencies,\n  typedDependencies,\n  createMaxTransform\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,OAAO,IAAIC,wBAAwB,GAAG;EACpCL,iBAAiB;EACjBC,kBAAkB;EAClBC,mBAAmB;EACnBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}