{"ast": null, "code": "export var eigsDocs = {\n  name: 'eigs',\n  category: 'Matrix',\n  syntax: ['eigs(x)'],\n  description: 'Calculate the eigenvalues and optionally eigenvectors of a square matrix',\n  examples: ['eigs([[5, 2.3], [2.3, 1]])', 'eigs([[1, 2, 3], [4, 5, 6], [7, 8, 9]], { precision: 1e-6, eigenvectors: false })'],\n  seealso: ['inv']\n};", "map": {"version": 3, "names": ["eigsDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/eigs.js"], "sourcesContent": ["export var eigsDocs = {\n  name: 'eigs',\n  category: 'Matrix',\n  syntax: ['eigs(x)'],\n  description: 'Calculate the eigenvalues and optionally eigenvectors of a square matrix',\n  examples: ['eigs([[5, 2.3], [2.3, 1]])', 'eigs([[1, 2, 3], [4, 5, 6], [7, 8, 9]], { precision: 1e-6, eigenvectors: false })'],\n  seealso: ['inv']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,0EAA0E;EACvFC,QAAQ,EAAE,CAAC,4BAA4B,EAAE,mFAAmF,CAAC;EAC7HC,OAAO,EAAE,CAAC,KAAK;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}