{"ast": null, "code": "export var tanhDocs = {\n  name: 'tanh',\n  category: 'Trigonometry',\n  syntax: ['tanh(x)'],\n  description: 'Compute the hyperbolic tangent of x in radians.',\n  examples: ['tanh(0.5)', 'sinh(0.5) / cosh(0.5)'],\n  seealso: ['sinh', 'cosh']\n};", "map": {"version": 3, "names": ["tanhDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/tanh.js"], "sourcesContent": ["export var tanhDocs = {\n  name: 'tanh',\n  category: 'Trigonometry',\n  syntax: ['tanh(x)'],\n  description: 'Compute the hyperbolic tangent of x in radians.',\n  examples: ['tanh(0.5)', 'sinh(0.5) / cosh(0.5)'],\n  seealso: ['sinh', 'cosh']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,iDAAiD;EAC9DC,QAAQ,EAAE,CAAC,WAAW,EAAE,uBAAuB,CAAC;EAChDC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}