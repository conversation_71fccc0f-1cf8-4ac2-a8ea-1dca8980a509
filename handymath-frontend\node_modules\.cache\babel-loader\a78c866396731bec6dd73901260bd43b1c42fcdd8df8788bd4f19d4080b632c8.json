{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport { isInteger } from './number.js';\nimport { isNumber, isBigNumber, isArray, isString } from './is.js';\nimport { format } from './string.js';\nimport { DimensionError } from '../error/DimensionError.js';\nimport { IndexError } from '../error/IndexError.js';\nimport { deepStrictEqual } from './object.js';\n\n/**\n * Calculate the size of a multi dimensional array.\n * This function checks the size of the first entry, it does not validate\n * whether all dimensions match. (use function `validate` for that)\n * @param {Array} x\n * @return {number[]} size\n */\nexport function arraySize(x) {\n  var s = [];\n  while (Array.isArray(x)) {\n    s.push(x.length);\n    x = x[0];\n  }\n  return s;\n}\n\n/**\n * Recursively validate whether each element in a multi dimensional array\n * has a size corresponding to the provided size array.\n * @param {Array} array    Array to be validated\n * @param {number[]} size  Array with the size of each dimension\n * @param {number} dim     Current dimension\n * @throws DimensionError\n * @private\n */\nfunction _validate(array, size, dim) {\n  var i;\n  var len = array.length;\n  if (len !== size[dim]) {\n    throw new DimensionError(len, size[dim]);\n  }\n  if (dim < size.length - 1) {\n    // recursively validate each child array\n    var dimNext = dim + 1;\n    for (i = 0; i < len; i++) {\n      var child = array[i];\n      if (!Array.isArray(child)) {\n        throw new DimensionError(size.length - 1, size.length, '<');\n      }\n      _validate(array[i], size, dimNext);\n    }\n  } else {\n    // last dimension. none of the children may be an array\n    for (i = 0; i < len; i++) {\n      if (Array.isArray(array[i])) {\n        throw new DimensionError(size.length + 1, size.length, '>');\n      }\n    }\n  }\n}\n\n/**\n * Validate whether each element in a multi dimensional array has\n * a size corresponding to the provided size array.\n * @param {Array} array    Array to be validated\n * @param {number[]} size  Array with the size of each dimension\n * @throws DimensionError\n */\nexport function validate(array, size) {\n  var isScalar = size.length === 0;\n  if (isScalar) {\n    // scalar\n    if (Array.isArray(array)) {\n      throw new DimensionError(array.length, 0);\n    }\n  } else {\n    // array\n    _validate(array, size, 0);\n  }\n}\n\n/**\n * Validate whether the source of the index matches the size of the Array\n * @param {Array | Matrix} value    Array to be validated\n * @param {Index} index  Index with the source information to validate\n * @throws DimensionError\n */\nexport function validateIndexSourceSize(value, index) {\n  var valueSize = value.isMatrix ? value._size : arraySize(value);\n  var sourceSize = index._sourceSize;\n  // checks if the source size is not null and matches the valueSize\n  sourceSize.forEach((sourceDim, i) => {\n    if (sourceDim !== null && sourceDim !== valueSize[i]) {\n      throw new DimensionError(sourceDim, valueSize[i]);\n    }\n  });\n}\n\n/**\n * Test whether index is an integer number with index >= 0 and index < length\n * when length is provided\n * @param {number} index    Zero-based index\n * @param {number} [length] Length of the array\n */\nexport function validateIndex(index, length) {\n  if (index !== undefined) {\n    if (!isNumber(index) || !isInteger(index)) {\n      throw new TypeError('Index must be an integer (value: ' + index + ')');\n    }\n    if (index < 0 || typeof length === 'number' && index >= length) {\n      throw new IndexError(index, length);\n    }\n  }\n}\n\n/**\n * Test if an index has empty values\n * @param {Index} index    Zero-based index\n */\nexport function isEmptyIndex(index) {\n  for (var i = 0; i < index._dimensions.length; ++i) {\n    var dimension = index._dimensions[i];\n    if (dimension._data && isArray(dimension._data)) {\n      if (dimension._size[0] === 0) {\n        return true;\n      }\n    } else if (dimension.isRange) {\n      if (dimension.start === dimension.end) {\n        return true;\n      }\n    } else if (isString(dimension)) {\n      if (dimension.length === 0) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\n\n/**\n * Resize a multi dimensional array. The resized array is returned.\n * @param {Array | number} array         Array to be resized\n * @param {number[]} size Array with the size of each dimension\n * @param {*} [defaultValue=0]  Value to be filled in new entries,\n *                              zero by default. Specify for example `null`,\n *                              to clearly see entries that are not explicitly\n *                              set.\n * @return {Array} array         The resized array\n */\nexport function resize(array, size, defaultValue) {\n  // check the type of the arguments\n  if (!Array.isArray(size)) {\n    throw new TypeError('Array expected');\n  }\n  if (size.length === 0) {\n    throw new Error('Resizing to scalar is not supported');\n  }\n\n  // check whether size contains positive integers\n  size.forEach(function (value) {\n    if (!isNumber(value) || !isInteger(value) || value < 0) {\n      throw new TypeError('Invalid size, must contain positive integers ' + '(size: ' + format(size) + ')');\n    }\n  });\n\n  // convert number to an array\n  if (isNumber(array) || isBigNumber(array)) {\n    array = [array];\n  }\n\n  // recursively resize the array\n  var _defaultValue = defaultValue !== undefined ? defaultValue : 0;\n  _resize(array, size, 0, _defaultValue);\n  return array;\n}\n\n/**\n * Recursively resize a multi dimensional array\n * @param {Array} array         Array to be resized\n * @param {number[]} size       Array with the size of each dimension\n * @param {number} dim          Current dimension\n * @param {*} [defaultValue]    Value to be filled in new entries,\n *                              undefined by default.\n * @private\n */\nfunction _resize(array, size, dim, defaultValue) {\n  var i;\n  var elem;\n  var oldLen = array.length;\n  var newLen = size[dim];\n  var minLen = Math.min(oldLen, newLen);\n\n  // apply new length\n  array.length = newLen;\n  if (dim < size.length - 1) {\n    // non-last dimension\n    var dimNext = dim + 1;\n\n    // resize existing child arrays\n    for (i = 0; i < minLen; i++) {\n      // resize child array\n      elem = array[i];\n      if (!Array.isArray(elem)) {\n        elem = [elem]; // add a dimension\n        array[i] = elem;\n      }\n      _resize(elem, size, dimNext, defaultValue);\n    }\n\n    // create new child arrays\n    for (i = minLen; i < newLen; i++) {\n      // get child array\n      elem = [];\n      array[i] = elem;\n\n      // resize new child array\n      _resize(elem, size, dimNext, defaultValue);\n    }\n  } else {\n    // last dimension\n\n    // remove dimensions of existing values\n    for (i = 0; i < minLen; i++) {\n      while (Array.isArray(array[i])) {\n        array[i] = array[i][0];\n      }\n    }\n\n    // fill new elements with the default value\n    for (i = minLen; i < newLen; i++) {\n      array[i] = defaultValue;\n    }\n  }\n}\n\n/**\n * Re-shape a multi dimensional array to fit the specified dimensions\n * @param {Array} array           Array to be reshaped\n * @param {number[]} sizes        List of sizes for each dimension\n * @returns {Array}               Array whose data has been formatted to fit the\n *                                specified dimensions\n *\n * @throws {DimensionError}       If the product of the new dimension sizes does\n *                                not equal that of the old ones\n */\nexport function reshape(array, sizes) {\n  var flatArray = flatten(array, true); // since it has rectangular\n  var currentLength = flatArray.length;\n  if (!Array.isArray(array) || !Array.isArray(sizes)) {\n    throw new TypeError('Array expected');\n  }\n  if (sizes.length === 0) {\n    throw new DimensionError(0, currentLength, '!=');\n  }\n  sizes = processSizesWildcard(sizes, currentLength);\n  var newLength = product(sizes);\n  if (currentLength !== newLength) {\n    throw new DimensionError(newLength, currentLength, '!=');\n  }\n  try {\n    return _reshape(flatArray, sizes);\n  } catch (e) {\n    if (e instanceof DimensionError) {\n      throw new DimensionError(newLength, currentLength, '!=');\n    }\n    throw e;\n  }\n}\n\n/**\n * Replaces the wildcard -1 in the sizes array.\n * @param {number[]} sizes  List of sizes for each dimension. At most one wildcard.\n * @param {number} currentLength  Number of elements in the array.\n * @throws {Error}                If more than one wildcard or unable to replace it.\n * @returns {number[]}      The sizes array with wildcard replaced.\n */\nexport function processSizesWildcard(sizes, currentLength) {\n  var newLength = product(sizes);\n  var processedSizes = sizes.slice();\n  var WILDCARD = -1;\n  var wildCardIndex = sizes.indexOf(WILDCARD);\n  var isMoreThanOneWildcard = sizes.indexOf(WILDCARD, wildCardIndex + 1) >= 0;\n  if (isMoreThanOneWildcard) {\n    throw new Error('More than one wildcard in sizes');\n  }\n  var hasWildcard = wildCardIndex >= 0;\n  var canReplaceWildcard = currentLength % newLength === 0;\n  if (hasWildcard) {\n    if (canReplaceWildcard) {\n      processedSizes[wildCardIndex] = -currentLength / newLength;\n    } else {\n      throw new Error('Could not replace wildcard, since ' + currentLength + ' is no multiple of ' + -newLength);\n    }\n  }\n  return processedSizes;\n}\n\n/**\n * Computes the product of all array elements.\n * @param {number[]} array Array of factors\n * @returns {number}            Product of all elements\n */\nfunction product(array) {\n  return array.reduce((prev, curr) => prev * curr, 1);\n}\n\n/**\n * Iteratively re-shape a multi dimensional array to fit the specified dimensions\n * @param {Array} array           Array to be reshaped\n * @param {number[]} sizes  List of sizes for each dimension\n * @returns {Array}               Array whose data has been formatted to fit the\n *                                specified dimensions\n */\n\nfunction _reshape(array, sizes) {\n  // testing if there are enough elements for the requested shape\n  var tmpArray = array;\n  var tmpArray2;\n  // for each dimension starting by the last one and ignoring the first one\n  for (var sizeIndex = sizes.length - 1; sizeIndex > 0; sizeIndex--) {\n    var size = sizes[sizeIndex];\n    tmpArray2 = [];\n\n    // aggregate the elements of the current tmpArray in elements of the requested size\n    var length = tmpArray.length / size;\n    for (var i = 0; i < length; i++) {\n      tmpArray2.push(tmpArray.slice(i * size, (i + 1) * size));\n    }\n    // set it as the new tmpArray for the next loop turn or for return\n    tmpArray = tmpArray2;\n  }\n  return tmpArray;\n}\n\n/**\n * Squeeze a multi dimensional array\n * @param {Array} array\n * @param {Array} [size]\n * @returns {Array} returns the array itself\n */\nexport function squeeze(array, size) {\n  var s = size || arraySize(array);\n\n  // squeeze outer dimensions\n  while (Array.isArray(array) && array.length === 1) {\n    array = array[0];\n    s.shift();\n  }\n\n  // find the first dimension to be squeezed\n  var dims = s.length;\n  while (s[dims - 1] === 1) {\n    dims--;\n  }\n\n  // squeeze inner dimensions\n  if (dims < s.length) {\n    array = _squeeze(array, dims, 0);\n    s.length = dims;\n  }\n  return array;\n}\n\n/**\n * Recursively squeeze a multi dimensional array\n * @param {Array} array\n * @param {number} dims Required number of dimensions\n * @param {number} dim  Current dimension\n * @returns {Array | *} Returns the squeezed array\n * @private\n */\nfunction _squeeze(array, dims, dim) {\n  var i, ii;\n  if (dim < dims) {\n    var next = dim + 1;\n    for (i = 0, ii = array.length; i < ii; i++) {\n      array[i] = _squeeze(array[i], dims, next);\n    }\n  } else {\n    while (Array.isArray(array)) {\n      array = array[0];\n    }\n  }\n  return array;\n}\n\n/**\n * Unsqueeze a multi dimensional array: add dimensions when missing\n *\n * Parameter `size` will be mutated to match the new, unsqueezed matrix size.\n *\n * @param {Array} array\n * @param {number} dims       Desired number of dimensions of the array\n * @param {number} [outer]    Number of outer dimensions to be added\n * @param {Array} [size] Current size of array.\n * @returns {Array} returns the array itself\n * @private\n */\nexport function unsqueeze(array, dims, outer, size) {\n  var s = size || arraySize(array);\n\n  // unsqueeze outer dimensions\n  if (outer) {\n    for (var i = 0; i < outer; i++) {\n      array = [array];\n      s.unshift(1);\n    }\n  }\n\n  // unsqueeze inner dimensions\n  array = _unsqueeze(array, dims, 0);\n  while (s.length < dims) {\n    s.push(1);\n  }\n  return array;\n}\n\n/**\n * Recursively unsqueeze a multi dimensional array\n * @param {Array} array\n * @param {number} dims Required number of dimensions\n * @param {number} dim  Current dimension\n * @returns {Array | *} Returns the unsqueezed array\n * @private\n */\nfunction _unsqueeze(array, dims, dim) {\n  var i, ii;\n  if (Array.isArray(array)) {\n    var next = dim + 1;\n    for (i = 0, ii = array.length; i < ii; i++) {\n      array[i] = _unsqueeze(array[i], dims, next);\n    }\n  } else {\n    for (var d = dim; d < dims; d++) {\n      array = [array];\n    }\n  }\n  return array;\n}\n/**\n * Flatten a multi dimensional array, put all elements in a one dimensional\n * array\n * @param {Array} array   A multi dimensional array\n * @param {boolean} isRectangular Optional. If the array is rectangular (not jagged)\n * @return {Array}        The flattened array (1 dimensional)\n */\nexport function flatten(array) {\n  var isRectangular = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  if (!Array.isArray(array)) {\n    // if not an array, return as is\n    return array;\n  }\n  if (typeof isRectangular !== 'boolean') {\n    throw new TypeError('Boolean expected for second argument of flatten');\n  }\n  var flat = [];\n  if (isRectangular) {\n    _flattenRectangular(array);\n  } else {\n    _flatten(array);\n  }\n  return flat;\n  function _flatten(array) {\n    for (var i = 0; i < array.length; i++) {\n      var item = array[i];\n      if (Array.isArray(item)) {\n        _flatten(item);\n      } else {\n        flat.push(item);\n      }\n    }\n  }\n  function _flattenRectangular(array) {\n    if (Array.isArray(array[0])) {\n      for (var i = 0; i < array.length; i++) {\n        _flattenRectangular(array[i]);\n      }\n    } else {\n      for (var _i = 0; _i < array.length; _i++) {\n        flat.push(array[_i]);\n      }\n    }\n  }\n}\n\n/**\n * A safe map\n * @param {Array} array\n * @param {function} callback\n */\nexport function map(array, callback) {\n  return Array.prototype.map.call(array, callback);\n}\n\n/**\n * A safe forEach\n * @param {Array} array\n * @param {function} callback\n */\nexport function forEach(array, callback) {\n  Array.prototype.forEach.call(array, callback);\n}\n\n/**\n * A safe filter\n * @param {Array} array\n * @param {function} callback\n */\nexport function filter(array, callback) {\n  if (arraySize(array).length !== 1) {\n    throw new Error('Only one dimensional matrices supported');\n  }\n  return Array.prototype.filter.call(array, callback);\n}\n\n/**\n * Filter values in an array given a regular expression\n * @param {Array} array\n * @param {RegExp} regexp\n * @return {Array} Returns the filtered array\n * @private\n */\nexport function filterRegExp(array, regexp) {\n  if (arraySize(array).length !== 1) {\n    throw new Error('Only one dimensional matrices supported');\n  }\n  return Array.prototype.filter.call(array, entry => regexp.test(entry));\n}\n\n/**\n * A safe join\n * @param {Array} array\n * @param {string} separator\n */\nexport function join(array, separator) {\n  return Array.prototype.join.call(array, separator);\n}\n\n/**\n * Assign a numeric identifier to every element of a sorted array\n * @param {Array} a  An array\n * @return {Array} An array of objects containing the original value and its identifier\n */\nexport function identify(a) {\n  if (!Array.isArray(a)) {\n    throw new TypeError('Array input expected');\n  }\n  if (a.length === 0) {\n    return a;\n  }\n  var b = [];\n  var count = 0;\n  b[0] = {\n    value: a[0],\n    identifier: 0\n  };\n  for (var i = 1; i < a.length; i++) {\n    if (a[i] === a[i - 1]) {\n      count++;\n    } else {\n      count = 0;\n    }\n    b.push({\n      value: a[i],\n      identifier: count\n    });\n  }\n  return b;\n}\n\n/**\n * Remove the numeric identifier from the elements\n * @param {array} a  An array\n * @return {array} An array of values without identifiers\n */\nexport function generalize(a) {\n  if (!Array.isArray(a)) {\n    throw new TypeError('Array input expected');\n  }\n  if (a.length === 0) {\n    return a;\n  }\n  var b = [];\n  for (var i = 0; i < a.length; i++) {\n    b.push(a[i].value);\n  }\n  return b;\n}\n\n/**\n * Check the datatype of a given object\n * This is a low level implementation that should only be used by\n * parent Matrix classes such as SparseMatrix or DenseMatrix\n * This method does not validate Array Matrix shape\n * @param {Array} array\n * @param {function} typeOf   Callback function to use to determine the type of a value\n * @return {string}\n */\nexport function getArrayDataType(array, typeOf) {\n  var type; // to hold type info\n  var length = 0; // to hold length value to ensure it has consistent sizes\n\n  for (var i = 0; i < array.length; i++) {\n    var item = array[i];\n    var _isArray = Array.isArray(item);\n\n    // Saving the target matrix row size\n    if (i === 0 && _isArray) {\n      length = item.length;\n    }\n\n    // If the current item is an array but the length does not equal the targetVectorSize\n    if (_isArray && item.length !== length) {\n      return undefined;\n    }\n    var itemType = _isArray ? getArrayDataType(item, typeOf) // recurse into a nested array\n    : typeOf(item);\n    if (type === undefined) {\n      type = itemType; // first item\n    } else if (type !== itemType) {\n      return 'mixed';\n    } else {\n      // we're good, everything has the same type so far\n    }\n  }\n  return type;\n}\n\n/**\n * Return the last item from an array\n * @param {Array} array\n * @returns {*}\n */\nexport function last(array) {\n  return array[array.length - 1];\n}\n\n/**\n * Get all but the last element of array.\n * @param {Array} array\n * @returns {Array}\n */\nexport function initial(array) {\n  return array.slice(0, array.length - 1);\n}\n\n/**\n * Recursively concatenate two matrices.\n * The contents of the matrices are not cloned.\n * @param {Array} a             Multi dimensional array\n * @param {Array} b             Multi dimensional array\n * @param {number} concatDim    The dimension on which to concatenate (zero-based)\n * @param {number} dim          The current dim (zero-based)\n * @return {Array} c            The concatenated matrix\n * @private\n */\nfunction concatRecursive(a, b, concatDim, dim) {\n  if (dim < concatDim) {\n    // recurse into next dimension\n    if (a.length !== b.length) {\n      throw new DimensionError(a.length, b.length);\n    }\n    var c = [];\n    for (var i = 0; i < a.length; i++) {\n      c[i] = concatRecursive(a[i], b[i], concatDim, dim + 1);\n    }\n    return c;\n  } else {\n    // concatenate this dimension\n    return a.concat(b);\n  }\n}\n\n/**\n * Concatenates many arrays in the specified direction\n * @param {...Array} arrays All the arrays to concatenate\n * @param {number} concatDim The dimension on which to concatenate (zero-based)\n * @returns {Array}\n */\nexport function concat() {\n  var arrays = Array.prototype.slice.call(arguments, 0, -1);\n  var concatDim = Array.prototype.slice.call(arguments, -1);\n  if (arrays.length === 1) {\n    return arrays[0];\n  }\n  if (arrays.length > 1) {\n    return arrays.slice(1).reduce(function (A, B) {\n      return concatRecursive(A, B, concatDim, 0);\n    }, arrays[0]);\n  } else {\n    throw new Error('Wrong number of arguments in function concat');\n  }\n}\n\n/**\n * Receives two or more sizes and gets the broadcasted size for both.\n * @param  {...number[]} sizes Sizes to broadcast together\n * @returns {number[]} The broadcasted size\n */\nexport function broadcastSizes() {\n  for (var _len = arguments.length, sizes = new Array(_len), _key = 0; _key < _len; _key++) {\n    sizes[_key] = arguments[_key];\n  }\n  var dimensions = sizes.map(s => s.length);\n  var N = Math.max(...dimensions);\n  var sizeMax = new Array(N).fill(null);\n  // check for every size\n  for (var i = 0; i < sizes.length; i++) {\n    var size = sizes[i];\n    var dim = dimensions[i];\n    for (var j = 0; j < dim; j++) {\n      var n = N - dim + j;\n      if (size[j] > sizeMax[n]) {\n        sizeMax[n] = size[j];\n      }\n    }\n  }\n  for (var _i2 = 0; _i2 < sizes.length; _i2++) {\n    checkBroadcastingRules(sizes[_i2], sizeMax);\n  }\n  return sizeMax;\n}\n\n/**\n * Checks if it's possible to broadcast a size to another size\n * @param {number[]} size The size of the array to check\n * @param {number[]} toSize The size of the array to validate if it can be broadcasted to\n */\nexport function checkBroadcastingRules(size, toSize) {\n  var N = toSize.length;\n  var dim = size.length;\n  for (var j = 0; j < dim; j++) {\n    var n = N - dim + j;\n    if (size[j] < toSize[n] && size[j] > 1 || size[j] > toSize[n]) {\n      throw new Error(\"shape mismatch: mismatch is found in arg with shape (\".concat(size, \") not possible to broadcast dimension \").concat(dim, \" with size \").concat(size[j], \" to size \").concat(toSize[n]));\n    }\n  }\n}\n\n/**\n * Broadcasts a single array to a certain size\n * @param {Array} array Array to be broadcasted\n * @param {number[]} toSize Size to broadcast the array\n * @returns {Array} The broadcasted array\n */\nexport function broadcastTo(array, toSize) {\n  var Asize = arraySize(array);\n  if (deepStrictEqual(Asize, toSize)) {\n    return array;\n  }\n  checkBroadcastingRules(Asize, toSize);\n  var broadcastedSize = broadcastSizes(Asize, toSize);\n  var N = broadcastedSize.length;\n  var paddedSize = [...Array(N - Asize.length).fill(1), ...Asize];\n  var A = clone(array);\n  // reshape A if needed to make it ready for concat\n  if (Asize.length < N) {\n    A = reshape(A, paddedSize);\n    Asize = arraySize(A);\n  }\n\n  // stretches the array on each dimension to make it the same size as index\n  for (var dim = 0; dim < N; dim++) {\n    if (Asize[dim] < broadcastedSize[dim]) {\n      A = stretch(A, broadcastedSize[dim], dim);\n      Asize = arraySize(A);\n    }\n  }\n  return A;\n}\n\n/**\n * Broadcasts arrays and returns the broadcasted arrays in an array\n * @param  {...Array | any} arrays\n * @returns {Array[]} The broadcasted arrays\n */\nexport function broadcastArrays() {\n  for (var _len2 = arguments.length, arrays = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    arrays[_key2] = arguments[_key2];\n  }\n  if (arrays.length === 0) {\n    throw new Error('Insufficient number of arguments in function broadcastArrays');\n  }\n  if (arrays.length === 1) {\n    return arrays[0];\n  }\n  var sizes = arrays.map(function (array) {\n    return arraySize(array);\n  });\n  var broadcastedSize = broadcastSizes(...sizes);\n  var broadcastedArrays = [];\n  arrays.forEach(function (array) {\n    broadcastedArrays.push(broadcastTo(array, broadcastedSize));\n  });\n  return broadcastedArrays;\n}\n\n/**\n * Stretches a matrix up to a certain size in a certain dimension\n * @param {Array} arrayToStretch\n * @param {number[]} sizeToStretch\n * @param {number} dimToStretch\n * @returns {Array} The stretched array\n */\nexport function stretch(arrayToStretch, sizeToStretch, dimToStretch) {\n  return concat(...Array(sizeToStretch).fill(arrayToStretch), dimToStretch);\n}\n\n/**\n* Retrieves a single element from an array given an index.\n*\n* @param {Array} array - The array from which to retrieve the value.\n* @param {Array<number>} index - An array of indices specifying the position of the desired element in each dimension.\n* @returns {*} - The value at the specified position in the array.\n*\n* @example\n* const arr = [[[1, 2], [3, 4]], [[5, 6], [7, 8]]];\n* const index = [1, 0, 1];\n* console.log(get(arr, index)); // 6\n*/\nexport function get(array, index) {\n  if (!Array.isArray(array)) {\n    throw new Error('Array expected');\n  }\n  var size = arraySize(array);\n  if (index.length !== size.length) {\n    throw new DimensionError(index.length, size.length);\n  }\n  for (var x = 0; x < index.length; x++) {\n    validateIndex(index[x], size[x]);\n  }\n  return index.reduce((acc, curr) => acc[curr], array);\n}\n\n/**\n * Recursively maps over each element of nested array using a provided callback function.\n *\n * @param {Array} array - The array to be mapped.\n * @param {Function} callback - The function to execute on each element, taking three arguments:\n *   - `value` (any): The current element being processed in the array.\n *   - `index` (Array<number>): The index of the current element being processed in the array.\n *   - `array` (Array): The array `deepMap` was called upon.\n * @param {boolean} [skipIndex=false] - If true, the callback function is called with only the value.\n * @returns {Array} A new array with each element being the result of the callback function.\n */\nexport function deepMap(array, callback) {\n  var skipIndex = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  if (array.length === 0) {\n    return [];\n  }\n  if (skipIndex) {\n    return recursiveMap(array);\n  }\n  var index = [];\n  return recursiveMapWithIndex(array, 0);\n  function recursiveMapWithIndex(value, depth) {\n    if (Array.isArray(value)) {\n      var N = value.length;\n      var result = Array(N);\n      for (var i = 0; i < N; i++) {\n        index[depth] = i;\n        result[i] = recursiveMapWithIndex(value[i], depth + 1);\n      }\n      return result;\n    } else {\n      return callback(value, index.slice(0, depth), array);\n    }\n  }\n  function recursiveMap(value) {\n    if (Array.isArray(value)) {\n      var N = value.length;\n      var result = Array(N);\n      for (var i = 0; i < N; i++) {\n        result[i] = recursiveMap(value[i]);\n      }\n      return result;\n    } else {\n      return callback(value);\n    }\n  }\n}\n\n/**\n * Recursively iterates over each element in a multi-dimensional array and applies a callback function.\n *\n * @param {Array} array - The multi-dimensional array to iterate over.\n * @param {Function} callback - The function to execute for each element. It receives three arguments:\n *   - {any} value: The current element being processed in the array.\n *   - {Array<number>} index: The index of the current element in each dimension.\n *   - {Array} array: The original array being processed.\n * @param {boolean} [skipIndex=false] - If true, the callback function is called with only the value.\n */\nexport function deepForEach(array, callback) {\n  var skipIndex = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  if (array.length === 0) {\n    return;\n  }\n  if (skipIndex) {\n    recursiveForEach(array);\n    return;\n  }\n  var index = [];\n  recursiveForEachWithIndex(array, 0);\n  function recursiveForEachWithIndex(value, depth) {\n    if (Array.isArray(value)) {\n      var N = value.length;\n      for (var i = 0; i < N; i++) {\n        index[depth] = i;\n        recursiveForEachWithIndex(value[i], depth + 1);\n      }\n    } else {\n      callback(value, index.slice(0, depth), array);\n    }\n  }\n  function recursiveForEach(value) {\n    if (Array.isArray(value)) {\n      var N = value.length;\n      for (var i = 0; i < N; i++) {\n        recursiveForEach(value[i]);\n      }\n    } else {\n      callback(value);\n    }\n  }\n}\n\n/**\n * Deep clones a multidimensional array\n * @param {Array} array\n * @returns {Array} cloned array\n */\nexport function clone(array) {\n  return _extends([], array);\n}", "map": {"version": 3, "names": ["_extends", "isInteger", "isNumber", "isBigNumber", "isArray", "isString", "format", "DimensionError", "IndexError", "deepStrictEqual", "arraySize", "x", "s", "Array", "push", "length", "_validate", "array", "size", "dim", "i", "len", "dimNext", "child", "validate", "isScalar", "validateIndexSourceSize", "value", "index", "valueSize", "isMatrix", "_size", "sourceSize", "_sourceSize", "for<PERSON>ach", "sourceDim", "validateIndex", "undefined", "TypeError", "isEmptyIndex", "_dimensions", "dimension", "_data", "isRange", "start", "end", "resize", "defaultValue", "Error", "_defaultValue", "_resize", "elem", "old<PERSON>en", "newLen", "minLen", "Math", "min", "reshape", "sizes", "flatArray", "flatten", "<PERSON><PERSON><PERSON><PERSON>", "processSizesWildcard", "<PERSON><PERSON><PERSON><PERSON>", "product", "_reshape", "e", "processedSizes", "slice", "WILDCARD", "wildCardIndex", "indexOf", "isMoreThanOneWildcard", "hasWildcard", "canReplaceWildcard", "reduce", "prev", "curr", "tmpArray", "tmpArray2", "sizeIndex", "squeeze", "shift", "dims", "_squeeze", "ii", "next", "unsqueeze", "outer", "unshift", "_unsqueeze", "d", "isRectangular", "arguments", "flat", "_flattenRectangular", "_flatten", "item", "_i", "map", "callback", "prototype", "call", "filter", "filterRegExp", "regexp", "entry", "test", "join", "separator", "identify", "a", "b", "count", "identifier", "generalize", "getArrayDataType", "typeOf", "type", "_isArray", "itemType", "last", "initial", "concatRecursive", "concatDim", "c", "concat", "arrays", "A", "B", "broadcastSizes", "_len", "_key", "dimensions", "N", "max", "sizeMax", "fill", "j", "n", "_i2", "checkBroadcastingRules", "toSize", "broadcastTo", "<PERSON><PERSON>", "broadcastedSize", "paddedSize", "clone", "stretch", "broadcastArrays", "_len2", "_key2", "broadcastedArrays", "arrayToStretch", "sizeToStretch", "dimToStretch", "get", "acc", "deepMap", "skipIndex", "recursiveMap", "recursiveMapWithIndex", "depth", "result", "deepForEach", "recursiveForEach", "recursiveForEachWithIndex"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/array.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport { isInteger } from './number.js';\nimport { isNumber, isBigNumber, isArray, isString } from './is.js';\nimport { format } from './string.js';\nimport { DimensionError } from '../error/DimensionError.js';\nimport { IndexError } from '../error/IndexError.js';\nimport { deepStrictEqual } from './object.js';\n\n/**\n * Calculate the size of a multi dimensional array.\n * This function checks the size of the first entry, it does not validate\n * whether all dimensions match. (use function `validate` for that)\n * @param {Array} x\n * @return {number[]} size\n */\nexport function arraySize(x) {\n  var s = [];\n  while (Array.isArray(x)) {\n    s.push(x.length);\n    x = x[0];\n  }\n  return s;\n}\n\n/**\n * Recursively validate whether each element in a multi dimensional array\n * has a size corresponding to the provided size array.\n * @param {Array} array    Array to be validated\n * @param {number[]} size  Array with the size of each dimension\n * @param {number} dim     Current dimension\n * @throws DimensionError\n * @private\n */\nfunction _validate(array, size, dim) {\n  var i;\n  var len = array.length;\n  if (len !== size[dim]) {\n    throw new DimensionError(len, size[dim]);\n  }\n  if (dim < size.length - 1) {\n    // recursively validate each child array\n    var dimNext = dim + 1;\n    for (i = 0; i < len; i++) {\n      var child = array[i];\n      if (!Array.isArray(child)) {\n        throw new DimensionError(size.length - 1, size.length, '<');\n      }\n      _validate(array[i], size, dimNext);\n    }\n  } else {\n    // last dimension. none of the children may be an array\n    for (i = 0; i < len; i++) {\n      if (Array.isArray(array[i])) {\n        throw new DimensionError(size.length + 1, size.length, '>');\n      }\n    }\n  }\n}\n\n/**\n * Validate whether each element in a multi dimensional array has\n * a size corresponding to the provided size array.\n * @param {Array} array    Array to be validated\n * @param {number[]} size  Array with the size of each dimension\n * @throws DimensionError\n */\nexport function validate(array, size) {\n  var isScalar = size.length === 0;\n  if (isScalar) {\n    // scalar\n    if (Array.isArray(array)) {\n      throw new DimensionError(array.length, 0);\n    }\n  } else {\n    // array\n    _validate(array, size, 0);\n  }\n}\n\n/**\n * Validate whether the source of the index matches the size of the Array\n * @param {Array | Matrix} value    Array to be validated\n * @param {Index} index  Index with the source information to validate\n * @throws DimensionError\n */\nexport function validateIndexSourceSize(value, index) {\n  var valueSize = value.isMatrix ? value._size : arraySize(value);\n  var sourceSize = index._sourceSize;\n  // checks if the source size is not null and matches the valueSize\n  sourceSize.forEach((sourceDim, i) => {\n    if (sourceDim !== null && sourceDim !== valueSize[i]) {\n      throw new DimensionError(sourceDim, valueSize[i]);\n    }\n  });\n}\n\n/**\n * Test whether index is an integer number with index >= 0 and index < length\n * when length is provided\n * @param {number} index    Zero-based index\n * @param {number} [length] Length of the array\n */\nexport function validateIndex(index, length) {\n  if (index !== undefined) {\n    if (!isNumber(index) || !isInteger(index)) {\n      throw new TypeError('Index must be an integer (value: ' + index + ')');\n    }\n    if (index < 0 || typeof length === 'number' && index >= length) {\n      throw new IndexError(index, length);\n    }\n  }\n}\n\n/**\n * Test if an index has empty values\n * @param {Index} index    Zero-based index\n */\nexport function isEmptyIndex(index) {\n  for (var i = 0; i < index._dimensions.length; ++i) {\n    var dimension = index._dimensions[i];\n    if (dimension._data && isArray(dimension._data)) {\n      if (dimension._size[0] === 0) {\n        return true;\n      }\n    } else if (dimension.isRange) {\n      if (dimension.start === dimension.end) {\n        return true;\n      }\n    } else if (isString(dimension)) {\n      if (dimension.length === 0) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\n\n/**\n * Resize a multi dimensional array. The resized array is returned.\n * @param {Array | number} array         Array to be resized\n * @param {number[]} size Array with the size of each dimension\n * @param {*} [defaultValue=0]  Value to be filled in new entries,\n *                              zero by default. Specify for example `null`,\n *                              to clearly see entries that are not explicitly\n *                              set.\n * @return {Array} array         The resized array\n */\nexport function resize(array, size, defaultValue) {\n  // check the type of the arguments\n  if (!Array.isArray(size)) {\n    throw new TypeError('Array expected');\n  }\n  if (size.length === 0) {\n    throw new Error('Resizing to scalar is not supported');\n  }\n\n  // check whether size contains positive integers\n  size.forEach(function (value) {\n    if (!isNumber(value) || !isInteger(value) || value < 0) {\n      throw new TypeError('Invalid size, must contain positive integers ' + '(size: ' + format(size) + ')');\n    }\n  });\n\n  // convert number to an array\n  if (isNumber(array) || isBigNumber(array)) {\n    array = [array];\n  }\n\n  // recursively resize the array\n  var _defaultValue = defaultValue !== undefined ? defaultValue : 0;\n  _resize(array, size, 0, _defaultValue);\n  return array;\n}\n\n/**\n * Recursively resize a multi dimensional array\n * @param {Array} array         Array to be resized\n * @param {number[]} size       Array with the size of each dimension\n * @param {number} dim          Current dimension\n * @param {*} [defaultValue]    Value to be filled in new entries,\n *                              undefined by default.\n * @private\n */\nfunction _resize(array, size, dim, defaultValue) {\n  var i;\n  var elem;\n  var oldLen = array.length;\n  var newLen = size[dim];\n  var minLen = Math.min(oldLen, newLen);\n\n  // apply new length\n  array.length = newLen;\n  if (dim < size.length - 1) {\n    // non-last dimension\n    var dimNext = dim + 1;\n\n    // resize existing child arrays\n    for (i = 0; i < minLen; i++) {\n      // resize child array\n      elem = array[i];\n      if (!Array.isArray(elem)) {\n        elem = [elem]; // add a dimension\n        array[i] = elem;\n      }\n      _resize(elem, size, dimNext, defaultValue);\n    }\n\n    // create new child arrays\n    for (i = minLen; i < newLen; i++) {\n      // get child array\n      elem = [];\n      array[i] = elem;\n\n      // resize new child array\n      _resize(elem, size, dimNext, defaultValue);\n    }\n  } else {\n    // last dimension\n\n    // remove dimensions of existing values\n    for (i = 0; i < minLen; i++) {\n      while (Array.isArray(array[i])) {\n        array[i] = array[i][0];\n      }\n    }\n\n    // fill new elements with the default value\n    for (i = minLen; i < newLen; i++) {\n      array[i] = defaultValue;\n    }\n  }\n}\n\n/**\n * Re-shape a multi dimensional array to fit the specified dimensions\n * @param {Array} array           Array to be reshaped\n * @param {number[]} sizes        List of sizes for each dimension\n * @returns {Array}               Array whose data has been formatted to fit the\n *                                specified dimensions\n *\n * @throws {DimensionError}       If the product of the new dimension sizes does\n *                                not equal that of the old ones\n */\nexport function reshape(array, sizes) {\n  var flatArray = flatten(array, true); // since it has rectangular\n  var currentLength = flatArray.length;\n  if (!Array.isArray(array) || !Array.isArray(sizes)) {\n    throw new TypeError('Array expected');\n  }\n  if (sizes.length === 0) {\n    throw new DimensionError(0, currentLength, '!=');\n  }\n  sizes = processSizesWildcard(sizes, currentLength);\n  var newLength = product(sizes);\n  if (currentLength !== newLength) {\n    throw new DimensionError(newLength, currentLength, '!=');\n  }\n  try {\n    return _reshape(flatArray, sizes);\n  } catch (e) {\n    if (e instanceof DimensionError) {\n      throw new DimensionError(newLength, currentLength, '!=');\n    }\n    throw e;\n  }\n}\n\n/**\n * Replaces the wildcard -1 in the sizes array.\n * @param {number[]} sizes  List of sizes for each dimension. At most one wildcard.\n * @param {number} currentLength  Number of elements in the array.\n * @throws {Error}                If more than one wildcard or unable to replace it.\n * @returns {number[]}      The sizes array with wildcard replaced.\n */\nexport function processSizesWildcard(sizes, currentLength) {\n  var newLength = product(sizes);\n  var processedSizes = sizes.slice();\n  var WILDCARD = -1;\n  var wildCardIndex = sizes.indexOf(WILDCARD);\n  var isMoreThanOneWildcard = sizes.indexOf(WILDCARD, wildCardIndex + 1) >= 0;\n  if (isMoreThanOneWildcard) {\n    throw new Error('More than one wildcard in sizes');\n  }\n  var hasWildcard = wildCardIndex >= 0;\n  var canReplaceWildcard = currentLength % newLength === 0;\n  if (hasWildcard) {\n    if (canReplaceWildcard) {\n      processedSizes[wildCardIndex] = -currentLength / newLength;\n    } else {\n      throw new Error('Could not replace wildcard, since ' + currentLength + ' is no multiple of ' + -newLength);\n    }\n  }\n  return processedSizes;\n}\n\n/**\n * Computes the product of all array elements.\n * @param {number[]} array Array of factors\n * @returns {number}            Product of all elements\n */\nfunction product(array) {\n  return array.reduce((prev, curr) => prev * curr, 1);\n}\n\n/**\n * Iteratively re-shape a multi dimensional array to fit the specified dimensions\n * @param {Array} array           Array to be reshaped\n * @param {number[]} sizes  List of sizes for each dimension\n * @returns {Array}               Array whose data has been formatted to fit the\n *                                specified dimensions\n */\n\nfunction _reshape(array, sizes) {\n  // testing if there are enough elements for the requested shape\n  var tmpArray = array;\n  var tmpArray2;\n  // for each dimension starting by the last one and ignoring the first one\n  for (var sizeIndex = sizes.length - 1; sizeIndex > 0; sizeIndex--) {\n    var size = sizes[sizeIndex];\n    tmpArray2 = [];\n\n    // aggregate the elements of the current tmpArray in elements of the requested size\n    var length = tmpArray.length / size;\n    for (var i = 0; i < length; i++) {\n      tmpArray2.push(tmpArray.slice(i * size, (i + 1) * size));\n    }\n    // set it as the new tmpArray for the next loop turn or for return\n    tmpArray = tmpArray2;\n  }\n  return tmpArray;\n}\n\n/**\n * Squeeze a multi dimensional array\n * @param {Array} array\n * @param {Array} [size]\n * @returns {Array} returns the array itself\n */\nexport function squeeze(array, size) {\n  var s = size || arraySize(array);\n\n  // squeeze outer dimensions\n  while (Array.isArray(array) && array.length === 1) {\n    array = array[0];\n    s.shift();\n  }\n\n  // find the first dimension to be squeezed\n  var dims = s.length;\n  while (s[dims - 1] === 1) {\n    dims--;\n  }\n\n  // squeeze inner dimensions\n  if (dims < s.length) {\n    array = _squeeze(array, dims, 0);\n    s.length = dims;\n  }\n  return array;\n}\n\n/**\n * Recursively squeeze a multi dimensional array\n * @param {Array} array\n * @param {number} dims Required number of dimensions\n * @param {number} dim  Current dimension\n * @returns {Array | *} Returns the squeezed array\n * @private\n */\nfunction _squeeze(array, dims, dim) {\n  var i, ii;\n  if (dim < dims) {\n    var next = dim + 1;\n    for (i = 0, ii = array.length; i < ii; i++) {\n      array[i] = _squeeze(array[i], dims, next);\n    }\n  } else {\n    while (Array.isArray(array)) {\n      array = array[0];\n    }\n  }\n  return array;\n}\n\n/**\n * Unsqueeze a multi dimensional array: add dimensions when missing\n *\n * Parameter `size` will be mutated to match the new, unsqueezed matrix size.\n *\n * @param {Array} array\n * @param {number} dims       Desired number of dimensions of the array\n * @param {number} [outer]    Number of outer dimensions to be added\n * @param {Array} [size] Current size of array.\n * @returns {Array} returns the array itself\n * @private\n */\nexport function unsqueeze(array, dims, outer, size) {\n  var s = size || arraySize(array);\n\n  // unsqueeze outer dimensions\n  if (outer) {\n    for (var i = 0; i < outer; i++) {\n      array = [array];\n      s.unshift(1);\n    }\n  }\n\n  // unsqueeze inner dimensions\n  array = _unsqueeze(array, dims, 0);\n  while (s.length < dims) {\n    s.push(1);\n  }\n  return array;\n}\n\n/**\n * Recursively unsqueeze a multi dimensional array\n * @param {Array} array\n * @param {number} dims Required number of dimensions\n * @param {number} dim  Current dimension\n * @returns {Array | *} Returns the unsqueezed array\n * @private\n */\nfunction _unsqueeze(array, dims, dim) {\n  var i, ii;\n  if (Array.isArray(array)) {\n    var next = dim + 1;\n    for (i = 0, ii = array.length; i < ii; i++) {\n      array[i] = _unsqueeze(array[i], dims, next);\n    }\n  } else {\n    for (var d = dim; d < dims; d++) {\n      array = [array];\n    }\n  }\n  return array;\n}\n/**\n * Flatten a multi dimensional array, put all elements in a one dimensional\n * array\n * @param {Array} array   A multi dimensional array\n * @param {boolean} isRectangular Optional. If the array is rectangular (not jagged)\n * @return {Array}        The flattened array (1 dimensional)\n */\nexport function flatten(array) {\n  var isRectangular = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  if (!Array.isArray(array)) {\n    // if not an array, return as is\n    return array;\n  }\n  if (typeof isRectangular !== 'boolean') {\n    throw new TypeError('Boolean expected for second argument of flatten');\n  }\n  var flat = [];\n  if (isRectangular) {\n    _flattenRectangular(array);\n  } else {\n    _flatten(array);\n  }\n  return flat;\n  function _flatten(array) {\n    for (var i = 0; i < array.length; i++) {\n      var item = array[i];\n      if (Array.isArray(item)) {\n        _flatten(item);\n      } else {\n        flat.push(item);\n      }\n    }\n  }\n  function _flattenRectangular(array) {\n    if (Array.isArray(array[0])) {\n      for (var i = 0; i < array.length; i++) {\n        _flattenRectangular(array[i]);\n      }\n    } else {\n      for (var _i = 0; _i < array.length; _i++) {\n        flat.push(array[_i]);\n      }\n    }\n  }\n}\n\n/**\n * A safe map\n * @param {Array} array\n * @param {function} callback\n */\nexport function map(array, callback) {\n  return Array.prototype.map.call(array, callback);\n}\n\n/**\n * A safe forEach\n * @param {Array} array\n * @param {function} callback\n */\nexport function forEach(array, callback) {\n  Array.prototype.forEach.call(array, callback);\n}\n\n/**\n * A safe filter\n * @param {Array} array\n * @param {function} callback\n */\nexport function filter(array, callback) {\n  if (arraySize(array).length !== 1) {\n    throw new Error('Only one dimensional matrices supported');\n  }\n  return Array.prototype.filter.call(array, callback);\n}\n\n/**\n * Filter values in an array given a regular expression\n * @param {Array} array\n * @param {RegExp} regexp\n * @return {Array} Returns the filtered array\n * @private\n */\nexport function filterRegExp(array, regexp) {\n  if (arraySize(array).length !== 1) {\n    throw new Error('Only one dimensional matrices supported');\n  }\n  return Array.prototype.filter.call(array, entry => regexp.test(entry));\n}\n\n/**\n * A safe join\n * @param {Array} array\n * @param {string} separator\n */\nexport function join(array, separator) {\n  return Array.prototype.join.call(array, separator);\n}\n\n/**\n * Assign a numeric identifier to every element of a sorted array\n * @param {Array} a  An array\n * @return {Array} An array of objects containing the original value and its identifier\n */\nexport function identify(a) {\n  if (!Array.isArray(a)) {\n    throw new TypeError('Array input expected');\n  }\n  if (a.length === 0) {\n    return a;\n  }\n  var b = [];\n  var count = 0;\n  b[0] = {\n    value: a[0],\n    identifier: 0\n  };\n  for (var i = 1; i < a.length; i++) {\n    if (a[i] === a[i - 1]) {\n      count++;\n    } else {\n      count = 0;\n    }\n    b.push({\n      value: a[i],\n      identifier: count\n    });\n  }\n  return b;\n}\n\n/**\n * Remove the numeric identifier from the elements\n * @param {array} a  An array\n * @return {array} An array of values without identifiers\n */\nexport function generalize(a) {\n  if (!Array.isArray(a)) {\n    throw new TypeError('Array input expected');\n  }\n  if (a.length === 0) {\n    return a;\n  }\n  var b = [];\n  for (var i = 0; i < a.length; i++) {\n    b.push(a[i].value);\n  }\n  return b;\n}\n\n/**\n * Check the datatype of a given object\n * This is a low level implementation that should only be used by\n * parent Matrix classes such as SparseMatrix or DenseMatrix\n * This method does not validate Array Matrix shape\n * @param {Array} array\n * @param {function} typeOf   Callback function to use to determine the type of a value\n * @return {string}\n */\nexport function getArrayDataType(array, typeOf) {\n  var type; // to hold type info\n  var length = 0; // to hold length value to ensure it has consistent sizes\n\n  for (var i = 0; i < array.length; i++) {\n    var item = array[i];\n    var _isArray = Array.isArray(item);\n\n    // Saving the target matrix row size\n    if (i === 0 && _isArray) {\n      length = item.length;\n    }\n\n    // If the current item is an array but the length does not equal the targetVectorSize\n    if (_isArray && item.length !== length) {\n      return undefined;\n    }\n    var itemType = _isArray ? getArrayDataType(item, typeOf) // recurse into a nested array\n    : typeOf(item);\n    if (type === undefined) {\n      type = itemType; // first item\n    } else if (type !== itemType) {\n      return 'mixed';\n    } else {\n      // we're good, everything has the same type so far\n    }\n  }\n  return type;\n}\n\n/**\n * Return the last item from an array\n * @param {Array} array\n * @returns {*}\n */\nexport function last(array) {\n  return array[array.length - 1];\n}\n\n/**\n * Get all but the last element of array.\n * @param {Array} array\n * @returns {Array}\n */\nexport function initial(array) {\n  return array.slice(0, array.length - 1);\n}\n\n/**\n * Recursively concatenate two matrices.\n * The contents of the matrices are not cloned.\n * @param {Array} a             Multi dimensional array\n * @param {Array} b             Multi dimensional array\n * @param {number} concatDim    The dimension on which to concatenate (zero-based)\n * @param {number} dim          The current dim (zero-based)\n * @return {Array} c            The concatenated matrix\n * @private\n */\nfunction concatRecursive(a, b, concatDim, dim) {\n  if (dim < concatDim) {\n    // recurse into next dimension\n    if (a.length !== b.length) {\n      throw new DimensionError(a.length, b.length);\n    }\n    var c = [];\n    for (var i = 0; i < a.length; i++) {\n      c[i] = concatRecursive(a[i], b[i], concatDim, dim + 1);\n    }\n    return c;\n  } else {\n    // concatenate this dimension\n    return a.concat(b);\n  }\n}\n\n/**\n * Concatenates many arrays in the specified direction\n * @param {...Array} arrays All the arrays to concatenate\n * @param {number} concatDim The dimension on which to concatenate (zero-based)\n * @returns {Array}\n */\nexport function concat() {\n  var arrays = Array.prototype.slice.call(arguments, 0, -1);\n  var concatDim = Array.prototype.slice.call(arguments, -1);\n  if (arrays.length === 1) {\n    return arrays[0];\n  }\n  if (arrays.length > 1) {\n    return arrays.slice(1).reduce(function (A, B) {\n      return concatRecursive(A, B, concatDim, 0);\n    }, arrays[0]);\n  } else {\n    throw new Error('Wrong number of arguments in function concat');\n  }\n}\n\n/**\n * Receives two or more sizes and gets the broadcasted size for both.\n * @param  {...number[]} sizes Sizes to broadcast together\n * @returns {number[]} The broadcasted size\n */\nexport function broadcastSizes() {\n  for (var _len = arguments.length, sizes = new Array(_len), _key = 0; _key < _len; _key++) {\n    sizes[_key] = arguments[_key];\n  }\n  var dimensions = sizes.map(s => s.length);\n  var N = Math.max(...dimensions);\n  var sizeMax = new Array(N).fill(null);\n  // check for every size\n  for (var i = 0; i < sizes.length; i++) {\n    var size = sizes[i];\n    var dim = dimensions[i];\n    for (var j = 0; j < dim; j++) {\n      var n = N - dim + j;\n      if (size[j] > sizeMax[n]) {\n        sizeMax[n] = size[j];\n      }\n    }\n  }\n  for (var _i2 = 0; _i2 < sizes.length; _i2++) {\n    checkBroadcastingRules(sizes[_i2], sizeMax);\n  }\n  return sizeMax;\n}\n\n/**\n * Checks if it's possible to broadcast a size to another size\n * @param {number[]} size The size of the array to check\n * @param {number[]} toSize The size of the array to validate if it can be broadcasted to\n */\nexport function checkBroadcastingRules(size, toSize) {\n  var N = toSize.length;\n  var dim = size.length;\n  for (var j = 0; j < dim; j++) {\n    var n = N - dim + j;\n    if (size[j] < toSize[n] && size[j] > 1 || size[j] > toSize[n]) {\n      throw new Error(\"shape mismatch: mismatch is found in arg with shape (\".concat(size, \") not possible to broadcast dimension \").concat(dim, \" with size \").concat(size[j], \" to size \").concat(toSize[n]));\n    }\n  }\n}\n\n/**\n * Broadcasts a single array to a certain size\n * @param {Array} array Array to be broadcasted\n * @param {number[]} toSize Size to broadcast the array\n * @returns {Array} The broadcasted array\n */\nexport function broadcastTo(array, toSize) {\n  var Asize = arraySize(array);\n  if (deepStrictEqual(Asize, toSize)) {\n    return array;\n  }\n  checkBroadcastingRules(Asize, toSize);\n  var broadcastedSize = broadcastSizes(Asize, toSize);\n  var N = broadcastedSize.length;\n  var paddedSize = [...Array(N - Asize.length).fill(1), ...Asize];\n  var A = clone(array);\n  // reshape A if needed to make it ready for concat\n  if (Asize.length < N) {\n    A = reshape(A, paddedSize);\n    Asize = arraySize(A);\n  }\n\n  // stretches the array on each dimension to make it the same size as index\n  for (var dim = 0; dim < N; dim++) {\n    if (Asize[dim] < broadcastedSize[dim]) {\n      A = stretch(A, broadcastedSize[dim], dim);\n      Asize = arraySize(A);\n    }\n  }\n  return A;\n}\n\n/**\n * Broadcasts arrays and returns the broadcasted arrays in an array\n * @param  {...Array | any} arrays\n * @returns {Array[]} The broadcasted arrays\n */\nexport function broadcastArrays() {\n  for (var _len2 = arguments.length, arrays = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    arrays[_key2] = arguments[_key2];\n  }\n  if (arrays.length === 0) {\n    throw new Error('Insufficient number of arguments in function broadcastArrays');\n  }\n  if (arrays.length === 1) {\n    return arrays[0];\n  }\n  var sizes = arrays.map(function (array) {\n    return arraySize(array);\n  });\n  var broadcastedSize = broadcastSizes(...sizes);\n  var broadcastedArrays = [];\n  arrays.forEach(function (array) {\n    broadcastedArrays.push(broadcastTo(array, broadcastedSize));\n  });\n  return broadcastedArrays;\n}\n\n/**\n * Stretches a matrix up to a certain size in a certain dimension\n * @param {Array} arrayToStretch\n * @param {number[]} sizeToStretch\n * @param {number} dimToStretch\n * @returns {Array} The stretched array\n */\nexport function stretch(arrayToStretch, sizeToStretch, dimToStretch) {\n  return concat(...Array(sizeToStretch).fill(arrayToStretch), dimToStretch);\n}\n\n/**\n* Retrieves a single element from an array given an index.\n*\n* @param {Array} array - The array from which to retrieve the value.\n* @param {Array<number>} index - An array of indices specifying the position of the desired element in each dimension.\n* @returns {*} - The value at the specified position in the array.\n*\n* @example\n* const arr = [[[1, 2], [3, 4]], [[5, 6], [7, 8]]];\n* const index = [1, 0, 1];\n* console.log(get(arr, index)); // 6\n*/\nexport function get(array, index) {\n  if (!Array.isArray(array)) {\n    throw new Error('Array expected');\n  }\n  var size = arraySize(array);\n  if (index.length !== size.length) {\n    throw new DimensionError(index.length, size.length);\n  }\n  for (var x = 0; x < index.length; x++) {\n    validateIndex(index[x], size[x]);\n  }\n  return index.reduce((acc, curr) => acc[curr], array);\n}\n\n/**\n * Recursively maps over each element of nested array using a provided callback function.\n *\n * @param {Array} array - The array to be mapped.\n * @param {Function} callback - The function to execute on each element, taking three arguments:\n *   - `value` (any): The current element being processed in the array.\n *   - `index` (Array<number>): The index of the current element being processed in the array.\n *   - `array` (Array): The array `deepMap` was called upon.\n * @param {boolean} [skipIndex=false] - If true, the callback function is called with only the value.\n * @returns {Array} A new array with each element being the result of the callback function.\n */\nexport function deepMap(array, callback) {\n  var skipIndex = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  if (array.length === 0) {\n    return [];\n  }\n  if (skipIndex) {\n    return recursiveMap(array);\n  }\n  var index = [];\n  return recursiveMapWithIndex(array, 0);\n  function recursiveMapWithIndex(value, depth) {\n    if (Array.isArray(value)) {\n      var N = value.length;\n      var result = Array(N);\n      for (var i = 0; i < N; i++) {\n        index[depth] = i;\n        result[i] = recursiveMapWithIndex(value[i], depth + 1);\n      }\n      return result;\n    } else {\n      return callback(value, index.slice(0, depth), array);\n    }\n  }\n  function recursiveMap(value) {\n    if (Array.isArray(value)) {\n      var N = value.length;\n      var result = Array(N);\n      for (var i = 0; i < N; i++) {\n        result[i] = recursiveMap(value[i]);\n      }\n      return result;\n    } else {\n      return callback(value);\n    }\n  }\n}\n\n/**\n * Recursively iterates over each element in a multi-dimensional array and applies a callback function.\n *\n * @param {Array} array - The multi-dimensional array to iterate over.\n * @param {Function} callback - The function to execute for each element. It receives three arguments:\n *   - {any} value: The current element being processed in the array.\n *   - {Array<number>} index: The index of the current element in each dimension.\n *   - {Array} array: The original array being processed.\n * @param {boolean} [skipIndex=false] - If true, the callback function is called with only the value.\n */\nexport function deepForEach(array, callback) {\n  var skipIndex = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  if (array.length === 0) {\n    return;\n  }\n  if (skipIndex) {\n    recursiveForEach(array);\n    return;\n  }\n  var index = [];\n  recursiveForEachWithIndex(array, 0);\n  function recursiveForEachWithIndex(value, depth) {\n    if (Array.isArray(value)) {\n      var N = value.length;\n      for (var i = 0; i < N; i++) {\n        index[depth] = i;\n        recursiveForEachWithIndex(value[i], depth + 1);\n      }\n    } else {\n      callback(value, index.slice(0, depth), array);\n    }\n  }\n  function recursiveForEach(value) {\n    if (Array.isArray(value)) {\n      var N = value.length;\n      for (var i = 0; i < N; i++) {\n        recursiveForEach(value[i]);\n      }\n    } else {\n      callback(value);\n    }\n  }\n}\n\n/**\n * Deep clones a multidimensional array\n * @param {Array} array\n * @returns {Array} cloned array\n */\nexport function clone(array) {\n  return _extends([], array);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,QAAQ,EAAEC,WAAW,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,SAAS;AAClE,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,eAAe,QAAQ,aAAa;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,CAAC,EAAE;EAC3B,IAAIC,CAAC,GAAG,EAAE;EACV,OAAOC,KAAK,CAACT,OAAO,CAACO,CAAC,CAAC,EAAE;IACvBC,CAAC,CAACE,IAAI,CAACH,CAAC,CAACI,MAAM,CAAC;IAChBJ,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;EACV;EACA,OAAOC,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,SAASA,CAACC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAE;EACnC,IAAIC,CAAC;EACL,IAAIC,GAAG,GAAGJ,KAAK,CAACF,MAAM;EACtB,IAAIM,GAAG,KAAKH,IAAI,CAACC,GAAG,CAAC,EAAE;IACrB,MAAM,IAAIZ,cAAc,CAACc,GAAG,EAAEH,IAAI,CAACC,GAAG,CAAC,CAAC;EAC1C;EACA,IAAIA,GAAG,GAAGD,IAAI,CAACH,MAAM,GAAG,CAAC,EAAE;IACzB;IACA,IAAIO,OAAO,GAAGH,GAAG,GAAG,CAAC;IACrB,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACxB,IAAIG,KAAK,GAAGN,KAAK,CAACG,CAAC,CAAC;MACpB,IAAI,CAACP,KAAK,CAACT,OAAO,CAACmB,KAAK,CAAC,EAAE;QACzB,MAAM,IAAIhB,cAAc,CAACW,IAAI,CAACH,MAAM,GAAG,CAAC,EAAEG,IAAI,CAACH,MAAM,EAAE,GAAG,CAAC;MAC7D;MACAC,SAAS,CAACC,KAAK,CAACG,CAAC,CAAC,EAAEF,IAAI,EAAEI,OAAO,CAAC;IACpC;EACF,CAAC,MAAM;IACL;IACA,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACxB,IAAIP,KAAK,CAACT,OAAO,CAACa,KAAK,CAACG,CAAC,CAAC,CAAC,EAAE;QAC3B,MAAM,IAAIb,cAAc,CAACW,IAAI,CAACH,MAAM,GAAG,CAAC,EAAEG,IAAI,CAACH,MAAM,EAAE,GAAG,CAAC;MAC7D;IACF;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,QAAQA,CAACP,KAAK,EAAEC,IAAI,EAAE;EACpC,IAAIO,QAAQ,GAAGP,IAAI,CAACH,MAAM,KAAK,CAAC;EAChC,IAAIU,QAAQ,EAAE;IACZ;IACA,IAAIZ,KAAK,CAACT,OAAO,CAACa,KAAK,CAAC,EAAE;MACxB,MAAM,IAAIV,cAAc,CAACU,KAAK,CAACF,MAAM,EAAE,CAAC,CAAC;IAC3C;EACF,CAAC,MAAM;IACL;IACAC,SAAS,CAACC,KAAK,EAAEC,IAAI,EAAE,CAAC,CAAC;EAC3B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASQ,uBAAuBA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACpD,IAAIC,SAAS,GAAGF,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACI,KAAK,GAAGrB,SAAS,CAACiB,KAAK,CAAC;EAC/D,IAAIK,UAAU,GAAGJ,KAAK,CAACK,WAAW;EAClC;EACAD,UAAU,CAACE,OAAO,CAAC,CAACC,SAAS,EAAEf,CAAC,KAAK;IACnC,IAAIe,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKN,SAAS,CAACT,CAAC,CAAC,EAAE;MACpD,MAAM,IAAIb,cAAc,CAAC4B,SAAS,EAAEN,SAAS,CAACT,CAAC,CAAC,CAAC;IACnD;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgB,aAAaA,CAACR,KAAK,EAAEb,MAAM,EAAE;EAC3C,IAAIa,KAAK,KAAKS,SAAS,EAAE;IACvB,IAAI,CAACnC,QAAQ,CAAC0B,KAAK,CAAC,IAAI,CAAC3B,SAAS,CAAC2B,KAAK,CAAC,EAAE;MACzC,MAAM,IAAIU,SAAS,CAAC,mCAAmC,GAAGV,KAAK,GAAG,GAAG,CAAC;IACxE;IACA,IAAIA,KAAK,GAAG,CAAC,IAAI,OAAOb,MAAM,KAAK,QAAQ,IAAIa,KAAK,IAAIb,MAAM,EAAE;MAC9D,MAAM,IAAIP,UAAU,CAACoB,KAAK,EAAEb,MAAM,CAAC;IACrC;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASwB,YAAYA,CAACX,KAAK,EAAE;EAClC,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,KAAK,CAACY,WAAW,CAACzB,MAAM,EAAE,EAAEK,CAAC,EAAE;IACjD,IAAIqB,SAAS,GAAGb,KAAK,CAACY,WAAW,CAACpB,CAAC,CAAC;IACpC,IAAIqB,SAAS,CAACC,KAAK,IAAItC,OAAO,CAACqC,SAAS,CAACC,KAAK,CAAC,EAAE;MAC/C,IAAID,SAAS,CAACV,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QAC5B,OAAO,IAAI;MACb;IACF,CAAC,MAAM,IAAIU,SAAS,CAACE,OAAO,EAAE;MAC5B,IAAIF,SAAS,CAACG,KAAK,KAAKH,SAAS,CAACI,GAAG,EAAE;QACrC,OAAO,IAAI;MACb;IACF,CAAC,MAAM,IAAIxC,QAAQ,CAACoC,SAAS,CAAC,EAAE;MAC9B,IAAIA,SAAS,CAAC1B,MAAM,KAAK,CAAC,EAAE;QAC1B,OAAO,IAAI;MACb;IACF;EACF;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS+B,MAAMA,CAAC7B,KAAK,EAAEC,IAAI,EAAE6B,YAAY,EAAE;EAChD;EACA,IAAI,CAAClC,KAAK,CAACT,OAAO,CAACc,IAAI,CAAC,EAAE;IACxB,MAAM,IAAIoB,SAAS,CAAC,gBAAgB,CAAC;EACvC;EACA,IAAIpB,IAAI,CAACH,MAAM,KAAK,CAAC,EAAE;IACrB,MAAM,IAAIiC,KAAK,CAAC,qCAAqC,CAAC;EACxD;;EAEA;EACA9B,IAAI,CAACgB,OAAO,CAAC,UAAUP,KAAK,EAAE;IAC5B,IAAI,CAACzB,QAAQ,CAACyB,KAAK,CAAC,IAAI,CAAC1B,SAAS,CAAC0B,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;MACtD,MAAM,IAAIW,SAAS,CAAC,+CAA+C,GAAG,SAAS,GAAGhC,MAAM,CAACY,IAAI,CAAC,GAAG,GAAG,CAAC;IACvG;EACF,CAAC,CAAC;;EAEF;EACA,IAAIhB,QAAQ,CAACe,KAAK,CAAC,IAAId,WAAW,CAACc,KAAK,CAAC,EAAE;IACzCA,KAAK,GAAG,CAACA,KAAK,CAAC;EACjB;;EAEA;EACA,IAAIgC,aAAa,GAAGF,YAAY,KAAKV,SAAS,GAAGU,YAAY,GAAG,CAAC;EACjEG,OAAO,CAACjC,KAAK,EAAEC,IAAI,EAAE,CAAC,EAAE+B,aAAa,CAAC;EACtC,OAAOhC,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiC,OAAOA,CAACjC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAE4B,YAAY,EAAE;EAC/C,IAAI3B,CAAC;EACL,IAAI+B,IAAI;EACR,IAAIC,MAAM,GAAGnC,KAAK,CAACF,MAAM;EACzB,IAAIsC,MAAM,GAAGnC,IAAI,CAACC,GAAG,CAAC;EACtB,IAAImC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACJ,MAAM,EAAEC,MAAM,CAAC;;EAErC;EACApC,KAAK,CAACF,MAAM,GAAGsC,MAAM;EACrB,IAAIlC,GAAG,GAAGD,IAAI,CAACH,MAAM,GAAG,CAAC,EAAE;IACzB;IACA,IAAIO,OAAO,GAAGH,GAAG,GAAG,CAAC;;IAErB;IACA,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkC,MAAM,EAAElC,CAAC,EAAE,EAAE;MAC3B;MACA+B,IAAI,GAAGlC,KAAK,CAACG,CAAC,CAAC;MACf,IAAI,CAACP,KAAK,CAACT,OAAO,CAAC+C,IAAI,CAAC,EAAE;QACxBA,IAAI,GAAG,CAACA,IAAI,CAAC,CAAC,CAAC;QACflC,KAAK,CAACG,CAAC,CAAC,GAAG+B,IAAI;MACjB;MACAD,OAAO,CAACC,IAAI,EAAEjC,IAAI,EAAEI,OAAO,EAAEyB,YAAY,CAAC;IAC5C;;IAEA;IACA,KAAK3B,CAAC,GAAGkC,MAAM,EAAElC,CAAC,GAAGiC,MAAM,EAAEjC,CAAC,EAAE,EAAE;MAChC;MACA+B,IAAI,GAAG,EAAE;MACTlC,KAAK,CAACG,CAAC,CAAC,GAAG+B,IAAI;;MAEf;MACAD,OAAO,CAACC,IAAI,EAAEjC,IAAI,EAAEI,OAAO,EAAEyB,YAAY,CAAC;IAC5C;EACF,CAAC,MAAM;IACL;;IAEA;IACA,KAAK3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkC,MAAM,EAAElC,CAAC,EAAE,EAAE;MAC3B,OAAOP,KAAK,CAACT,OAAO,CAACa,KAAK,CAACG,CAAC,CAAC,CAAC,EAAE;QAC9BH,KAAK,CAACG,CAAC,CAAC,GAAGH,KAAK,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB;IACF;;IAEA;IACA,KAAKA,CAAC,GAAGkC,MAAM,EAAElC,CAAC,GAAGiC,MAAM,EAAEjC,CAAC,EAAE,EAAE;MAChCH,KAAK,CAACG,CAAC,CAAC,GAAG2B,YAAY;IACzB;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASU,OAAOA,CAACxC,KAAK,EAAEyC,KAAK,EAAE;EACpC,IAAIC,SAAS,GAAGC,OAAO,CAAC3C,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;EACtC,IAAI4C,aAAa,GAAGF,SAAS,CAAC5C,MAAM;EACpC,IAAI,CAACF,KAAK,CAACT,OAAO,CAACa,KAAK,CAAC,IAAI,CAACJ,KAAK,CAACT,OAAO,CAACsD,KAAK,CAAC,EAAE;IAClD,MAAM,IAAIpB,SAAS,CAAC,gBAAgB,CAAC;EACvC;EACA,IAAIoB,KAAK,CAAC3C,MAAM,KAAK,CAAC,EAAE;IACtB,MAAM,IAAIR,cAAc,CAAC,CAAC,EAAEsD,aAAa,EAAE,IAAI,CAAC;EAClD;EACAH,KAAK,GAAGI,oBAAoB,CAACJ,KAAK,EAAEG,aAAa,CAAC;EAClD,IAAIE,SAAS,GAAGC,OAAO,CAACN,KAAK,CAAC;EAC9B,IAAIG,aAAa,KAAKE,SAAS,EAAE;IAC/B,MAAM,IAAIxD,cAAc,CAACwD,SAAS,EAAEF,aAAa,EAAE,IAAI,CAAC;EAC1D;EACA,IAAI;IACF,OAAOI,QAAQ,CAACN,SAAS,EAAED,KAAK,CAAC;EACnC,CAAC,CAAC,OAAOQ,CAAC,EAAE;IACV,IAAIA,CAAC,YAAY3D,cAAc,EAAE;MAC/B,MAAM,IAAIA,cAAc,CAACwD,SAAS,EAAEF,aAAa,EAAE,IAAI,CAAC;IAC1D;IACA,MAAMK,CAAC;EACT;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASJ,oBAAoBA,CAACJ,KAAK,EAAEG,aAAa,EAAE;EACzD,IAAIE,SAAS,GAAGC,OAAO,CAACN,KAAK,CAAC;EAC9B,IAAIS,cAAc,GAAGT,KAAK,CAACU,KAAK,CAAC,CAAC;EAClC,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAIC,aAAa,GAAGZ,KAAK,CAACa,OAAO,CAACF,QAAQ,CAAC;EAC3C,IAAIG,qBAAqB,GAAGd,KAAK,CAACa,OAAO,CAACF,QAAQ,EAAEC,aAAa,GAAG,CAAC,CAAC,IAAI,CAAC;EAC3E,IAAIE,qBAAqB,EAAE;IACzB,MAAM,IAAIxB,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACA,IAAIyB,WAAW,GAAGH,aAAa,IAAI,CAAC;EACpC,IAAII,kBAAkB,GAAGb,aAAa,GAAGE,SAAS,KAAK,CAAC;EACxD,IAAIU,WAAW,EAAE;IACf,IAAIC,kBAAkB,EAAE;MACtBP,cAAc,CAACG,aAAa,CAAC,GAAG,CAACT,aAAa,GAAGE,SAAS;IAC5D,CAAC,MAAM;MACL,MAAM,IAAIf,KAAK,CAAC,oCAAoC,GAAGa,aAAa,GAAG,qBAAqB,GAAG,CAACE,SAAS,CAAC;IAC5G;EACF;EACA,OAAOI,cAAc;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASH,OAAOA,CAAC/C,KAAK,EAAE;EACtB,OAAOA,KAAK,CAAC0D,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAKD,IAAI,GAAGC,IAAI,EAAE,CAAC,CAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASZ,QAAQA,CAAChD,KAAK,EAAEyC,KAAK,EAAE;EAC9B;EACA,IAAIoB,QAAQ,GAAG7D,KAAK;EACpB,IAAI8D,SAAS;EACb;EACA,KAAK,IAAIC,SAAS,GAAGtB,KAAK,CAAC3C,MAAM,GAAG,CAAC,EAAEiE,SAAS,GAAG,CAAC,EAAEA,SAAS,EAAE,EAAE;IACjE,IAAI9D,IAAI,GAAGwC,KAAK,CAACsB,SAAS,CAAC;IAC3BD,SAAS,GAAG,EAAE;;IAEd;IACA,IAAIhE,MAAM,GAAG+D,QAAQ,CAAC/D,MAAM,GAAGG,IAAI;IACnC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,EAAEK,CAAC,EAAE,EAAE;MAC/B2D,SAAS,CAACjE,IAAI,CAACgE,QAAQ,CAACV,KAAK,CAAChD,CAAC,GAAGF,IAAI,EAAE,CAACE,CAAC,GAAG,CAAC,IAAIF,IAAI,CAAC,CAAC;IAC1D;IACA;IACA4D,QAAQ,GAAGC,SAAS;EACtB;EACA,OAAOD,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,OAAOA,CAAChE,KAAK,EAAEC,IAAI,EAAE;EACnC,IAAIN,CAAC,GAAGM,IAAI,IAAIR,SAAS,CAACO,KAAK,CAAC;;EAEhC;EACA,OAAOJ,KAAK,CAACT,OAAO,CAACa,KAAK,CAAC,IAAIA,KAAK,CAACF,MAAM,KAAK,CAAC,EAAE;IACjDE,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;IAChBL,CAAC,CAACsE,KAAK,CAAC,CAAC;EACX;;EAEA;EACA,IAAIC,IAAI,GAAGvE,CAAC,CAACG,MAAM;EACnB,OAAOH,CAAC,CAACuE,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;IACxBA,IAAI,EAAE;EACR;;EAEA;EACA,IAAIA,IAAI,GAAGvE,CAAC,CAACG,MAAM,EAAE;IACnBE,KAAK,GAAGmE,QAAQ,CAACnE,KAAK,EAAEkE,IAAI,EAAE,CAAC,CAAC;IAChCvE,CAAC,CAACG,MAAM,GAAGoE,IAAI;EACjB;EACA,OAAOlE,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmE,QAAQA,CAACnE,KAAK,EAAEkE,IAAI,EAAEhE,GAAG,EAAE;EAClC,IAAIC,CAAC,EAAEiE,EAAE;EACT,IAAIlE,GAAG,GAAGgE,IAAI,EAAE;IACd,IAAIG,IAAI,GAAGnE,GAAG,GAAG,CAAC;IAClB,KAAKC,CAAC,GAAG,CAAC,EAAEiE,EAAE,GAAGpE,KAAK,CAACF,MAAM,EAAEK,CAAC,GAAGiE,EAAE,EAAEjE,CAAC,EAAE,EAAE;MAC1CH,KAAK,CAACG,CAAC,CAAC,GAAGgE,QAAQ,CAACnE,KAAK,CAACG,CAAC,CAAC,EAAE+D,IAAI,EAAEG,IAAI,CAAC;IAC3C;EACF,CAAC,MAAM;IACL,OAAOzE,KAAK,CAACT,OAAO,CAACa,KAAK,CAAC,EAAE;MAC3BA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;IAClB;EACF;EACA,OAAOA,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASsE,SAASA,CAACtE,KAAK,EAAEkE,IAAI,EAAEK,KAAK,EAAEtE,IAAI,EAAE;EAClD,IAAIN,CAAC,GAAGM,IAAI,IAAIR,SAAS,CAACO,KAAK,CAAC;;EAEhC;EACA,IAAIuE,KAAK,EAAE;IACT,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoE,KAAK,EAAEpE,CAAC,EAAE,EAAE;MAC9BH,KAAK,GAAG,CAACA,KAAK,CAAC;MACfL,CAAC,CAAC6E,OAAO,CAAC,CAAC,CAAC;IACd;EACF;;EAEA;EACAxE,KAAK,GAAGyE,UAAU,CAACzE,KAAK,EAAEkE,IAAI,EAAE,CAAC,CAAC;EAClC,OAAOvE,CAAC,CAACG,MAAM,GAAGoE,IAAI,EAAE;IACtBvE,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;EACX;EACA,OAAOG,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyE,UAAUA,CAACzE,KAAK,EAAEkE,IAAI,EAAEhE,GAAG,EAAE;EACpC,IAAIC,CAAC,EAAEiE,EAAE;EACT,IAAIxE,KAAK,CAACT,OAAO,CAACa,KAAK,CAAC,EAAE;IACxB,IAAIqE,IAAI,GAAGnE,GAAG,GAAG,CAAC;IAClB,KAAKC,CAAC,GAAG,CAAC,EAAEiE,EAAE,GAAGpE,KAAK,CAACF,MAAM,EAAEK,CAAC,GAAGiE,EAAE,EAAEjE,CAAC,EAAE,EAAE;MAC1CH,KAAK,CAACG,CAAC,CAAC,GAAGsE,UAAU,CAACzE,KAAK,CAACG,CAAC,CAAC,EAAE+D,IAAI,EAAEG,IAAI,CAAC;IAC7C;EACF,CAAC,MAAM;IACL,KAAK,IAAIK,CAAC,GAAGxE,GAAG,EAAEwE,CAAC,GAAGR,IAAI,EAAEQ,CAAC,EAAE,EAAE;MAC/B1E,KAAK,GAAG,CAACA,KAAK,CAAC;IACjB;EACF;EACA,OAAOA,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS2C,OAAOA,CAAC3C,KAAK,EAAE;EAC7B,IAAI2E,aAAa,GAAGC,SAAS,CAAC9E,MAAM,GAAG,CAAC,IAAI8E,SAAS,CAAC,CAAC,CAAC,KAAKxD,SAAS,GAAGwD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAC7F,IAAI,CAAChF,KAAK,CAACT,OAAO,CAACa,KAAK,CAAC,EAAE;IACzB;IACA,OAAOA,KAAK;EACd;EACA,IAAI,OAAO2E,aAAa,KAAK,SAAS,EAAE;IACtC,MAAM,IAAItD,SAAS,CAAC,iDAAiD,CAAC;EACxE;EACA,IAAIwD,IAAI,GAAG,EAAE;EACb,IAAIF,aAAa,EAAE;IACjBG,mBAAmB,CAAC9E,KAAK,CAAC;EAC5B,CAAC,MAAM;IACL+E,QAAQ,CAAC/E,KAAK,CAAC;EACjB;EACA,OAAO6E,IAAI;EACX,SAASE,QAAQA,CAAC/E,KAAK,EAAE;IACvB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACF,MAAM,EAAEK,CAAC,EAAE,EAAE;MACrC,IAAI6E,IAAI,GAAGhF,KAAK,CAACG,CAAC,CAAC;MACnB,IAAIP,KAAK,CAACT,OAAO,CAAC6F,IAAI,CAAC,EAAE;QACvBD,QAAQ,CAACC,IAAI,CAAC;MAChB,CAAC,MAAM;QACLH,IAAI,CAAChF,IAAI,CAACmF,IAAI,CAAC;MACjB;IACF;EACF;EACA,SAASF,mBAAmBA,CAAC9E,KAAK,EAAE;IAClC,IAAIJ,KAAK,CAACT,OAAO,CAACa,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACF,MAAM,EAAEK,CAAC,EAAE,EAAE;QACrC2E,mBAAmB,CAAC9E,KAAK,CAACG,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC,MAAM;MACL,KAAK,IAAI8E,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGjF,KAAK,CAACF,MAAM,EAAEmF,EAAE,EAAE,EAAE;QACxCJ,IAAI,CAAChF,IAAI,CAACG,KAAK,CAACiF,EAAE,CAAC,CAAC;MACtB;IACF;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,GAAGA,CAAClF,KAAK,EAAEmF,QAAQ,EAAE;EACnC,OAAOvF,KAAK,CAACwF,SAAS,CAACF,GAAG,CAACG,IAAI,CAACrF,KAAK,EAAEmF,QAAQ,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASlE,OAAOA,CAACjB,KAAK,EAAEmF,QAAQ,EAAE;EACvCvF,KAAK,CAACwF,SAAS,CAACnE,OAAO,CAACoE,IAAI,CAACrF,KAAK,EAAEmF,QAAQ,CAAC;AAC/C;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,MAAMA,CAACtF,KAAK,EAAEmF,QAAQ,EAAE;EACtC,IAAI1F,SAAS,CAACO,KAAK,CAAC,CAACF,MAAM,KAAK,CAAC,EAAE;IACjC,MAAM,IAAIiC,KAAK,CAAC,yCAAyC,CAAC;EAC5D;EACA,OAAOnC,KAAK,CAACwF,SAAS,CAACE,MAAM,CAACD,IAAI,CAACrF,KAAK,EAAEmF,QAAQ,CAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,YAAYA,CAACvF,KAAK,EAAEwF,MAAM,EAAE;EAC1C,IAAI/F,SAAS,CAACO,KAAK,CAAC,CAACF,MAAM,KAAK,CAAC,EAAE;IACjC,MAAM,IAAIiC,KAAK,CAAC,yCAAyC,CAAC;EAC5D;EACA,OAAOnC,KAAK,CAACwF,SAAS,CAACE,MAAM,CAACD,IAAI,CAACrF,KAAK,EAAEyF,KAAK,IAAID,MAAM,CAACE,IAAI,CAACD,KAAK,CAAC,CAAC;AACxE;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,IAAIA,CAAC3F,KAAK,EAAE4F,SAAS,EAAE;EACrC,OAAOhG,KAAK,CAACwF,SAAS,CAACO,IAAI,CAACN,IAAI,CAACrF,KAAK,EAAE4F,SAAS,CAAC;AACpD;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,CAAC,EAAE;EAC1B,IAAI,CAAClG,KAAK,CAACT,OAAO,CAAC2G,CAAC,CAAC,EAAE;IACrB,MAAM,IAAIzE,SAAS,CAAC,sBAAsB,CAAC;EAC7C;EACA,IAAIyE,CAAC,CAAChG,MAAM,KAAK,CAAC,EAAE;IAClB,OAAOgG,CAAC;EACV;EACA,IAAIC,CAAC,GAAG,EAAE;EACV,IAAIC,KAAK,GAAG,CAAC;EACbD,CAAC,CAAC,CAAC,CAAC,GAAG;IACLrF,KAAK,EAAEoF,CAAC,CAAC,CAAC,CAAC;IACXG,UAAU,EAAE;EACd,CAAC;EACD,KAAK,IAAI9F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2F,CAAC,CAAChG,MAAM,EAAEK,CAAC,EAAE,EAAE;IACjC,IAAI2F,CAAC,CAAC3F,CAAC,CAAC,KAAK2F,CAAC,CAAC3F,CAAC,GAAG,CAAC,CAAC,EAAE;MACrB6F,KAAK,EAAE;IACT,CAAC,MAAM;MACLA,KAAK,GAAG,CAAC;IACX;IACAD,CAAC,CAAClG,IAAI,CAAC;MACLa,KAAK,EAAEoF,CAAC,CAAC3F,CAAC,CAAC;MACX8F,UAAU,EAAED;IACd,CAAC,CAAC;EACJ;EACA,OAAOD,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,UAAUA,CAACJ,CAAC,EAAE;EAC5B,IAAI,CAAClG,KAAK,CAACT,OAAO,CAAC2G,CAAC,CAAC,EAAE;IACrB,MAAM,IAAIzE,SAAS,CAAC,sBAAsB,CAAC;EAC7C;EACA,IAAIyE,CAAC,CAAChG,MAAM,KAAK,CAAC,EAAE;IAClB,OAAOgG,CAAC;EACV;EACA,IAAIC,CAAC,GAAG,EAAE;EACV,KAAK,IAAI5F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2F,CAAC,CAAChG,MAAM,EAAEK,CAAC,EAAE,EAAE;IACjC4F,CAAC,CAAClG,IAAI,CAACiG,CAAC,CAAC3F,CAAC,CAAC,CAACO,KAAK,CAAC;EACpB;EACA,OAAOqF,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,gBAAgBA,CAACnG,KAAK,EAAEoG,MAAM,EAAE;EAC9C,IAAIC,IAAI,CAAC,CAAC;EACV,IAAIvG,MAAM,GAAG,CAAC,CAAC,CAAC;;EAEhB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACF,MAAM,EAAEK,CAAC,EAAE,EAAE;IACrC,IAAI6E,IAAI,GAAGhF,KAAK,CAACG,CAAC,CAAC;IACnB,IAAImG,QAAQ,GAAG1G,KAAK,CAACT,OAAO,CAAC6F,IAAI,CAAC;;IAElC;IACA,IAAI7E,CAAC,KAAK,CAAC,IAAImG,QAAQ,EAAE;MACvBxG,MAAM,GAAGkF,IAAI,CAAClF,MAAM;IACtB;;IAEA;IACA,IAAIwG,QAAQ,IAAItB,IAAI,CAAClF,MAAM,KAAKA,MAAM,EAAE;MACtC,OAAOsB,SAAS;IAClB;IACA,IAAImF,QAAQ,GAAGD,QAAQ,GAAGH,gBAAgB,CAACnB,IAAI,EAAEoB,MAAM,CAAC,CAAC;IAAA,EACvDA,MAAM,CAACpB,IAAI,CAAC;IACd,IAAIqB,IAAI,KAAKjF,SAAS,EAAE;MACtBiF,IAAI,GAAGE,QAAQ,CAAC,CAAC;IACnB,CAAC,MAAM,IAAIF,IAAI,KAAKE,QAAQ,EAAE;MAC5B,OAAO,OAAO;IAChB,CAAC,MAAM;MACL;IAAA;EAEJ;EACA,OAAOF,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,IAAIA,CAACxG,KAAK,EAAE;EAC1B,OAAOA,KAAK,CAACA,KAAK,CAACF,MAAM,GAAG,CAAC,CAAC;AAChC;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS2G,OAAOA,CAACzG,KAAK,EAAE;EAC7B,OAAOA,KAAK,CAACmD,KAAK,CAAC,CAAC,EAAEnD,KAAK,CAACF,MAAM,GAAG,CAAC,CAAC;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4G,eAAeA,CAACZ,CAAC,EAAEC,CAAC,EAAEY,SAAS,EAAEzG,GAAG,EAAE;EAC7C,IAAIA,GAAG,GAAGyG,SAAS,EAAE;IACnB;IACA,IAAIb,CAAC,CAAChG,MAAM,KAAKiG,CAAC,CAACjG,MAAM,EAAE;MACzB,MAAM,IAAIR,cAAc,CAACwG,CAAC,CAAChG,MAAM,EAAEiG,CAAC,CAACjG,MAAM,CAAC;IAC9C;IACA,IAAI8G,CAAC,GAAG,EAAE;IACV,KAAK,IAAIzG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2F,CAAC,CAAChG,MAAM,EAAEK,CAAC,EAAE,EAAE;MACjCyG,CAAC,CAACzG,CAAC,CAAC,GAAGuG,eAAe,CAACZ,CAAC,CAAC3F,CAAC,CAAC,EAAE4F,CAAC,CAAC5F,CAAC,CAAC,EAAEwG,SAAS,EAAEzG,GAAG,GAAG,CAAC,CAAC;IACxD;IACA,OAAO0G,CAAC;EACV,CAAC,MAAM;IACL;IACA,OAAOd,CAAC,CAACe,MAAM,CAACd,CAAC,CAAC;EACpB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASc,MAAMA,CAAA,EAAG;EACvB,IAAIC,MAAM,GAAGlH,KAAK,CAACwF,SAAS,CAACjC,KAAK,CAACkC,IAAI,CAACT,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACzD,IAAI+B,SAAS,GAAG/G,KAAK,CAACwF,SAAS,CAACjC,KAAK,CAACkC,IAAI,CAACT,SAAS,EAAE,CAAC,CAAC,CAAC;EACzD,IAAIkC,MAAM,CAAChH,MAAM,KAAK,CAAC,EAAE;IACvB,OAAOgH,MAAM,CAAC,CAAC,CAAC;EAClB;EACA,IAAIA,MAAM,CAAChH,MAAM,GAAG,CAAC,EAAE;IACrB,OAAOgH,MAAM,CAAC3D,KAAK,CAAC,CAAC,CAAC,CAACO,MAAM,CAAC,UAAUqD,CAAC,EAAEC,CAAC,EAAE;MAC5C,OAAON,eAAe,CAACK,CAAC,EAAEC,CAAC,EAAEL,SAAS,EAAE,CAAC,CAAC;IAC5C,CAAC,EAAEG,MAAM,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,MAAM;IACL,MAAM,IAAI/E,KAAK,CAAC,8CAA8C,CAAC;EACjE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASkF,cAAcA,CAAA,EAAG;EAC/B,KAAK,IAAIC,IAAI,GAAGtC,SAAS,CAAC9E,MAAM,EAAE2C,KAAK,GAAG,IAAI7C,KAAK,CAACsH,IAAI,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGD,IAAI,EAAEC,IAAI,EAAE,EAAE;IACxF1E,KAAK,CAAC0E,IAAI,CAAC,GAAGvC,SAAS,CAACuC,IAAI,CAAC;EAC/B;EACA,IAAIC,UAAU,GAAG3E,KAAK,CAACyC,GAAG,CAACvF,CAAC,IAAIA,CAAC,CAACG,MAAM,CAAC;EACzC,IAAIuH,CAAC,GAAG/E,IAAI,CAACgF,GAAG,CAAC,GAAGF,UAAU,CAAC;EAC/B,IAAIG,OAAO,GAAG,IAAI3H,KAAK,CAACyH,CAAC,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC;EACrC;EACA,KAAK,IAAIrH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,KAAK,CAAC3C,MAAM,EAAEK,CAAC,EAAE,EAAE;IACrC,IAAIF,IAAI,GAAGwC,KAAK,CAACtC,CAAC,CAAC;IACnB,IAAID,GAAG,GAAGkH,UAAU,CAACjH,CAAC,CAAC;IACvB,KAAK,IAAIsH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvH,GAAG,EAAEuH,CAAC,EAAE,EAAE;MAC5B,IAAIC,CAAC,GAAGL,CAAC,GAAGnH,GAAG,GAAGuH,CAAC;MACnB,IAAIxH,IAAI,CAACwH,CAAC,CAAC,GAAGF,OAAO,CAACG,CAAC,CAAC,EAAE;QACxBH,OAAO,CAACG,CAAC,CAAC,GAAGzH,IAAI,CAACwH,CAAC,CAAC;MACtB;IACF;EACF;EACA,KAAK,IAAIE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGlF,KAAK,CAAC3C,MAAM,EAAE6H,GAAG,EAAE,EAAE;IAC3CC,sBAAsB,CAACnF,KAAK,CAACkF,GAAG,CAAC,EAAEJ,OAAO,CAAC;EAC7C;EACA,OAAOA,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,sBAAsBA,CAAC3H,IAAI,EAAE4H,MAAM,EAAE;EACnD,IAAIR,CAAC,GAAGQ,MAAM,CAAC/H,MAAM;EACrB,IAAII,GAAG,GAAGD,IAAI,CAACH,MAAM;EACrB,KAAK,IAAI2H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvH,GAAG,EAAEuH,CAAC,EAAE,EAAE;IAC5B,IAAIC,CAAC,GAAGL,CAAC,GAAGnH,GAAG,GAAGuH,CAAC;IACnB,IAAIxH,IAAI,CAACwH,CAAC,CAAC,GAAGI,MAAM,CAACH,CAAC,CAAC,IAAIzH,IAAI,CAACwH,CAAC,CAAC,GAAG,CAAC,IAAIxH,IAAI,CAACwH,CAAC,CAAC,GAAGI,MAAM,CAACH,CAAC,CAAC,EAAE;MAC7D,MAAM,IAAI3F,KAAK,CAAC,uDAAuD,CAAC8E,MAAM,CAAC5G,IAAI,EAAE,wCAAwC,CAAC,CAAC4G,MAAM,CAAC3G,GAAG,EAAE,aAAa,CAAC,CAAC2G,MAAM,CAAC5G,IAAI,CAACwH,CAAC,CAAC,EAAE,WAAW,CAAC,CAACZ,MAAM,CAACgB,MAAM,CAACH,CAAC,CAAC,CAAC,CAAC;IAC3M;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,WAAWA,CAAC9H,KAAK,EAAE6H,MAAM,EAAE;EACzC,IAAIE,KAAK,GAAGtI,SAAS,CAACO,KAAK,CAAC;EAC5B,IAAIR,eAAe,CAACuI,KAAK,EAAEF,MAAM,CAAC,EAAE;IAClC,OAAO7H,KAAK;EACd;EACA4H,sBAAsB,CAACG,KAAK,EAAEF,MAAM,CAAC;EACrC,IAAIG,eAAe,GAAGf,cAAc,CAACc,KAAK,EAAEF,MAAM,CAAC;EACnD,IAAIR,CAAC,GAAGW,eAAe,CAAClI,MAAM;EAC9B,IAAImI,UAAU,GAAG,CAAC,GAAGrI,KAAK,CAACyH,CAAC,GAAGU,KAAK,CAACjI,MAAM,CAAC,CAAC0H,IAAI,CAAC,CAAC,CAAC,EAAE,GAAGO,KAAK,CAAC;EAC/D,IAAIhB,CAAC,GAAGmB,KAAK,CAAClI,KAAK,CAAC;EACpB;EACA,IAAI+H,KAAK,CAACjI,MAAM,GAAGuH,CAAC,EAAE;IACpBN,CAAC,GAAGvE,OAAO,CAACuE,CAAC,EAAEkB,UAAU,CAAC;IAC1BF,KAAK,GAAGtI,SAAS,CAACsH,CAAC,CAAC;EACtB;;EAEA;EACA,KAAK,IAAI7G,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGmH,CAAC,EAAEnH,GAAG,EAAE,EAAE;IAChC,IAAI6H,KAAK,CAAC7H,GAAG,CAAC,GAAG8H,eAAe,CAAC9H,GAAG,CAAC,EAAE;MACrC6G,CAAC,GAAGoB,OAAO,CAACpB,CAAC,EAAEiB,eAAe,CAAC9H,GAAG,CAAC,EAAEA,GAAG,CAAC;MACzC6H,KAAK,GAAGtI,SAAS,CAACsH,CAAC,CAAC;IACtB;EACF;EACA,OAAOA,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASqB,eAAeA,CAAA,EAAG;EAChC,KAAK,IAAIC,KAAK,GAAGzD,SAAS,CAAC9E,MAAM,EAAEgH,MAAM,GAAG,IAAIlH,KAAK,CAACyI,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;IAC/FxB,MAAM,CAACwB,KAAK,CAAC,GAAG1D,SAAS,CAAC0D,KAAK,CAAC;EAClC;EACA,IAAIxB,MAAM,CAAChH,MAAM,KAAK,CAAC,EAAE;IACvB,MAAM,IAAIiC,KAAK,CAAC,8DAA8D,CAAC;EACjF;EACA,IAAI+E,MAAM,CAAChH,MAAM,KAAK,CAAC,EAAE;IACvB,OAAOgH,MAAM,CAAC,CAAC,CAAC;EAClB;EACA,IAAIrE,KAAK,GAAGqE,MAAM,CAAC5B,GAAG,CAAC,UAAUlF,KAAK,EAAE;IACtC,OAAOP,SAAS,CAACO,KAAK,CAAC;EACzB,CAAC,CAAC;EACF,IAAIgI,eAAe,GAAGf,cAAc,CAAC,GAAGxE,KAAK,CAAC;EAC9C,IAAI8F,iBAAiB,GAAG,EAAE;EAC1BzB,MAAM,CAAC7F,OAAO,CAAC,UAAUjB,KAAK,EAAE;IAC9BuI,iBAAiB,CAAC1I,IAAI,CAACiI,WAAW,CAAC9H,KAAK,EAAEgI,eAAe,CAAC,CAAC;EAC7D,CAAC,CAAC;EACF,OAAOO,iBAAiB;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASJ,OAAOA,CAACK,cAAc,EAAEC,aAAa,EAAEC,YAAY,EAAE;EACnE,OAAO7B,MAAM,CAAC,GAAGjH,KAAK,CAAC6I,aAAa,CAAC,CAACjB,IAAI,CAACgB,cAAc,CAAC,EAAEE,YAAY,CAAC;AAC3E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,GAAGA,CAAC3I,KAAK,EAAEW,KAAK,EAAE;EAChC,IAAI,CAACf,KAAK,CAACT,OAAO,CAACa,KAAK,CAAC,EAAE;IACzB,MAAM,IAAI+B,KAAK,CAAC,gBAAgB,CAAC;EACnC;EACA,IAAI9B,IAAI,GAAGR,SAAS,CAACO,KAAK,CAAC;EAC3B,IAAIW,KAAK,CAACb,MAAM,KAAKG,IAAI,CAACH,MAAM,EAAE;IAChC,MAAM,IAAIR,cAAc,CAACqB,KAAK,CAACb,MAAM,EAAEG,IAAI,CAACH,MAAM,CAAC;EACrD;EACA,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,KAAK,CAACb,MAAM,EAAEJ,CAAC,EAAE,EAAE;IACrCyB,aAAa,CAACR,KAAK,CAACjB,CAAC,CAAC,EAAEO,IAAI,CAACP,CAAC,CAAC,CAAC;EAClC;EACA,OAAOiB,KAAK,CAAC+C,MAAM,CAAC,CAACkF,GAAG,EAAEhF,IAAI,KAAKgF,GAAG,CAAChF,IAAI,CAAC,EAAE5D,KAAK,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS6I,OAAOA,CAAC7I,KAAK,EAAEmF,QAAQ,EAAE;EACvC,IAAI2D,SAAS,GAAGlE,SAAS,CAAC9E,MAAM,GAAG,CAAC,IAAI8E,SAAS,CAAC,CAAC,CAAC,KAAKxD,SAAS,GAAGwD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACzF,IAAI5E,KAAK,CAACF,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,EAAE;EACX;EACA,IAAIgJ,SAAS,EAAE;IACb,OAAOC,YAAY,CAAC/I,KAAK,CAAC;EAC5B;EACA,IAAIW,KAAK,GAAG,EAAE;EACd,OAAOqI,qBAAqB,CAAChJ,KAAK,EAAE,CAAC,CAAC;EACtC,SAASgJ,qBAAqBA,CAACtI,KAAK,EAAEuI,KAAK,EAAE;IAC3C,IAAIrJ,KAAK,CAACT,OAAO,CAACuB,KAAK,CAAC,EAAE;MACxB,IAAI2G,CAAC,GAAG3G,KAAK,CAACZ,MAAM;MACpB,IAAIoJ,MAAM,GAAGtJ,KAAK,CAACyH,CAAC,CAAC;MACrB,KAAK,IAAIlH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkH,CAAC,EAAElH,CAAC,EAAE,EAAE;QAC1BQ,KAAK,CAACsI,KAAK,CAAC,GAAG9I,CAAC;QAChB+I,MAAM,CAAC/I,CAAC,CAAC,GAAG6I,qBAAqB,CAACtI,KAAK,CAACP,CAAC,CAAC,EAAE8I,KAAK,GAAG,CAAC,CAAC;MACxD;MACA,OAAOC,MAAM;IACf,CAAC,MAAM;MACL,OAAO/D,QAAQ,CAACzE,KAAK,EAAEC,KAAK,CAACwC,KAAK,CAAC,CAAC,EAAE8F,KAAK,CAAC,EAAEjJ,KAAK,CAAC;IACtD;EACF;EACA,SAAS+I,YAAYA,CAACrI,KAAK,EAAE;IAC3B,IAAId,KAAK,CAACT,OAAO,CAACuB,KAAK,CAAC,EAAE;MACxB,IAAI2G,CAAC,GAAG3G,KAAK,CAACZ,MAAM;MACpB,IAAIoJ,MAAM,GAAGtJ,KAAK,CAACyH,CAAC,CAAC;MACrB,KAAK,IAAIlH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkH,CAAC,EAAElH,CAAC,EAAE,EAAE;QAC1B+I,MAAM,CAAC/I,CAAC,CAAC,GAAG4I,YAAY,CAACrI,KAAK,CAACP,CAAC,CAAC,CAAC;MACpC;MACA,OAAO+I,MAAM;IACf,CAAC,MAAM;MACL,OAAO/D,QAAQ,CAACzE,KAAK,CAAC;IACxB;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASyI,WAAWA,CAACnJ,KAAK,EAAEmF,QAAQ,EAAE;EAC3C,IAAI2D,SAAS,GAAGlE,SAAS,CAAC9E,MAAM,GAAG,CAAC,IAAI8E,SAAS,CAAC,CAAC,CAAC,KAAKxD,SAAS,GAAGwD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACzF,IAAI5E,KAAK,CAACF,MAAM,KAAK,CAAC,EAAE;IACtB;EACF;EACA,IAAIgJ,SAAS,EAAE;IACbM,gBAAgB,CAACpJ,KAAK,CAAC;IACvB;EACF;EACA,IAAIW,KAAK,GAAG,EAAE;EACd0I,yBAAyB,CAACrJ,KAAK,EAAE,CAAC,CAAC;EACnC,SAASqJ,yBAAyBA,CAAC3I,KAAK,EAAEuI,KAAK,EAAE;IAC/C,IAAIrJ,KAAK,CAACT,OAAO,CAACuB,KAAK,CAAC,EAAE;MACxB,IAAI2G,CAAC,GAAG3G,KAAK,CAACZ,MAAM;MACpB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkH,CAAC,EAAElH,CAAC,EAAE,EAAE;QAC1BQ,KAAK,CAACsI,KAAK,CAAC,GAAG9I,CAAC;QAChBkJ,yBAAyB,CAAC3I,KAAK,CAACP,CAAC,CAAC,EAAE8I,KAAK,GAAG,CAAC,CAAC;MAChD;IACF,CAAC,MAAM;MACL9D,QAAQ,CAACzE,KAAK,EAAEC,KAAK,CAACwC,KAAK,CAAC,CAAC,EAAE8F,KAAK,CAAC,EAAEjJ,KAAK,CAAC;IAC/C;EACF;EACA,SAASoJ,gBAAgBA,CAAC1I,KAAK,EAAE;IAC/B,IAAId,KAAK,CAACT,OAAO,CAACuB,KAAK,CAAC,EAAE;MACxB,IAAI2G,CAAC,GAAG3G,KAAK,CAACZ,MAAM;MACpB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkH,CAAC,EAAElH,CAAC,EAAE,EAAE;QAC1BiJ,gBAAgB,CAAC1I,KAAK,CAACP,CAAC,CAAC,CAAC;MAC5B;IACF,CAAC,MAAM;MACLgF,QAAQ,CAACzE,KAAK,CAAC;IACjB;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASwH,KAAKA,CAAClI,KAAK,EAAE;EAC3B,OAAOjB,QAAQ,CAAC,EAAE,EAAEiB,KAAK,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}