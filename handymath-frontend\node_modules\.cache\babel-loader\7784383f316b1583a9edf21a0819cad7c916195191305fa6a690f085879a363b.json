{"ast": null, "code": "export var erfDocs = {\n  name: 'erf',\n  category: 'Special',\n  syntax: ['erf(x)'],\n  description: 'Compute the erf function of a value using a rational Chebyshev approximations for different intervals of x',\n  examples: ['erf(0.2)', 'erf(-0.5)', 'erf(4)'],\n  seealso: []\n};", "map": {"version": 3, "names": ["erfDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/special/erf.js"], "sourcesContent": ["export var erfDocs = {\n  name: 'erf',\n  category: 'Special',\n  syntax: ['erf(x)'],\n  description: 'Compute the erf function of a value using a rational Chebyshev approximations for different intervals of x',\n  examples: ['erf(0.2)', 'erf(-0.5)', 'erf(4)'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,4GAA4G;EACzHC,QAAQ,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,QAAQ,CAAC;EAC7CC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}