{"ast": null, "code": "/**\n * Bitwise and for Bignumbers\n *\n * Special Cases:\n *   N &  n =  N\n *   n &  0 =  0\n *   n & -1 =  n\n *   n &  n =  n\n *   I &  I =  I\n *  -I & -I = -I\n *   I & -I =  0\n *   I &  n =  n\n *   I & -n =  I\n *  -I &  n =  0\n *  -I & -n = -I\n *\n * @param {BigNumber} x\n * @param {BigNumber} y\n * @return {BigNumber} Result of `x` & `y`, is fully precise\n * @private\n */\nexport function bitAndBigNumber(x, y) {\n  if (x.isFinite() && !x.isInteger() || y.isFinite() && !y.isInteger()) {\n    throw new Error('Integers expected in function bitAnd');\n  }\n  var BigNumber = x.constructor;\n  if (x.isNaN() || y.isNaN()) {\n    return new BigNumber(NaN);\n  }\n  if (x.isZero() || y.eq(-1) || x.eq(y)) {\n    return x;\n  }\n  if (y.isZero() || x.eq(-1)) {\n    return y;\n  }\n  if (!x.isFinite() || !y.isFinite()) {\n    if (!x.isFinite() && !y.isFinite()) {\n      if (x.isNegative() === y.isNegative()) {\n        return x;\n      }\n      return new BigNumber(0);\n    }\n    if (!x.isFinite()) {\n      if (y.isNegative()) {\n        return x;\n      }\n      if (x.isNegative()) {\n        return new BigNumber(0);\n      }\n      return y;\n    }\n    if (!y.isFinite()) {\n      if (x.isNegative()) {\n        return y;\n      }\n      if (y.isNegative()) {\n        return new BigNumber(0);\n      }\n      return x;\n    }\n  }\n  return bitwise(x, y, function (a, b) {\n    return a & b;\n  });\n}\n\n/**\n * Bitwise not\n * @param {BigNumber} x\n * @return {BigNumber} Result of ~`x`, fully precise\n *\n */\nexport function bitNotBigNumber(x) {\n  if (x.isFinite() && !x.isInteger()) {\n    throw new Error('Integer expected in function bitNot');\n  }\n  var BigNumber = x.constructor;\n  var prevPrec = BigNumber.precision;\n  BigNumber.config({\n    precision: 1E9\n  });\n  var result = x.plus(new BigNumber(1));\n  result.s = -result.s || null;\n  BigNumber.config({\n    precision: prevPrec\n  });\n  return result;\n}\n\n/**\n * Bitwise OR for BigNumbers\n *\n * Special Cases:\n *   N |  n =  N\n *   n |  0 =  n\n *   n | -1 = -1\n *   n |  n =  n\n *   I |  I =  I\n *  -I | -I = -I\n *   I | -n = -1\n *   I | -I = -1\n *   I |  n =  I\n *  -I |  n = -I\n *  -I | -n = -n\n *\n * @param {BigNumber} x\n * @param {BigNumber} y\n * @return {BigNumber} Result of `x` | `y`, fully precise\n */\nexport function bitOrBigNumber(x, y) {\n  if (x.isFinite() && !x.isInteger() || y.isFinite() && !y.isInteger()) {\n    throw new Error('Integers expected in function bitOr');\n  }\n  var BigNumber = x.constructor;\n  if (x.isNaN() || y.isNaN()) {\n    return new BigNumber(NaN);\n  }\n  var negOne = new BigNumber(-1);\n  if (x.isZero() || y.eq(negOne) || x.eq(y)) {\n    return y;\n  }\n  if (y.isZero() || x.eq(negOne)) {\n    return x;\n  }\n  if (!x.isFinite() || !y.isFinite()) {\n    if (!x.isFinite() && !x.isNegative() && y.isNegative() || x.isNegative() && !y.isNegative() && !y.isFinite()) {\n      return negOne;\n    }\n    if (x.isNegative() && y.isNegative()) {\n      return x.isFinite() ? x : y;\n    }\n    return x.isFinite() ? y : x;\n  }\n  return bitwise(x, y, function (a, b) {\n    return a | b;\n  });\n}\n\n/**\n * Applies bitwise function to numbers\n * @param {BigNumber} x\n * @param {BigNumber} y\n * @param {function (a, b)} func\n * @return {BigNumber}\n */\nexport function bitwise(x, y, func) {\n  var BigNumber = x.constructor;\n  var xBits, yBits;\n  var xSign = +(x.s < 0);\n  var ySign = +(y.s < 0);\n  if (xSign) {\n    xBits = decCoefficientToBinaryString(bitNotBigNumber(x));\n    for (var i = 0; i < xBits.length; ++i) {\n      xBits[i] ^= 1;\n    }\n  } else {\n    xBits = decCoefficientToBinaryString(x);\n  }\n  if (ySign) {\n    yBits = decCoefficientToBinaryString(bitNotBigNumber(y));\n    for (var _i = 0; _i < yBits.length; ++_i) {\n      yBits[_i] ^= 1;\n    }\n  } else {\n    yBits = decCoefficientToBinaryString(y);\n  }\n  var minBits, maxBits, minSign;\n  if (xBits.length <= yBits.length) {\n    minBits = xBits;\n    maxBits = yBits;\n    minSign = xSign;\n  } else {\n    minBits = yBits;\n    maxBits = xBits;\n    minSign = ySign;\n  }\n  var shortLen = minBits.length;\n  var longLen = maxBits.length;\n  var expFuncVal = func(xSign, ySign) ^ 1;\n  var outVal = new BigNumber(expFuncVal ^ 1);\n  var twoPower = new BigNumber(1);\n  var two = new BigNumber(2);\n  var prevPrec = BigNumber.precision;\n  BigNumber.config({\n    precision: 1E9\n  });\n  while (shortLen > 0) {\n    if (func(minBits[--shortLen], maxBits[--longLen]) === expFuncVal) {\n      outVal = outVal.plus(twoPower);\n    }\n    twoPower = twoPower.times(two);\n  }\n  while (longLen > 0) {\n    if (func(minSign, maxBits[--longLen]) === expFuncVal) {\n      outVal = outVal.plus(twoPower);\n    }\n    twoPower = twoPower.times(two);\n  }\n  BigNumber.config({\n    precision: prevPrec\n  });\n  if (expFuncVal === 0) {\n    outVal.s = -outVal.s;\n  }\n  return outVal;\n}\n\n/* Extracted from decimal.js, and edited to specialize. */\nfunction decCoefficientToBinaryString(x) {\n  // Convert to string\n  var a = x.d; // array with digits\n  var r = a[0] + '';\n  for (var i = 1; i < a.length; ++i) {\n    var s = a[i] + '';\n    for (var z = 7 - s.length; z--;) {\n      s = '0' + s;\n    }\n    r += s;\n  }\n  var j = r.length;\n  while (r.charAt(j) === '0') {\n    j--;\n  }\n  var xe = x.e;\n  var str = r.slice(0, j + 1 || 1);\n  var strL = str.length;\n  if (xe > 0) {\n    if (++xe > strL) {\n      // Append zeros.\n      xe -= strL;\n      while (xe--) {\n        str += '0';\n      }\n    } else if (xe < strL) {\n      str = str.slice(0, xe) + '.' + str.slice(xe);\n    }\n  }\n\n  // Convert from base 10 (decimal) to base 2\n  var arr = [0];\n  for (var _i2 = 0; _i2 < str.length;) {\n    var arrL = arr.length;\n    while (arrL--) {\n      arr[arrL] *= 10;\n    }\n    arr[0] += parseInt(str.charAt(_i2++)); // convert to int\n    for (var _j = 0; _j < arr.length; ++_j) {\n      if (arr[_j] > 1) {\n        if (arr[_j + 1] === null || arr[_j + 1] === undefined) {\n          arr[_j + 1] = 0;\n        }\n        arr[_j + 1] += arr[_j] >> 1;\n        arr[_j] &= 1;\n      }\n    }\n  }\n  return arr.reverse();\n}\n\n/**\n * Bitwise XOR for BigNumbers\n *\n * Special Cases:\n *   N ^  n =  N\n *   n ^  0 =  n\n *   n ^  n =  0\n *   n ^ -1 = ~n\n *   I ^  n =  I\n *   I ^ -n = -I\n *   I ^ -I = -1\n *  -I ^  n = -I\n *  -I ^ -n =  I\n *\n * @param {BigNumber} x\n * @param {BigNumber} y\n * @return {BigNumber} Result of `x` ^ `y`, fully precise\n *\n */\nexport function bitXor(x, y) {\n  if (x.isFinite() && !x.isInteger() || y.isFinite() && !y.isInteger()) {\n    throw new Error('Integers expected in function bitXor');\n  }\n  var BigNumber = x.constructor;\n  if (x.isNaN() || y.isNaN()) {\n    return new BigNumber(NaN);\n  }\n  if (x.isZero()) {\n    return y;\n  }\n  if (y.isZero()) {\n    return x;\n  }\n  if (x.eq(y)) {\n    return new BigNumber(0);\n  }\n  var negOne = new BigNumber(-1);\n  if (x.eq(negOne)) {\n    return bitNotBigNumber(y);\n  }\n  if (y.eq(negOne)) {\n    return bitNotBigNumber(x);\n  }\n  if (!x.isFinite() || !y.isFinite()) {\n    if (!x.isFinite() && !y.isFinite()) {\n      return negOne;\n    }\n    return new BigNumber(x.isNegative() === y.isNegative() ? Infinity : -Infinity);\n  }\n  return bitwise(x, y, function (a, b) {\n    return a ^ b;\n  });\n}\n\n/**\n * Bitwise left shift\n *\n * Special Cases:\n *  n << -n = N\n *  n <<  N = N\n *  N <<  n = N\n *  n <<  0 = n\n *  0 <<  n = 0\n *  I <<  I = N\n *  I <<  n = I\n *  n <<  I = I\n *\n * @param {BigNumber} x\n * @param {BigNumber} y\n * @return {BigNumber} Result of `x` << `y`\n *\n */\nexport function leftShiftBigNumber(x, y) {\n  if (x.isFinite() && !x.isInteger() || y.isFinite() && !y.isInteger()) {\n    throw new Error('Integers expected in function leftShift');\n  }\n  var BigNumber = x.constructor;\n  if (x.isNaN() || y.isNaN() || y.isNegative() && !y.isZero()) {\n    return new BigNumber(NaN);\n  }\n  if (x.isZero() || y.isZero()) {\n    return x;\n  }\n  if (!x.isFinite() && !y.isFinite()) {\n    return new BigNumber(NaN);\n  }\n\n  // Math.pow(2, y) is fully precise for y < 55, and fast\n  if (y.lt(55)) {\n    return x.times(Math.pow(2, y.toNumber()) + '');\n  }\n  return x.times(new BigNumber(2).pow(y));\n}\n\n/*\n * Special Cases:\n *   n >> -n =  N\n *   n >>  N =  N\n *   N >>  n =  N\n *   I >>  I =  N\n *   n >>  0 =  n\n *   I >>  n =  I\n *  -I >>  n = -I\n *  -I >>  I = -I\n *   n >>  I =  I\n *  -n >>  I = -1\n *   0 >>  n =  0\n *\n * @param {BigNumber} value\n * @param {BigNumber} value\n * @return {BigNumber} Result of `x` >> `y`\n *\n */\nexport function rightArithShiftBigNumber(x, y) {\n  if (x.isFinite() && !x.isInteger() || y.isFinite() && !y.isInteger()) {\n    throw new Error('Integers expected in function rightArithShift');\n  }\n  var BigNumber = x.constructor;\n  if (x.isNaN() || y.isNaN() || y.isNegative() && !y.isZero()) {\n    return new BigNumber(NaN);\n  }\n  if (x.isZero() || y.isZero()) {\n    return x;\n  }\n  if (!y.isFinite()) {\n    if (x.isNegative()) {\n      return new BigNumber(-1);\n    }\n    if (!x.isFinite()) {\n      return new BigNumber(NaN);\n    }\n    return new BigNumber(0);\n  }\n\n  // Math.pow(2, y) is fully precise for y < 55, and fast\n  if (y.lt(55)) {\n    return x.div(Math.pow(2, y.toNumber()) + '').floor();\n  }\n  return x.div(new BigNumber(2).pow(y)).floor();\n}", "map": {"version": 3, "names": ["bitAndBigNumber", "x", "y", "isFinite", "isInteger", "Error", "BigNumber", "constructor", "isNaN", "NaN", "isZero", "eq", "isNegative", "bitwise", "a", "b", "bitNotBigNumber", "prevPrec", "precision", "config", "result", "plus", "s", "bitOrBigNumber", "negOne", "func", "xBits", "yBits", "xSign", "ySign", "decCoefficientToBinaryString", "i", "length", "_i", "minBits", "maxBits", "minSign", "shortLen", "long<PERSON>en", "expFuncVal", "outVal", "twoPower", "two", "times", "d", "r", "z", "j", "char<PERSON>t", "xe", "e", "str", "slice", "strL", "arr", "_i2", "arrL", "parseInt", "_j", "undefined", "reverse", "bitXor", "Infinity", "leftShiftBigNumber", "lt", "Math", "pow", "toNumber", "rightArithShiftBigNumber", "div", "floor"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/bignumber/bitwise.js"], "sourcesContent": ["/**\n * Bitwise and for Bignumbers\n *\n * Special Cases:\n *   N &  n =  N\n *   n &  0 =  0\n *   n & -1 =  n\n *   n &  n =  n\n *   I &  I =  I\n *  -I & -I = -I\n *   I & -I =  0\n *   I &  n =  n\n *   I & -n =  I\n *  -I &  n =  0\n *  -I & -n = -I\n *\n * @param {BigNumber} x\n * @param {BigNumber} y\n * @return {BigNumber} Result of `x` & `y`, is fully precise\n * @private\n */\nexport function bitAndBigNumber(x, y) {\n  if (x.isFinite() && !x.isInteger() || y.isFinite() && !y.isInteger()) {\n    throw new Error('Integers expected in function bitAnd');\n  }\n  var BigNumber = x.constructor;\n  if (x.isNaN() || y.isNaN()) {\n    return new BigNumber(NaN);\n  }\n  if (x.isZero() || y.eq(-1) || x.eq(y)) {\n    return x;\n  }\n  if (y.isZero() || x.eq(-1)) {\n    return y;\n  }\n  if (!x.isFinite() || !y.isFinite()) {\n    if (!x.isFinite() && !y.isFinite()) {\n      if (x.isNegative() === y.isNegative()) {\n        return x;\n      }\n      return new BigNumber(0);\n    }\n    if (!x.isFinite()) {\n      if (y.isNegative()) {\n        return x;\n      }\n      if (x.isNegative()) {\n        return new BigNumber(0);\n      }\n      return y;\n    }\n    if (!y.isFinite()) {\n      if (x.isNegative()) {\n        return y;\n      }\n      if (y.isNegative()) {\n        return new BigNumber(0);\n      }\n      return x;\n    }\n  }\n  return bitwise(x, y, function (a, b) {\n    return a & b;\n  });\n}\n\n/**\n * Bitwise not\n * @param {BigNumber} x\n * @return {BigNumber} Result of ~`x`, fully precise\n *\n */\nexport function bitNotBigNumber(x) {\n  if (x.isFinite() && !x.isInteger()) {\n    throw new Error('Integer expected in function bitNot');\n  }\n  var BigNumber = x.constructor;\n  var prevPrec = BigNumber.precision;\n  BigNumber.config({\n    precision: 1E9\n  });\n  var result = x.plus(new BigNumber(1));\n  result.s = -result.s || null;\n  BigNumber.config({\n    precision: prevPrec\n  });\n  return result;\n}\n\n/**\n * Bitwise OR for BigNumbers\n *\n * Special Cases:\n *   N |  n =  N\n *   n |  0 =  n\n *   n | -1 = -1\n *   n |  n =  n\n *   I |  I =  I\n *  -I | -I = -I\n *   I | -n = -1\n *   I | -I = -1\n *   I |  n =  I\n *  -I |  n = -I\n *  -I | -n = -n\n *\n * @param {BigNumber} x\n * @param {BigNumber} y\n * @return {BigNumber} Result of `x` | `y`, fully precise\n */\nexport function bitOrBigNumber(x, y) {\n  if (x.isFinite() && !x.isInteger() || y.isFinite() && !y.isInteger()) {\n    throw new Error('Integers expected in function bitOr');\n  }\n  var BigNumber = x.constructor;\n  if (x.isNaN() || y.isNaN()) {\n    return new BigNumber(NaN);\n  }\n  var negOne = new BigNumber(-1);\n  if (x.isZero() || y.eq(negOne) || x.eq(y)) {\n    return y;\n  }\n  if (y.isZero() || x.eq(negOne)) {\n    return x;\n  }\n  if (!x.isFinite() || !y.isFinite()) {\n    if (!x.isFinite() && !x.isNegative() && y.isNegative() || x.isNegative() && !y.isNegative() && !y.isFinite()) {\n      return negOne;\n    }\n    if (x.isNegative() && y.isNegative()) {\n      return x.isFinite() ? x : y;\n    }\n    return x.isFinite() ? y : x;\n  }\n  return bitwise(x, y, function (a, b) {\n    return a | b;\n  });\n}\n\n/**\n * Applies bitwise function to numbers\n * @param {BigNumber} x\n * @param {BigNumber} y\n * @param {function (a, b)} func\n * @return {BigNumber}\n */\nexport function bitwise(x, y, func) {\n  var BigNumber = x.constructor;\n  var xBits, yBits;\n  var xSign = +(x.s < 0);\n  var ySign = +(y.s < 0);\n  if (xSign) {\n    xBits = decCoefficientToBinaryString(bitNotBigNumber(x));\n    for (var i = 0; i < xBits.length; ++i) {\n      xBits[i] ^= 1;\n    }\n  } else {\n    xBits = decCoefficientToBinaryString(x);\n  }\n  if (ySign) {\n    yBits = decCoefficientToBinaryString(bitNotBigNumber(y));\n    for (var _i = 0; _i < yBits.length; ++_i) {\n      yBits[_i] ^= 1;\n    }\n  } else {\n    yBits = decCoefficientToBinaryString(y);\n  }\n  var minBits, maxBits, minSign;\n  if (xBits.length <= yBits.length) {\n    minBits = xBits;\n    maxBits = yBits;\n    minSign = xSign;\n  } else {\n    minBits = yBits;\n    maxBits = xBits;\n    minSign = ySign;\n  }\n  var shortLen = minBits.length;\n  var longLen = maxBits.length;\n  var expFuncVal = func(xSign, ySign) ^ 1;\n  var outVal = new BigNumber(expFuncVal ^ 1);\n  var twoPower = new BigNumber(1);\n  var two = new BigNumber(2);\n  var prevPrec = BigNumber.precision;\n  BigNumber.config({\n    precision: 1E9\n  });\n  while (shortLen > 0) {\n    if (func(minBits[--shortLen], maxBits[--longLen]) === expFuncVal) {\n      outVal = outVal.plus(twoPower);\n    }\n    twoPower = twoPower.times(two);\n  }\n  while (longLen > 0) {\n    if (func(minSign, maxBits[--longLen]) === expFuncVal) {\n      outVal = outVal.plus(twoPower);\n    }\n    twoPower = twoPower.times(two);\n  }\n  BigNumber.config({\n    precision: prevPrec\n  });\n  if (expFuncVal === 0) {\n    outVal.s = -outVal.s;\n  }\n  return outVal;\n}\n\n/* Extracted from decimal.js, and edited to specialize. */\nfunction decCoefficientToBinaryString(x) {\n  // Convert to string\n  var a = x.d; // array with digits\n  var r = a[0] + '';\n  for (var i = 1; i < a.length; ++i) {\n    var s = a[i] + '';\n    for (var z = 7 - s.length; z--;) {\n      s = '0' + s;\n    }\n    r += s;\n  }\n  var j = r.length;\n  while (r.charAt(j) === '0') {\n    j--;\n  }\n  var xe = x.e;\n  var str = r.slice(0, j + 1 || 1);\n  var strL = str.length;\n  if (xe > 0) {\n    if (++xe > strL) {\n      // Append zeros.\n      xe -= strL;\n      while (xe--) {\n        str += '0';\n      }\n    } else if (xe < strL) {\n      str = str.slice(0, xe) + '.' + str.slice(xe);\n    }\n  }\n\n  // Convert from base 10 (decimal) to base 2\n  var arr = [0];\n  for (var _i2 = 0; _i2 < str.length;) {\n    var arrL = arr.length;\n    while (arrL--) {\n      arr[arrL] *= 10;\n    }\n    arr[0] += parseInt(str.charAt(_i2++)); // convert to int\n    for (var _j = 0; _j < arr.length; ++_j) {\n      if (arr[_j] > 1) {\n        if (arr[_j + 1] === null || arr[_j + 1] === undefined) {\n          arr[_j + 1] = 0;\n        }\n        arr[_j + 1] += arr[_j] >> 1;\n        arr[_j] &= 1;\n      }\n    }\n  }\n  return arr.reverse();\n}\n\n/**\n * Bitwise XOR for BigNumbers\n *\n * Special Cases:\n *   N ^  n =  N\n *   n ^  0 =  n\n *   n ^  n =  0\n *   n ^ -1 = ~n\n *   I ^  n =  I\n *   I ^ -n = -I\n *   I ^ -I = -1\n *  -I ^  n = -I\n *  -I ^ -n =  I\n *\n * @param {BigNumber} x\n * @param {BigNumber} y\n * @return {BigNumber} Result of `x` ^ `y`, fully precise\n *\n */\nexport function bitXor(x, y) {\n  if (x.isFinite() && !x.isInteger() || y.isFinite() && !y.isInteger()) {\n    throw new Error('Integers expected in function bitXor');\n  }\n  var BigNumber = x.constructor;\n  if (x.isNaN() || y.isNaN()) {\n    return new BigNumber(NaN);\n  }\n  if (x.isZero()) {\n    return y;\n  }\n  if (y.isZero()) {\n    return x;\n  }\n  if (x.eq(y)) {\n    return new BigNumber(0);\n  }\n  var negOne = new BigNumber(-1);\n  if (x.eq(negOne)) {\n    return bitNotBigNumber(y);\n  }\n  if (y.eq(negOne)) {\n    return bitNotBigNumber(x);\n  }\n  if (!x.isFinite() || !y.isFinite()) {\n    if (!x.isFinite() && !y.isFinite()) {\n      return negOne;\n    }\n    return new BigNumber(x.isNegative() === y.isNegative() ? Infinity : -Infinity);\n  }\n  return bitwise(x, y, function (a, b) {\n    return a ^ b;\n  });\n}\n\n/**\n * Bitwise left shift\n *\n * Special Cases:\n *  n << -n = N\n *  n <<  N = N\n *  N <<  n = N\n *  n <<  0 = n\n *  0 <<  n = 0\n *  I <<  I = N\n *  I <<  n = I\n *  n <<  I = I\n *\n * @param {BigNumber} x\n * @param {BigNumber} y\n * @return {BigNumber} Result of `x` << `y`\n *\n */\nexport function leftShiftBigNumber(x, y) {\n  if (x.isFinite() && !x.isInteger() || y.isFinite() && !y.isInteger()) {\n    throw new Error('Integers expected in function leftShift');\n  }\n  var BigNumber = x.constructor;\n  if (x.isNaN() || y.isNaN() || y.isNegative() && !y.isZero()) {\n    return new BigNumber(NaN);\n  }\n  if (x.isZero() || y.isZero()) {\n    return x;\n  }\n  if (!x.isFinite() && !y.isFinite()) {\n    return new BigNumber(NaN);\n  }\n\n  // Math.pow(2, y) is fully precise for y < 55, and fast\n  if (y.lt(55)) {\n    return x.times(Math.pow(2, y.toNumber()) + '');\n  }\n  return x.times(new BigNumber(2).pow(y));\n}\n\n/*\n * Special Cases:\n *   n >> -n =  N\n *   n >>  N =  N\n *   N >>  n =  N\n *   I >>  I =  N\n *   n >>  0 =  n\n *   I >>  n =  I\n *  -I >>  n = -I\n *  -I >>  I = -I\n *   n >>  I =  I\n *  -n >>  I = -1\n *   0 >>  n =  0\n *\n * @param {BigNumber} value\n * @param {BigNumber} value\n * @return {BigNumber} Result of `x` >> `y`\n *\n */\nexport function rightArithShiftBigNumber(x, y) {\n  if (x.isFinite() && !x.isInteger() || y.isFinite() && !y.isInteger()) {\n    throw new Error('Integers expected in function rightArithShift');\n  }\n  var BigNumber = x.constructor;\n  if (x.isNaN() || y.isNaN() || y.isNegative() && !y.isZero()) {\n    return new BigNumber(NaN);\n  }\n  if (x.isZero() || y.isZero()) {\n    return x;\n  }\n  if (!y.isFinite()) {\n    if (x.isNegative()) {\n      return new BigNumber(-1);\n    }\n    if (!x.isFinite()) {\n      return new BigNumber(NaN);\n    }\n    return new BigNumber(0);\n  }\n\n  // Math.pow(2, y) is fully precise for y < 55, and fast\n  if (y.lt(55)) {\n    return x.div(Math.pow(2, y.toNumber()) + '').floor();\n  }\n  return x.div(new BigNumber(2).pow(y)).floor();\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACpC,IAAID,CAAC,CAACE,QAAQ,CAAC,CAAC,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAAC,IAAIF,CAAC,CAACC,QAAQ,CAAC,CAAC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,CAAC,EAAE;IACpE,MAAM,IAAIC,KAAK,CAAC,sCAAsC,CAAC;EACzD;EACA,IAAIC,SAAS,GAAGL,CAAC,CAACM,WAAW;EAC7B,IAAIN,CAAC,CAACO,KAAK,CAAC,CAAC,IAAIN,CAAC,CAACM,KAAK,CAAC,CAAC,EAAE;IAC1B,OAAO,IAAIF,SAAS,CAACG,GAAG,CAAC;EAC3B;EACA,IAAIR,CAAC,CAACS,MAAM,CAAC,CAAC,IAAIR,CAAC,CAACS,EAAE,CAAC,CAAC,CAAC,CAAC,IAAIV,CAAC,CAACU,EAAE,CAACT,CAAC,CAAC,EAAE;IACrC,OAAOD,CAAC;EACV;EACA,IAAIC,CAAC,CAACQ,MAAM,CAAC,CAAC,IAAIT,CAAC,CAACU,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1B,OAAOT,CAAC;EACV;EACA,IAAI,CAACD,CAAC,CAACE,QAAQ,CAAC,CAAC,IAAI,CAACD,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE;IAClC,IAAI,CAACF,CAAC,CAACE,QAAQ,CAAC,CAAC,IAAI,CAACD,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE;MAClC,IAAIF,CAAC,CAACW,UAAU,CAAC,CAAC,KAAKV,CAAC,CAACU,UAAU,CAAC,CAAC,EAAE;QACrC,OAAOX,CAAC;MACV;MACA,OAAO,IAAIK,SAAS,CAAC,CAAC,CAAC;IACzB;IACA,IAAI,CAACL,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE;MACjB,IAAID,CAAC,CAACU,UAAU,CAAC,CAAC,EAAE;QAClB,OAAOX,CAAC;MACV;MACA,IAAIA,CAAC,CAACW,UAAU,CAAC,CAAC,EAAE;QAClB,OAAO,IAAIN,SAAS,CAAC,CAAC,CAAC;MACzB;MACA,OAAOJ,CAAC;IACV;IACA,IAAI,CAACA,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE;MACjB,IAAIF,CAAC,CAACW,UAAU,CAAC,CAAC,EAAE;QAClB,OAAOV,CAAC;MACV;MACA,IAAIA,CAAC,CAACU,UAAU,CAAC,CAAC,EAAE;QAClB,OAAO,IAAIN,SAAS,CAAC,CAAC,CAAC;MACzB;MACA,OAAOL,CAAC;IACV;EACF;EACA,OAAOY,OAAO,CAACZ,CAAC,EAAEC,CAAC,EAAE,UAAUY,CAAC,EAAEC,CAAC,EAAE;IACnC,OAAOD,CAAC,GAAGC,CAAC;EACd,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACf,CAAC,EAAE;EACjC,IAAIA,CAAC,CAACE,QAAQ,CAAC,CAAC,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAAC,EAAE;IAClC,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;EACxD;EACA,IAAIC,SAAS,GAAGL,CAAC,CAACM,WAAW;EAC7B,IAAIU,QAAQ,GAAGX,SAAS,CAACY,SAAS;EAClCZ,SAAS,CAACa,MAAM,CAAC;IACfD,SAAS,EAAE;EACb,CAAC,CAAC;EACF,IAAIE,MAAM,GAAGnB,CAAC,CAACoB,IAAI,CAAC,IAAIf,SAAS,CAAC,CAAC,CAAC,CAAC;EACrCc,MAAM,CAACE,CAAC,GAAG,CAACF,MAAM,CAACE,CAAC,IAAI,IAAI;EAC5BhB,SAAS,CAACa,MAAM,CAAC;IACfD,SAAS,EAAED;EACb,CAAC,CAAC;EACF,OAAOG,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,cAAcA,CAACtB,CAAC,EAAEC,CAAC,EAAE;EACnC,IAAID,CAAC,CAACE,QAAQ,CAAC,CAAC,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAAC,IAAIF,CAAC,CAACC,QAAQ,CAAC,CAAC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,CAAC,EAAE;IACpE,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;EACxD;EACA,IAAIC,SAAS,GAAGL,CAAC,CAACM,WAAW;EAC7B,IAAIN,CAAC,CAACO,KAAK,CAAC,CAAC,IAAIN,CAAC,CAACM,KAAK,CAAC,CAAC,EAAE;IAC1B,OAAO,IAAIF,SAAS,CAACG,GAAG,CAAC;EAC3B;EACA,IAAIe,MAAM,GAAG,IAAIlB,SAAS,CAAC,CAAC,CAAC,CAAC;EAC9B,IAAIL,CAAC,CAACS,MAAM,CAAC,CAAC,IAAIR,CAAC,CAACS,EAAE,CAACa,MAAM,CAAC,IAAIvB,CAAC,CAACU,EAAE,CAACT,CAAC,CAAC,EAAE;IACzC,OAAOA,CAAC;EACV;EACA,IAAIA,CAAC,CAACQ,MAAM,CAAC,CAAC,IAAIT,CAAC,CAACU,EAAE,CAACa,MAAM,CAAC,EAAE;IAC9B,OAAOvB,CAAC;EACV;EACA,IAAI,CAACA,CAAC,CAACE,QAAQ,CAAC,CAAC,IAAI,CAACD,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE;IAClC,IAAI,CAACF,CAAC,CAACE,QAAQ,CAAC,CAAC,IAAI,CAACF,CAAC,CAACW,UAAU,CAAC,CAAC,IAAIV,CAAC,CAACU,UAAU,CAAC,CAAC,IAAIX,CAAC,CAACW,UAAU,CAAC,CAAC,IAAI,CAACV,CAAC,CAACU,UAAU,CAAC,CAAC,IAAI,CAACV,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE;MAC5G,OAAOqB,MAAM;IACf;IACA,IAAIvB,CAAC,CAACW,UAAU,CAAC,CAAC,IAAIV,CAAC,CAACU,UAAU,CAAC,CAAC,EAAE;MACpC,OAAOX,CAAC,CAACE,QAAQ,CAAC,CAAC,GAAGF,CAAC,GAAGC,CAAC;IAC7B;IACA,OAAOD,CAAC,CAACE,QAAQ,CAAC,CAAC,GAAGD,CAAC,GAAGD,CAAC;EAC7B;EACA,OAAOY,OAAO,CAACZ,CAAC,EAAEC,CAAC,EAAE,UAAUY,CAAC,EAAEC,CAAC,EAAE;IACnC,OAAOD,CAAC,GAAGC,CAAC;EACd,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASF,OAAOA,CAACZ,CAAC,EAAEC,CAAC,EAAEuB,IAAI,EAAE;EAClC,IAAInB,SAAS,GAAGL,CAAC,CAACM,WAAW;EAC7B,IAAImB,KAAK,EAAEC,KAAK;EAChB,IAAIC,KAAK,GAAG,EAAE3B,CAAC,CAACqB,CAAC,GAAG,CAAC,CAAC;EACtB,IAAIO,KAAK,GAAG,EAAE3B,CAAC,CAACoB,CAAC,GAAG,CAAC,CAAC;EACtB,IAAIM,KAAK,EAAE;IACTF,KAAK,GAAGI,4BAA4B,CAACd,eAAe,CAACf,CAAC,CAAC,CAAC;IACxD,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACM,MAAM,EAAE,EAAED,CAAC,EAAE;MACrCL,KAAK,CAACK,CAAC,CAAC,IAAI,CAAC;IACf;EACF,CAAC,MAAM;IACLL,KAAK,GAAGI,4BAA4B,CAAC7B,CAAC,CAAC;EACzC;EACA,IAAI4B,KAAK,EAAE;IACTF,KAAK,GAAGG,4BAA4B,CAACd,eAAe,CAACd,CAAC,CAAC,CAAC;IACxD,KAAK,IAAI+B,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGN,KAAK,CAACK,MAAM,EAAE,EAAEC,EAAE,EAAE;MACxCN,KAAK,CAACM,EAAE,CAAC,IAAI,CAAC;IAChB;EACF,CAAC,MAAM;IACLN,KAAK,GAAGG,4BAA4B,CAAC5B,CAAC,CAAC;EACzC;EACA,IAAIgC,OAAO,EAAEC,OAAO,EAAEC,OAAO;EAC7B,IAAIV,KAAK,CAACM,MAAM,IAAIL,KAAK,CAACK,MAAM,EAAE;IAChCE,OAAO,GAAGR,KAAK;IACfS,OAAO,GAAGR,KAAK;IACfS,OAAO,GAAGR,KAAK;EACjB,CAAC,MAAM;IACLM,OAAO,GAAGP,KAAK;IACfQ,OAAO,GAAGT,KAAK;IACfU,OAAO,GAAGP,KAAK;EACjB;EACA,IAAIQ,QAAQ,GAAGH,OAAO,CAACF,MAAM;EAC7B,IAAIM,OAAO,GAAGH,OAAO,CAACH,MAAM;EAC5B,IAAIO,UAAU,GAAGd,IAAI,CAACG,KAAK,EAAEC,KAAK,CAAC,GAAG,CAAC;EACvC,IAAIW,MAAM,GAAG,IAAIlC,SAAS,CAACiC,UAAU,GAAG,CAAC,CAAC;EAC1C,IAAIE,QAAQ,GAAG,IAAInC,SAAS,CAAC,CAAC,CAAC;EAC/B,IAAIoC,GAAG,GAAG,IAAIpC,SAAS,CAAC,CAAC,CAAC;EAC1B,IAAIW,QAAQ,GAAGX,SAAS,CAACY,SAAS;EAClCZ,SAAS,CAACa,MAAM,CAAC;IACfD,SAAS,EAAE;EACb,CAAC,CAAC;EACF,OAAOmB,QAAQ,GAAG,CAAC,EAAE;IACnB,IAAIZ,IAAI,CAACS,OAAO,CAAC,EAAEG,QAAQ,CAAC,EAAEF,OAAO,CAAC,EAAEG,OAAO,CAAC,CAAC,KAAKC,UAAU,EAAE;MAChEC,MAAM,GAAGA,MAAM,CAACnB,IAAI,CAACoB,QAAQ,CAAC;IAChC;IACAA,QAAQ,GAAGA,QAAQ,CAACE,KAAK,CAACD,GAAG,CAAC;EAChC;EACA,OAAOJ,OAAO,GAAG,CAAC,EAAE;IAClB,IAAIb,IAAI,CAACW,OAAO,EAAED,OAAO,CAAC,EAAEG,OAAO,CAAC,CAAC,KAAKC,UAAU,EAAE;MACpDC,MAAM,GAAGA,MAAM,CAACnB,IAAI,CAACoB,QAAQ,CAAC;IAChC;IACAA,QAAQ,GAAGA,QAAQ,CAACE,KAAK,CAACD,GAAG,CAAC;EAChC;EACApC,SAAS,CAACa,MAAM,CAAC;IACfD,SAAS,EAAED;EACb,CAAC,CAAC;EACF,IAAIsB,UAAU,KAAK,CAAC,EAAE;IACpBC,MAAM,CAAClB,CAAC,GAAG,CAACkB,MAAM,CAAClB,CAAC;EACtB;EACA,OAAOkB,MAAM;AACf;;AAEA;AACA,SAASV,4BAA4BA,CAAC7B,CAAC,EAAE;EACvC;EACA,IAAIa,CAAC,GAAGb,CAAC,CAAC2C,CAAC,CAAC,CAAC;EACb,IAAIC,CAAC,GAAG/B,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;EACjB,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,CAAC,CAACkB,MAAM,EAAE,EAAED,CAAC,EAAE;IACjC,IAAIT,CAAC,GAAGR,CAAC,CAACiB,CAAC,CAAC,GAAG,EAAE;IACjB,KAAK,IAAIe,CAAC,GAAG,CAAC,GAAGxB,CAAC,CAACU,MAAM,EAAEc,CAAC,EAAE,GAAG;MAC/BxB,CAAC,GAAG,GAAG,GAAGA,CAAC;IACb;IACAuB,CAAC,IAAIvB,CAAC;EACR;EACA,IAAIyB,CAAC,GAAGF,CAAC,CAACb,MAAM;EAChB,OAAOa,CAAC,CAACG,MAAM,CAACD,CAAC,CAAC,KAAK,GAAG,EAAE;IAC1BA,CAAC,EAAE;EACL;EACA,IAAIE,EAAE,GAAGhD,CAAC,CAACiD,CAAC;EACZ,IAAIC,GAAG,GAAGN,CAAC,CAACO,KAAK,CAAC,CAAC,EAAEL,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;EAChC,IAAIM,IAAI,GAAGF,GAAG,CAACnB,MAAM;EACrB,IAAIiB,EAAE,GAAG,CAAC,EAAE;IACV,IAAI,EAAEA,EAAE,GAAGI,IAAI,EAAE;MACf;MACAJ,EAAE,IAAII,IAAI;MACV,OAAOJ,EAAE,EAAE,EAAE;QACXE,GAAG,IAAI,GAAG;MACZ;IACF,CAAC,MAAM,IAAIF,EAAE,GAAGI,IAAI,EAAE;MACpBF,GAAG,GAAGA,GAAG,CAACC,KAAK,CAAC,CAAC,EAAEH,EAAE,CAAC,GAAG,GAAG,GAAGE,GAAG,CAACC,KAAK,CAACH,EAAE,CAAC;IAC9C;EACF;;EAEA;EACA,IAAIK,GAAG,GAAG,CAAC,CAAC,CAAC;EACb,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGJ,GAAG,CAACnB,MAAM,GAAG;IACnC,IAAIwB,IAAI,GAAGF,GAAG,CAACtB,MAAM;IACrB,OAAOwB,IAAI,EAAE,EAAE;MACbF,GAAG,CAACE,IAAI,CAAC,IAAI,EAAE;IACjB;IACAF,GAAG,CAAC,CAAC,CAAC,IAAIG,QAAQ,CAACN,GAAG,CAACH,MAAM,CAACO,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IACvC,KAAK,IAAIG,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGJ,GAAG,CAACtB,MAAM,EAAE,EAAE0B,EAAE,EAAE;MACtC,IAAIJ,GAAG,CAACI,EAAE,CAAC,GAAG,CAAC,EAAE;QACf,IAAIJ,GAAG,CAACI,EAAE,GAAG,CAAC,CAAC,KAAK,IAAI,IAAIJ,GAAG,CAACI,EAAE,GAAG,CAAC,CAAC,KAAKC,SAAS,EAAE;UACrDL,GAAG,CAACI,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;QACjB;QACAJ,GAAG,CAACI,EAAE,GAAG,CAAC,CAAC,IAAIJ,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC;QAC3BJ,GAAG,CAACI,EAAE,CAAC,IAAI,CAAC;MACd;IACF;EACF;EACA,OAAOJ,GAAG,CAACM,OAAO,CAAC,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAAC5D,CAAC,EAAEC,CAAC,EAAE;EAC3B,IAAID,CAAC,CAACE,QAAQ,CAAC,CAAC,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAAC,IAAIF,CAAC,CAACC,QAAQ,CAAC,CAAC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,CAAC,EAAE;IACpE,MAAM,IAAIC,KAAK,CAAC,sCAAsC,CAAC;EACzD;EACA,IAAIC,SAAS,GAAGL,CAAC,CAACM,WAAW;EAC7B,IAAIN,CAAC,CAACO,KAAK,CAAC,CAAC,IAAIN,CAAC,CAACM,KAAK,CAAC,CAAC,EAAE;IAC1B,OAAO,IAAIF,SAAS,CAACG,GAAG,CAAC;EAC3B;EACA,IAAIR,CAAC,CAACS,MAAM,CAAC,CAAC,EAAE;IACd,OAAOR,CAAC;EACV;EACA,IAAIA,CAAC,CAACQ,MAAM,CAAC,CAAC,EAAE;IACd,OAAOT,CAAC;EACV;EACA,IAAIA,CAAC,CAACU,EAAE,CAACT,CAAC,CAAC,EAAE;IACX,OAAO,IAAII,SAAS,CAAC,CAAC,CAAC;EACzB;EACA,IAAIkB,MAAM,GAAG,IAAIlB,SAAS,CAAC,CAAC,CAAC,CAAC;EAC9B,IAAIL,CAAC,CAACU,EAAE,CAACa,MAAM,CAAC,EAAE;IAChB,OAAOR,eAAe,CAACd,CAAC,CAAC;EAC3B;EACA,IAAIA,CAAC,CAACS,EAAE,CAACa,MAAM,CAAC,EAAE;IAChB,OAAOR,eAAe,CAACf,CAAC,CAAC;EAC3B;EACA,IAAI,CAACA,CAAC,CAACE,QAAQ,CAAC,CAAC,IAAI,CAACD,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE;IAClC,IAAI,CAACF,CAAC,CAACE,QAAQ,CAAC,CAAC,IAAI,CAACD,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE;MAClC,OAAOqB,MAAM;IACf;IACA,OAAO,IAAIlB,SAAS,CAACL,CAAC,CAACW,UAAU,CAAC,CAAC,KAAKV,CAAC,CAACU,UAAU,CAAC,CAAC,GAAGkD,QAAQ,GAAG,CAACA,QAAQ,CAAC;EAChF;EACA,OAAOjD,OAAO,CAACZ,CAAC,EAAEC,CAAC,EAAE,UAAUY,CAAC,EAAEC,CAAC,EAAE;IACnC,OAAOD,CAAC,GAAGC,CAAC;EACd,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgD,kBAAkBA,CAAC9D,CAAC,EAAEC,CAAC,EAAE;EACvC,IAAID,CAAC,CAACE,QAAQ,CAAC,CAAC,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAAC,IAAIF,CAAC,CAACC,QAAQ,CAAC,CAAC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,CAAC,EAAE;IACpE,MAAM,IAAIC,KAAK,CAAC,yCAAyC,CAAC;EAC5D;EACA,IAAIC,SAAS,GAAGL,CAAC,CAACM,WAAW;EAC7B,IAAIN,CAAC,CAACO,KAAK,CAAC,CAAC,IAAIN,CAAC,CAACM,KAAK,CAAC,CAAC,IAAIN,CAAC,CAACU,UAAU,CAAC,CAAC,IAAI,CAACV,CAAC,CAACQ,MAAM,CAAC,CAAC,EAAE;IAC3D,OAAO,IAAIJ,SAAS,CAACG,GAAG,CAAC;EAC3B;EACA,IAAIR,CAAC,CAACS,MAAM,CAAC,CAAC,IAAIR,CAAC,CAACQ,MAAM,CAAC,CAAC,EAAE;IAC5B,OAAOT,CAAC;EACV;EACA,IAAI,CAACA,CAAC,CAACE,QAAQ,CAAC,CAAC,IAAI,CAACD,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE;IAClC,OAAO,IAAIG,SAAS,CAACG,GAAG,CAAC;EAC3B;;EAEA;EACA,IAAIP,CAAC,CAAC8D,EAAE,CAAC,EAAE,CAAC,EAAE;IACZ,OAAO/D,CAAC,CAAC0C,KAAK,CAACsB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEhE,CAAC,CAACiE,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;EAChD;EACA,OAAOlE,CAAC,CAAC0C,KAAK,CAAC,IAAIrC,SAAS,CAAC,CAAC,CAAC,CAAC4D,GAAG,CAAChE,CAAC,CAAC,CAAC;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASkE,wBAAwBA,CAACnE,CAAC,EAAEC,CAAC,EAAE;EAC7C,IAAID,CAAC,CAACE,QAAQ,CAAC,CAAC,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAAC,IAAIF,CAAC,CAACC,QAAQ,CAAC,CAAC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,CAAC,EAAE;IACpE,MAAM,IAAIC,KAAK,CAAC,+CAA+C,CAAC;EAClE;EACA,IAAIC,SAAS,GAAGL,CAAC,CAACM,WAAW;EAC7B,IAAIN,CAAC,CAACO,KAAK,CAAC,CAAC,IAAIN,CAAC,CAACM,KAAK,CAAC,CAAC,IAAIN,CAAC,CAACU,UAAU,CAAC,CAAC,IAAI,CAACV,CAAC,CAACQ,MAAM,CAAC,CAAC,EAAE;IAC3D,OAAO,IAAIJ,SAAS,CAACG,GAAG,CAAC;EAC3B;EACA,IAAIR,CAAC,CAACS,MAAM,CAAC,CAAC,IAAIR,CAAC,CAACQ,MAAM,CAAC,CAAC,EAAE;IAC5B,OAAOT,CAAC;EACV;EACA,IAAI,CAACC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE;IACjB,IAAIF,CAAC,CAACW,UAAU,CAAC,CAAC,EAAE;MAClB,OAAO,IAAIN,SAAS,CAAC,CAAC,CAAC,CAAC;IAC1B;IACA,IAAI,CAACL,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE;MACjB,OAAO,IAAIG,SAAS,CAACG,GAAG,CAAC;IAC3B;IACA,OAAO,IAAIH,SAAS,CAAC,CAAC,CAAC;EACzB;;EAEA;EACA,IAAIJ,CAAC,CAAC8D,EAAE,CAAC,EAAE,CAAC,EAAE;IACZ,OAAO/D,CAAC,CAACoE,GAAG,CAACJ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEhE,CAAC,CAACiE,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAACG,KAAK,CAAC,CAAC;EACtD;EACA,OAAOrE,CAAC,CAACoE,GAAG,CAAC,IAAI/D,SAAS,CAAC,CAAC,CAAC,CAAC4D,GAAG,CAAChE,CAAC,CAAC,CAAC,CAACoE,KAAK,CAAC,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}