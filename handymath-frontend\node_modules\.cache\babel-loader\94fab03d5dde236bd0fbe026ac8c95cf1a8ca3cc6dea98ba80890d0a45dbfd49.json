{"ast": null, "code": "export var version = '14.5.2';\n// Note: This file is automatically generated when building math.js.\n// Changes made in this file will be overwritten.", "map": {"version": 3, "names": ["version"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/version.js"], "sourcesContent": ["export var version = '14.5.2';\n// Note: This file is automatically generated when building math.js.\n// Changes made in this file will be overwritten."], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG,QAAQ;AAC7B;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}