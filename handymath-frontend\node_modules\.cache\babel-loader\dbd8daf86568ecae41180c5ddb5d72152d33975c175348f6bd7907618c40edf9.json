{"ast": null, "code": "export var stirlingS2Docs = {\n  name: 'stirlingS2',\n  category: 'Combinatorics',\n  syntax: ['stirlingS2(n, k)'],\n  description: 'he Stirling numbers of the second kind, counts the number of ways to partition a set of n labelled objects into k nonempty unlabelled subsets. `stirlingS2` only takes integer arguments. The following condition must be enforced: k <= n. If n = k or k = 1, then s(n,k) = 1.',\n  examples: ['stirlingS2(5, 3)'],\n  seealso: ['bellNumbers']\n};", "map": {"version": 3, "names": ["stirlingS2Docs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/combinatorics/stirlingS2.js"], "sourcesContent": ["export var stirlingS2Docs = {\n  name: 'stirlingS2',\n  category: 'Combinatorics',\n  syntax: ['stirlingS2(n, k)'],\n  description: 'he Stirling numbers of the second kind, counts the number of ways to partition a set of n labelled objects into k nonempty unlabelled subsets. `stirlingS2` only takes integer arguments. The following condition must be enforced: k <= n. If n = k or k = 1, then s(n,k) = 1.',\n  examples: ['stirlingS2(5, 3)'],\n  seealso: ['bellNumbers']\n};"], "mappings": "AAAA,OAAO,IAAIA,cAAc,GAAG;EAC1BC,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,CAAC,kBAAkB,CAAC;EAC5BC,WAAW,EAAE,iRAAiR;EAC9RC,QAAQ,EAAE,CAAC,kBAAkB,CAAC;EAC9BC,OAAO,EAAE,CAAC,aAAa;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}