{"ast": null, "code": "export var complexDocs = {\n  name: 'complex',\n  category: 'Construction',\n  syntax: ['complex()', 'complex(re, im)', 'complex(string)'],\n  description: 'Create a complex number.',\n  examples: ['complex()', 'complex(2, 3)', 'complex(\"7 - 2i\")'],\n  seealso: ['bignumber', 'boolean', 'index', 'matrix', 'number', 'string', 'unit']\n};", "map": {"version": 3, "names": ["complexDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/construction/complex.js"], "sourcesContent": ["export var complexDocs = {\n  name: 'complex',\n  category: 'Construction',\n  syntax: ['complex()', 'complex(re, im)', 'complex(string)'],\n  description: 'Create a complex number.',\n  examples: ['complex()', 'complex(2, 3)', 'complex(\"7 - 2i\")'],\n  seealso: ['bignumber', 'boolean', 'index', 'matrix', 'number', 'string', 'unit']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,CAAC;EAC3DC,WAAW,EAAE,0BAA0B;EACvCC,QAAQ,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,mBAAmB,CAAC;EAC7DC,OAAO,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM;AACjF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}