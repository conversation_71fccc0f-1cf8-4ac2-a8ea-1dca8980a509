{"ast": null, "code": "import { px } from '../../../value/types/numbers/units.mjs';\nconst dashKeys = {\n  offset: \"stroke-dashoffset\",\n  array: \"stroke-dasharray\"\n};\nconst camelKeys = {\n  offset: \"strokeDashoffset\",\n  array: \"strokeDasharray\"\n};\n/**\n * Build SVG path properties. Uses the path's measured length to convert\n * our custom pathLength, pathSpacing and pathOffset into stroke-dashoffset\n * and stroke-dasharray attributes.\n *\n * This function is mutative to reduce per-frame GC.\n */\nfunction buildSVGPath(attrs, length) {\n  let spacing = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  let offset = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n  let useDashCase = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n  // Normalise path length by setting SVG attribute pathLength to 1\n  attrs.pathLength = 1;\n  // We use dash case when setting attributes directly to the DOM node and camel case\n  // when defining props on a React component.\n  const keys = useDashCase ? dashKeys : camelKeys;\n  // Build the dash offset\n  attrs[keys.offset] = px.transform(-offset);\n  // Build the dash array\n  const pathLength = px.transform(length);\n  const pathSpacing = px.transform(spacing);\n  attrs[keys.array] = \"\".concat(pathLength, \" \").concat(pathSpacing);\n}\nexport { buildSVGPath };", "map": {"version": 3, "names": ["px", "dashKeys", "offset", "array", "camel<PERSON><PERSON>s", "buildSVGPath", "attrs", "length", "spacing", "arguments", "undefined", "useDashCase", "<PERSON><PERSON><PERSON><PERSON>", "keys", "transform", "pathSpacing", "concat"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/framer-motion/dist/es/render/svg/utils/path.mjs"], "sourcesContent": ["import { px } from '../../../value/types/numbers/units.mjs';\n\nconst dashKeys = {\n    offset: \"stroke-dashoffset\",\n    array: \"stroke-dasharray\",\n};\nconst camelKeys = {\n    offset: \"strokeDashoffset\",\n    array: \"strokeDasharray\",\n};\n/**\n * Build SVG path properties. Uses the path's measured length to convert\n * our custom pathLength, pathSpacing and pathOffset into stroke-dashoffset\n * and stroke-dasharray attributes.\n *\n * This function is mutative to reduce per-frame GC.\n */\nfunction buildSVGPath(attrs, length, spacing = 1, offset = 0, useDashCase = true) {\n    // Normalise path length by setting SVG attribute pathLength to 1\n    attrs.pathLength = 1;\n    // We use dash case when setting attributes directly to the DOM node and camel case\n    // when defining props on a React component.\n    const keys = useDashCase ? dashKeys : camelKeys;\n    // Build the dash offset\n    attrs[keys.offset] = px.transform(-offset);\n    // Build the dash array\n    const pathLength = px.transform(length);\n    const pathSpacing = px.transform(spacing);\n    attrs[keys.array] = `${pathLength} ${pathSpacing}`;\n}\n\nexport { buildSVGPath };\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,wCAAwC;AAE3D,MAAMC,QAAQ,GAAG;EACbC,MAAM,EAAE,mBAAmB;EAC3BC,KAAK,EAAE;AACX,CAAC;AACD,MAAMC,SAAS,GAAG;EACdF,MAAM,EAAE,kBAAkB;EAC1BC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,YAAYA,CAACC,KAAK,EAAEC,MAAM,EAA+C;EAAA,IAA7CC,OAAO,GAAAC,SAAA,CAAAF,MAAA,QAAAE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;EAAA,IAAEP,MAAM,GAAAO,SAAA,CAAAF,MAAA,QAAAE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;EAAA,IAAEE,WAAW,GAAAF,SAAA,CAAAF,MAAA,QAAAE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;EAC5E;EACAH,KAAK,CAACM,UAAU,GAAG,CAAC;EACpB;EACA;EACA,MAAMC,IAAI,GAAGF,WAAW,GAAGV,QAAQ,GAAGG,SAAS;EAC/C;EACAE,KAAK,CAACO,IAAI,CAACX,MAAM,CAAC,GAAGF,EAAE,CAACc,SAAS,CAAC,CAACZ,MAAM,CAAC;EAC1C;EACA,MAAMU,UAAU,GAAGZ,EAAE,CAACc,SAAS,CAACP,MAAM,CAAC;EACvC,MAAMQ,WAAW,GAAGf,EAAE,CAACc,SAAS,CAACN,OAAO,CAAC;EACzCF,KAAK,CAACO,IAAI,CAACV,KAAK,CAAC,MAAAa,MAAA,CAAMJ,UAAU,OAAAI,MAAA,CAAID,WAAW,CAAE;AACtD;AAEA,SAASV,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}