{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createProtonMass } from '../../factoriesAny.js';\nexport var protonMassDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createProtonMass\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createProtonMass", "protonMassDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesProtonMass.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createProtonMass } from '../../factoriesAny.js';\nexport var protonMassDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createProtonMass\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,OAAO,IAAIC,sBAAsB,GAAG;EAClCH,qBAAqB;EACrBC,gBAAgB;EAChBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}