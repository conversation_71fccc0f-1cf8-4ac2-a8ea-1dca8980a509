{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { AccessorNodeDependencies } from './dependenciesAccessorNode.generated.js';\nimport { ArrayNodeDependencies } from './dependenciesArrayNode.generated.js';\nimport { ConstantNodeDependencies } from './dependenciesConstantNode.generated.js';\nimport { FunctionNodeDependencies } from './dependenciesFunctionNode.generated.js';\nimport { IndexNodeDependencies } from './dependenciesIndexNode.generated.js';\nimport { ObjectNodeDependencies } from './dependenciesObjectNode.generated.js';\nimport { OperatorNodeDependencies } from './dependenciesOperatorNode.generated.js';\nimport { ParenthesisNodeDependencies } from './dependenciesParenthesisNode.generated.js';\nimport { SymbolNodeDependencies } from './dependenciesSymbolNode.generated.js';\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { equalDependencies } from './dependenciesEqual.generated.js';\nimport { isZeroDependencies } from './dependenciesIsZero.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { parseDependencies } from './dependenciesParse.generated.js';\nimport { powDependencies } from './dependenciesPow.generated.js';\nimport { subtractDependencies } from './dependenciesSubtract.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSimplifyCore } from '../../factoriesAny.js';\nexport var simplifyCoreDependencies = {\n  AccessorNodeDependencies,\n  ArrayNodeDependencies,\n  ConstantNodeDependencies,\n  FunctionNodeDependencies,\n  IndexNodeDependencies,\n  ObjectNodeDependencies,\n  OperatorNodeDependencies,\n  ParenthesisNodeDependencies,\n  SymbolNodeDependencies,\n  addDependencies,\n  divideDependencies,\n  equalDependencies,\n  isZeroDependencies,\n  multiplyDependencies,\n  parseDependencies,\n  powDependencies,\n  subtractDependencies,\n  typedDependencies,\n  createSimplifyCore\n};", "map": {"version": 3, "names": ["AccessorNodeDependencies", "ArrayNodeDependencies", "ConstantNodeDependencies", "FunctionNodeDependencies", "IndexNodeDependencies", "ObjectNodeDependencies", "OperatorNodeDependencies", "ParenthesisNodeDependencies", "SymbolNodeDependencies", "addDependencies", "divideDependencies", "equalDependencies", "isZeroDependencies", "multiplyDependencies", "parseDependencies", "powDependencies", "subtractDependencies", "typedDependencies", "createSimplifyCore", "simplifyCoreDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSimplifyCore.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { AccessorNodeDependencies } from './dependenciesAccessorNode.generated.js';\nimport { ArrayNodeDependencies } from './dependenciesArrayNode.generated.js';\nimport { ConstantNodeDependencies } from './dependenciesConstantNode.generated.js';\nimport { FunctionNodeDependencies } from './dependenciesFunctionNode.generated.js';\nimport { IndexNodeDependencies } from './dependenciesIndexNode.generated.js';\nimport { ObjectNodeDependencies } from './dependenciesObjectNode.generated.js';\nimport { OperatorNodeDependencies } from './dependenciesOperatorNode.generated.js';\nimport { ParenthesisNodeDependencies } from './dependenciesParenthesisNode.generated.js';\nimport { SymbolNodeDependencies } from './dependenciesSymbolNode.generated.js';\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { equalDependencies } from './dependenciesEqual.generated.js';\nimport { isZeroDependencies } from './dependenciesIsZero.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { parseDependencies } from './dependenciesParse.generated.js';\nimport { powDependencies } from './dependenciesPow.generated.js';\nimport { subtractDependencies } from './dependenciesSubtract.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSimplifyCore } from '../../factoriesAny.js';\nexport var simplifyCoreDependencies = {\n  AccessorNodeDependencies,\n  ArrayNodeDependencies,\n  ConstantNodeDependencies,\n  FunctionNodeDependencies,\n  IndexNodeDependencies,\n  ObjectNodeDependencies,\n  OperatorNodeDependencies,\n  ParenthesisNodeDependencies,\n  SymbolNodeDependencies,\n  addDependencies,\n  divideDependencies,\n  equalDependencies,\n  isZeroDependencies,\n  multiplyDependencies,\n  parseDependencies,\n  powDependencies,\n  subtractDependencies,\n  typedDependencies,\n  createSimplifyCore\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,OAAO,IAAIC,wBAAwB,GAAG;EACpCnB,wBAAwB;EACxBC,qBAAqB;EACrBC,wBAAwB;EACxBC,wBAAwB;EACxBC,qBAAqB;EACrBC,sBAAsB;EACtBC,wBAAwB;EACxBC,2BAA2B;EAC3BC,sBAAsB;EACtBC,eAAe;EACfC,kBAAkB;EAClBC,iBAAiB;EACjBC,kBAAkB;EAClBC,oBAAoB;EACpBC,iBAAiB;EACjBC,eAAe;EACfC,oBAAoB;EACpBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}