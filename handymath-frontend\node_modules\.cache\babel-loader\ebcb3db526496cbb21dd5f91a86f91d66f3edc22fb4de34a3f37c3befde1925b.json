{"ast": null, "code": "export var isZeroDocs = {\n  name: 'isZero',\n  category: 'Utils',\n  syntax: ['isZero(x)'],\n  description: 'Test whether a value is zero.',\n  examples: ['isZero(2)', 'isZero(0)', 'isZero(-4)', 'isZero([3, 0, -2, 0])'],\n  seealso: ['isInteger', 'isNumeric', 'isNegative', 'isPositive']\n};", "map": {"version": 3, "names": ["isZeroDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/isZero.js"], "sourcesContent": ["export var isZeroDocs = {\n  name: 'isZero',\n  category: 'Utils',\n  syntax: ['isZero(x)'],\n  description: 'Test whether a value is zero.',\n  examples: ['isZero(2)', 'isZero(0)', 'isZero(-4)', 'isZero([3, 0, -2, 0])'],\n  seealso: ['isInteger', 'isNumeric', 'isNegative', 'isPositive']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,WAAW,CAAC;EACrBC,WAAW,EAAE,+BAA+B;EAC5CC,QAAQ,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,uBAAuB,CAAC;EAC3EC,OAAO,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY;AAChE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}