{"ast": null, "code": "export var SQRT12Docs = {\n  name: 'SQRT1_2',\n  category: 'Constants',\n  syntax: ['SQRT1_2'],\n  description: 'Returns the square root of 1/2, approximately equal to 0.707',\n  examples: ['SQRT1_2', 'sqrt(1/2)'],\n  seealso: []\n};", "map": {"version": 3, "names": ["SQRT12Docs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/SQRT1_2.js"], "sourcesContent": ["export var SQRT12Docs = {\n  name: 'SQRT1_2',\n  category: 'Constants',\n  syntax: ['SQRT1_2'],\n  description: 'Returns the square root of 1/2, approximately equal to 0.707',\n  examples: ['SQRT1_2', 'sqrt(1/2)'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,8DAA8D;EAC3EC,QAAQ,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;EAClCC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}