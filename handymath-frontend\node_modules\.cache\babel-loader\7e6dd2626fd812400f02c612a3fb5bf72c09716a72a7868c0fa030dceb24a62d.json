{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { parseDependencies } from './dependenciesParse.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createLeafCount } from '../../factoriesAny.js';\nexport var leafCountDependencies = {\n  parseDependencies,\n  typedDependencies,\n  createLeafCount\n};", "map": {"version": 3, "names": ["parseDependencies", "typedDependencies", "createLeafCount", "leafCountDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesLeafCount.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { parseDependencies } from './dependenciesParse.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createLeafCount } from '../../factoriesAny.js';\nexport var leafCountDependencies = {\n  parseDependencies,\n  typedDependencies,\n  createLeafCount\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,eAAe,QAAQ,uBAAuB;AACvD,OAAO,IAAIC,qBAAqB,GAAG;EACjCH,iBAAiB;EACjBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}