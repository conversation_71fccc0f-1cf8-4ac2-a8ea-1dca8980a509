{"ast": null, "code": "export var hasNumericValueDocs = {\n  name: 'hasNumericValue',\n  category: 'Utils',\n  syntax: ['hasNumericValue(x)'],\n  description: 'Test whether a value is an numeric value. ' + 'In case of a string, true is returned if the string contains a numeric value.',\n  examples: ['hasNumericValue(2)', 'hasNumericValue(\"2\")', 'isNumeric(\"2\")', 'hasNumericValue(0)', 'hasNumericValue(bignumber(500))', 'hasNumericValue(fraction(0.125))', 'hasNumericValue(2 + 3i)', 'hasNumericValue([2.3, \"foo\", false])'],\n  seealso: ['isInteger', 'isZero', 'isNegative', 'isPositive', 'isNaN', 'isNumeric']\n};", "map": {"version": 3, "names": ["hasNumericValueDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/hasNumericValue.js"], "sourcesContent": ["export var hasNumericValueDocs = {\n  name: 'hasNumericValue',\n  category: 'Utils',\n  syntax: ['hasNumericValue(x)'],\n  description: 'Test whether a value is an numeric value. ' + 'In case of a string, true is returned if the string contains a numeric value.',\n  examples: ['hasNumericValue(2)', 'hasNumericValue(\"2\")', 'isNumeric(\"2\")', 'hasNumericValue(0)', 'hasNumericValue(bignumber(500))', 'hasNumericValue(fraction(0.125))', 'hasNumericValue(2 + 3i)', 'hasNumericValue([2.3, \"foo\", false])'],\n  seealso: ['isInteger', 'isZero', 'isNegative', 'isPositive', 'isNaN', 'isNumeric']\n};"], "mappings": "AAAA,OAAO,IAAIA,mBAAmB,GAAG;EAC/BC,IAAI,EAAE,iBAAiB;EACvBC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,oBAAoB,CAAC;EAC9BC,WAAW,EAAE,4CAA4C,GAAG,+EAA+E;EAC3IC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,iCAAiC,EAAE,kCAAkC,EAAE,yBAAyB,EAAE,sCAAsC,CAAC;EAC1OC,OAAO,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW;AACnF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}