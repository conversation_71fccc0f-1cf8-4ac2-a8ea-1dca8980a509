{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * Permutes a vector; x = P'b. In MATLAB notation, x(p)=b.\n *\n * @param {Array} p           The permutation vector of length n. null value denotes identity\n * @param {Array} b           The input vector\n *\n * @return {Array}            The output vector x = P'b\n */\nexport function csIpvec(p, b) {\n  // vars\n  var k;\n  var n = b.length;\n  var x = [];\n  // check permutation vector was provided, p = null denotes identity\n  if (p) {\n    // loop vector\n    for (k = 0; k < n; k++) {\n      // apply permutation\n      x[p[k]] = b[k];\n    }\n  } else {\n    // loop vector\n    for (k = 0; k < n; k++) {\n      // x[i] = b[i]\n      x[k] = b[k];\n    }\n  }\n  return x;\n}", "map": {"version": 3, "names": ["csIpvec", "p", "b", "k", "n", "length", "x"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csIpvec.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * Permutes a vector; x = P'b. In MATLAB notation, x(p)=b.\n *\n * @param {Array} p           The permutation vector of length n. null value denotes identity\n * @param {Array} b           The input vector\n *\n * @return {Array}            The output vector x = P'b\n */\nexport function csIpvec(p, b) {\n  // vars\n  var k;\n  var n = b.length;\n  var x = [];\n  // check permutation vector was provided, p = null denotes identity\n  if (p) {\n    // loop vector\n    for (k = 0; k < n; k++) {\n      // apply permutation\n      x[p[k]] = b[k];\n    }\n  } else {\n    // loop vector\n    for (k = 0; k < n; k++) {\n      // x[i] = b[i]\n      x[k] = b[k];\n    }\n  }\n  return x;\n}"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC5B;EACA,IAAIC,CAAC;EACL,IAAIC,CAAC,GAAGF,CAAC,CAACG,MAAM;EAChB,IAAIC,CAAC,GAAG,EAAE;EACV;EACA,IAAIL,CAAC,EAAE;IACL;IACA,KAAKE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACtB;MACAG,CAAC,CAACL,CAAC,CAACE,CAAC,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC;IAChB;EACF,CAAC,MAAM;IACL;IACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACtB;MACAG,CAAC,CAACH,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC;IACb;EACF;EACA,OAAOG,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}