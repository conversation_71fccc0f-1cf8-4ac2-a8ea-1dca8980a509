{"ast": null, "code": "export var bignumberDocs = {\n  name: 'bignumber',\n  category: 'Construction',\n  syntax: ['bignumber(x)'],\n  description: 'Create a big number from a number or string.',\n  examples: ['0.1 + 0.2', 'bignumber(0.1) + bignumber(0.2)', 'bignumber(\"7.2\")', 'bignumber(\"7.2e500\")', 'bignumber([0.1, 0.2, 0.3])'],\n  seealso: ['boolean', 'bigint', 'complex', 'fraction', 'index', 'matrix', 'string', 'unit']\n};", "map": {"version": 3, "names": ["bignumberDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/construction/bignumber.js"], "sourcesContent": ["export var bignumberDocs = {\n  name: 'bignumber',\n  category: 'Construction',\n  syntax: ['bignumber(x)'],\n  description: 'Create a big number from a number or string.',\n  examples: ['0.1 + 0.2', 'bignumber(0.1) + bignumber(0.2)', 'bignumber(\"7.2\")', 'bignumber(\"7.2e500\")', 'bignumber([0.1, 0.2, 0.3])'],\n  seealso: ['boolean', 'bigint', 'complex', 'fraction', 'index', 'matrix', 'string', 'unit']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,cAAc,CAAC;EACxBC,WAAW,EAAE,8CAA8C;EAC3DC,QAAQ,EAAE,CAAC,WAAW,EAAE,iCAAiC,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,4BAA4B,CAAC;EACpIC,OAAO,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM;AAC3F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}