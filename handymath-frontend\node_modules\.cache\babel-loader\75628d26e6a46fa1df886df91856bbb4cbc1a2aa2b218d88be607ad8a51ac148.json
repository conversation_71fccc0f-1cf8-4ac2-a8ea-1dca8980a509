{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createTrue } from '../../factoriesAny.js';\nexport var trueDependencies = {\n  createTrue\n};", "map": {"version": 3, "names": ["createTrue", "trueDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesTrue.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createTrue } from '../../factoriesAny.js';\nexport var trueDependencies = {\n  createTrue\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,UAAU,QAAQ,uBAAuB;AAClD,OAAO,IAAIC,gBAAgB,GAAG;EAC5BD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}