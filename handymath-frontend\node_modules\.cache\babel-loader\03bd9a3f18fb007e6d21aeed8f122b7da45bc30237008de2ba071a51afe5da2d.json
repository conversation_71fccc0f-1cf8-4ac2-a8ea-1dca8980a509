{"ast": null, "code": "export var corrDocs = {\n  name: 'corr',\n  category: 'Statistics',\n  syntax: ['corr(A,B)'],\n  description: 'Compute the correlation coefficient of a two list with values, For matrices, the matrix correlation coefficient is calculated.',\n  examples: ['corr([2, 4, 6, 8],[1, 2, 3, 6])', 'corr(matrix([[1, 2.2, 3, 4.8, 5], [1, 2, 3, 4, 5]]), matrix([[4, 5.3, 6.6, 7, 8], [1, 2, 3, 4, 5]]))'],\n  seealso: ['max', 'mean', 'min', 'median', 'min', 'prod', 'std', 'sum']\n};", "map": {"version": 3, "names": ["corrDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/statistics/corr.js"], "sourcesContent": ["export var corrDocs = {\n  name: 'corr',\n  category: 'Statistics',\n  syntax: ['corr(A,B)'],\n  description: 'Compute the correlation coefficient of a two list with values, For matrices, the matrix correlation coefficient is calculated.',\n  examples: ['corr([2, 4, 6, 8],[1, 2, 3, 6])', 'corr(matrix([[1, 2.2, 3, 4.8, 5], [1, 2, 3, 4, 5]]), matrix([[4, 5.3, 6.6, 7, 8], [1, 2, 3, 4, 5]]))'],\n  seealso: ['max', 'mean', 'min', 'median', 'min', 'prod', 'std', 'sum']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,WAAW,CAAC;EACrBC,WAAW,EAAE,gIAAgI;EAC7IC,QAAQ,EAAE,CAAC,iCAAiC,EAAE,sGAAsG,CAAC;EACrJC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK;AACvE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}