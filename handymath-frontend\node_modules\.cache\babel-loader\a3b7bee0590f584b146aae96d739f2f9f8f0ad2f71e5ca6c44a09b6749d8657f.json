{"ast": null, "code": "export var smallerEqDocs = {\n  name: 'smallerEq',\n  category: 'Relational',\n  syntax: ['x <= y', 'smallerEq(x, y)'],\n  description: 'Check if value x is smaller or equal to value y. Returns true if x is smaller than y, and false if not.',\n  examples: ['2 <= 1+1', '2 < 1+1', 'a = 3.2', 'b = 6-2.8', '(a <= b)'],\n  seealso: ['equal', 'unequal', 'larger', 'smaller', 'largerEq', 'compare']\n};", "map": {"version": 3, "names": ["smallerEqDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/relational/smallerEq.js"], "sourcesContent": ["export var smallerEqDocs = {\n  name: 'smallerEq',\n  category: 'Relational',\n  syntax: ['x <= y', 'smallerEq(x, y)'],\n  description: 'Check if value x is smaller or equal to value y. Returns true if x is smaller than y, and false if not.',\n  examples: ['2 <= 1+1', '2 < 1+1', 'a = 3.2', 'b = 6-2.8', '(a <= b)'],\n  seealso: ['equal', 'unequal', 'larger', 'smaller', 'largerEq', 'compare']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;EACrCC,WAAW,EAAE,yGAAyG;EACtHC,QAAQ,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,CAAC;EACrEC,OAAO,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS;AAC1E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}