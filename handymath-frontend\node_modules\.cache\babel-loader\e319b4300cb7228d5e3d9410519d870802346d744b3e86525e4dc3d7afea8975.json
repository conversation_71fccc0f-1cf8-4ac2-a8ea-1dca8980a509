{"ast": null, "code": "export var multinomialDocs = {\n  name: 'multinomial',\n  category: 'Probability',\n  syntax: ['multinomial(A)'],\n  description: 'Multinomial Coefficients compute the number of ways of picking a1, a2, ..., ai unordered outcomes from `n` possibilities. multinomial takes one array of integers as an argument. The following condition must be enforced: every ai > 0.',\n  examples: ['multinomial([1, 2, 1])'],\n  seealso: ['combinations', 'factorial']\n};", "map": {"version": 3, "names": ["multinomialDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/probability/multinomial.js"], "sourcesContent": ["export var multinomialDocs = {\n  name: 'multinomial',\n  category: 'Probability',\n  syntax: ['multinomial(A)'],\n  description: 'Multinomial Coefficients compute the number of ways of picking a1, a2, ..., ai unordered outcomes from `n` possibilities. multinomial takes one array of integers as an argument. The following condition must be enforced: every ai > 0.',\n  examples: ['multinomial([1, 2, 1])'],\n  seealso: ['combinations', 'factorial']\n};"], "mappings": "AAAA,OAAO,IAAIA,eAAe,GAAG;EAC3BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CAAC,gBAAgB,CAAC;EAC1BC,WAAW,EAAE,2OAA2O;EACxPC,QAAQ,EAAE,CAAC,wBAAwB,CAAC;EACpCC,OAAO,EAAE,CAAC,cAAc,EAAE,WAAW;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}