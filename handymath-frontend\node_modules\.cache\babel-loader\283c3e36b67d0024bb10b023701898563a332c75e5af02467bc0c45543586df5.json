{"ast": null, "code": "export var coshDocs = {\n  name: 'cosh',\n  category: 'Trigonometry',\n  syntax: ['cosh(x)'],\n  description: 'Compute the hyperbolic cosine of x in radians.',\n  examples: ['cosh(0.5)'],\n  seealso: ['sinh', 'tanh', 'coth']\n};", "map": {"version": 3, "names": ["coshDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/cosh.js"], "sourcesContent": ["export var coshDocs = {\n  name: 'cosh',\n  category: 'Trigonometry',\n  syntax: ['cosh(x)'],\n  description: 'Compute the hyperbolic cosine of x in radians.',\n  examples: ['cosh(0.5)'],\n  seealso: ['sinh', 'tanh', 'coth']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,gDAAgD;EAC7DC,QAAQ,EAAE,CAAC,WAAW,CAAC;EACvBC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}