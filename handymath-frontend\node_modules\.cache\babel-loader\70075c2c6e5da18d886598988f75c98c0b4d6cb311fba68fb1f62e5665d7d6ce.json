{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\CoursesPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport StudentNavbar from '../components/StudentNavbar';\nimport Pagination from '../components/Pagination';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CoursesPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n\n  // State management\n  const [courses, setCourses] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    level: '',\n    featured: false,\n    search: '',\n    sortBy: 'title',\n    sortOrder: 'asc'\n  });\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(9);\n  const [totalItems, setTotalItems] = useState(0);\n  const [searchValue, setSearchValue] = useState('');\n  const [searchTimeout, setSearchTimeout] = useState(null);\n\n  // Pagination options\n  const coursePaginationOptions = [6, 9, 12, 18];\n\n  // Fetch courses from API\n  const fetchCourses = async () => {\n    try {\n      var _response$data$pagina;\n      setLoading(true);\n      const params = new URLSearchParams();\n      if (filters.level) params.append('level', filters.level);\n      if (filters.featured) params.append('featured', 'true');\n      if (filters.search) params.append('search', filters.search);\n      params.append('ordering', filters.sortOrder === 'desc' ? `-${filters.sortBy}` : filters.sortBy);\n      params.append('page', currentPage.toString());\n      params.append('page_size', itemsPerPage.toString());\n      const response = await api.get(`/courses/?${params.toString()}`);\n      // L'API retourne une structure avec 'courses' et 'pagination'\n      setCourses(response.data.courses || response.data.results || []);\n      setTotalItems(((_response$data$pagina = response.data.pagination) === null || _response$data$pagina === void 0 ? void 0 : _response$data$pagina.total_count) || response.data.count || 0);\n    } catch (error) {\n      console.error('Error fetching courses:', error);\n      setCourses([]);\n      setTotalItems(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Effects\n  useEffect(() => {\n    if (user) {\n      fetchCourses();\n    }\n  }, [user, filters, currentPage, itemsPerPage]);\n\n  // Pagination handlers\n  const handlePageChange = page => {\n    setCurrentPage(page);\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  const handleItemsPerPageChange = newItemsPerPage => {\n    setItemsPerPage(newItemsPerPage);\n    setCurrentPage(1);\n  };\n\n  // Search handler with debounce\n  const handleSearchChange = value => {\n    setSearchValue(value);\n    if (searchTimeout) {\n      clearTimeout(searchTimeout);\n    }\n    const timeout = setTimeout(() => {\n      setFilters(prev => ({\n        ...prev,\n        search: value\n      }));\n      setCurrentPage(1);\n    }, 300);\n    setSearchTimeout(timeout);\n  };\n\n  // Clear filters\n  const clearFilters = () => {\n    setFilters({\n      level: '',\n      featured: false,\n      search: '',\n      sortBy: 'title',\n      sortOrder: 'asc'\n    });\n    setSearchValue('');\n    setCurrentPage(1);\n  };\n\n  // Enrollment handler\n  const handleEnroll = async (courseId, courseTitle) => {\n    try {\n      await api.post(`/courses/${courseId}/enroll/`);\n      // Refresh courses after enrollment\n      fetchCourses();\n      alert(`Vous êtes maintenant inscrit au cours: ${courseTitle}`);\n    } catch (error) {\n      console.error('Error enrolling in course:', error);\n      alert('Erreur lors de l\\'inscription au cours');\n    }\n  };\n\n  // Get status badge\n  const getStatusBadge = course => {\n    if (course.is_enrolled) {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"px-2 py-1 text-xs font-semibold bg-green-100 text-green-800 rounded-full\",\n        children: \"Inscrit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 14\n      }, this);\n    }\n    if (course.is_featured) {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"px-2 py-1 text-xs font-semibold bg-yellow-100 text-yellow-800 rounded-full\",\n        children: \"\\u2B50 Vedette\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"px-2 py-1 text-xs font-semibold bg-blue-100 text-blue-800 rounded-full\",\n      children: \"Disponible\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 12\n    }, this);\n  };\n\n  // Calculate total pages\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n          children: \"Acc\\xE8s non autoris\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Veuillez vous connecter pour acc\\xE9der aux cours.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\",\n    children: [/*#__PURE__*/_jsxDEV(StudentNavbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4\",\n          children: [\"D\\xE9couvrez nos \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-blue-600\",\n            children: \"Cours\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 27\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\",\n          children: \"Explorez notre collection de cours de math\\xE9matiques con\\xE7us pour vous aider \\xE0 ma\\xEEtriser les concepts essentiels\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400\",\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Rechercher un cours...\",\n            value: searchValue,\n            onChange: e => handleSearchChange(e.target.value),\n            className: \"w-full pl-12 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-4 gap-4 items-end\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"\\uD83D\\uDCDA Niveau\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filters.level,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                level: e.target.value\n              })),\n              className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tous les niveaux\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"beginner\",\n                children: \"D\\xE9butant\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"intermediate\",\n                children: \"Interm\\xE9diaire\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"advanced\",\n                children: \"Avanc\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"\\uD83D\\uDD04 Trier par\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filters.sortBy,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                sortBy: e.target.value\n              })),\n              className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"title\",\n                children: \"Titre\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"level\",\n                children: \"Niveau\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"duration\",\n                children: \"Dur\\xE9e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"created_at\",\n                children: \"Date de cr\\xE9ation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"\\uD83D\\uDCCA Ordre\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filters.sortOrder,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                sortOrder: e.target.value\n              })),\n              className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"asc\",\n                children: \"Croissant\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"desc\",\n                children: \"D\\xE9croissant\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: filters.featured,\n                onChange: e => setFilters(prev => ({\n                  ...prev,\n                  featured: e.target.checked\n                })),\n                className: \"mr-2 rounded text-blue-600 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-yellow-500 mr-1\",\n                children: \"\\u2B50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                children: \"Vedettes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: clearFilters,\n              className: \"px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\",\n              children: \"Effacer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.3\n        },\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-4 text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl mr-3\",\n              children: \"\\uD83D\\uDCDA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm opacity-90\",\n                children: \"Total des cours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold\",\n                children: totalItems\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-4 text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl mr-3\",\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm opacity-90\",\n                children: \"Cours inscrits\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold\",\n                children: courses.filter(c => c.is_enrolled).length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-4 text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl mr-3\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm opacity-90\",\n                children: \"Progr\\xE8s moyen\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold\",\n                children: [courses.length > 0 ? Math.round(courses.reduce((acc, c) => acc + (c.progress_percentage || 0), 0) / courses.length) : 0, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center items-center py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 11\n      }, this), !loading && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\",\n        children: courses.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-full text-center py-12\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 dark:text-gray-400\",\n            children: \"Aucun cours trouv\\xE9 avec les crit\\xE8res s\\xE9lectionn\\xE9s.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 15\n        }, this) : courses.map((course, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: index * 0.1\n          },\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-start mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 dark:text-white line-clamp-2\",\n                children: course.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 23\n              }, this), getStatusBadge(course)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3\",\n              children: course.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"\\uD83D\\uDCCA \", course.level_display]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"\\u23F1\\uFE0F \", Math.round(course.estimated_duration / 60), \"h\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 21\n            }, this), course.is_enrolled && course.progress_percentage !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Progr\\xE8s\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [course.progress_percentage, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                  style: {\n                    width: `${course.progress_percentage}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-2\",\n              children: [!course.is_enrolled ? /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEnroll(course.id, course.title),\n                className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors font-medium\",\n                children: \"S'inscrire\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 25\n              }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors font-medium\",\n                children: \"Continuer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                children: \"D\\xE9tails\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 19\n          }, this)\n        }, course.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 17\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 11\n      }, this), !loading && courses.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        className: \"mt-12\",\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          currentPage: currentPage,\n          totalPages: totalPages,\n          totalItems: totalItems,\n          itemsPerPage: itemsPerPage,\n          onPageChange: handlePageChange,\n          onItemsPerPageChange: handleItemsPerPageChange,\n          showItemsPerPage: true,\n          showInfo: true,\n          itemsPerPageOptions: coursePaginationOptions\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n};\n_s(CoursesPage, \"M+NsxvK3ONxenXNwqCv5trYcTms=\", false, function () {\n  return [useAuth];\n});\n_c = CoursesPage;\nexport default CoursesPage;\nvar _c;\n$RefreshReg$(_c, \"CoursesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useAuth", "StudentNavbar", "Pagination", "api", "jsxDEV", "_jsxDEV", "CoursesPage", "_s", "user", "courses", "setCourses", "loading", "setLoading", "filters", "setFilters", "level", "featured", "search", "sortBy", "sortOrder", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "totalItems", "setTotalItems", "searchValue", "setSearchValue", "searchTimeout", "setSearchTimeout", "coursePaginationOptions", "fetchCourses", "_response$data$pagina", "params", "URLSearchParams", "append", "toString", "response", "get", "data", "results", "pagination", "total_count", "count", "error", "console", "handlePageChange", "page", "window", "scrollTo", "top", "behavior", "handleItemsPerPageChange", "newItemsPerPage", "handleSearchChange", "value", "clearTimeout", "timeout", "setTimeout", "prev", "clearFilters", "handleEnroll", "courseId", "courseTitle", "post", "alert", "getStatusBadge", "course", "is_enrolled", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "is_featured", "totalPages", "Math", "ceil", "div", "initial", "opacity", "y", "animate", "transition", "duration", "delay", "type", "placeholder", "onChange", "e", "target", "checked", "onClick", "filter", "c", "length", "round", "reduce", "acc", "progress_percentage", "map", "index", "title", "description", "level_display", "estimated_duration", "undefined", "style", "width", "id", "onPageChange", "onItemsPerPageChange", "showItemsPerPage", "showInfo", "itemsPerPageOptions", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/pages/CoursesPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport StudentNavbar from '../components/StudentNavbar';\nimport Pagination from '../components/Pagination';\nimport api from '../services/api';\n\ninterface Course {\n  id: number;\n  title: string;\n  description: string;\n  level: string;\n  level_display: string;\n  estimated_duration: number;\n  is_featured: boolean;\n  is_enrolled: boolean;\n  progress_percentage?: number;\n  chapters_count: number;\n  lessons_count: number;\n  thumbnail: string;\n  created_at: string;\n}\n\ninterface Filters {\n  level: string;\n  featured: boolean;\n  search: string;\n  sortBy: 'title' | 'level' | 'duration' | 'created_at';\n  sortOrder: 'asc' | 'desc';\n}\n\nconst CoursesPage: React.FC = () => {\n  const { user } = useAuth();\n  \n  // State management\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState<Filters>({\n    level: '',\n    featured: false,\n    search: '',\n    sortBy: 'title',\n    sortOrder: 'asc'\n  });\n  \n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(9);\n  const [totalItems, setTotalItems] = useState(0);\n  const [searchValue, setSearchValue] = useState('');\n  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);\n\n  // Pagination options\n  const coursePaginationOptions = [6, 9, 12, 18];\n\n  // Fetch courses from API\n  const fetchCourses = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      \n      if (filters.level) params.append('level', filters.level);\n      if (filters.featured) params.append('featured', 'true');\n      if (filters.search) params.append('search', filters.search);\n      params.append('ordering', filters.sortOrder === 'desc' ? `-${filters.sortBy}` : filters.sortBy);\n      params.append('page', currentPage.toString());\n      params.append('page_size', itemsPerPage.toString());\n\n      const response = await api.get(`/courses/?${params.toString()}`);\n      // L'API retourne une structure avec 'courses' et 'pagination'\n      setCourses(response.data.courses || response.data.results || []);\n      setTotalItems(response.data.pagination?.total_count || response.data.count || 0);\n    } catch (error) {\n      console.error('Error fetching courses:', error);\n      setCourses([]);\n      setTotalItems(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Effects\n  useEffect(() => {\n    if (user) {\n      fetchCourses();\n    }\n  }, [user, filters, currentPage, itemsPerPage]);\n\n  // Pagination handlers\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const handleItemsPerPageChange = (newItemsPerPage: number) => {\n    setItemsPerPage(newItemsPerPage);\n    setCurrentPage(1);\n  };\n\n  // Search handler with debounce\n  const handleSearchChange = (value: string) => {\n    setSearchValue(value);\n    if (searchTimeout) {\n      clearTimeout(searchTimeout);\n    }\n    const timeout = setTimeout(() => {\n      setFilters(prev => ({ ...prev, search: value }));\n      setCurrentPage(1);\n    }, 300);\n    setSearchTimeout(timeout);\n  };\n\n  // Clear filters\n  const clearFilters = () => {\n    setFilters({\n      level: '',\n      featured: false,\n      search: '',\n      sortBy: 'title',\n      sortOrder: 'asc'\n    });\n    setSearchValue('');\n    setCurrentPage(1);\n  };\n\n  // Enrollment handler\n  const handleEnroll = async (courseId: number, courseTitle: string) => {\n    try {\n      await api.post(`/courses/${courseId}/enroll/`);\n      // Refresh courses after enrollment\n      fetchCourses();\n      alert(`Vous êtes maintenant inscrit au cours: ${courseTitle}`);\n    } catch (error) {\n      console.error('Error enrolling in course:', error);\n      alert('Erreur lors de l\\'inscription au cours');\n    }\n  };\n\n  // Get status badge\n  const getStatusBadge = (course: Course) => {\n    if (course.is_enrolled) {\n      return <span className=\"px-2 py-1 text-xs font-semibold bg-green-100 text-green-800 rounded-full\">Inscrit</span>;\n    }\n    if (course.is_featured) {\n      return <span className=\"px-2 py-1 text-xs font-semibold bg-yellow-100 text-yellow-800 rounded-full\">⭐ Vedette</span>;\n    }\n    return <span className=\"px-2 py-1 text-xs font-semibold bg-blue-100 text-blue-800 rounded-full\">Disponible</span>;\n  };\n\n  // Calculate total pages\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\n\n  if (!user) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n            Accès non autorisé\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Veuillez vous connecter pour accéder aux cours.\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n      <StudentNavbar />\n      \n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Hero Section */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-12\"\n        >\n          <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4\">\n            Découvrez nos <span className=\"text-blue-600\">Cours</span>\n          </h1>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n            Explorez notre collection de cours de mathématiques conçus pour vous aider à maîtriser les concepts essentiels\n          </p>\n        </motion.div>\n\n        {/* Search and Filters */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6 mb-8\"\n        >\n          {/* Search Bar */}\n          <div className=\"relative mb-6\">\n            <span className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400\">🔍</span>\n            <input\n              type=\"text\"\n              placeholder=\"Rechercher un cours...\"\n              value={searchValue}\n              onChange={(e) => handleSearchChange(e.target.value)}\n              className=\"w-full pl-12 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\"\n            />\n          </div>\n\n          {/* Filters */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 items-end\">\n            {/* Level Filter */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                📚 Niveau\n              </label>\n              <select\n                value={filters.level}\n                onChange={(e) => setFilters(prev => ({ ...prev, level: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"\">Tous les niveaux</option>\n                <option value=\"beginner\">Débutant</option>\n                <option value=\"intermediate\">Intermédiaire</option>\n                <option value=\"advanced\">Avancé</option>\n              </select>\n            </div>\n\n            {/* Sort Filter */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                🔄 Trier par\n              </label>\n              <select\n                value={filters.sortBy}\n                onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as any }))}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"title\">Titre</option>\n                <option value=\"level\">Niveau</option>\n                <option value=\"duration\">Durée</option>\n                <option value=\"created_at\">Date de création</option>\n              </select>\n            </div>\n\n            {/* Sort Order */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                📊 Ordre\n              </label>\n              <select\n                value={filters.sortOrder}\n                onChange={(e) => setFilters(prev => ({ ...prev, sortOrder: e.target.value as any }))}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"asc\">Croissant</option>\n                <option value=\"desc\">Décroissant</option>\n              </select>\n            </div>\n\n            {/* Featured and Clear */}\n            <div className=\"flex flex-col space-y-2\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={filters.featured}\n                  onChange={(e) => setFilters(prev => ({ ...prev, featured: e.target.checked }))}\n                  className=\"mr-2 rounded text-blue-600 focus:ring-blue-500\"\n                />\n                <span className=\"text-yellow-500 mr-1\">⭐</span>\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">Vedettes</span>\n              </label>\n              <button\n                onClick={clearFilters}\n                className=\"px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n              >\n                Effacer\n              </button>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Statistics */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n          className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\"\n        >\n          <div className=\"bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-4 text-white\">\n            <div className=\"flex items-center\">\n              <span className=\"text-2xl mr-3\">📚</span>\n              <div>\n                <p className=\"text-sm opacity-90\">Total des cours</p>\n                <p className=\"text-2xl font-bold\">{totalItems}</p>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-4 text-white\">\n            <div className=\"flex items-center\">\n              <span className=\"text-2xl mr-3\">✅</span>\n              <div>\n                <p className=\"text-sm opacity-90\">Cours inscrits</p>\n                <p className=\"text-2xl font-bold\">{courses.filter(c => c.is_enrolled).length}</p>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-4 text-white\">\n            <div className=\"flex items-center\">\n              <span className=\"text-2xl mr-3\">📊</span>\n              <div>\n                <p className=\"text-sm opacity-90\">Progrès moyen</p>\n                <p className=\"text-2xl font-bold\">\n                  {courses.length > 0\n                    ? Math.round(courses.reduce((acc, c) => acc + (c.progress_percentage || 0), 0) / courses.length)\n                    : 0}%\n                </p>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Loading State */}\n        {loading && (\n          <div className=\"flex justify-center items-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n          </div>\n        )}\n\n        {/* Courses Grid */}\n        {!loading && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n            className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\"\n          >\n            {courses.length === 0 ? (\n              <div className=\"col-span-full text-center py-12\">\n                <p className=\"text-xl text-gray-600 dark:text-gray-400\">\n                  Aucun cours trouvé avec les critères sélectionnés.\n                </p>\n              </div>\n            ) : (\n              courses.map((course, index) => (\n                <motion.div\n                  key={course.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1\"\n                >\n                  <div className=\"p-6\">\n                    <div className=\"flex justify-between items-start mb-4\">\n                      <h3 className=\"text-xl font-bold text-gray-900 dark:text-white line-clamp-2\">\n                        {course.title}\n                      </h3>\n                      {getStatusBadge(course)}\n                    </div>\n                    \n                    <p className=\"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3\">\n                      {course.description}\n                    </p>\n                    \n                    <div className=\"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4\">\n                      <span>📊 {course.level_display}</span>\n                      <span>⏱️ {Math.round(course.estimated_duration / 60)}h</span>\n                    </div>\n                    \n                    {course.is_enrolled && course.progress_percentage !== undefined && (\n                      <div className=\"mb-4\">\n                        <div className=\"flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1\">\n                          <span>Progrès</span>\n                          <span>{course.progress_percentage}%</span>\n                        </div>\n                        <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                          <div\n                            className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                            style={{ width: `${course.progress_percentage}%` }}\n                          ></div>\n                        </div>\n                      </div>\n                    )}\n                    \n                    <div className=\"flex space-x-2\">\n                      {!course.is_enrolled ? (\n                        <button\n                          onClick={() => handleEnroll(course.id, course.title)}\n                          className=\"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors font-medium\"\n                        >\n                          S'inscrire\n                        </button>\n                      ) : (\n                        <button className=\"flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors font-medium\">\n                          Continuer\n                        </button>\n                      )}\n                      <button className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                        Détails\n                      </button>\n                    </div>\n                  </div>\n                </motion.div>\n              ))\n            )}\n          </motion.div>\n        )}\n\n        {/* Pagination */}\n        {!loading && courses.length > 0 && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3 }}\n            className=\"mt-12\"\n          >\n            <Pagination\n              currentPage={currentPage}\n              totalPages={totalPages}\n              totalItems={totalItems}\n              itemsPerPage={itemsPerPage}\n              onPageChange={handlePageChange}\n              onItemsPerPageChange={handleItemsPerPageChange}\n              showItemsPerPage={true}\n              showInfo={true}\n              itemsPerPageOptions={coursePaginationOptions}\n            />\n          </motion.div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CoursesPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA0BlC,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;;EAE1B;EACA,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAU;IAC9CkB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAwB,IAAI,CAAC;;EAE/E;EACA,MAAMiC,uBAAuB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;;EAE9C;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MAAA,IAAAC,qBAAA;MACFpB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqB,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MAEpC,IAAIrB,OAAO,CAACE,KAAK,EAAEkB,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEtB,OAAO,CAACE,KAAK,CAAC;MACxD,IAAIF,OAAO,CAACG,QAAQ,EAAEiB,MAAM,CAACE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC;MACvD,IAAItB,OAAO,CAACI,MAAM,EAAEgB,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEtB,OAAO,CAACI,MAAM,CAAC;MAC3DgB,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEtB,OAAO,CAACM,SAAS,KAAK,MAAM,GAAG,IAAIN,OAAO,CAACK,MAAM,EAAE,GAAGL,OAAO,CAACK,MAAM,CAAC;MAC/Fe,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEf,WAAW,CAACgB,QAAQ,CAAC,CAAC,CAAC;MAC7CH,MAAM,CAACE,MAAM,CAAC,WAAW,EAAEb,YAAY,CAACc,QAAQ,CAAC,CAAC,CAAC;MAEnD,MAAMC,QAAQ,GAAG,MAAMlC,GAAG,CAACmC,GAAG,CAAC,aAAaL,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;MAChE;MACA1B,UAAU,CAAC2B,QAAQ,CAACE,IAAI,CAAC9B,OAAO,IAAI4B,QAAQ,CAACE,IAAI,CAACC,OAAO,IAAI,EAAE,CAAC;MAChEf,aAAa,CAAC,EAAAO,qBAAA,GAAAK,QAAQ,CAACE,IAAI,CAACE,UAAU,cAAAT,qBAAA,uBAAxBA,qBAAA,CAA0BU,WAAW,KAAIL,QAAQ,CAACE,IAAI,CAACI,KAAK,IAAI,CAAC,CAAC;IAClF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/ClC,UAAU,CAAC,EAAE,CAAC;MACde,aAAa,CAAC,CAAC,CAAC;IAClB,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAd,SAAS,CAAC,MAAM;IACd,IAAIU,IAAI,EAAE;MACRuB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACvB,IAAI,EAAEK,OAAO,EAAEO,WAAW,EAAEE,YAAY,CAAC,CAAC;;EAE9C;EACA,MAAMwB,gBAAgB,GAAIC,IAAY,IAAK;IACzC1B,cAAc,CAAC0B,IAAI,CAAC;IACpBC,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;EAED,MAAMC,wBAAwB,GAAIC,eAAuB,IAAK;IAC5D9B,eAAe,CAAC8B,eAAe,CAAC;IAChChC,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMiC,kBAAkB,GAAIC,KAAa,IAAK;IAC5C5B,cAAc,CAAC4B,KAAK,CAAC;IACrB,IAAI3B,aAAa,EAAE;MACjB4B,YAAY,CAAC5B,aAAa,CAAC;IAC7B;IACA,MAAM6B,OAAO,GAAGC,UAAU,CAAC,MAAM;MAC/B5C,UAAU,CAAC6C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE1C,MAAM,EAAEsC;MAAM,CAAC,CAAC,CAAC;MAChDlC,cAAc,CAAC,CAAC,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC;IACPQ,gBAAgB,CAAC4B,OAAO,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB9C,UAAU,CAAC;MACTC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,SAAS,EAAE;IACb,CAAC,CAAC;IACFQ,cAAc,CAAC,EAAE,CAAC;IAClBN,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMwC,YAAY,GAAG,MAAAA,CAAOC,QAAgB,EAAEC,WAAmB,KAAK;IACpE,IAAI;MACF,MAAM5D,GAAG,CAAC6D,IAAI,CAAC,YAAYF,QAAQ,UAAU,CAAC;MAC9C;MACA/B,YAAY,CAAC,CAAC;MACdkC,KAAK,CAAC,0CAA0CF,WAAW,EAAE,CAAC;IAChE,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDqB,KAAK,CAAC,wCAAwC,CAAC;IACjD;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,MAAc,IAAK;IACzC,IAAIA,MAAM,CAACC,WAAW,EAAE;MACtB,oBAAO/D,OAAA;QAAMgE,SAAS,EAAC,0EAA0E;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAClH;IACA,IAAIP,MAAM,CAACQ,WAAW,EAAE;MACtB,oBAAOtE,OAAA;QAAMgE,SAAS,EAAC,4EAA4E;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACtH;IACA,oBAAOrE,OAAA;MAAMgE,SAAS,EAAC,wEAAwE;MAAAC,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EACnH,CAAC;;EAED;EACA,MAAME,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACtD,UAAU,GAAGF,YAAY,CAAC;EAEvD,IAAI,CAACd,IAAI,EAAE;IACT,oBACEH,OAAA;MAAKgE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CjE,OAAA;QAAKgE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjE,OAAA;UAAIgE,SAAS,EAAC,uDAAuD;UAAAC,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrE,OAAA;UAAGgE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAEhD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErE,OAAA;IAAKgE,SAAS,EAAC,0HAA0H;IAAAC,QAAA,gBACvIjE,OAAA,CAACJ,aAAa;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEjBrE,OAAA;MAAKgE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1CjE,OAAA,CAACN,MAAM,CAACgF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BhB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BjE,OAAA;UAAIgE,SAAS,EAAC,mEAAmE;UAAAC,QAAA,GAAC,mBAClE,eAAAjE,OAAA;YAAMgE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACLrE,OAAA;UAAGgE,SAAS,EAAC,4DAA4D;UAAAC,QAAA,EAAC;QAE1E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGbrE,OAAA,CAACN,MAAM,CAACgF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC1CjB,SAAS,EAAC,sGAAsG;QAAAC,QAAA,gBAGhHjE,OAAA;UAAKgE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BjE,OAAA;YAAMgE,SAAS,EAAC,kEAAkE;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5FrE,OAAA;YACEkF,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,wBAAwB;YACpCjC,KAAK,EAAE7B,WAAY;YACnB+D,QAAQ,EAAGC,CAAC,IAAKpC,kBAAkB,CAACoC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;YACpDc,SAAS,EAAC;UAA0P;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNrE,OAAA;UAAKgE,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAE9DjE,OAAA;YAAAiE,QAAA,gBACEjE,OAAA;cAAOgE,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAEnF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrE,OAAA;cACEkD,KAAK,EAAE1C,OAAO,CAACE,KAAM;cACrB0E,QAAQ,EAAGC,CAAC,IAAK5E,UAAU,CAAC6C,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE5C,KAAK,EAAE2E,CAAC,CAACC,MAAM,CAACpC;cAAM,CAAC,CAAC,CAAE;cAC1Ec,SAAS,EAAC,qLAAqL;cAAAC,QAAA,gBAE/LjE,OAAA;gBAAQkD,KAAK,EAAC,EAAE;gBAAAe,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1CrE,OAAA;gBAAQkD,KAAK,EAAC,UAAU;gBAAAe,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1CrE,OAAA;gBAAQkD,KAAK,EAAC,cAAc;gBAAAe,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnDrE,OAAA;gBAAQkD,KAAK,EAAC,UAAU;gBAAAe,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNrE,OAAA;YAAAiE,QAAA,gBACEjE,OAAA;cAAOgE,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAEnF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrE,OAAA;cACEkD,KAAK,EAAE1C,OAAO,CAACK,MAAO;cACtBuE,QAAQ,EAAGC,CAAC,IAAK5E,UAAU,CAAC6C,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEzC,MAAM,EAAEwE,CAAC,CAACC,MAAM,CAACpC;cAAa,CAAC,CAAC,CAAE;cAClFc,SAAS,EAAC,qLAAqL;cAAAC,QAAA,gBAE/LjE,OAAA;gBAAQkD,KAAK,EAAC,OAAO;gBAAAe,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCrE,OAAA;gBAAQkD,KAAK,EAAC,OAAO;gBAAAe,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrCrE,OAAA;gBAAQkD,KAAK,EAAC,UAAU;gBAAAe,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvCrE,OAAA;gBAAQkD,KAAK,EAAC,YAAY;gBAAAe,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNrE,OAAA;YAAAiE,QAAA,gBACEjE,OAAA;cAAOgE,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAEnF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrE,OAAA;cACEkD,KAAK,EAAE1C,OAAO,CAACM,SAAU;cACzBsE,QAAQ,EAAGC,CAAC,IAAK5E,UAAU,CAAC6C,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAExC,SAAS,EAAEuE,CAAC,CAACC,MAAM,CAACpC;cAAa,CAAC,CAAC,CAAE;cACrFc,SAAS,EAAC,qLAAqL;cAAAC,QAAA,gBAE/LjE,OAAA;gBAAQkD,KAAK,EAAC,KAAK;gBAAAe,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCrE,OAAA;gBAAQkD,KAAK,EAAC,MAAM;gBAAAe,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNrE,OAAA;YAAKgE,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCjE,OAAA;cAAOgE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClCjE,OAAA;gBACEkF,IAAI,EAAC,UAAU;gBACfK,OAAO,EAAE/E,OAAO,CAACG,QAAS;gBAC1ByE,QAAQ,EAAGC,CAAC,IAAK5E,UAAU,CAAC6C,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE3C,QAAQ,EAAE0E,CAAC,CAACC,MAAM,CAACC;gBAAQ,CAAC,CAAC,CAAE;gBAC/EvB,SAAS,EAAC;cAAgD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACFrE,OAAA;gBAAMgE,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CrE,OAAA;gBAAMgE,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACRrE,OAAA;cACEwF,OAAO,EAAEjC,YAAa;cACtBS,SAAS,EAAC,uJAAuJ;cAAAC,QAAA,EAClK;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbrE,OAAA,CAACN,MAAM,CAACgF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC1CjB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBAEtDjE,OAAA;UAAKgE,SAAS,EAAC,sEAAsE;UAAAC,QAAA,eACnFjE,OAAA;YAAKgE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCjE,OAAA;cAAMgE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzCrE,OAAA;cAAAiE,QAAA,gBACEjE,OAAA;gBAAGgE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrDrE,OAAA;gBAAGgE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAE9C;cAAU;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA;UAAKgE,SAAS,EAAC,wEAAwE;UAAAC,QAAA,eACrFjE,OAAA;YAAKgE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCjE,OAAA;cAAMgE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxCrE,OAAA;cAAAiE,QAAA,gBACEjE,OAAA;gBAAGgE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpDrE,OAAA;gBAAGgE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAE7D,OAAO,CAACqF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC3B,WAAW,CAAC,CAAC4B;cAAM;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA;UAAKgE,SAAS,EAAC,0EAA0E;UAAAC,QAAA,eACvFjE,OAAA;YAAKgE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCjE,OAAA;cAAMgE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzCrE,OAAA;cAAAiE,QAAA,gBACEjE,OAAA;gBAAGgE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnDrE,OAAA;gBAAGgE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAC9B7D,OAAO,CAACuF,MAAM,GAAG,CAAC,GACfnB,IAAI,CAACoB,KAAK,CAACxF,OAAO,CAACyF,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC,KAAKI,GAAG,IAAIJ,CAAC,CAACK,mBAAmB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG3F,OAAO,CAACuF,MAAM,CAAC,GAC9F,CAAC,EAAC,GACR;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGZ/D,OAAO,iBACNN,OAAA;QAAKgE,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDjE,OAAA;UAAKgE,SAAS,EAAC;QAAgE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF,CACN,EAGA,CAAC/D,OAAO,iBACPN,OAAA,CAACN,MAAM,CAACgF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBE,OAAO,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACxBG,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC1CjB,SAAS,EAAC,2DAA2D;QAAAC,QAAA,EAEpE7D,OAAO,CAACuF,MAAM,KAAK,CAAC,gBACnB3F,OAAA;UAAKgE,SAAS,EAAC,iCAAiC;UAAAC,QAAA,eAC9CjE,OAAA;YAAGgE,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,GAENjE,OAAO,CAAC4F,GAAG,CAAC,CAAClC,MAAM,EAAEmC,KAAK,kBACxBjG,OAAA,CAACN,MAAM,CAACgF,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAEgB,KAAK,GAAG;UAAI,CAAE;UAClDjC,SAAS,EAAC,uLAAuL;UAAAC,QAAA,eAEjMjE,OAAA;YAAKgE,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBjE,OAAA;cAAKgE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDjE,OAAA;gBAAIgE,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,EACzEH,MAAM,CAACoC;cAAK;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,EACJR,cAAc,CAACC,MAAM,CAAC;YAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eAENrE,OAAA;cAAGgE,SAAS,EAAC,oDAAoD;cAAAC,QAAA,EAC9DH,MAAM,CAACqC;YAAW;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eAEJrE,OAAA;cAAKgE,SAAS,EAAC,iFAAiF;cAAAC,QAAA,gBAC9FjE,OAAA;gBAAAiE,QAAA,GAAM,eAAG,EAACH,MAAM,CAACsC,aAAa;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtCrE,OAAA;gBAAAiE,QAAA,GAAM,eAAG,EAACO,IAAI,CAACoB,KAAK,CAAC9B,MAAM,CAACuC,kBAAkB,GAAG,EAAE,CAAC,EAAC,GAAC;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,EAELP,MAAM,CAACC,WAAW,IAAID,MAAM,CAACiC,mBAAmB,KAAKO,SAAS,iBAC7DtG,OAAA;cAAKgE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjE,OAAA;gBAAKgE,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,gBACjFjE,OAAA;kBAAAiE,QAAA,EAAM;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpBrE,OAAA;kBAAAiE,QAAA,GAAOH,MAAM,CAACiC,mBAAmB,EAAC,GAAC;gBAAA;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACNrE,OAAA;gBAAKgE,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,eACnEjE,OAAA;kBACEgE,SAAS,EAAC,0DAA0D;kBACpEuC,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAG1C,MAAM,CAACiC,mBAAmB;kBAAI;gBAAE;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eAEDrE,OAAA;cAAKgE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAC5B,CAACH,MAAM,CAACC,WAAW,gBAClB/D,OAAA;gBACEwF,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAACM,MAAM,CAAC2C,EAAE,EAAE3C,MAAM,CAACoC,KAAK,CAAE;gBACrDlC,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,EAC/G;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,gBAETrE,OAAA;gBAAQgE,SAAS,EAAC,sGAAsG;gBAAAC,QAAA,EAAC;cAEzH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT,eACDrE,OAAA;gBAAQgE,SAAS,EAAC,6JAA6J;gBAAAC,QAAA,EAAC;cAEhL;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAvDDP,MAAM,CAAC2C,EAAE;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwDJ,CACb;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CACb,EAGA,CAAC/D,OAAO,IAAIF,OAAO,CAACuF,MAAM,GAAG,CAAC,iBAC7B3F,OAAA,CAACN,MAAM,CAACgF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEE,KAAK,EAAE;QAAI,CAAE;QAC3BjB,SAAS,EAAC,OAAO;QAAAC,QAAA,eAEjBjE,OAAA,CAACH,UAAU;UACTkB,WAAW,EAAEA,WAAY;UACzBwD,UAAU,EAAEA,UAAW;UACvBpD,UAAU,EAAEA,UAAW;UACvBF,YAAY,EAAEA,YAAa;UAC3ByF,YAAY,EAAEjE,gBAAiB;UAC/BkE,oBAAoB,EAAE5D,wBAAyB;UAC/C6D,gBAAgB,EAAE,IAAK;UACvBC,QAAQ,EAAE,IAAK;UACfC,mBAAmB,EAAErF;QAAwB;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnE,EAAA,CAhZID,WAAqB;EAAA,QACRN,OAAO;AAAA;AAAAoH,EAAA,GADpB9G,WAAqB;AAkZ3B,eAAeA,WAAW;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}