{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ParserDependencies } from './dependenciesParserClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createParser } from '../../factoriesAny.js';\nexport var parserDependencies = {\n  ParserDependencies,\n  typedDependencies,\n  createParser\n};", "map": {"version": 3, "names": ["ParserDependencies", "typedDependencies", "create<PERSON><PERSON><PERSON>", "parserDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesParser.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ParserDependencies } from './dependenciesParserClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createParser } from '../../factoriesAny.js';\nexport var parserDependencies = {\n  ParserDependencies,\n  typedDependencies,\n  createParser\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAO,IAAIC,kBAAkB,GAAG;EAC9BH,kBAAkB;EAClBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}