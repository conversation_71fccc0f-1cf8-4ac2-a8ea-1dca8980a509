{"ast": null, "code": "export var dotPowDocs = {\n  name: 'dotPow',\n  category: 'Operators',\n  syntax: ['x .^ y', 'dotPow(x, y)'],\n  description: 'Calculates the power of x to y element wise.',\n  examples: ['a = [1, 2, 3; 4, 5, 6]', 'a .^ 2'],\n  seealso: ['pow']\n};", "map": {"version": 3, "names": ["dotPowDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/dotPow.js"], "sourcesContent": ["export var dotPowDocs = {\n  name: 'dotPow',\n  category: 'Operators',\n  syntax: ['x .^ y', 'dotPow(x, y)'],\n  description: 'Calculates the power of x to y element wise.',\n  examples: ['a = [1, 2, 3; 4, 5, 6]', 'a .^ 2'],\n  seealso: ['pow']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,QAAQ,EAAE,cAAc,CAAC;EAClCC,WAAW,EAAE,8CAA8C;EAC3DC,QAAQ,EAAE,CAAC,wBAAwB,EAAE,QAAQ,CAAC;EAC9CC,OAAO,EAAE,CAAC,KAAK;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}