{"ast": null, "code": "export var piDocs = {\n  name: 'pi',\n  category: 'Constants',\n  syntax: ['pi'],\n  description: 'The number pi is a mathematical constant that is the ratio of a circle\\'s circumference to its diameter, and is approximately equal to 3.14159',\n  examples: ['pi', 'sin(pi/2)'],\n  seealso: ['tau']\n};", "map": {"version": 3, "names": ["piDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/pi.js"], "sourcesContent": ["export var piDocs = {\n  name: 'pi',\n  category: 'Constants',\n  syntax: ['pi'],\n  description: 'The number pi is a mathematical constant that is the ratio of a circle\\'s circumference to its diameter, and is approximately equal to 3.14159',\n  examples: ['pi', 'sin(pi/2)'],\n  seealso: ['tau']\n};"], "mappings": "AAAA,OAAO,IAAIA,MAAM,GAAG;EAClBC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,IAAI,CAAC;EACdC,WAAW,EAAE,gJAAgJ;EAC7JC,QAAQ,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;EAC7BC,OAAO,EAAE,CAAC,KAAK;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}