{"ast": null, "code": "export var atan2Docs = {\n  name: 'atan2',\n  category: 'Trigonometry',\n  syntax: ['atan2(y, x)'],\n  description: 'Computes the principal value of the arc tangent of y/x in radians.',\n  examples: ['atan2(2, 2) / pi', 'angle = 60 deg in rad', 'x = cos(angle)', 'y = sin(angle)', 'atan2(y, x)'],\n  seealso: ['sin', 'cos', 'tan']\n};", "map": {"version": 3, "names": ["atan2Docs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/atan2.js"], "sourcesContent": ["export var atan2Docs = {\n  name: 'atan2',\n  category: 'Trigonometry',\n  syntax: ['atan2(y, x)'],\n  description: 'Computes the principal value of the arc tangent of y/x in radians.',\n  examples: ['atan2(2, 2) / pi', 'angle = 60 deg in rad', 'x = cos(angle)', 'y = sin(angle)', 'atan2(y, x)'],\n  seealso: ['sin', 'cos', 'tan']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,aAAa,CAAC;EACvBC,WAAW,EAAE,oEAAoE;EACjFC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,uBAAuB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,CAAC;EAC1GC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}