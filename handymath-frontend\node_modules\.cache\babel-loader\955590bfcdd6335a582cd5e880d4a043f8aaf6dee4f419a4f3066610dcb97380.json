{"ast": null, "code": "export var setSymDifferenceDocs = {\n  name: 'setSymDifference',\n  category: 'Set',\n  syntax: ['setSymDifference(set1, set2)'],\n  description: 'Create the symmetric difference of two (multi)sets. Multi-dimension arrays will be converted to single-dimension arrays before the operation.',\n  examples: ['setSymDifference([1, 2, 3, 4], [3, 4, 5, 6])', 'setSymDifference([[1, 2], [3, 4]], [[3, 4], [5, 6]])'],\n  seealso: ['setUnion', 'setIntersect', 'setDifference']\n};", "map": {"version": 3, "names": ["setSymDifferenceDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/set/setSymDifference.js"], "sourcesContent": ["export var setSymDifferenceDocs = {\n  name: 'setSymDifference',\n  category: 'Set',\n  syntax: ['setSymDifference(set1, set2)'],\n  description: 'Create the symmetric difference of two (multi)sets. Multi-dimension arrays will be converted to single-dimension arrays before the operation.',\n  examples: ['setSymDifference([1, 2, 3, 4], [3, 4, 5, 6])', 'setSymDifference([[1, 2], [3, 4]], [[3, 4], [5, 6]])'],\n  seealso: ['setUnion', 'setIntersect', 'setDifference']\n};"], "mappings": "AAAA,OAAO,IAAIA,oBAAoB,GAAG;EAChCC,IAAI,EAAE,kBAAkB;EACxBC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,CAAC,8BAA8B,CAAC;EACxCC,WAAW,EAAE,+IAA+I;EAC5JC,QAAQ,EAAE,CAAC,8CAA8C,EAAE,sDAAsD,CAAC;EAClHC,OAAO,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,eAAe;AACvD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}