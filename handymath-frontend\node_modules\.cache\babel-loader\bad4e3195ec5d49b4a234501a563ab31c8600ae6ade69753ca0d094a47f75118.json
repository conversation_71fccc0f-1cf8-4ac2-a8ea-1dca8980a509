{"ast": null, "code": "export var compareTextDocs = {\n  name: 'compareText',\n  category: 'Relational',\n  syntax: ['compareText(x, y)'],\n  description: 'Compare two strings lexically. Comparison is case sensitive. ' + 'Returns 1 when x > y, -1 when x < y, and 0 when x == y.',\n  examples: ['compareText(\"B\", \"A\")', 'compareText(\"A\", \"B\")', 'compareText(\"A\", \"A\")', 'compareText(\"2\", \"10\")', 'compare(\"2\", \"10\")', 'compare(2, 10)', 'compareNatural(\"2\", \"10\")', 'compareText(\"B\", [\"A\", \"B\", \"C\"])'],\n  seealso: ['compare', 'compareNatural']\n};", "map": {"version": 3, "names": ["compareTextDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/relational/compareText.js"], "sourcesContent": ["export var compareTextDocs = {\n  name: 'compareText',\n  category: 'Relational',\n  syntax: ['compareText(x, y)'],\n  description: 'Compare two strings lexically. Comparison is case sensitive. ' + 'Returns 1 when x > y, -1 when x < y, and 0 when x == y.',\n  examples: ['compareText(\"B\", \"A\")', 'compareText(\"A\", \"B\")', 'compareText(\"A\", \"A\")', 'compareText(\"2\", \"10\")', 'compare(\"2\", \"10\")', 'compare(2, 10)', 'compareNatural(\"2\", \"10\")', 'compareText(\"B\", [\"A\", \"B\", \"C\"])'],\n  seealso: ['compare', 'compareNatural']\n};"], "mappings": "AAAA,OAAO,IAAIA,eAAe,GAAG;EAC3BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,mBAAmB,CAAC;EAC7BC,WAAW,EAAE,+DAA+D,GAAG,yDAAyD;EACxIC,QAAQ,EAAE,CAAC,uBAAuB,EAAE,uBAAuB,EAAE,uBAAuB,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,2BAA2B,EAAE,mCAAmC,CAAC;EACzNC,OAAO,EAAE,CAAC,SAAS,EAAE,gBAAgB;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}