{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { isZeroDependencies } from './dependenciesIsZero.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMatrixFromFunction } from '../../factoriesAny.js';\nexport var matrixFromFunctionDependencies = {\n  isZeroDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createMatrixFromFunction\n};", "map": {"version": 3, "names": ["isZeroDependencies", "matrixDependencies", "typedDependencies", "createMatrixFromFunction", "matrixFromFunctionDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMatrixFromFunction.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { isZeroDependencies } from './dependenciesIsZero.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMatrixFromFunction } from '../../factoriesAny.js';\nexport var matrixFromFunctionDependencies = {\n  isZeroDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createMatrixFromFunction\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,OAAO,IAAIC,8BAA8B,GAAG;EAC1CJ,kBAAkB;EAClBC,kBAAkB;EAClBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}