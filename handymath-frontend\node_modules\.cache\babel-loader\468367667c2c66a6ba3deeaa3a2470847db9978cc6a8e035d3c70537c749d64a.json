{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { clone } from '../../../utils/object.js';\nvar name = 'matAlgo14xDs';\nvar dependencies = ['typed'];\nexport var createMatAlgo14xDs = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Iterates over DenseMatrix items and invokes the callback function f(Aij..z, b).\n   * Callback function invoked MxN times.\n   *\n   * C(i,j,...z) = f(Aij..z, b)\n   *\n   * @param {Matrix}   a                 The DenseMatrix instance (A)\n   * @param {Scalar}   b                 The Scalar value\n   * @param {Function} callback          The f(Aij..z,b) operation to invoke\n   * @param {boolean}  inverse           A true value indicates callback should be invoked f(b,Aij..z)\n   *\n   * @return {Matrix}                    DenseMatrix (C)\n   *\n   * https://github.com/josdejong/mathjs/pull/346#issuecomment-97659042\n   */\n  return function matAlgo14xDs(a, b, callback, inverse) {\n    // a arrays\n    var adata = a._data;\n    var asize = a._size;\n    var adt = a._datatype;\n\n    // datatype\n    var dt;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string') {\n      // datatype\n      dt = adt;\n      // convert b to the same datatype\n      b = typed.convert(b, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // populate cdata, iterate through dimensions\n    var cdata = asize.length > 0 ? _iterate(cf, 0, asize, asize[0], adata, b, inverse) : [];\n\n    // c matrix\n    return a.createDenseMatrix({\n      data: cdata,\n      size: clone(asize),\n      datatype: dt\n    });\n  };\n\n  // recursive function\n  function _iterate(f, level, s, n, av, bv, inverse) {\n    // initialize array for this level\n    var cv = [];\n    // check we reach the last level\n    if (level === s.length - 1) {\n      // loop arrays in last level\n      for (var i = 0; i < n; i++) {\n        // invoke callback and store value\n        cv[i] = inverse ? f(bv, av[i]) : f(av[i], bv);\n      }\n    } else {\n      // iterate current level\n      for (var j = 0; j < n; j++) {\n        // iterate next level\n        cv[j] = _iterate(f, level + 1, s, s[level + 1], av[j], bv, inverse);\n      }\n    }\n    return cv;\n  }\n});", "map": {"version": 3, "names": ["factory", "clone", "name", "dependencies", "createMatAlgo14xDs", "_ref", "typed", "matAlgo14xDs", "a", "b", "callback", "inverse", "adata", "_data", "asize", "_size", "adt", "_datatype", "dt", "cf", "convert", "find", "cdata", "length", "_iterate", "createDenseMatrix", "data", "size", "datatype", "f", "level", "s", "n", "av", "bv", "cv", "i", "j"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/utils/matAlgo14xDs.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nimport { clone } from '../../../utils/object.js';\nvar name = 'matAlgo14xDs';\nvar dependencies = ['typed'];\nexport var createMatAlgo14xDs = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Iterates over DenseMatrix items and invokes the callback function f(Aij..z, b).\n   * Callback function invoked MxN times.\n   *\n   * C(i,j,...z) = f(Aij..z, b)\n   *\n   * @param {Matrix}   a                 The DenseMatrix instance (A)\n   * @param {Scalar}   b                 The Scalar value\n   * @param {Function} callback          The f(Aij..z,b) operation to invoke\n   * @param {boolean}  inverse           A true value indicates callback should be invoked f(b,Aij..z)\n   *\n   * @return {Matrix}                    DenseMatrix (C)\n   *\n   * https://github.com/josdejong/mathjs/pull/346#issuecomment-97659042\n   */\n  return function matAlgo14xDs(a, b, callback, inverse) {\n    // a arrays\n    var adata = a._data;\n    var asize = a._size;\n    var adt = a._datatype;\n\n    // datatype\n    var dt;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string') {\n      // datatype\n      dt = adt;\n      // convert b to the same datatype\n      b = typed.convert(b, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // populate cdata, iterate through dimensions\n    var cdata = asize.length > 0 ? _iterate(cf, 0, asize, asize[0], adata, b, inverse) : [];\n\n    // c matrix\n    return a.createDenseMatrix({\n      data: cdata,\n      size: clone(asize),\n      datatype: dt\n    });\n  };\n\n  // recursive function\n  function _iterate(f, level, s, n, av, bv, inverse) {\n    // initialize array for this level\n    var cv = [];\n    // check we reach the last level\n    if (level === s.length - 1) {\n      // loop arrays in last level\n      for (var i = 0; i < n; i++) {\n        // invoke callback and store value\n        cv[i] = inverse ? f(bv, av[i]) : f(av[i], bv);\n      }\n    } else {\n      // iterate current level\n      for (var j = 0; j < n; j++) {\n        // iterate next level\n        cv[j] = _iterate(f, level + 1, s, s[level + 1], av[j], bv, inverse);\n      }\n    }\n    return cv;\n  }\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,KAAK,QAAQ,0BAA0B;AAChD,IAAIC,IAAI,GAAG,cAAc;AACzB,IAAIC,YAAY,GAAG,CAAC,OAAO,CAAC;AAC5B,OAAO,IAAIC,kBAAkB,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACjF,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASE,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACpD;IACA,IAAIC,KAAK,GAAGJ,CAAC,CAACK,KAAK;IACnB,IAAIC,KAAK,GAAGN,CAAC,CAACO,KAAK;IACnB,IAAIC,GAAG,GAAGR,CAAC,CAACS,SAAS;;IAErB;IACA,IAAIC,EAAE;IACN;IACA,IAAIC,EAAE,GAAGT,QAAQ;;IAEjB;IACA,IAAI,OAAOM,GAAG,KAAK,QAAQ,EAAE;MAC3B;MACAE,EAAE,GAAGF,GAAG;MACR;MACAP,CAAC,GAAGH,KAAK,CAACc,OAAO,CAACX,CAAC,EAAES,EAAE,CAAC;MACxB;MACAC,EAAE,GAAGb,KAAK,CAACe,IAAI,CAACX,QAAQ,EAAE,CAACQ,EAAE,EAAEA,EAAE,CAAC,CAAC;IACrC;;IAEA;IACA,IAAII,KAAK,GAAGR,KAAK,CAACS,MAAM,GAAG,CAAC,GAAGC,QAAQ,CAACL,EAAE,EAAE,CAAC,EAAEL,KAAK,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAEF,KAAK,EAAEH,CAAC,EAAEE,OAAO,CAAC,GAAG,EAAE;;IAEvF;IACA,OAAOH,CAAC,CAACiB,iBAAiB,CAAC;MACzBC,IAAI,EAAEJ,KAAK;MACXK,IAAI,EAAE1B,KAAK,CAACa,KAAK,CAAC;MAClBc,QAAQ,EAAEV;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,SAASM,QAAQA,CAACK,CAAC,EAAEC,KAAK,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEvB,OAAO,EAAE;IACjD;IACA,IAAIwB,EAAE,GAAG,EAAE;IACX;IACA,IAAIL,KAAK,KAAKC,CAAC,CAACR,MAAM,GAAG,CAAC,EAAE;MAC1B;MACA,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAAE;QAC1B;QACAD,EAAE,CAACC,CAAC,CAAC,GAAGzB,OAAO,GAAGkB,CAAC,CAACK,EAAE,EAAED,EAAE,CAACG,CAAC,CAAC,CAAC,GAAGP,CAAC,CAACI,EAAE,CAACG,CAAC,CAAC,EAAEF,EAAE,CAAC;MAC/C;IACF,CAAC,MAAM;MACL;MACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,EAAEK,CAAC,EAAE,EAAE;QAC1B;QACAF,EAAE,CAACE,CAAC,CAAC,GAAGb,QAAQ,CAACK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEC,CAAC,EAAEA,CAAC,CAACD,KAAK,GAAG,CAAC,CAAC,EAAEG,EAAE,CAACI,CAAC,CAAC,EAAEH,EAAE,EAAEvB,OAAO,CAAC;MACrE;IACF;IACA,OAAOwB,EAAE;EACX;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}