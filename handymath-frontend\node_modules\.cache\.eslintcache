[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\AdminDashboard.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\LoadingSpinner.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\services\\api.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\StudentNavbar.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Pagination.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\AdminNavbar.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Logo.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\TestLoginPage.tsx": "40"}, {"size": 277, "mtime": 1749080956168, "results": "41", "hashOfConfig": "42"}, {"size": 6142, "mtime": 1751065375235, "results": "43", "hashOfConfig": "42"}, {"size": 802, "mtime": 1749046473890, "results": "44", "hashOfConfig": "42"}, {"size": 16820, "mtime": 1751058742068, "results": "45", "hashOfConfig": "42"}, {"size": 31759, "mtime": 1748983338825, "results": "46", "hashOfConfig": "42"}, {"size": 218, "mtime": 1748952089453, "results": "47", "hashOfConfig": "42"}, {"size": 1931, "mtime": 1748954075713, "results": "48", "hashOfConfig": "42"}, {"size": 4380, "mtime": 1749046473889, "results": "49", "hashOfConfig": "42"}, {"size": 4293, "mtime": 1749292696790, "results": "50", "hashOfConfig": "42"}, {"size": 7365, "mtime": 1749046902629, "results": "51", "hashOfConfig": "42"}, {"size": 14753, "mtime": 1751059007476, "results": "52", "hashOfConfig": "42"}, {"size": 15014, "mtime": 1749047338072, "results": "53", "hashOfConfig": "42"}, {"size": 21412, "mtime": 1751059040214, "results": "54", "hashOfConfig": "42"}, {"size": 15273, "mtime": 1749047898131, "results": "55", "hashOfConfig": "42"}, {"size": 13174, "mtime": 1751064861095, "results": "56", "hashOfConfig": "42"}, {"size": 17585, "mtime": 1751064766210, "results": "57", "hashOfConfig": "42"}, {"size": 19109, "mtime": 1749051118945, "results": "58", "hashOfConfig": "42"}, {"size": 14898, "mtime": 1751061448129, "results": "59", "hashOfConfig": "42"}, {"size": 10229, "mtime": 1749070380168, "results": "60", "hashOfConfig": "42"}, {"size": 12577, "mtime": 1751064903153, "results": "61", "hashOfConfig": "42"}, {"size": 8794, "mtime": 1749070718719, "results": "62", "hashOfConfig": "42"}, {"size": 18459, "mtime": 1749070497141, "results": "63", "hashOfConfig": "42"}, {"size": 7692, "mtime": 1749051903569, "results": "64", "hashOfConfig": "42"}, {"size": 5122, "mtime": 1749052811678, "results": "65", "hashOfConfig": "42"}, {"size": 20371, "mtime": 1749070759007, "results": "66", "hashOfConfig": "42"}, {"size": 2721, "mtime": 1749293050442, "results": "67", "hashOfConfig": "42"}, {"size": 6715, "mtime": 1749068251807, "results": "68", "hashOfConfig": "42"}, {"size": 8596, "mtime": 1751060631955, "results": "69", "hashOfConfig": "42"}, {"size": 7912, "mtime": 1751060763713, "results": "70", "hashOfConfig": "42"}, {"size": 11434, "mtime": 1749076479018, "results": "71", "hashOfConfig": "42"}, {"size": 12684, "mtime": 1749087573195, "results": "72", "hashOfConfig": "42"}, {"size": 13617, "mtime": 1749073014242, "results": "73", "hashOfConfig": "42"}, {"size": 6024, "mtime": 1749072916570, "results": "74", "hashOfConfig": "42"}, {"size": 6719, "mtime": 1751060595509, "results": "75", "hashOfConfig": "42"}, {"size": 19389, "mtime": 1751059099295, "results": "76", "hashOfConfig": "42"}, {"size": 10020, "mtime": 1751060676365, "results": "77", "hashOfConfig": "42"}, {"size": 6490, "mtime": 1751060815006, "results": "78", "hashOfConfig": "42"}, {"size": 10078, "mtime": 1751060709665, "results": "79", "hashOfConfig": "42"}, {"size": 1993, "mtime": 1751060524742, "results": "80", "hashOfConfig": "42"}, {"size": 5581, "mtime": 1751065339614, "results": "81", "hashOfConfig": "42"}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1spffp8", {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx", ["202", "203"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx", ["204"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx", [], ["205"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx", ["206"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx", ["207"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx", ["208"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx", ["209", "210", "211", "212"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx", ["213", "214", "215"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx", ["216"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx", ["217"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx", ["218"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx", ["219"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx", ["220"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx", ["221", "222", "223", "224"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx", ["225", "226", "227", "228"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\StudentNavbar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Pagination.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\AdminNavbar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Logo.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\TestLoginPage.tsx", [], [], {"ruleId": "229", "severity": 1, "message": "230", "line": 69, "column": 9, "nodeType": "231", "messageId": "232", "endLine": 69, "endColumn": 20}, {"ruleId": "233", "severity": 1, "message": "234", "line": 78, "column": 6, "nodeType": "235", "endLine": 78, "endColumn": 12, "suggestions": "236"}, {"ruleId": "233", "severity": 1, "message": "237", "line": 52, "column": 6, "nodeType": "235", "endLine": 52, "endColumn": 8, "suggestions": "238"}, {"ruleId": "233", "severity": 1, "message": "239", "line": 60, "column": 6, "nodeType": "235", "endLine": 60, "endColumn": 12, "suggestions": "240", "suppressions": "241"}, {"ruleId": "233", "severity": 1, "message": "242", "line": 82, "column": 6, "nodeType": "235", "endLine": 82, "endColumn": 22, "suggestions": "243"}, {"ruleId": "233", "severity": 1, "message": "244", "line": 63, "column": 6, "nodeType": "235", "endLine": 63, "endColumn": 16, "suggestions": "245"}, {"ruleId": "233", "severity": 1, "message": "246", "line": 89, "column": 6, "nodeType": "235", "endLine": 89, "endColumn": 48, "suggestions": "247"}, {"ruleId": "229", "severity": 1, "message": "248", "line": 69, "column": 22, "nodeType": "231", "messageId": "232", "endLine": 69, "endColumn": 35}, {"ruleId": "233", "severity": 1, "message": "249", "line": 79, "column": 6, "nodeType": "235", "endLine": 79, "endColumn": 16, "suggestions": "250"}, {"ruleId": "233", "severity": 1, "message": "251", "line": 98, "column": 6, "nodeType": "235", "endLine": 98, "endColumn": 48, "suggestions": "252"}, {"ruleId": "229", "severity": 1, "message": "253", "line": 173, "column": 60, "nodeType": "231", "messageId": "232", "endLine": 173, "endColumn": 71}, {"ruleId": "229", "severity": 1, "message": "230", "line": 45, "column": 9, "nodeType": "231", "messageId": "232", "endLine": 45, "endColumn": 20}, {"ruleId": "229", "severity": 1, "message": "254", "line": 51, "column": 9, "nodeType": "231", "messageId": "232", "endLine": 51, "endColumn": 20}, {"ruleId": "233", "severity": 1, "message": "255", "line": 78, "column": 6, "nodeType": "235", "endLine": 78, "endColumn": 48, "suggestions": "256"}, {"ruleId": "233", "severity": 1, "message": "257", "line": 61, "column": 6, "nodeType": "235", "endLine": 61, "endColumn": 16, "suggestions": "258"}, {"ruleId": "233", "severity": 1, "message": "259", "line": 113, "column": 6, "nodeType": "235", "endLine": 113, "endColumn": 21, "suggestions": "260"}, {"ruleId": "233", "severity": 1, "message": "261", "line": 39, "column": 6, "nodeType": "235", "endLine": 39, "endColumn": 12, "suggestions": "262"}, {"ruleId": "233", "severity": 1, "message": "263", "line": 45, "column": 6, "nodeType": "235", "endLine": 45, "endColumn": 12, "suggestions": "264"}, {"ruleId": "229", "severity": 1, "message": "265", "line": 1, "column": 27, "nodeType": "231", "messageId": "232", "endLine": 1, "endColumn": 36}, {"ruleId": "266", "severity": 1, "message": "267", "line": 137, "column": 17, "nodeType": "268", "endLine": 140, "endColumn": 18}, {"ruleId": "266", "severity": 1, "message": "267", "line": 159, "column": 15, "nodeType": "268", "endLine": 162, "endColumn": 16}, {"ruleId": "266", "severity": 1, "message": "267", "line": 165, "column": 15, "nodeType": "268", "endLine": 168, "endColumn": 16}, {"ruleId": "266", "severity": 1, "message": "267", "line": 171, "column": 15, "nodeType": "268", "endLine": 174, "endColumn": 16}, {"ruleId": "229", "severity": 1, "message": "269", "line": 51, "column": 10, "nodeType": "231", "messageId": "232", "endLine": 51, "endColumn": 25}, {"ruleId": "229", "severity": 1, "message": "270", "line": 52, "column": 10, "nodeType": "231", "messageId": "232", "endLine": 52, "endColumn": 19}, {"ruleId": "233", "severity": 1, "message": "271", "line": 75, "column": 6, "nodeType": "235", "endLine": 75, "endColumn": 38, "suggestions": "272"}, {"ruleId": "229", "severity": 1, "message": "273", "line": 114, "column": 9, "nodeType": "231", "messageId": "232", "endLine": 114, "endColumn": 27}, "@typescript-eslint/no-unused-vars", "'showSuccess' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStudentData'. Either include it or remove the dependency array.", "ArrayExpression", ["274"], "React Hook useCallback has a missing dependency: 'removeNotification'. Either include it or remove the dependency array.", ["275"], "React Hook useEffect has a missing dependency: 'fetchAdminData'. Either include it or remove the dependency array.", ["276"], ["277"], "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["278"], "React Hook useEffect has a missing dependency: 'fetchCourse'. Either include it or remove the dependency array.", ["279"], "React Hook useEffect has a missing dependency: 'fetchCourses'. Either include it or remove the dependency array.", ["280"], "'setUserAnswer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExercise'. Either include it or remove the dependency array.", ["281"], "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["282"], "'explanation' is assigned a value but never used.", "'showWarning' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExercises'. Either include it or remove the dependency array.", ["283"], "React Hook useEffect has a missing dependency: 'fetchLesson'. Either include it or remove the dependency array.", ["284"], "React Hook useEffect has a missing dependency: 'renderFunction2D'. Either include it or remove the dependency array.", ["285"], "React Hook useEffect has a missing dependency: 'fetchProgressData'. Either include it or remove the dependency array.", ["286"], "React Hook useEffect has a missing dependency: 'fetchProfile'. Either include it or remove the dependency array.", ["287"], "'useEffect' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'selectedMessage' is assigned a value but never used.", "'showModal' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array.", ["288"], "'handleStatusUpdate' is assigned a value but never used.", {"desc": "289", "fix": "290"}, {"desc": "291", "fix": "292"}, {"desc": "293", "fix": "294"}, {"kind": "295", "justification": "296"}, {"desc": "297", "fix": "298"}, {"desc": "299", "fix": "300"}, {"desc": "301", "fix": "302"}, {"desc": "303", "fix": "304"}, {"desc": "305", "fix": "306"}, {"desc": "307", "fix": "308"}, {"desc": "309", "fix": "310"}, {"desc": "311", "fix": "312"}, {"desc": "313", "fix": "314"}, {"desc": "315", "fix": "316"}, {"desc": "317", "fix": "318"}, "Update the dependencies array to be: [fetchStudentData, user]", {"range": "319", "text": "320"}, "Update the dependencies array to be: [removeNotification]", {"range": "321", "text": "322"}, "Update the dependencies array to be: [fetchAdminData, user]", {"range": "323", "text": "324"}, "directive", "", "Update the dependencies array to be: [user, navigate, loadUsers]", {"range": "325", "text": "326"}, "Update the dependencies array to be: [user, id, fetchCourse]", {"range": "327", "text": "328"}, "Update the dependencies array to be: [user, filters, currentPage, itemsPerPage, fetchCourses]", {"range": "329", "text": "330"}, "Update the dependencies array to be: [user, id, fetchExercise]", {"range": "331", "text": "332"}, "Update the dependencies array to be: [timeLeft, exercise?.user_attempt?.status, handleSubmit]", {"range": "333", "text": "334"}, "Update the dependencies array to be: [user, filters, currentPage, itemsPerPage, fetchExercises]", {"range": "335", "text": "336"}, "Update the dependencies array to be: [user, id, fetchLesson]", {"range": "337", "text": "338"}, "Update the dependencies array to be: [functionInput, renderFunction2D]", {"range": "339", "text": "340"}, "Update the dependencies array to be: [fetchProgressData, user]", {"range": "341", "text": "342"}, "Update the dependencies array to be: [fetchProfile, user]", {"range": "343", "text": "344"}, "Update the dependencies array to be: [user, filters, pagination.page, fetchMessages]", {"range": "345", "text": "346"}, [2091, 2097], "[fetchStudentData, user]", [1627, 1629], "[removeNotification]", [1562, 1568], "[fetchAdmin<PERSON><PERSON>, user]", [2194, 2210], "[user, navigate, loadUsers]", [1522, 1532], "[user, id, fetchCourse]", [2731, 2773], "[user, filters, currentPage, itemsPerPage, fetchCourses]", [2224, 2234], "[user, id, fetchExercise]", [2759, 2801], "[timeLeft, exercise?.user_attempt?.status, handleSubmit]", [2079, 2121], "[user, filters, currentPage, itemsPerPage, fetchExercises]", [1549, 1559], "[user, id, fetchLesson]", [3341, 3356], "[functionInput, renderFunction2D]", [1044, 1050], "[fetchProgress<PERSON>ata, user]", [1117, 1123], "[fetchProfile, user]", [1969, 2001], "[user, filters, pagination.page, fetchMessages]"]