[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\AdminDashboard.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\LoadingSpinner.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\services\\api.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\StudentNavbar.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Pagination.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\AdminNavbar.tsx": "38"}, {"size": 277, "mtime": 1749080956168, "results": "39", "hashOfConfig": "40"}, {"size": 6024, "mtime": 1749087688409, "results": "41", "hashOfConfig": "40"}, {"size": 802, "mtime": 1749046473890, "results": "42", "hashOfConfig": "40"}, {"size": 16820, "mtime": 1751058742068, "results": "43", "hashOfConfig": "40"}, {"size": 31759, "mtime": 1748983338825, "results": "44", "hashOfConfig": "40"}, {"size": 218, "mtime": 1748952089453, "results": "45", "hashOfConfig": "40"}, {"size": 1931, "mtime": 1748954075713, "results": "46", "hashOfConfig": "40"}, {"size": 4380, "mtime": 1749046473889, "results": "47", "hashOfConfig": "40"}, {"size": 4293, "mtime": 1749292696790, "results": "48", "hashOfConfig": "40"}, {"size": 7365, "mtime": 1749046902629, "results": "49", "hashOfConfig": "40"}, {"size": 14753, "mtime": 1751059007476, "results": "50", "hashOfConfig": "40"}, {"size": 15014, "mtime": 1749047338072, "results": "51", "hashOfConfig": "40"}, {"size": 21412, "mtime": 1751059040214, "results": "52", "hashOfConfig": "40"}, {"size": 15273, "mtime": 1749047898131, "results": "53", "hashOfConfig": "40"}, {"size": 15195, "mtime": 1749048128423, "results": "54", "hashOfConfig": "40"}, {"size": 14570, "mtime": 1751058932187, "results": "55", "hashOfConfig": "40"}, {"size": 19109, "mtime": 1749051118945, "results": "56", "hashOfConfig": "40"}, {"size": 14538, "mtime": 1751058866451, "results": "57", "hashOfConfig": "40"}, {"size": 10229, "mtime": 1749070380168, "results": "58", "hashOfConfig": "40"}, {"size": 12569, "mtime": 1749051153981, "results": "59", "hashOfConfig": "40"}, {"size": 8794, "mtime": 1749070718719, "results": "60", "hashOfConfig": "40"}, {"size": 18459, "mtime": 1749070497141, "results": "61", "hashOfConfig": "40"}, {"size": 7692, "mtime": 1749051903569, "results": "62", "hashOfConfig": "40"}, {"size": 5122, "mtime": 1749052811678, "results": "63", "hashOfConfig": "40"}, {"size": 20371, "mtime": 1749070759007, "results": "64", "hashOfConfig": "40"}, {"size": 2721, "mtime": 1749293050442, "results": "65", "hashOfConfig": "40"}, {"size": 6715, "mtime": 1749068251807, "results": "66", "hashOfConfig": "40"}, {"size": 8736, "mtime": 1749073183622, "results": "67", "hashOfConfig": "40"}, {"size": 7800, "mtime": 1749076875344, "results": "68", "hashOfConfig": "40"}, {"size": 11434, "mtime": 1749076479018, "results": "69", "hashOfConfig": "40"}, {"size": 12684, "mtime": 1749087573195, "results": "70", "hashOfConfig": "40"}, {"size": 13617, "mtime": 1749073014242, "results": "71", "hashOfConfig": "40"}, {"size": 6024, "mtime": 1749072916570, "results": "72", "hashOfConfig": "40"}, {"size": 6853, "mtime": 1749076802984, "results": "73", "hashOfConfig": "40"}, {"size": 19389, "mtime": 1751059099295, "results": "74", "hashOfConfig": "40"}, {"size": 10350, "mtime": 1751059428733, "results": "75", "hashOfConfig": "40"}, {"size": 6514, "mtime": 1751058704220, "results": "76", "hashOfConfig": "40"}, {"size": 10406, "mtime": 1751059552538, "results": "77", "hashOfConfig": "40"}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1spffp8", {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx", ["192", "193"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx", ["194"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx", [], ["195"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx", ["196"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx", ["197"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx", ["198", "199", "200", "201", "202", "203", "204"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx", ["205", "206", "207", "208"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx", ["209", "210", "211"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx", ["212"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx", ["213"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx", ["214"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx", ["215"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx", ["216"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx", ["217", "218", "219", "220"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx", ["221", "222", "223", "224"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\StudentNavbar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Pagination.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\AdminNavbar.tsx", [], [], {"ruleId": "225", "severity": 1, "message": "226", "line": 69, "column": 9, "nodeType": "227", "messageId": "228", "endLine": 69, "endColumn": 20}, {"ruleId": "229", "severity": 1, "message": "230", "line": 78, "column": 6, "nodeType": "231", "endLine": 78, "endColumn": 12, "suggestions": "232"}, {"ruleId": "229", "severity": 1, "message": "233", "line": 52, "column": 6, "nodeType": "231", "endLine": 52, "endColumn": 8, "suggestions": "234"}, {"ruleId": "229", "severity": 1, "message": "235", "line": 60, "column": 6, "nodeType": "231", "endLine": 60, "endColumn": 12, "suggestions": "236", "suppressions": "237"}, {"ruleId": "229", "severity": 1, "message": "238", "line": 82, "column": 6, "nodeType": "231", "endLine": 82, "endColumn": 22, "suggestions": "239"}, {"ruleId": "229", "severity": 1, "message": "240", "line": 60, "column": 6, "nodeType": "231", "endLine": 60, "endColumn": 16, "suggestions": "241"}, {"ruleId": "225", "severity": 1, "message": "242", "line": 5, "column": 8, "nodeType": "227", "messageId": "228", "endLine": 5, "endColumn": 18}, {"ruleId": "225", "severity": 1, "message": "243", "line": 45, "column": 10, "nodeType": "227", "messageId": "228", "endLine": 45, "endColumn": 21}, {"ruleId": "225", "severity": 1, "message": "244", "line": 45, "column": 23, "nodeType": "227", "messageId": "228", "endLine": 45, "endColumn": 37}, {"ruleId": "225", "severity": 1, "message": "245", "line": 46, "column": 10, "nodeType": "227", "messageId": "228", "endLine": 46, "endColumn": 22}, {"ruleId": "225", "severity": 1, "message": "246", "line": 46, "column": 24, "nodeType": "227", "messageId": "228", "endLine": 46, "endColumn": 39}, {"ruleId": "225", "severity": 1, "message": "247", "line": 47, "column": 10, "nodeType": "227", "messageId": "228", "endLine": 47, "endColumn": 20}, {"ruleId": "225", "severity": 1, "message": "248", "line": 47, "column": 22, "nodeType": "227", "messageId": "228", "endLine": 47, "endColumn": 35}, {"ruleId": "225", "severity": 1, "message": "249", "line": 69, "column": 22, "nodeType": "227", "messageId": "228", "endLine": 69, "endColumn": 35}, {"ruleId": "229", "severity": 1, "message": "250", "line": 79, "column": 6, "nodeType": "231", "endLine": 79, "endColumn": 16, "suggestions": "251"}, {"ruleId": "229", "severity": 1, "message": "252", "line": 98, "column": 6, "nodeType": "231", "endLine": 98, "endColumn": 48, "suggestions": "253"}, {"ruleId": "225", "severity": 1, "message": "254", "line": 173, "column": 60, "nodeType": "227", "messageId": "228", "endLine": 173, "endColumn": 71}, {"ruleId": "225", "severity": 1, "message": "226", "line": 45, "column": 9, "nodeType": "227", "messageId": "228", "endLine": 45, "endColumn": 20}, {"ruleId": "225", "severity": 1, "message": "255", "line": 51, "column": 9, "nodeType": "227", "messageId": "228", "endLine": 51, "endColumn": 20}, {"ruleId": "229", "severity": 1, "message": "256", "line": 75, "column": 6, "nodeType": "231", "endLine": 75, "endColumn": 48, "suggestions": "257"}, {"ruleId": "229", "severity": 1, "message": "258", "line": 61, "column": 6, "nodeType": "231", "endLine": 61, "endColumn": 16, "suggestions": "259"}, {"ruleId": "229", "severity": 1, "message": "260", "line": 113, "column": 6, "nodeType": "231", "endLine": 113, "endColumn": 21, "suggestions": "261"}, {"ruleId": "229", "severity": 1, "message": "262", "line": 39, "column": 6, "nodeType": "231", "endLine": 39, "endColumn": 12, "suggestions": "263"}, {"ruleId": "229", "severity": 1, "message": "264", "line": 45, "column": 6, "nodeType": "231", "endLine": 45, "endColumn": 12, "suggestions": "265"}, {"ruleId": "225", "severity": 1, "message": "266", "line": 1, "column": 27, "nodeType": "227", "messageId": "228", "endLine": 1, "endColumn": 36}, {"ruleId": "267", "severity": 1, "message": "268", "line": 139, "column": 17, "nodeType": "269", "endLine": 142, "endColumn": 18}, {"ruleId": "267", "severity": 1, "message": "268", "line": 161, "column": 15, "nodeType": "269", "endLine": 164, "endColumn": 16}, {"ruleId": "267", "severity": 1, "message": "268", "line": 167, "column": 15, "nodeType": "269", "endLine": 170, "endColumn": 16}, {"ruleId": "267", "severity": 1, "message": "268", "line": 173, "column": 15, "nodeType": "269", "endLine": 176, "endColumn": 16}, {"ruleId": "225", "severity": 1, "message": "270", "line": 51, "column": 10, "nodeType": "227", "messageId": "228", "endLine": 51, "endColumn": 25}, {"ruleId": "225", "severity": 1, "message": "271", "line": 52, "column": 10, "nodeType": "227", "messageId": "228", "endLine": 52, "endColumn": 19}, {"ruleId": "229", "severity": 1, "message": "272", "line": 75, "column": 6, "nodeType": "231", "endLine": 75, "endColumn": 38, "suggestions": "273"}, {"ruleId": "225", "severity": 1, "message": "274", "line": 114, "column": 9, "nodeType": "227", "messageId": "228", "endLine": 114, "endColumn": 27}, "@typescript-eslint/no-unused-vars", "'showSuccess' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStudentData'. Either include it or remove the dependency array.", "ArrayExpression", ["275"], "React Hook useCallback has a missing dependency: 'removeNotification'. Either include it or remove the dependency array.", ["276"], "React Hook useEffect has a missing dependency: 'fetchAdminData'. Either include it or remove the dependency array.", ["277"], ["278"], "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["279"], "React Hook useEffect has a missing dependency: 'fetchCourse'. Either include it or remove the dependency array.", ["280"], "'Pagination' is defined but never used.", "'currentPage' is assigned a value but never used.", "'setCurrentPage' is assigned a value but never used.", "'itemsPerPage' is assigned a value but never used.", "'setItemsPerPage' is assigned a value but never used.", "'totalItems' is assigned a value but never used.", "'setTotalItems' is assigned a value but never used.", "'setUserAnswer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExercise'. Either include it or remove the dependency array.", ["281"], "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["282"], "'explanation' is assigned a value but never used.", "'showWarning' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExercises'. Either include it or remove the dependency array.", ["283"], "React Hook useEffect has a missing dependency: 'fetchLesson'. Either include it or remove the dependency array.", ["284"], "React Hook useEffect has a missing dependency: 'renderFunction2D'. Either include it or remove the dependency array.", ["285"], "React Hook useEffect has a missing dependency: 'fetchProgressData'. Either include it or remove the dependency array.", ["286"], "React Hook useEffect has a missing dependency: 'fetchProfile'. Either include it or remove the dependency array.", ["287"], "'useEffect' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'selectedMessage' is assigned a value but never used.", "'showModal' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array.", ["288"], "'handleStatusUpdate' is assigned a value but never used.", {"desc": "289", "fix": "290"}, {"desc": "291", "fix": "292"}, {"desc": "293", "fix": "294"}, {"kind": "295", "justification": "296"}, {"desc": "297", "fix": "298"}, {"desc": "299", "fix": "300"}, {"desc": "301", "fix": "302"}, {"desc": "303", "fix": "304"}, {"desc": "305", "fix": "306"}, {"desc": "307", "fix": "308"}, {"desc": "309", "fix": "310"}, {"desc": "311", "fix": "312"}, {"desc": "313", "fix": "314"}, {"desc": "315", "fix": "316"}, "Update the dependencies array to be: [fetchStudentData, user]", {"range": "317", "text": "318"}, "Update the dependencies array to be: [removeNotification]", {"range": "319", "text": "320"}, "Update the dependencies array to be: [fetchAdminData, user]", {"range": "321", "text": "322"}, "directive", "", "Update the dependencies array to be: [user, navigate, loadUsers]", {"range": "323", "text": "324"}, "Update the dependencies array to be: [user, id, fetchCourse]", {"range": "325", "text": "326"}, "Update the dependencies array to be: [user, id, fetchExercise]", {"range": "327", "text": "328"}, "Update the dependencies array to be: [timeLeft, exercise?.user_attempt?.status, handleSubmit]", {"range": "329", "text": "330"}, "Update the dependencies array to be: [user, filters, currentPage, itemsPerPage, fetchExercises]", {"range": "331", "text": "332"}, "Update the dependencies array to be: [user, id, fetchLesson]", {"range": "333", "text": "334"}, "Update the dependencies array to be: [functionInput, renderFunction2D]", {"range": "335", "text": "336"}, "Update the dependencies array to be: [fetchProgressData, user]", {"range": "337", "text": "338"}, "Update the dependencies array to be: [fetchProfile, user]", {"range": "339", "text": "340"}, "Update the dependencies array to be: [user, filters, pagination.page, fetchMessages]", {"range": "341", "text": "342"}, [2091, 2097], "[fetchStudentData, user]", [1627, 1629], "[removeNotification]", [1562, 1568], "[fetchAdmin<PERSON><PERSON>, user]", [2194, 2210], "[user, navigate, loadUsers]", [1368, 1378], "[user, id, fetchCourse]", [2224, 2234], "[user, id, fetchExercise]", [2759, 2801], "[timeLeft, exercise?.user_attempt?.status, handleSubmit]", [1979, 2021], "[user, filters, currentPage, itemsPerPage, fetchExercises]", [1549, 1559], "[user, id, fetchLesson]", [3341, 3356], "[functionInput, renderFunction2D]", [1044, 1050], "[fetchProgress<PERSON>ata, user]", [1117, 1123], "[fetchProfile, user]", [1969, 2001], "[user, filters, pagination.page, fetchMessages]"]