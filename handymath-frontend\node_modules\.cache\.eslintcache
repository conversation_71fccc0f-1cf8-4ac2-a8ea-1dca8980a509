[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\AdminDashboard.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\LoadingSpinner.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\services\\api.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\StudentNavbar.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Pagination.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\AdminNavbar.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Logo.tsx": "39"}, {"size": 277, "mtime": 1749080956168, "results": "40", "hashOfConfig": "41"}, {"size": 6024, "mtime": 1749087688409, "results": "42", "hashOfConfig": "41"}, {"size": 802, "mtime": 1749046473890, "results": "43", "hashOfConfig": "41"}, {"size": 16820, "mtime": 1751058742068, "results": "44", "hashOfConfig": "41"}, {"size": 31759, "mtime": 1748983338825, "results": "45", "hashOfConfig": "41"}, {"size": 218, "mtime": 1748952089453, "results": "46", "hashOfConfig": "41"}, {"size": 1931, "mtime": 1748954075713, "results": "47", "hashOfConfig": "41"}, {"size": 4380, "mtime": 1749046473889, "results": "48", "hashOfConfig": "41"}, {"size": 4293, "mtime": 1749292696790, "results": "49", "hashOfConfig": "41"}, {"size": 7365, "mtime": 1749046902629, "results": "50", "hashOfConfig": "41"}, {"size": 14753, "mtime": 1751059007476, "results": "51", "hashOfConfig": "41"}, {"size": 15014, "mtime": 1749047338072, "results": "52", "hashOfConfig": "41"}, {"size": 21412, "mtime": 1751059040214, "results": "53", "hashOfConfig": "41"}, {"size": 15273, "mtime": 1749047898131, "results": "54", "hashOfConfig": "41"}, {"size": 15195, "mtime": 1749048128423, "results": "55", "hashOfConfig": "41"}, {"size": 16883, "mtime": 1751063590452, "results": "56", "hashOfConfig": "41"}, {"size": 19109, "mtime": 1749051118945, "results": "57", "hashOfConfig": "41"}, {"size": 14898, "mtime": 1751061448129, "results": "58", "hashOfConfig": "41"}, {"size": 10229, "mtime": 1749070380168, "results": "59", "hashOfConfig": "41"}, {"size": 12569, "mtime": 1749051153981, "results": "60", "hashOfConfig": "41"}, {"size": 8794, "mtime": 1749070718719, "results": "61", "hashOfConfig": "41"}, {"size": 18459, "mtime": 1749070497141, "results": "62", "hashOfConfig": "41"}, {"size": 7692, "mtime": 1749051903569, "results": "63", "hashOfConfig": "41"}, {"size": 5122, "mtime": 1749052811678, "results": "64", "hashOfConfig": "41"}, {"size": 20371, "mtime": 1749070759007, "results": "65", "hashOfConfig": "41"}, {"size": 2721, "mtime": 1749293050442, "results": "66", "hashOfConfig": "41"}, {"size": 6715, "mtime": 1749068251807, "results": "67", "hashOfConfig": "41"}, {"size": 8596, "mtime": 1751060631955, "results": "68", "hashOfConfig": "41"}, {"size": 7912, "mtime": 1751060763713, "results": "69", "hashOfConfig": "41"}, {"size": 11434, "mtime": 1749076479018, "results": "70", "hashOfConfig": "41"}, {"size": 12684, "mtime": 1749087573195, "results": "71", "hashOfConfig": "41"}, {"size": 13617, "mtime": 1749073014242, "results": "72", "hashOfConfig": "41"}, {"size": 6024, "mtime": 1749072916570, "results": "73", "hashOfConfig": "41"}, {"size": 6719, "mtime": 1751060595509, "results": "74", "hashOfConfig": "41"}, {"size": 19389, "mtime": 1751059099295, "results": "75", "hashOfConfig": "41"}, {"size": 10020, "mtime": 1751060676365, "results": "76", "hashOfConfig": "41"}, {"size": 6490, "mtime": 1751060815006, "results": "77", "hashOfConfig": "41"}, {"size": 10078, "mtime": 1751060709665, "results": "78", "hashOfConfig": "41"}, {"size": 1993, "mtime": 1751060524742, "results": "79", "hashOfConfig": "41"}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1spffp8", {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\student\\StudentDashboard.tsx", ["197", "198"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\NotificationSystem.tsx", ["199"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminAnalytics.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminDashboard.tsx", [], ["200"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminCourses.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminUsers.tsx", ["201"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AdminExercises.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CourseDetailPage.tsx", ["202"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\CoursesPage.tsx", ["203"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExerciseDetailPage.tsx", ["204", "205", "206", "207"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ExercisesPage.tsx", ["208", "209", "210"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LessonDetailPage.tsx", ["211"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SolverPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\VisualizerPage.tsx", ["212"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProgressPage.tsx", ["213"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\ImageUpload.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\SimpleHeader.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\AboutPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ProfilePage.tsx", ["214"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\ContactPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\SettingsPage.tsx", ["215"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\errors\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Footer.tsx", ["216", "217", "218", "219"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\pages\\admin\\ContactMessagesPage.tsx", ["220", "221", "222", "223"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\StudentNavbar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Pagination.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\AdminNavbar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\HandyMath2\\handymath-frontend\\src\\components\\Logo.tsx", [], [], {"ruleId": "224", "severity": 1, "message": "225", "line": 69, "column": 9, "nodeType": "226", "messageId": "227", "endLine": 69, "endColumn": 20}, {"ruleId": "228", "severity": 1, "message": "229", "line": 78, "column": 6, "nodeType": "230", "endLine": 78, "endColumn": 12, "suggestions": "231"}, {"ruleId": "228", "severity": 1, "message": "232", "line": 52, "column": 6, "nodeType": "230", "endLine": 52, "endColumn": 8, "suggestions": "233"}, {"ruleId": "228", "severity": 1, "message": "234", "line": 60, "column": 6, "nodeType": "230", "endLine": 60, "endColumn": 12, "suggestions": "235", "suppressions": "236"}, {"ruleId": "228", "severity": 1, "message": "237", "line": 82, "column": 6, "nodeType": "230", "endLine": 82, "endColumn": 22, "suggestions": "238"}, {"ruleId": "228", "severity": 1, "message": "239", "line": 60, "column": 6, "nodeType": "230", "endLine": 60, "endColumn": 16, "suggestions": "240"}, {"ruleId": "228", "severity": 1, "message": "241", "line": 82, "column": 6, "nodeType": "230", "endLine": 82, "endColumn": 48, "suggestions": "242"}, {"ruleId": "224", "severity": 1, "message": "243", "line": 69, "column": 22, "nodeType": "226", "messageId": "227", "endLine": 69, "endColumn": 35}, {"ruleId": "228", "severity": 1, "message": "244", "line": 79, "column": 6, "nodeType": "230", "endLine": 79, "endColumn": 16, "suggestions": "245"}, {"ruleId": "228", "severity": 1, "message": "246", "line": 98, "column": 6, "nodeType": "230", "endLine": 98, "endColumn": 48, "suggestions": "247"}, {"ruleId": "224", "severity": 1, "message": "248", "line": 173, "column": 60, "nodeType": "226", "messageId": "227", "endLine": 173, "endColumn": 71}, {"ruleId": "224", "severity": 1, "message": "225", "line": 45, "column": 9, "nodeType": "226", "messageId": "227", "endLine": 45, "endColumn": 20}, {"ruleId": "224", "severity": 1, "message": "249", "line": 51, "column": 9, "nodeType": "226", "messageId": "227", "endLine": 51, "endColumn": 20}, {"ruleId": "228", "severity": 1, "message": "250", "line": 78, "column": 6, "nodeType": "230", "endLine": 78, "endColumn": 48, "suggestions": "251"}, {"ruleId": "228", "severity": 1, "message": "252", "line": 61, "column": 6, "nodeType": "230", "endLine": 61, "endColumn": 16, "suggestions": "253"}, {"ruleId": "228", "severity": 1, "message": "254", "line": 113, "column": 6, "nodeType": "230", "endLine": 113, "endColumn": 21, "suggestions": "255"}, {"ruleId": "228", "severity": 1, "message": "256", "line": 39, "column": 6, "nodeType": "230", "endLine": 39, "endColumn": 12, "suggestions": "257"}, {"ruleId": "228", "severity": 1, "message": "258", "line": 45, "column": 6, "nodeType": "230", "endLine": 45, "endColumn": 12, "suggestions": "259"}, {"ruleId": "224", "severity": 1, "message": "260", "line": 1, "column": 27, "nodeType": "226", "messageId": "227", "endLine": 1, "endColumn": 36}, {"ruleId": "261", "severity": 1, "message": "262", "line": 137, "column": 17, "nodeType": "263", "endLine": 140, "endColumn": 18}, {"ruleId": "261", "severity": 1, "message": "262", "line": 159, "column": 15, "nodeType": "263", "endLine": 162, "endColumn": 16}, {"ruleId": "261", "severity": 1, "message": "262", "line": 165, "column": 15, "nodeType": "263", "endLine": 168, "endColumn": 16}, {"ruleId": "261", "severity": 1, "message": "262", "line": 171, "column": 15, "nodeType": "263", "endLine": 174, "endColumn": 16}, {"ruleId": "224", "severity": 1, "message": "264", "line": 51, "column": 10, "nodeType": "226", "messageId": "227", "endLine": 51, "endColumn": 25}, {"ruleId": "224", "severity": 1, "message": "265", "line": 52, "column": 10, "nodeType": "226", "messageId": "227", "endLine": 52, "endColumn": 19}, {"ruleId": "228", "severity": 1, "message": "266", "line": 75, "column": 6, "nodeType": "230", "endLine": 75, "endColumn": 38, "suggestions": "267"}, {"ruleId": "224", "severity": 1, "message": "268", "line": 114, "column": 9, "nodeType": "226", "messageId": "227", "endLine": 114, "endColumn": 27}, "@typescript-eslint/no-unused-vars", "'showSuccess' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStudentData'. Either include it or remove the dependency array.", "ArrayExpression", ["269"], "React Hook useCallback has a missing dependency: 'removeNotification'. Either include it or remove the dependency array.", ["270"], "React Hook useEffect has a missing dependency: 'fetchAdminData'. Either include it or remove the dependency array.", ["271"], ["272"], "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["273"], "React Hook useEffect has a missing dependency: 'fetchCourse'. Either include it or remove the dependency array.", ["274"], "React Hook useEffect has a missing dependency: 'fetchCourses'. Either include it or remove the dependency array.", ["275"], "'setUserAnswer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExercise'. Either include it or remove the dependency array.", ["276"], "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["277"], "'explanation' is assigned a value but never used.", "'showWarning' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExercises'. Either include it or remove the dependency array.", ["278"], "React Hook useEffect has a missing dependency: 'fetchLesson'. Either include it or remove the dependency array.", ["279"], "React Hook useEffect has a missing dependency: 'renderFunction2D'. Either include it or remove the dependency array.", ["280"], "React Hook useEffect has a missing dependency: 'fetchProgressData'. Either include it or remove the dependency array.", ["281"], "React Hook useEffect has a missing dependency: 'fetchProfile'. Either include it or remove the dependency array.", ["282"], "'useEffect' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'selectedMessage' is assigned a value but never used.", "'showModal' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array.", ["283"], "'handleStatusUpdate' is assigned a value but never used.", {"desc": "284", "fix": "285"}, {"desc": "286", "fix": "287"}, {"desc": "288", "fix": "289"}, {"kind": "290", "justification": "291"}, {"desc": "292", "fix": "293"}, {"desc": "294", "fix": "295"}, {"desc": "296", "fix": "297"}, {"desc": "298", "fix": "299"}, {"desc": "300", "fix": "301"}, {"desc": "302", "fix": "303"}, {"desc": "304", "fix": "305"}, {"desc": "306", "fix": "307"}, {"desc": "308", "fix": "309"}, {"desc": "310", "fix": "311"}, {"desc": "312", "fix": "313"}, "Update the dependencies array to be: [fetchStudentData, user]", {"range": "314", "text": "315"}, "Update the dependencies array to be: [removeNotification]", {"range": "316", "text": "317"}, "Update the dependencies array to be: [fetchAdminData, user]", {"range": "318", "text": "319"}, "directive", "", "Update the dependencies array to be: [user, navigate, loadUsers]", {"range": "320", "text": "321"}, "Update the dependencies array to be: [user, id, fetchCourse]", {"range": "322", "text": "323"}, "Update the dependencies array to be: [user, filters, currentPage, itemsPerPage, fetchCourses]", {"range": "324", "text": "325"}, "Update the dependencies array to be: [user, id, fetchExercise]", {"range": "326", "text": "327"}, "Update the dependencies array to be: [timeLeft, exercise?.user_attempt?.status, handleSubmit]", {"range": "328", "text": "329"}, "Update the dependencies array to be: [user, filters, currentPage, itemsPerPage, fetchExercises]", {"range": "330", "text": "331"}, "Update the dependencies array to be: [user, id, fetchLesson]", {"range": "332", "text": "333"}, "Update the dependencies array to be: [functionInput, renderFunction2D]", {"range": "334", "text": "335"}, "Update the dependencies array to be: [fetchProgressData, user]", {"range": "336", "text": "337"}, "Update the dependencies array to be: [fetchProfile, user]", {"range": "338", "text": "339"}, "Update the dependencies array to be: [user, filters, pagination.page, fetchMessages]", {"range": "340", "text": "341"}, [2091, 2097], "[fetchStudentData, user]", [1627, 1629], "[removeNotification]", [1562, 1568], "[fetchAdmin<PERSON><PERSON>, user]", [2194, 2210], "[user, navigate, loadUsers]", [1368, 1378], "[user, id, fetchCourse]", [2380, 2422], "[user, filters, currentPage, itemsPerPage, fetchCourses]", [2224, 2234], "[user, id, fetchExercise]", [2759, 2801], "[timeLeft, exercise?.user_attempt?.status, handleSubmit]", [2079, 2121], "[user, filters, currentPage, itemsPerPage, fetchExercises]", [1549, 1559], "[user, id, fetchLesson]", [3341, 3356], "[functionInput, renderFunction2D]", [1044, 1050], "[fetchProgress<PERSON>ata, user]", [1117, 1123], "[fetchProfile, user]", [1969, 2001], "[user, filters, pagination.page, fetchMessages]"]