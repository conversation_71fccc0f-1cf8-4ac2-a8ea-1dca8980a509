{"ast": null, "code": "export var sylvesterDocs = {\n  name: 'sylvester',\n  category: 'Algebra',\n  syntax: ['sylvester(A,B,C)'],\n  description: 'Solves the real-valued Sylvester equation AX+XB=C for X',\n  examples: ['sylvester([[-1, -2], [1, 1]], [[-2, 1], [-1, 2]], [[-3, 2], [3, 0]])', 'A = [[-1, -2], [1, 1]]; B = [[2, -1], [1, -2]]; C = [[-3, 2], [3, 0]]', 'sylvester(A, B, C)'],\n  seealso: ['schur', 'lyap']\n};", "map": {"version": 3, "names": ["sylvesterDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/sylvester.js"], "sourcesContent": ["export var sylvesterDocs = {\n  name: 'sylvester',\n  category: 'Algebra',\n  syntax: ['sylvester(A,B,C)'],\n  description: 'Solves the real-valued Sylvester equation AX+XB=C for X',\n  examples: ['sylvester([[-1, -2], [1, 1]], [[-2, 1], [-1, 2]], [[-3, 2], [3, 0]])', 'A = [[-1, -2], [1, 1]]; B = [[2, -1], [1, -2]]; C = [[-3, 2], [3, 0]]', 'sylvester(A, B, C)'],\n  seealso: ['schur', 'lyap']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,kBAAkB,CAAC;EAC5BC,WAAW,EAAE,yDAAyD;EACtEC,QAAQ,EAAE,CAAC,sEAAsE,EAAE,uEAAuE,EAAE,oBAAoB,CAAC;EACjLC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}