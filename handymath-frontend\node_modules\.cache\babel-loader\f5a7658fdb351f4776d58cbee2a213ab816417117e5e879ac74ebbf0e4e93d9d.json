{"ast": null, "code": "export var asinDocs = {\n  name: 'asin',\n  category: 'Trigonometry',\n  syntax: ['asin(x)'],\n  description: 'Compute the inverse sine of a value in radians.',\n  examples: ['asin(0.5)', 'asin(sin(0.5))'],\n  seealso: ['sin', 'acos', 'atan']\n};", "map": {"version": 3, "names": ["asinDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/asin.js"], "sourcesContent": ["export var asinDocs = {\n  name: 'asin',\n  category: 'Trigonometry',\n  syntax: ['asin(x)'],\n  description: 'Compute the inverse sine of a value in radians.',\n  examples: ['asin(0.5)', 'asin(sin(0.5))'],\n  seealso: ['sin', 'acos', 'atan']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,iDAAiD;EAC9DC,QAAQ,EAAE,CAAC,WAAW,EAAE,gBAAgB,CAAC;EACzCC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM;AACjC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}