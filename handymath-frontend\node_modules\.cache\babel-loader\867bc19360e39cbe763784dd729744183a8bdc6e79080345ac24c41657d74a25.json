{"ast": null, "code": "import { isInteger } from '../../utils/number.js';\nimport { product } from '../../utils/product.js';\nexport function combinationsNumber(n, k) {\n  if (!isInteger(n) || n < 0) {\n    throw new TypeError('Positive integer value expected in function combinations');\n  }\n  if (!isInteger(k) || k < 0) {\n    throw new TypeError('Positive integer value expected in function combinations');\n  }\n  if (k > n) {\n    throw new TypeError('k must be less than or equal to n');\n  }\n  var nMinusk = n - k;\n  var answer = 1;\n  var firstnumerator = k < nMinusk ? nMinusk + 1 : k + 1;\n  var nextdivisor = 2;\n  var lastdivisor = k < nMinusk ? k : nMinusk;\n  // balance multiplications and divisions to try to keep intermediate values\n  // in exact-integer range as long as possible\n  for (var nextnumerator = firstnumerator; nextnumerator <= n; ++nextnumerator) {\n    answer *= nextnumerator;\n    while (nextdivisor <= lastdivisor && answer % nextdivisor === 0) {\n      answer /= nextdivisor;\n      ++nextdivisor;\n    }\n  }\n  // for big n, k, floating point may have caused weirdness in remainder\n  if (nextdivisor <= lastdivisor) {\n    answer /= product(nextdivisor, lastdivisor);\n  }\n  return answer;\n}\ncombinationsNumber.signature = 'number, number';", "map": {"version": 3, "names": ["isInteger", "product", "combinationsNumber", "n", "k", "TypeError", "nMinusk", "answer", "firstnumerator", "nextdivisor", "lastdivisor", "nextnumerator", "signature"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/plain/number/combinations.js"], "sourcesContent": ["import { isInteger } from '../../utils/number.js';\nimport { product } from '../../utils/product.js';\nexport function combinationsNumber(n, k) {\n  if (!isInteger(n) || n < 0) {\n    throw new TypeError('Positive integer value expected in function combinations');\n  }\n  if (!isInteger(k) || k < 0) {\n    throw new TypeError('Positive integer value expected in function combinations');\n  }\n  if (k > n) {\n    throw new TypeError('k must be less than or equal to n');\n  }\n  var nMinusk = n - k;\n  var answer = 1;\n  var firstnumerator = k < nMinusk ? nMinusk + 1 : k + 1;\n  var nextdivisor = 2;\n  var lastdivisor = k < nMinusk ? k : nMinusk;\n  // balance multiplications and divisions to try to keep intermediate values\n  // in exact-integer range as long as possible\n  for (var nextnumerator = firstnumerator; nextnumerator <= n; ++nextnumerator) {\n    answer *= nextnumerator;\n    while (nextdivisor <= lastdivisor && answer % nextdivisor === 0) {\n      answer /= nextdivisor;\n      ++nextdivisor;\n    }\n  }\n  // for big n, k, floating point may have caused weirdness in remainder\n  if (nextdivisor <= lastdivisor) {\n    answer /= product(nextdivisor, lastdivisor);\n  }\n  return answer;\n}\ncombinationsNumber.signature = 'number, number';"], "mappings": "AAAA,SAASA,SAAS,QAAQ,uBAAuB;AACjD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,SAASC,kBAAkBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACvC,IAAI,CAACJ,SAAS,CAACG,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;IAC1B,MAAM,IAAIE,SAAS,CAAC,0DAA0D,CAAC;EACjF;EACA,IAAI,CAACL,SAAS,CAACI,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;IAC1B,MAAM,IAAIC,SAAS,CAAC,0DAA0D,CAAC;EACjF;EACA,IAAID,CAAC,GAAGD,CAAC,EAAE;IACT,MAAM,IAAIE,SAAS,CAAC,mCAAmC,CAAC;EAC1D;EACA,IAAIC,OAAO,GAAGH,CAAC,GAAGC,CAAC;EACnB,IAAIG,MAAM,GAAG,CAAC;EACd,IAAIC,cAAc,GAAGJ,CAAC,GAAGE,OAAO,GAAGA,OAAO,GAAG,CAAC,GAAGF,CAAC,GAAG,CAAC;EACtD,IAAIK,WAAW,GAAG,CAAC;EACnB,IAAIC,WAAW,GAAGN,CAAC,GAAGE,OAAO,GAAGF,CAAC,GAAGE,OAAO;EAC3C;EACA;EACA,KAAK,IAAIK,aAAa,GAAGH,cAAc,EAAEG,aAAa,IAAIR,CAAC,EAAE,EAAEQ,aAAa,EAAE;IAC5EJ,MAAM,IAAII,aAAa;IACvB,OAAOF,WAAW,IAAIC,WAAW,IAAIH,MAAM,GAAGE,WAAW,KAAK,CAAC,EAAE;MAC/DF,MAAM,IAAIE,WAAW;MACrB,EAAEA,WAAW;IACf;EACF;EACA;EACA,IAAIA,WAAW,IAAIC,WAAW,EAAE;IAC9BH,MAAM,IAAIN,OAAO,CAACQ,WAAW,EAAEC,WAAW,CAAC;EAC7C;EACA,OAAOH,MAAM;AACf;AACAL,kBAAkB,CAACU,SAAS,GAAG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}