{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createIsZero } from '../../factoriesAny.js';\nexport var isZeroDependencies = {\n  equalScalarDependencies,\n  typedDependencies,\n  createIsZero\n};", "map": {"version": 3, "names": ["equalScalarDependencies", "typedDependencies", "createIsZero", "isZeroDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesIsZero.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createIsZero } from '../../factoriesAny.js';\nexport var isZeroDependencies = {\n  equalScalarDependencies,\n  typedDependencies,\n  createIsZero\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,uBAAuB,QAAQ,wCAAwC;AAChF,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAO,IAAIC,kBAAkB,GAAG;EAC9BH,uBAAuB;EACvBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}