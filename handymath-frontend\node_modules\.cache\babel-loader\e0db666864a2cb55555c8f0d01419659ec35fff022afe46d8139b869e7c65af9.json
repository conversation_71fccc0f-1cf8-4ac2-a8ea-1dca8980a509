{"ast": null, "code": "export var modeDocs = {\n  name: 'mode',\n  category: 'Statistics',\n  syntax: ['mode(a, b, c, ...)', 'mode(A)', 'mode(A, a, b, B, c, ...)'],\n  description: 'Computes the mode of all values as an array. In case mode being more than one, multiple values are returned in an array.',\n  examples: ['mode(2, 1, 4, 3, 1)', 'mode([1, 2.7, 3.2, 4, 2.7])', 'mode(1, 4, 6, 1, 6)'],\n  seealso: ['max', 'mean', 'min', 'median', 'prod', 'std', 'sum', 'variance']\n};", "map": {"version": 3, "names": ["modeDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/statistics/mode.js"], "sourcesContent": ["export var modeDocs = {\n  name: 'mode',\n  category: 'Statistics',\n  syntax: ['mode(a, b, c, ...)', 'mode(A)', 'mode(A, a, b, B, c, ...)'],\n  description: 'Computes the mode of all values as an array. In case mode being more than one, multiple values are returned in an array.',\n  examples: ['mode(2, 1, 4, 3, 1)', 'mode([1, 2.7, 3.2, 4, 2.7])', 'mode(1, 4, 6, 1, 6)'],\n  seealso: ['max', 'mean', 'min', 'median', 'prod', 'std', 'sum', 'variance']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,oBAAoB,EAAE,SAAS,EAAE,0BAA0B,CAAC;EACrEC,WAAW,EAAE,0HAA0H;EACvIC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,6BAA6B,EAAE,qBAAqB,CAAC;EACvFC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU;AAC5E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}