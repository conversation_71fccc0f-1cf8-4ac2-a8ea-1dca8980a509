{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { createParenthesisNode } from '../../factoriesAny.js';\nexport var ParenthesisNodeDependencies = {\n  NodeDependencies,\n  createParenthesisNode\n};", "map": {"version": 3, "names": ["NodeDependencies", "createParenthesisNode", "ParenthesisNodeDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesParenthesisNode.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { createParenthesisNode } from '../../factoriesAny.js';\nexport var ParenthesisNodeDependencies = {\n  NodeDependencies,\n  createParenthesisNode\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,OAAO,IAAIC,2BAA2B,GAAG;EACvCF,gBAAgB;EAChBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}