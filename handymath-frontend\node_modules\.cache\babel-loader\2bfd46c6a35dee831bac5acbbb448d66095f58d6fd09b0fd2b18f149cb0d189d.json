{"ast": null, "code": "export var lusolveDocs = {\n  name: 'lusolve',\n  category: 'Algebra',\n  syntax: ['x=lusolve(A, b)', 'x=lusolve(lu, b)'],\n  description: 'Solves the linear system A * x = b where A is an [n x n] matrix and b is a [n] column vector.',\n  examples: ['a = [-2, 3; 2, 1]', 'b = [11, 9]', 'x = lusolve(a, b)'],\n  seealso: ['lup', 'slu', 'lsolve', 'usolve', 'matrix', 'sparse']\n};", "map": {"version": 3, "names": ["lusolveDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/lusolve.js"], "sourcesContent": ["export var lusolveDocs = {\n  name: 'lusolve',\n  category: 'Algebra',\n  syntax: ['x=lusolve(A, b)', 'x=lusolve(lu, b)'],\n  description: 'Solves the linear system A * x = b where A is an [n x n] matrix and b is a [n] column vector.',\n  examples: ['a = [-2, 3; 2, 1]', 'b = [11, 9]', 'x = lusolve(a, b)'],\n  seealso: ['lup', 'slu', 'lsolve', 'usolve', 'matrix', 'sparse']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,CAAC;EAC/CC,WAAW,EAAE,+FAA+F;EAC5GC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,aAAa,EAAE,mBAAmB,CAAC;EACnEC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;AAChE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}