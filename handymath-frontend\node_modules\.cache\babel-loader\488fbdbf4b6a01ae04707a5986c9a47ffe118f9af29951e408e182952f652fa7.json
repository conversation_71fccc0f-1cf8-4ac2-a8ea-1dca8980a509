{"ast": null, "code": "export var equalTextDocs = {\n  name: 'equalText',\n  category: 'Relational',\n  syntax: ['equalText(x, y)'],\n  description: 'Check equality of two strings. Comparison is case sensitive. Returns true if the values are equal, and false if not.',\n  examples: ['equalText(\"Hello\", \"Hello\")', 'equalText(\"a\", \"A\")', 'equal(\"2e3\", \"2000\")', 'equalText(\"2e3\", \"2000\")', 'equalText(\"B\", [\"A\", \"B\", \"C\"])'],\n  seealso: ['compare', 'compareNatural', 'compareText', 'equal']\n};", "map": {"version": 3, "names": ["equalTextDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/relational/equalText.js"], "sourcesContent": ["export var equalTextDocs = {\n  name: 'equalText',\n  category: 'Relational',\n  syntax: ['equalText(x, y)'],\n  description: 'Check equality of two strings. Comparison is case sensitive. Returns true if the values are equal, and false if not.',\n  examples: ['equalText(\"Hello\", \"Hello\")', 'equalText(\"a\", \"A\")', 'equal(\"2e3\", \"2000\")', 'equalText(\"2e3\", \"2000\")', 'equalText(\"B\", [\"A\", \"B\", \"C\"])'],\n  seealso: ['compare', 'compareNatural', 'compareText', 'equal']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,iBAAiB,CAAC;EAC3BC,WAAW,EAAE,sHAAsH;EACnIC,QAAQ,EAAE,CAAC,6BAA6B,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,0BAA0B,EAAE,iCAAiC,CAAC;EACvJC,OAAO,EAAE,CAAC,SAAS,EAAE,gBAAgB,EAAE,aAAa,EAAE,OAAO;AAC/D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}