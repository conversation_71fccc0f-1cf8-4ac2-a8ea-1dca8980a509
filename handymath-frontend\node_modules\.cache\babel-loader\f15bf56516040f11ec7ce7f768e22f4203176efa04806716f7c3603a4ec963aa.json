{"ast": null, "code": "export var asecDocs = {\n  name: 'asec',\n  category: 'Trigonometry',\n  syntax: ['asec(x)'],\n  description: 'Calculate the inverse secant of a value.',\n  examples: ['asec(0.5)', 'asec(sec(0.5))', 'asec(2)'],\n  seealso: ['acos', 'acot', 'acsc']\n};", "map": {"version": 3, "names": ["asecDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/asec.js"], "sourcesContent": ["export var asecDocs = {\n  name: 'asec',\n  category: 'Trigonometry',\n  syntax: ['asec(x)'],\n  description: 'Calculate the inverse secant of a value.',\n  examples: ['asec(0.5)', 'asec(sec(0.5))', 'asec(2)'],\n  seealso: ['acos', 'acot', 'acsc']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,0CAA0C;EACvDC,QAAQ,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,SAAS,CAAC;EACpDC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}