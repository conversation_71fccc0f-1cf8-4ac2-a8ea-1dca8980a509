{"ast": null, "code": "export var madDocs = {\n  name: 'mad',\n  category: 'Statistics',\n  syntax: ['mad(a, b, c, ...)', 'mad(A)'],\n  description: 'Compute the median absolute deviation of a matrix or a list with values. The median absolute deviation is defined as the median of the absolute deviations from the median.',\n  examples: ['mad(10, 20, 30)', 'mad([1, 2, 3])'],\n  seealso: ['mean', 'median', 'std', 'abs']\n};", "map": {"version": 3, "names": ["madDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/statistics/mad.js"], "sourcesContent": ["export var madDocs = {\n  name: 'mad',\n  category: 'Statistics',\n  syntax: ['mad(a, b, c, ...)', 'mad(A)'],\n  description: 'Compute the median absolute deviation of a matrix or a list with values. The median absolute deviation is defined as the median of the absolute deviations from the median.',\n  examples: ['mad(10, 20, 30)', 'mad([1, 2, 3])'],\n  seealso: ['mean', 'median', 'std', 'abs']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,mBAAmB,EAAE,QAAQ,CAAC;EACvCC,WAAW,EAAE,6KAA6K;EAC1LC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;EAC/CC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}