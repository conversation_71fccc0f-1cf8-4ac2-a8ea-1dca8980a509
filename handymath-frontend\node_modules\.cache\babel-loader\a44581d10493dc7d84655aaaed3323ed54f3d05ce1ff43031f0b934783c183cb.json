{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { createObjectNode } from '../../factoriesAny.js';\nexport var ObjectNodeDependencies = {\n  NodeDependencies,\n  createObjectNode\n};", "map": {"version": 3, "names": ["NodeDependencies", "createObjectNode", "ObjectNodeDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesObjectNode.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { createObjectNode } from '../../factoriesAny.js';\nexport var ObjectNodeDependencies = {\n  NodeDependencies,\n  createObjectNode\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,OAAO,IAAIC,sBAAsB,GAAG;EAClCF,gBAAgB;EAChBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}