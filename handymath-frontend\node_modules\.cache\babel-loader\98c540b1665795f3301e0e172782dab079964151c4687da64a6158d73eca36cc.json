{"ast": null, "code": "export var kldivergenceDocs = {\n  name: 'kldivergence',\n  category: 'Probability',\n  syntax: ['kldivergence(x, y)'],\n  description: 'Calculate the Kullback-Leibler (KL) divergence  between two distributions.',\n  examples: ['kldivergence([0.7,0.5,0.4], [0.2,0.9,0.5])'],\n  seealso: []\n};", "map": {"version": 3, "names": ["kldivergenceDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/probability/kldivergence.js"], "sourcesContent": ["export var kldivergenceDocs = {\n  name: 'kldivergence',\n  category: 'Probability',\n  syntax: ['kldivergence(x, y)'],\n  description: 'Calculate the Kullback-Leibler (KL) divergence  between two distributions.',\n  examples: ['kldivergence([0.7,0.5,0.4], [0.2,0.9,0.5])'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,gBAAgB,GAAG;EAC5BC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CAAC,oBAAoB,CAAC;EAC9BC,WAAW,EAAE,4EAA4E;EACzFC,QAAQ,EAAE,CAAC,4CAA4C,CAAC;EACxDC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}