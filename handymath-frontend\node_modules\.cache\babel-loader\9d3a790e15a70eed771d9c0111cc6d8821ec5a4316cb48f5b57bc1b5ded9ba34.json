{"ast": null, "code": "export var orDocs = {\n  name: 'or',\n  category: 'Logical',\n  syntax: ['x or y', 'or(x, y)'],\n  description: 'Logical or. Test if at least one value is defined with a nonzero/nonempty value.',\n  examples: ['true or false', 'false or false', '0 or 4'],\n  seealso: ['not', 'and', 'xor']\n};", "map": {"version": 3, "names": ["orDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/logical/or.js"], "sourcesContent": ["export var orDocs = {\n  name: 'or',\n  category: 'Logical',\n  syntax: ['x or y', 'or(x, y)'],\n  description: 'Logical or. Test if at least one value is defined with a nonzero/nonempty value.',\n  examples: ['true or false', 'false or false', '0 or 4'],\n  seealso: ['not', 'and', 'xor']\n};"], "mappings": "AAAA,OAAO,IAAIA,MAAM,GAAG;EAClBC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;EAC9BC,WAAW,EAAE,kFAAkF;EAC/FC,QAAQ,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,QAAQ,CAAC;EACvDC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}