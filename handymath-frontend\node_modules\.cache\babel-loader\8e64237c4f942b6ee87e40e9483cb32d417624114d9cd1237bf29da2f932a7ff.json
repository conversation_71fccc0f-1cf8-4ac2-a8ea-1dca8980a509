{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\CoursesPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport StudentNavbar from '../components/StudentNavbar';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CoursesPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [courses, setCourses] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    level: '',\n    featured: false\n  });\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(9);\n  const [totalItems, setTotalItems] = useState(0);\n  useEffect(() => {\n    if (user) {\n      fetchCourses();\n    }\n  }, [user, filters]);\n  const fetchCourses = async () => {\n    try {\n      setLoading(true);\n      setTimeout(() => {\n        setCourses([{\n          id: 1,\n          title: 'Algèbre de base',\n          description: 'Apprenez les concepts fondamentaux de l\\'algèbre avec des exercices pratiques et des exemples concrets.',\n          level: 'beginner',\n          level_display: 'Débutant',\n          thumbnail: '📚',\n          estimated_duration: 180,\n          is_featured: true,\n          chapters_count: 4,\n          lessons_count: 12,\n          progress_percentage: 45,\n          is_accessible: true,\n          is_enrolled: true,\n          enrollment_date: '2024-01-15T10:30:00Z',\n          prerequisites: [],\n          created_at: '2024-01-15T10:30:00Z'\n        }, {\n          id: 2,\n          title: 'Géométrie euclidienne',\n          description: 'Découvrez les principes de la géométrie euclidienne et ses applications pratiques.',\n          level: 'intermediate',\n          level_display: 'Intermédiaire',\n          thumbnail: '📐',\n          estimated_duration: 240,\n          is_featured: false,\n          chapters_count: 6,\n          lessons_count: 18,\n          progress_percentage: 0,\n          is_accessible: true,\n          is_enrolled: false,\n          prerequisites: [],\n          created_at: '2024-01-16T14:20:00Z'\n        }, {\n          id: 3,\n          title: 'Calcul différentiel',\n          description: 'Maîtrisez les concepts du calcul différentiel et intégral.',\n          level: 'advanced',\n          level_display: 'Avancé',\n          thumbnail: '∫',\n          estimated_duration: 360,\n          is_featured: true,\n          chapters_count: 8,\n          lessons_count: 24,\n          progress_percentage: 0,\n          is_accessible: false,\n          is_enrolled: false,\n          prerequisites: [{\n            id: 1,\n            title: 'Algèbre de base',\n            progress: 45\n          }],\n          created_at: '2024-01-17T09:45:00Z'\n        }]);\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      setLoading(false);\n    }\n  };\n  const handleEnroll = async (courseId, courseTitle) => {\n    try {\n      setCourses(courses.map(course => course.id === courseId ? {\n        ...course,\n        is_enrolled: true,\n        enrollment_date: new Date().toISOString()\n      } : course));\n      alert(`Inscription réussie au cours \"${courseTitle}\" !`);\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      alert('Erreur lors de l\\'inscription');\n    }\n  };\n  const getLevelColor = level => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n  const formatDuration = minutes => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n  const filteredCourses = courses.filter(course => {\n    if (filters.level && course.level !== filters.level) return false;\n    if (filters.featured && !course.is_featured) return false;\n    return true;\n  });\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Cours Structur\\xE9s\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Vous devez \\xEAtre connect\\xE9 pour acc\\xE9der aux cours.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(StudentNavbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n        className: \"text-4xl font-bold mb-8 text-center text-primary-600\",\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: \"Cours Structur\\xE9s\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-4\",\n          children: \"Filtres\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Niveau\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filters.level,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                level: e.target.value\n              })),\n              className: \"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tous les niveaux\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"beginner\",\n                children: \"D\\xE9butant\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"intermediate\",\n                children: \"Interm\\xE9diaire\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"advanced\",\n                children: \"Avanc\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: filters.featured,\n                onChange: e => setFilters(prev => ({\n                  ...prev,\n                  featured: e.target.checked\n                })),\n                className: \"mr-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-900 dark:text-white\",\n                children: \"Cours vedettes uniquement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-end\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setFilters({\n                level: '',\n                featured: false\n              }),\n              className: \"w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\",\n              children: \"Effacer filtres\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600 dark:text-gray-400\",\n          children: [filteredCourses.length, \" cours trouv\\xE9\", filteredCourses.length !== 1 ? 's' : '']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 7\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Chargement des cours...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this) : filteredCourses.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2\",\n          children: \"Aucun cours trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-4\",\n          children: \"Essayez de modifier vos filtres ou revenez plus tard.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setFilters({\n            level: '',\n            featured: false\n          }),\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors\",\n          children: \"Voir tous les cours\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: filteredCourses.map((course, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: index * 0.1\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-3xl mr-3\",\n                  children: course.thumbnail\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold text-lg text-gray-900 dark:text-white\",\n                    children: course.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 23\n                  }, this), course.is_featured && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\",\n                    children: \"Vedette\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`,\n                children: course.level_display\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 dark:text-gray-300 text-sm mb-4 line-clamp-2\",\n              children: course.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [course.chapters_count, \" chapitres\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [course.lessons_count, \" le\\xE7ons\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1\",\n                  children: \"\\u23F1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: formatDuration(course.estimated_duration)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [course.progress_percentage, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this), course.is_enrolled && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between text-sm mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Progression\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [course.progress_percentage, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                  style: {\n                    width: `${course.progress_percentage}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 19\n            }, this), course.prerequisites.length > 0 && !course.is_accessible && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 p-3 bg-yellow-50 dark:bg-yellow-900 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2\",\n                children: \"Pr\\xE9requis requis :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 21\n              }, this), course.prerequisites.map(prereq => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-yellow-700 dark:text-yellow-300\",\n                children: [\"\\u2022 \", prereq.title, \" (\", prereq.progress, \"% termin\\xE9)\"]\n              }, prereq.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 23\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [!course.is_accessible ? /*#__PURE__*/_jsxDEV(\"button\", {\n                disabled: true,\n                className: \"w-full bg-gray-400 text-white font-medium py-3 px-4 rounded-lg cursor-not-allowed\",\n                children: \"Non accessible\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 21\n              }, this) : !course.is_enrolled ? /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEnroll(course.id, course.title),\n                className: \"w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-colors\",\n                children: \"S'inscrire\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"a\", {\n                href: `/courses/${course.id}`,\n                className: \"block w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg text-center transition-colors\",\n                children: course.progress_percentage > 0 ? 'Continuer' : 'Commencer'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: `/courses/${course.id}`,\n                className: \"block w-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium py-2 px-4 rounded-lg text-center transition-colors\",\n                children: \"Voir les d\\xE9tails\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this)\n        }, course.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(CoursesPage, \"V2W9XMTkyGr4HXpQWi0qIYBTwXw=\", false, function () {\n  return [useAuth];\n});\n_c = CoursesPage;\nexport default CoursesPage;\nvar _c;\n$RefreshReg$(_c, \"CoursesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useAuth", "StudentNavbar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CoursesPage", "_s", "user", "courses", "setCourses", "loading", "setLoading", "filters", "setFilters", "level", "featured", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "totalItems", "setTotalItems", "fetchCourses", "setTimeout", "id", "title", "description", "level_display", "thumbnail", "estimated_duration", "is_featured", "chapters_count", "lessons_count", "progress_percentage", "is_accessible", "is_enrolled", "enrollment_date", "prerequisites", "created_at", "progress", "error", "console", "handleEnroll", "courseId", "courseTitle", "map", "course", "Date", "toISOString", "alert", "getLevelColor", "formatDuration", "minutes", "hours", "Math", "floor", "mins", "filteredCourses", "filter", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "h1", "initial", "opacity", "y", "animate", "div", "transition", "delay", "value", "onChange", "e", "prev", "target", "type", "checked", "onClick", "length", "index", "style", "width", "prereq", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/pages/CoursesPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport StudentNavbar from '../components/StudentNavbar';\nimport Pagination from '../components/Pagination';\n\ninterface Course {\n  id: number;\n  title: string;\n  description: string;\n  level: string;\n  level_display: string;\n  thumbnail: string;\n  estimated_duration: number;\n  is_featured: boolean;\n  chapters_count: number;\n  lessons_count: number;\n  progress_percentage: number;\n  is_accessible: boolean;\n  is_enrolled: boolean;\n  enrollment_date?: string;\n  prerequisites: Array<{\n    id: number;\n    title: string;\n    progress: number;\n  }>;\n  created_at: string;\n}\n\ninterface Filters {\n  level: string;\n  featured: boolean;\n}\n\nconst CoursesPage: React.FC = () => {\n  const { user } = useAuth();\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState<Filters>({\n    level: '',\n    featured: false\n  });\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(9);\n  const [totalItems, setTotalItems] = useState(0);\n\n  useEffect(() => {\n    if (user) {\n      fetchCourses();\n    }\n  }, [user, filters]);\n\n  const fetchCourses = async () => {\n    try {\n      setLoading(true);\n      setTimeout(() => {\n        setCourses([\n          {\n            id: 1,\n            title: 'Algèbre de base',\n            description: 'Apprenez les concepts fondamentaux de l\\'algèbre avec des exercices pratiques et des exemples concrets.',\n            level: 'beginner',\n            level_display: 'Débutant',\n            thumbnail: '📚',\n            estimated_duration: 180,\n            is_featured: true,\n            chapters_count: 4,\n            lessons_count: 12,\n            progress_percentage: 45,\n            is_accessible: true,\n            is_enrolled: true,\n            enrollment_date: '2024-01-15T10:30:00Z',\n            prerequisites: [],\n            created_at: '2024-01-15T10:30:00Z'\n          },\n          {\n            id: 2,\n            title: 'Géométrie euclidienne',\n            description: 'Découvrez les principes de la géométrie euclidienne et ses applications pratiques.',\n            level: 'intermediate',\n            level_display: 'Intermédiaire',\n            thumbnail: '📐',\n            estimated_duration: 240,\n            is_featured: false,\n            chapters_count: 6,\n            lessons_count: 18,\n            progress_percentage: 0,\n            is_accessible: true,\n            is_enrolled: false,\n            prerequisites: [],\n            created_at: '2024-01-16T14:20:00Z'\n          },\n          {\n            id: 3,\n            title: 'Calcul différentiel',\n            description: 'Maîtrisez les concepts du calcul différentiel et intégral.',\n            level: 'advanced',\n            level_display: 'Avancé',\n            thumbnail: '∫',\n            estimated_duration: 360,\n            is_featured: true,\n            chapters_count: 8,\n            lessons_count: 24,\n            progress_percentage: 0,\n            is_accessible: false,\n            is_enrolled: false,\n            prerequisites: [\n              { id: 1, title: 'Algèbre de base', progress: 45 }\n            ],\n            created_at: '2024-01-17T09:45:00Z'\n          }\n        ]);\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      console.error('Erreur lors de la récupération des cours:', error);\n      setLoading(false);\n    }\n  };\n\n  const handleEnroll = async (courseId: number, courseTitle: string) => {\n    try {\n      setCourses(courses.map(course => \n        course.id === courseId \n          ? { ...course, is_enrolled: true, enrollment_date: new Date().toISOString() }\n          : course\n      ));\n      alert(`Inscription réussie au cours \"${courseTitle}\" !`);\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      alert('Erreur lors de l\\'inscription');\n    }\n  };\n\n  const getLevelColor = (level: string) => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n\n  const formatDuration = (minutes: number) => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n\n  const filteredCourses = courses.filter(course => {\n    if (filters.level && course.level !== filters.level) return false;\n    if (filters.featured && !course.is_featured) return false;\n    return true;\n  });\n\n  if (!user) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Cours Structurés</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            Vous devez être connecté pour accéder aux cours.\n          </p>\n          <a\n            href=\"/login\"\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Se connecter\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <StudentNavbar />\n      <div className=\"container mx-auto px-4 py-8\">\n      <motion.h1\n        className=\"text-4xl font-bold mb-8 text-center text-primary-600\"\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        Cours Structurés\n      </motion.h1>\n\n      <motion.div\n        className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1 }}\n      >\n        <h2 className=\"text-xl font-semibold mb-4\">\n          Filtres\n        </h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Niveau\n            </label>\n            <select\n              value={filters.level}\n              onChange={(e) => setFilters(prev => ({ ...prev, level: e.target.value }))}\n              className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"\">Tous les niveaux</option>\n              <option value=\"beginner\">Débutant</option>\n              <option value=\"intermediate\">Intermédiaire</option>\n              <option value=\"advanced\">Avancé</option>\n            </select>\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Type\n            </label>\n            <label className=\"flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700\">\n              <input\n                type=\"checkbox\"\n                checked={filters.featured}\n                onChange={(e) => setFilters(prev => ({ ...prev, featured: e.target.checked }))}\n                className=\"mr-3\"\n              />\n              <span className=\"text-gray-900 dark:text-white\">Cours vedettes uniquement</span>\n            </label>\n          </div>\n          \n          <div className=\"flex items-end\">\n            <button\n              onClick={() => setFilters({ level: '', featured: false })}\n              className=\"w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n            >\n              Effacer filtres\n            </button>\n          </div>\n        </div>\n        \n        <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n          {filteredCourses.length} cours trouvé{filteredCourses.length !== 1 ? 's' : ''}\n        </div>\n      </motion.div>\n\n      {loading ? (\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Chargement des cours...</p>\n        </div>\n      ) : filteredCourses.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <h3 className=\"text-xl font-semibold mb-2\">Aucun cours trouvé</h3>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n            Essayez de modifier vos filtres ou revenez plus tard.\n          </p>\n          <button\n            onClick={() => setFilters({ level: '', featured: false })}\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors\"\n          >\n            Voir tous les cours\n          </button>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {filteredCourses.map((course, index) => (\n            <motion.div\n              key={course.id}\n              className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n            >\n              <div className=\"p-6\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-3xl mr-3\">{course.thumbnail}</span>\n                    <div>\n                      <h3 className=\"font-semibold text-lg text-gray-900 dark:text-white\">\n                        {course.title}\n                      </h3>\n                      {course.is_featured && (\n                        <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\">\n                          Vedette\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`}>\n                    {course.level_display}\n                  </span>\n                </div>\n\n                <p className=\"text-gray-700 dark:text-gray-300 text-sm mb-4 line-clamp-2\">\n                  {course.description}\n                </p>\n\n                <div className=\"grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                  <div className=\"flex items-center\">\n                    <span>{course.chapters_count} chapitres</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span>{course.lessons_count} leçons</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span className=\"mr-1\">⏱</span>\n                    <span>{formatDuration(course.estimated_duration)}</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span>{course.progress_percentage}%</span>\n                  </div>\n                </div>\n\n                {course.is_enrolled && (\n                  <div className=\"mb-4\">\n                    <div className=\"flex justify-between text-sm mb-1\">\n                      <span>Progression</span>\n                      <span>{course.progress_percentage}%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                      <div\n                        className=\"bg-primary-600 h-2 rounded-full transition-all duration-300\"\n                        style={{ width: `${course.progress_percentage}%` }}\n                      />\n                    </div>\n                  </div>\n                )}\n\n                {course.prerequisites.length > 0 && !course.is_accessible && (\n                  <div className=\"mb-4 p-3 bg-yellow-50 dark:bg-yellow-900 rounded-lg\">\n                    <p className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2\">\n                      Prérequis requis :\n                    </p>\n                    {course.prerequisites.map(prereq => (\n                      <div key={prereq.id} className=\"text-xs text-yellow-700 dark:text-yellow-300\">\n                        • {prereq.title} ({prereq.progress}% terminé)\n                      </div>\n                    ))}\n                  </div>\n                )}\n\n                <div className=\"space-y-2\">\n                  {!course.is_accessible ? (\n                    <button\n                      disabled\n                      className=\"w-full bg-gray-400 text-white font-medium py-3 px-4 rounded-lg cursor-not-allowed\"\n                    >\n                      Non accessible\n                    </button>\n                  ) : !course.is_enrolled ? (\n                    <button\n                      onClick={() => handleEnroll(course.id, course.title)}\n                      className=\"w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n                    >\n                      S'inscrire\n                    </button>\n                  ) : (\n                    <a\n                      href={`/courses/${course.id}`}\n                      className=\"block w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg text-center transition-colors\"\n                    >\n                      {course.progress_percentage > 0 ? 'Continuer' : 'Commencer'}\n                    </a>\n                  )}\n                  \n                  <a\n                    href={`/courses/${course.id}`}\n                    className=\"block w-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium py-2 px-4 rounded-lg text-center transition-colors\"\n                  >\n                    Voir les détails\n                  </a>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      )}\n    </div>\n    </>\n  );\n};\n\nexport default CoursesPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,aAAa,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA+BxD,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAU;IAC9CkB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC;EAE/CC,SAAS,CAAC,MAAM;IACd,IAAIU,IAAI,EAAE;MACRe,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACf,IAAI,EAAEK,OAAO,CAAC,CAAC;EAEnB,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChBY,UAAU,CAAC,MAAM;QACfd,UAAU,CAAC,CACT;UACEe,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,iBAAiB;UACxBC,WAAW,EAAE,yGAAyG;UACtHZ,KAAK,EAAE,UAAU;UACjBa,aAAa,EAAE,UAAU;UACzBC,SAAS,EAAE,IAAI;UACfC,kBAAkB,EAAE,GAAG;UACvBC,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,EAAE;UACjBC,mBAAmB,EAAE,EAAE;UACvBC,aAAa,EAAE,IAAI;UACnBC,WAAW,EAAE,IAAI;UACjBC,eAAe,EAAE,sBAAsB;UACvCC,aAAa,EAAE,EAAE;UACjBC,UAAU,EAAE;QACd,CAAC,EACD;UACEd,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,uBAAuB;UAC9BC,WAAW,EAAE,oFAAoF;UACjGZ,KAAK,EAAE,cAAc;UACrBa,aAAa,EAAE,eAAe;UAC9BC,SAAS,EAAE,IAAI;UACfC,kBAAkB,EAAE,GAAG;UACvBC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,EAAE;UACjBC,mBAAmB,EAAE,CAAC;UACtBC,aAAa,EAAE,IAAI;UACnBC,WAAW,EAAE,KAAK;UAClBE,aAAa,EAAE,EAAE;UACjBC,UAAU,EAAE;QACd,CAAC,EACD;UACEd,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,qBAAqB;UAC5BC,WAAW,EAAE,4DAA4D;UACzEZ,KAAK,EAAE,UAAU;UACjBa,aAAa,EAAE,QAAQ;UACvBC,SAAS,EAAE,GAAG;UACdC,kBAAkB,EAAE,GAAG;UACvBC,WAAW,EAAE,IAAI;UACjBC,cAAc,EAAE,CAAC;UACjBC,aAAa,EAAE,EAAE;UACjBC,mBAAmB,EAAE,CAAC;UACtBC,aAAa,EAAE,KAAK;UACpBC,WAAW,EAAE,KAAK;UAClBE,aAAa,EAAE,CACb;YAAEb,EAAE,EAAE,CAAC;YAAEC,KAAK,EAAE,iBAAiB;YAAEc,QAAQ,EAAE;UAAG,CAAC,CAClD;UACDD,UAAU,EAAE;QACd,CAAC,CACF,CAAC;QACF3B,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+B,YAAY,GAAG,MAAAA,CAAOC,QAAgB,EAAEC,WAAmB,KAAK;IACpE,IAAI;MACFnC,UAAU,CAACD,OAAO,CAACqC,GAAG,CAACC,MAAM,IAC3BA,MAAM,CAACtB,EAAE,KAAKmB,QAAQ,GAClB;QAAE,GAAGG,MAAM;QAAEX,WAAW,EAAE,IAAI;QAAEC,eAAe,EAAE,IAAIW,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAAE,CAAC,GAC3EF,MACN,CAAC,CAAC;MACFG,KAAK,CAAC,iCAAiCL,WAAW,KAAK,CAAC;IAC1D,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDS,KAAK,CAAC,+BAA+B,CAAC;IACxC;EACF,CAAC;EAED,MAAMC,aAAa,GAAIpC,KAAa,IAAK;IACvC,QAAQA,KAAK;MACX,KAAK,UAAU;QACb,OAAO,mEAAmE;MAC5E,KAAK,cAAc;QACjB,OAAO,uEAAuE;MAChF,KAAK,UAAU;QACb,OAAO,2DAA2D;MACpE;QACE,OAAO,+DAA+D;IAC1E;EACF,CAAC;EAED,MAAMqC,cAAc,GAAIC,OAAe,IAAK;IAC1C,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIG,IAAI,GAAG,CAAC,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE,EAAE;IACpD;IACA,OAAO,GAAGA,IAAI,KAAK;EACrB,CAAC;EAED,MAAMC,eAAe,GAAGjD,OAAO,CAACkD,MAAM,CAACZ,MAAM,IAAI;IAC/C,IAAIlC,OAAO,CAACE,KAAK,IAAIgC,MAAM,CAAChC,KAAK,KAAKF,OAAO,CAACE,KAAK,EAAE,OAAO,KAAK;IACjE,IAAIF,OAAO,CAACG,QAAQ,IAAI,CAAC+B,MAAM,CAAChB,WAAW,EAAE,OAAO,KAAK;IACzD,OAAO,IAAI;EACb,CAAC,CAAC;EAEF,IAAI,CAACvB,IAAI,EAAE;IACT,oBACEL,OAAA;MAAKyD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C1D,OAAA;QAAKyD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1D,OAAA;UAAIyD,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7D9D,OAAA;UAAGyD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ9D,OAAA;UACE+D,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE9D,OAAA,CAAAE,SAAA;IAAAwD,QAAA,gBACE1D,OAAA,CAACF,aAAa;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjB9D,OAAA;MAAKyD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC5C1D,OAAA,CAACJ,MAAM,CAACoE,EAAE;QACRP,SAAS,EAAC,sDAAsD;QAChEQ,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAT,QAAA,EAC/B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAEZ9D,OAAA,CAACJ,MAAM,CAACyE,GAAG;QACTZ,SAAS,EAAC,yDAAyD;QACnEQ,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAb,QAAA,gBAE3B1D,OAAA;UAAIyD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE3C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL9D,OAAA;UAAKyD,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzD1D,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAOyD,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAEnF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9D,OAAA;cACEwE,KAAK,EAAE9D,OAAO,CAACE,KAAM;cACrB6D,QAAQ,EAAGC,CAAC,IAAK/D,UAAU,CAACgE,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE/D,KAAK,EAAE8D,CAAC,CAACE,MAAM,CAACJ;cAAM,CAAC,CAAC,CAAE;cAC1Ef,SAAS,EAAC,2HAA2H;cAAAC,QAAA,gBAErI1D,OAAA;gBAAQwE,KAAK,EAAC,EAAE;gBAAAd,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1C9D,OAAA;gBAAQwE,KAAK,EAAC,UAAU;gBAAAd,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1C9D,OAAA;gBAAQwE,KAAK,EAAC,cAAc;gBAAAd,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnD9D,OAAA;gBAAQwE,KAAK,EAAC,UAAU;gBAAAd,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN9D,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAOyD,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAEnF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9D,OAAA;cAAOyD,SAAS,EAAC,wGAAwG;cAAAC,QAAA,gBACvH1D,OAAA;gBACE6E,IAAI,EAAC,UAAU;gBACfC,OAAO,EAAEpE,OAAO,CAACG,QAAS;gBAC1B4D,QAAQ,EAAGC,CAAC,IAAK/D,UAAU,CAACgE,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE9D,QAAQ,EAAE6D,CAAC,CAACE,MAAM,CAACE;gBAAQ,CAAC,CAAC,CAAE;gBAC/ErB,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACF9D,OAAA;gBAAMyD,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN9D,OAAA;YAAKyD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7B1D,OAAA;cACE+E,OAAO,EAAEA,CAAA,KAAMpE,UAAU,CAAC;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAE;cAC1D4C,SAAS,EAAC,oGAAoG;cAAAC,QAAA,EAC/G;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,GACtDH,eAAe,CAACyB,MAAM,EAAC,kBAAa,EAACzB,eAAe,CAACyB,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAEZtD,OAAO,gBACNR,OAAA;QAAKyD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC1D,OAAA;UAAKyD,SAAS,EAAC;QAAgF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtG9D,OAAA;UAAGyD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,GACJP,eAAe,CAACyB,MAAM,KAAK,CAAC,gBAC9BhF,OAAA;QAAKyD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC1D,OAAA;UAAIyD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClE9D,OAAA;UAAGyD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ9D,OAAA;UACE+E,OAAO,EAAEA,CAAA,KAAMpE,UAAU,CAAC;YAAEC,KAAK,EAAE,EAAE;YAAEC,QAAQ,EAAE;UAAM,CAAC,CAAE;UAC1D4C,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,gBAEN9D,OAAA;QAAKyD,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEH,eAAe,CAACZ,GAAG,CAAC,CAACC,MAAM,EAAEqC,KAAK,kBACjCjF,OAAA,CAACJ,MAAM,CAACyE,GAAG;UAETZ,SAAS,EAAC,kGAAkG;UAC5GQ,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BG,UAAU,EAAE;YAAEC,KAAK,EAAEU,KAAK,GAAG;UAAI,CAAE;UAAAvB,QAAA,eAEnC1D,OAAA;YAAKyD,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB1D,OAAA;cAAKyD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD1D,OAAA;gBAAKyD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC1D,OAAA;kBAAMyD,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEd,MAAM,CAAClB;gBAAS;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzD9D,OAAA;kBAAA0D,QAAA,gBACE1D,OAAA;oBAAIyD,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAChEd,MAAM,CAACrB;kBAAK;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,EACJlB,MAAM,CAAChB,WAAW,iBACjB5B,OAAA;oBAAMyD,SAAS,EAAC,2IAA2I;oBAAAC,QAAA,EAAC;kBAE5J;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9D,OAAA;gBAAMyD,SAAS,EAAE,8CAA8CT,aAAa,CAACJ,MAAM,CAAChC,KAAK,CAAC,EAAG;gBAAA8C,QAAA,EAC1Fd,MAAM,CAACnB;cAAa;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN9D,OAAA;cAAGyD,SAAS,EAAC,4DAA4D;cAAAC,QAAA,EACtEd,MAAM,CAACpB;YAAW;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eAEJ9D,OAAA;cAAKyD,SAAS,EAAC,sEAAsE;cAAAC,QAAA,gBACnF1D,OAAA;gBAAKyD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,eAChC1D,OAAA;kBAAA0D,QAAA,GAAOd,MAAM,CAACf,cAAc,EAAC,YAAU;gBAAA;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACN9D,OAAA;gBAAKyD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,eAChC1D,OAAA;kBAAA0D,QAAA,GAAOd,MAAM,CAACd,aAAa,EAAC,YAAO;gBAAA;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACN9D,OAAA;gBAAKyD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC1D,OAAA;kBAAMyD,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/B9D,OAAA;kBAAA0D,QAAA,EAAOT,cAAc,CAACL,MAAM,CAACjB,kBAAkB;gBAAC;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACN9D,OAAA;gBAAKyD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,eAChC1D,OAAA;kBAAA0D,QAAA,GAAOd,MAAM,CAACb,mBAAmB,EAAC,GAAC;gBAAA;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELlB,MAAM,CAACX,WAAW,iBACjBjC,OAAA;cAAKyD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB1D,OAAA;gBAAKyD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD1D,OAAA;kBAAA0D,QAAA,EAAM;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxB9D,OAAA;kBAAA0D,QAAA,GAAOd,MAAM,CAACb,mBAAmB,EAAC,GAAC;gBAAA;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACN9D,OAAA;gBAAKyD,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,eACnE1D,OAAA;kBACEyD,SAAS,EAAC,6DAA6D;kBACvEyB,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAGvC,MAAM,CAACb,mBAAmB;kBAAI;gBAAE;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAEAlB,MAAM,CAACT,aAAa,CAAC6C,MAAM,GAAG,CAAC,IAAI,CAACpC,MAAM,CAACZ,aAAa,iBACvDhC,OAAA;cAAKyD,SAAS,EAAC,qDAAqD;cAAAC,QAAA,gBAClE1D,OAAA;gBAAGyD,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAAC;cAE7E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACHlB,MAAM,CAACT,aAAa,CAACQ,GAAG,CAACyC,MAAM,iBAC9BpF,OAAA;gBAAqByD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,SAC1E,EAAC0B,MAAM,CAAC7D,KAAK,EAAC,IAAE,EAAC6D,MAAM,CAAC/C,QAAQ,EAAC,eACrC;cAAA,GAFU+C,MAAM,CAAC9D,EAAE;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEd,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAED9D,OAAA;cAAKyD,SAAS,EAAC,WAAW;cAAAC,QAAA,GACvB,CAACd,MAAM,CAACZ,aAAa,gBACpBhC,OAAA;gBACEqF,QAAQ;gBACR5B,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,EAC9F;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,GACP,CAAClB,MAAM,CAACX,WAAW,gBACrBjC,OAAA;gBACE+E,OAAO,EAAEA,CAAA,KAAMvC,YAAY,CAACI,MAAM,CAACtB,EAAE,EAAEsB,MAAM,CAACrB,KAAK,CAAE;gBACrDkC,SAAS,EAAC,0GAA0G;gBAAAC,QAAA,EACrH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,gBAET9D,OAAA;gBACE+D,IAAI,EAAE,YAAYnB,MAAM,CAACtB,EAAE,EAAG;gBAC9BmC,SAAS,EAAC,wHAAwH;gBAAAC,QAAA,EAEjId,MAAM,CAACb,mBAAmB,GAAG,CAAC,GAAG,WAAW,GAAG;cAAW;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CACJ,eAED9D,OAAA;gBACE+D,IAAI,EAAE,YAAYnB,MAAM,CAACtB,EAAE,EAAG;gBAC9BmC,SAAS,EAAC,oLAAoL;gBAAAC,QAAA,EAC/L;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAzGDlB,MAAM,CAACtB,EAAE;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0GJ,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAC1D,EAAA,CAjWID,WAAqB;EAAA,QACRN,OAAO;AAAA;AAAAyF,EAAA,GADpBnF,WAAqB;AAmW3B,eAAeA,WAAW;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}