{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createMatrixClass } from '../../factoriesAny.js';\nexport var MatrixDependencies = {\n  createMatrixClass\n};", "map": {"version": 3, "names": ["createMatrixClass", "MatrixDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMatrixClass.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createMatrixClass } from '../../factoriesAny.js';\nexport var MatrixDependencies = {\n  createMatrixClass\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,uBAAuB;AACzD,OAAO,IAAIC,kBAAkB,GAAG;EAC9BD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}