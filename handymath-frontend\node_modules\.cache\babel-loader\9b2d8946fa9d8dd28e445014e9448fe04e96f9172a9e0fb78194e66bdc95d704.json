{"ast": null, "code": "export var medianDocs = {\n  name: 'median',\n  category: 'Statistics',\n  syntax: ['median(a, b, c, ...)', 'median(A)'],\n  description: 'Compute the median of all values. The values are sorted and the middle value is returned. In case of an even number of values, the average of the two middle values is returned.',\n  examples: ['median(5, 2, 7)', 'median([3, -1, 5, 7])'],\n  seealso: ['max', 'mean', 'min', 'prod', 'std', 'sum', 'variance', 'quantileSeq']\n};", "map": {"version": 3, "names": ["medianDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/statistics/median.js"], "sourcesContent": ["export var medianDocs = {\n  name: 'median',\n  category: 'Statistics',\n  syntax: ['median(a, b, c, ...)', 'median(A)'],\n  description: 'Compute the median of all values. The values are sorted and the middle value is returned. In case of an even number of values, the average of the two middle values is returned.',\n  examples: ['median(5, 2, 7)', 'median([3, -1, 5, 7])'],\n  seealso: ['max', 'mean', 'min', 'prod', 'std', 'sum', 'variance', 'quantileSeq']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,sBAAsB,EAAE,WAAW,CAAC;EAC7CC,WAAW,EAAE,kLAAkL;EAC/LC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,uBAAuB,CAAC;EACtDC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa;AACjF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}