{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';\nimport { typeOfDependencies } from './dependenciesTypeOf.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createLog } from '../../factoriesAny.js';\nexport var logDependencies = {\n  ComplexDependencies,\n  divideScalarDependencies,\n  typeOfDependencies,\n  typedDependencies,\n  createLog\n};", "map": {"version": 3, "names": ["ComplexDependencies", "divideScalarDependencies", "typeOfDependencies", "typedDependencies", "createLog", "logDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesLog.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';\nimport { typeOfDependencies } from './dependenciesTypeOf.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createLog } from '../../factoriesAny.js';\nexport var logDependencies = {\n  ComplexDependencies,\n  divideScalarDependencies,\n  typeOfDependencies,\n  typedDependencies,\n  createLog\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAO,IAAIC,eAAe,GAAG;EAC3BL,mBAAmB;EACnBC,wBAAwB;EACxBC,kBAAkB;EAClBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}