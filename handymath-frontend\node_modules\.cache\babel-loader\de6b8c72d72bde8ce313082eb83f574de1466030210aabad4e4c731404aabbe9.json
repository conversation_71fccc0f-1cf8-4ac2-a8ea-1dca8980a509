{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\components\\\\Logo.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Logo = ({\n  variant = 'default',\n  size = 'md',\n  showText = true,\n  className = '',\n  linkTo = '/'\n}) => {\n  // Tailles du logo\n  const sizeClasses = {\n    sm: 'w-6 h-6',\n    md: 'w-8 h-8',\n    lg: 'w-10 h-10',\n    xl: 'w-12 h-12'\n  };\n\n  // Tailles du texte\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-2xl',\n    xl: 'text-3xl'\n  };\n\n  // Couleurs selon la variante\n  const getLogoColors = () => {\n    switch (variant) {\n      case 'admin':\n        return 'bg-red-600 text-white';\n      case 'student':\n        return 'bg-primary-600 text-white';\n      default:\n        return 'bg-primary-600 text-white';\n    }\n  };\n\n  // Couleurs du texte selon la variante\n  const getTextColors = () => {\n    switch (variant) {\n      case 'admin':\n        return 'text-gray-900 dark:text-white';\n      case 'student':\n        return 'text-gray-900 dark:text-white';\n      default:\n        return 'text-primary-600 hover:text-primary-700 dark:text-white';\n    }\n  };\n  const logoElement = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex items-center space-x-2 ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${sizeClasses[size]} ${getLogoColors()} rounded-lg flex items-center justify-center`,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-bold text-lg\",\n        children: \"H\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), showText && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `${textSizeClasses[size]} font-bold ${getTextColors()} transition-colors`,\n      children: \"HandyMath\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n\n  // Si linkTo est fourni, envelopper dans un Link\n  if (linkTo) {\n    return /*#__PURE__*/_jsxDEV(Link, {\n      to: linkTo,\n      className: \"inline-block\",\n      children: logoElement\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this);\n  }\n  return logoElement;\n};\n_c = Logo;\nexport default Logo;\nvar _c;\n$RefreshReg$(_c, \"Logo\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Logo", "variant", "size", "showText", "className", "linkTo", "sizeClasses", "sm", "md", "lg", "xl", "textSizeClasses", "getLogoColors", "getTextColors", "logoElement", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/components/Logo.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\ninterface LogoProps {\n  variant?: 'default' | 'admin' | 'student';\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  showText?: boolean;\n  className?: string;\n  linkTo?: string;\n}\n\nconst Logo: React.FC<LogoProps> = ({\n  variant = 'default',\n  size = 'md',\n  showText = true,\n  className = '',\n  linkTo = '/'\n}) => {\n  // Tailles du logo\n  const sizeClasses = {\n    sm: 'w-6 h-6',\n    md: 'w-8 h-8',\n    lg: 'w-10 h-10',\n    xl: 'w-12 h-12'\n  };\n\n  // Tailles du texte\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-2xl',\n    xl: 'text-3xl'\n  };\n\n  // Couleurs selon la variante\n  const getLogoColors = () => {\n    switch (variant) {\n      case 'admin':\n        return 'bg-red-600 text-white';\n      case 'student':\n        return 'bg-primary-600 text-white';\n      default:\n        return 'bg-primary-600 text-white';\n    }\n  };\n\n  // Couleurs du texte selon la variante\n  const getTextColors = () => {\n    switch (variant) {\n      case 'admin':\n        return 'text-gray-900 dark:text-white';\n      case 'student':\n        return 'text-gray-900 dark:text-white';\n      default:\n        return 'text-primary-600 hover:text-primary-700 dark:text-white';\n    }\n  };\n\n  const logoElement = (\n    <div className={`flex items-center space-x-2 ${className}`}>\n      {/* Icône du logo */}\n      <div className={`${sizeClasses[size]} ${getLogoColors()} rounded-lg flex items-center justify-center`}>\n        <span className=\"font-bold text-lg\">H</span>\n      </div>\n      \n      {/* Texte du logo */}\n      {showText && (\n        <span className={`${textSizeClasses[size]} font-bold ${getTextColors()} transition-colors`}>\n          HandyMath\n        </span>\n      )}\n    </div>\n  );\n\n  // Si linkTo est fourni, envelopper dans un Link\n  if (linkTo) {\n    return (\n      <Link to={linkTo} className=\"inline-block\">\n        {logoElement}\n      </Link>\n    );\n  }\n\n  return logoElement;\n};\n\nexport default Logo;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUxC,MAAMC,IAAyB,GAAGA,CAAC;EACjCC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,IAAI;EACXC,QAAQ,GAAG,IAAI;EACfC,SAAS,GAAG,EAAE;EACdC,MAAM,GAAG;AACX,CAAC,KAAK;EACJ;EACA,MAAMC,WAAW,GAAG;IAClBC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE;EACN,CAAC;;EAED;EACA,MAAMC,eAAe,GAAG;IACtBJ,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE;EACN,CAAC;;EAED;EACA,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQX,OAAO;MACb,KAAK,OAAO;QACV,OAAO,uBAAuB;MAChC,KAAK,SAAS;QACZ,OAAO,2BAA2B;MACpC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;;EAED;EACA,MAAMY,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQZ,OAAO;MACb,KAAK,OAAO;QACV,OAAO,+BAA+B;MACxC,KAAK,SAAS;QACZ,OAAO,+BAA+B;MACxC;QACE,OAAO,yDAAyD;IACpE;EACF,CAAC;EAED,MAAMa,WAAW,gBACff,OAAA;IAAKK,SAAS,EAAE,+BAA+BA,SAAS,EAAG;IAAAW,QAAA,gBAEzDhB,OAAA;MAAKK,SAAS,EAAE,GAAGE,WAAW,CAACJ,IAAI,CAAC,IAAIU,aAAa,CAAC,CAAC,8CAA+C;MAAAG,QAAA,eACpGhB,OAAA;QAAMK,SAAS,EAAC,mBAAmB;QAAAW,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC,EAGLhB,QAAQ,iBACPJ,OAAA;MAAMK,SAAS,EAAE,GAAGO,eAAe,CAACT,IAAI,CAAC,cAAcW,aAAa,CAAC,CAAC,oBAAqB;MAAAE,QAAA,EAAC;IAE5F;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;;EAED;EACA,IAAId,MAAM,EAAE;IACV,oBACEN,OAAA,CAACF,IAAI;MAACuB,EAAE,EAAEf,MAAO;MAACD,SAAS,EAAC,cAAc;MAAAW,QAAA,EACvCD;IAAW;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEX;EAEA,OAAOL,WAAW;AACpB,CAAC;AAACO,EAAA,GAzEIrB,IAAyB;AA2E/B,eAAeA,IAAI;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}