{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nvar name = 'transformCallback';\nvar dependencies = ['typed'];\nexport var createTransformCallback = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n         * Transforms the given callback function based on its type and number of arrays.\n         *\n         * @param {Function} callback - The callback function to transform.\n         * @param {number} numberOfArrays - The number of arrays to pass to the callback function.\n         * @returns {*} - The transformed callback function.\n         */\n  return function (callback, numberOfArrays) {\n    if (typed.isTypedFunction(callback)) {\n      return _transformTypedCallbackFunction(callback, numberOfArrays);\n    } else {\n      return _transformCallbackFunction(callback, callback.length, numberOfArrays);\n    }\n  };\n\n  /**\n       * Transforms the given typed callback function based on the number of arrays.\n       *\n       * @param {Function} typedFunction - The typed callback function to transform.\n       * @param {number} numberOfArrays - The number of arrays to pass to the callback function.\n       * @returns {*} - The transformed callback function.\n       */\n  function _transformTypedCallbackFunction(typedFunction, numberOfArrays) {\n    var signatures = Object.fromEntries(Object.entries(typedFunction.signatures).map(_ref2 => {\n      var [signature, callbackFunction] = _ref2;\n      var numberOfCallbackInputs = signature.split(',').length;\n      if (typed.isTypedFunction(callbackFunction)) {\n        return [signature, _transformTypedCallbackFunction(callbackFunction, numberOfArrays)];\n      } else {\n        return [signature, _transformCallbackFunction(callbackFunction, numberOfCallbackInputs, numberOfArrays)];\n      }\n    }));\n    if (typeof typedFunction.name === 'string') {\n      return typed(typedFunction.name, signatures);\n    } else {\n      return typed(signatures);\n    }\n  }\n});\n\n/**\n     * Transforms the callback function based on the number of callback inputs and arrays.\n     * There are three cases:\n     * 1. The callback function has N arguments.\n     * 2. The callback function has N+1 arguments.\n     * 3. The callback function has 2N+1 arguments.\n     *\n     * @param {Function} callbackFunction - The callback function to transform.\n     * @param {number} numberOfCallbackInputs - The number of callback inputs.\n     * @param {number} numberOfArrays - The number of arrays.\n     * @returns {Function} The transformed callback function.\n     */\nfunction _transformCallbackFunction(callbackFunction, numberOfCallbackInputs, numberOfArrays) {\n  if (numberOfCallbackInputs === numberOfArrays) {\n    return callbackFunction;\n  } else if (numberOfCallbackInputs === numberOfArrays + 1) {\n    return function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      var vals = args.slice(0, numberOfArrays);\n      var idx = _transformDims(args[numberOfArrays]);\n      return callbackFunction(...vals, idx);\n    };\n  } else if (numberOfCallbackInputs > numberOfArrays + 1) {\n    return function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      var vals = args.slice(0, numberOfArrays);\n      var idx = _transformDims(args[numberOfArrays]);\n      var rest = args.slice(numberOfArrays + 1);\n      return callbackFunction(...vals, idx, ...rest);\n    };\n  } else {\n    return callbackFunction;\n  }\n}\n\n/**\n   * Transforms the dimensions by adding 1 to each dimension.\n   *\n   * @param {Array} dims - The dimensions to transform.\n   * @returns {Array} The transformed dimensions.\n   */\nfunction _transformDims(dims) {\n  return dims.map(dim => dim + 1);\n}", "map": {"version": 3, "names": ["factory", "name", "dependencies", "createTransformCallback", "_ref", "typed", "callback", "numberOfArrays", "isTypedFunction", "_transformTypedCallbackFunction", "_transformCallbackFunction", "length", "typedFunction", "signatures", "Object", "fromEntries", "entries", "map", "_ref2", "signature", "callbackFunction", "numberOfCallbackInputs", "split", "_len", "arguments", "args", "Array", "_key", "vals", "slice", "idx", "_transformDims", "_len2", "_key2", "rest", "dims", "dim"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/transform/utils/transformCallback.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nvar name = 'transformCallback';\nvar dependencies = ['typed'];\nexport var createTransformCallback = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n         * Transforms the given callback function based on its type and number of arrays.\n         *\n         * @param {Function} callback - The callback function to transform.\n         * @param {number} numberOfArrays - The number of arrays to pass to the callback function.\n         * @returns {*} - The transformed callback function.\n         */\n  return function (callback, numberOfArrays) {\n    if (typed.isTypedFunction(callback)) {\n      return _transformTypedCallbackFunction(callback, numberOfArrays);\n    } else {\n      return _transformCallbackFunction(callback, callback.length, numberOfArrays);\n    }\n  };\n\n  /**\n       * Transforms the given typed callback function based on the number of arrays.\n       *\n       * @param {Function} typedFunction - The typed callback function to transform.\n       * @param {number} numberOfArrays - The number of arrays to pass to the callback function.\n       * @returns {*} - The transformed callback function.\n       */\n  function _transformTypedCallbackFunction(typedFunction, numberOfArrays) {\n    var signatures = Object.fromEntries(Object.entries(typedFunction.signatures).map(_ref2 => {\n      var [signature, callbackFunction] = _ref2;\n      var numberOfCallbackInputs = signature.split(',').length;\n      if (typed.isTypedFunction(callbackFunction)) {\n        return [signature, _transformTypedCallbackFunction(callbackFunction, numberOfArrays)];\n      } else {\n        return [signature, _transformCallbackFunction(callbackFunction, numberOfCallbackInputs, numberOfArrays)];\n      }\n    }));\n    if (typeof typedFunction.name === 'string') {\n      return typed(typedFunction.name, signatures);\n    } else {\n      return typed(signatures);\n    }\n  }\n});\n\n/**\n     * Transforms the callback function based on the number of callback inputs and arrays.\n     * There are three cases:\n     * 1. The callback function has N arguments.\n     * 2. The callback function has N+1 arguments.\n     * 3. The callback function has 2N+1 arguments.\n     *\n     * @param {Function} callbackFunction - The callback function to transform.\n     * @param {number} numberOfCallbackInputs - The number of callback inputs.\n     * @param {number} numberOfArrays - The number of arrays.\n     * @returns {Function} The transformed callback function.\n     */\nfunction _transformCallbackFunction(callbackFunction, numberOfCallbackInputs, numberOfArrays) {\n  if (numberOfCallbackInputs === numberOfArrays) {\n    return callbackFunction;\n  } else if (numberOfCallbackInputs === numberOfArrays + 1) {\n    return function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      var vals = args.slice(0, numberOfArrays);\n      var idx = _transformDims(args[numberOfArrays]);\n      return callbackFunction(...vals, idx);\n    };\n  } else if (numberOfCallbackInputs > numberOfArrays + 1) {\n    return function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      var vals = args.slice(0, numberOfArrays);\n      var idx = _transformDims(args[numberOfArrays]);\n      var rest = args.slice(numberOfArrays + 1);\n      return callbackFunction(...vals, idx, ...rest);\n    };\n  } else {\n    return callbackFunction;\n  }\n}\n\n/**\n   * Transforms the dimensions by adding 1 to each dimension.\n   *\n   * @param {Array} dims - The dimensions to transform.\n   * @returns {Array} The transformed dimensions.\n   */\nfunction _transformDims(dims) {\n  return dims.map(dim => dim + 1);\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,IAAIC,IAAI,GAAG,mBAAmB;AAC9B,IAAIC,YAAY,GAAG,CAAC,OAAO,CAAC;AAC5B,OAAO,IAAIC,uBAAuB,GAAG,eAAeH,OAAO,CAACC,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACtF,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,UAAUE,QAAQ,EAAEC,cAAc,EAAE;IACzC,IAAIF,KAAK,CAACG,eAAe,CAACF,QAAQ,CAAC,EAAE;MACnC,OAAOG,+BAA+B,CAACH,QAAQ,EAAEC,cAAc,CAAC;IAClE,CAAC,MAAM;MACL,OAAOG,0BAA0B,CAACJ,QAAQ,EAAEA,QAAQ,CAACK,MAAM,EAAEJ,cAAc,CAAC;IAC9E;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,+BAA+BA,CAACG,aAAa,EAAEL,cAAc,EAAE;IACtE,IAAIM,UAAU,GAAGC,MAAM,CAACC,WAAW,CAACD,MAAM,CAACE,OAAO,CAACJ,aAAa,CAACC,UAAU,CAAC,CAACI,GAAG,CAACC,KAAK,IAAI;MACxF,IAAI,CAACC,SAAS,EAAEC,gBAAgB,CAAC,GAAGF,KAAK;MACzC,IAAIG,sBAAsB,GAAGF,SAAS,CAACG,KAAK,CAAC,GAAG,CAAC,CAACX,MAAM;MACxD,IAAIN,KAAK,CAACG,eAAe,CAACY,gBAAgB,CAAC,EAAE;QAC3C,OAAO,CAACD,SAAS,EAAEV,+BAA+B,CAACW,gBAAgB,EAAEb,cAAc,CAAC,CAAC;MACvF,CAAC,MAAM;QACL,OAAO,CAACY,SAAS,EAAET,0BAA0B,CAACU,gBAAgB,EAAEC,sBAAsB,EAAEd,cAAc,CAAC,CAAC;MAC1G;IACF,CAAC,CAAC,CAAC;IACH,IAAI,OAAOK,aAAa,CAACX,IAAI,KAAK,QAAQ,EAAE;MAC1C,OAAOI,KAAK,CAACO,aAAa,CAACX,IAAI,EAAEY,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,OAAOR,KAAK,CAACQ,UAAU,CAAC;IAC1B;EACF;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASH,0BAA0BA,CAACU,gBAAgB,EAAEC,sBAAsB,EAAEd,cAAc,EAAE;EAC5F,IAAIc,sBAAsB,KAAKd,cAAc,EAAE;IAC7C,OAAOa,gBAAgB;EACzB,CAAC,MAAM,IAAIC,sBAAsB,KAAKd,cAAc,GAAG,CAAC,EAAE;IACxD,OAAO,YAAY;MACjB,KAAK,IAAIgB,IAAI,GAAGC,SAAS,CAACb,MAAM,EAAEc,IAAI,GAAG,IAAIC,KAAK,CAACH,IAAI,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;QACvFF,IAAI,CAACE,IAAI,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;MAC9B;MACA,IAAIC,IAAI,GAAGH,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEtB,cAAc,CAAC;MACxC,IAAIuB,GAAG,GAAGC,cAAc,CAACN,IAAI,CAAClB,cAAc,CAAC,CAAC;MAC9C,OAAOa,gBAAgB,CAAC,GAAGQ,IAAI,EAAEE,GAAG,CAAC;IACvC,CAAC;EACH,CAAC,MAAM,IAAIT,sBAAsB,GAAGd,cAAc,GAAG,CAAC,EAAE;IACtD,OAAO,YAAY;MACjB,KAAK,IAAIyB,KAAK,GAAGR,SAAS,CAACb,MAAM,EAAEc,IAAI,GAAG,IAAIC,KAAK,CAACM,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QAC7FR,IAAI,CAACQ,KAAK,CAAC,GAAGT,SAAS,CAACS,KAAK,CAAC;MAChC;MACA,IAAIL,IAAI,GAAGH,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEtB,cAAc,CAAC;MACxC,IAAIuB,GAAG,GAAGC,cAAc,CAACN,IAAI,CAAClB,cAAc,CAAC,CAAC;MAC9C,IAAI2B,IAAI,GAAGT,IAAI,CAACI,KAAK,CAACtB,cAAc,GAAG,CAAC,CAAC;MACzC,OAAOa,gBAAgB,CAAC,GAAGQ,IAAI,EAAEE,GAAG,EAAE,GAAGI,IAAI,CAAC;IAChD,CAAC;EACH,CAAC,MAAM;IACL,OAAOd,gBAAgB;EACzB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,cAAcA,CAACI,IAAI,EAAE;EAC5B,OAAOA,IAAI,CAAClB,GAAG,CAACmB,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}