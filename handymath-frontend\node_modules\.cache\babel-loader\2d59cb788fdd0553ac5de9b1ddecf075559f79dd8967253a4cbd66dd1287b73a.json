{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\ExercisesPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport StudentNavbar from '../components/StudentNavbar';\nimport Pagination from '../components/Pagination';\nimport api from '../services/api';\n\n// Types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ExercisesPage = () => {\n  _s();\n  var _availableFilters$dif, _availableFilters$typ;\n  const {\n    user\n  } = useAuth();\n  const {\n    addNotification\n  } = useNotifications();\n\n  // Helper functions for notifications\n  const showSuccess = (title, message) => {\n    addNotification({\n      type: 'success',\n      title,\n      message\n    });\n  };\n  const showError = (title, message) => {\n    addNotification({\n      type: 'error',\n      title,\n      message\n    });\n  };\n  const showWarning = (title, message) => {\n    addNotification({\n      type: 'warning',\n      title,\n      message\n    });\n  };\n\n  // State\n  const [exercises, setExercises] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    difficulty: '',\n    type: '',\n    course: ''\n  });\n  const [availableFilters, setAvailableFilters] = useState({});\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(12);\n  const [totalItems, setTotalItems] = useState(0);\n\n  // Options de pagination pour les exercices\n  const exercisePaginationOptions = [6, 12, 18, 24];\n\n  // Effects\n  useEffect(() => {\n    if (user) {\n      fetchExercises();\n    }\n  }, [user, filters, currentPage, itemsPerPage]);\n\n  // API Functions\n  const fetchExercises = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      if (filters.difficulty) params.append('difficulty', filters.difficulty);\n      if (filters.type) params.append('type', filters.type);\n      if (filters.course) params.append('course', filters.course);\n\n      // Pagination parameters\n      params.append('page', currentPage.toString());\n      params.append('page_size', itemsPerPage.toString());\n      const response = await api.get(`/exercises/?${params.toString()}`);\n      if (response.data) {\n        setExercises(response.data.exercises || response.data.results || []);\n        setAvailableFilters(response.data.filters || {});\n        setTotalItems(response.data.count || response.data.total || 0);\n      }\n    } catch (error) {\n      console.error('Erreur lors de la récupération des exercices:', error);\n      showError('Erreur', 'Impossible de charger les exercices');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Event Handlers\n  const handleFilterChange = (filterType, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n  };\n  const clearFilters = () => {\n    setFilters({\n      difficulty: '',\n      type: '',\n      course: ''\n    });\n    setCurrentPage(1); // Reset to first page when clearing filters\n  };\n\n  // Pagination handlers\n  const handlePageChange = page => {\n    setCurrentPage(page);\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  const handleItemsPerPageChange = newItemsPerPage => {\n    setItemsPerPage(newItemsPerPage);\n    setCurrentPage(1); // Reset to first page when changing items per page\n  };\n\n  // Utility Functions\n  const getDifficultyColor = difficulty => {\n    switch (difficulty) {\n      case 'easy':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'medium':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'hard':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n  const getTypeIcon = type => {\n    switch (type) {\n      case 'equation':\n        return '📐';\n      case 'multiple_choice':\n        return '📝';\n      case 'calculation':\n        return '🧮';\n      case 'proof':\n        return '📋';\n      default:\n        return '📚';\n    }\n  };\n  const getStatusIcon = exercise => {\n    if (!exercise.user_attempt) return '🆕';\n    if (exercise.user_attempt.completed) {\n      return exercise.user_attempt.is_correct ? '✅' : '❌';\n    }\n    return '⏳';\n  };\n  const getStatusText = exercise => {\n    if (!exercise.user_attempt) return 'Nouveau';\n    if (exercise.user_attempt.completed) {\n      return exercise.user_attempt.is_correct ? 'Réussi' : 'Échoué';\n    }\n    return 'En cours';\n  };\n\n  // Loading and Error States\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Exercices Interactifs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Vous devez \\xEAtre connect\\xE9 pour acc\\xE9der aux exercices.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Calculate total pages\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\n\n  // Main Render\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(StudentNavbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n        className: \"text-4xl font-bold mb-8 text-center text-primary-600\",\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: \"Exercices Interactifs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-4 flex items-center\",\n          children: \"\\uD83D\\uDD0D Filtres\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Difficult\\xE9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filters.difficulty,\n              onChange: e => handleFilterChange('difficulty', e.target.value),\n              className: \"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Toutes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 15\n              }, this), (_availableFilters$dif = availableFilters.difficulties) === null || _availableFilters$dif === void 0 ? void 0 : _availableFilters$dif.map(([value, label]) => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: value,\n                children: label\n              }, value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filters.type,\n              onChange: e => handleFilterChange('type', e.target.value),\n              className: \"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tous\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 15\n              }, this), (_availableFilters$typ = availableFilters.types) === null || _availableFilters$typ === void 0 ? void 0 : _availableFilters$typ.map(([value, label]) => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: value,\n                children: label\n              }, value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-end\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: clearFilters,\n              className: \"w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\",\n              children: \"Effacer filtres\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600 dark:text-gray-400\",\n          children: [exercises.length, \" exercice\", exercises.length !== 1 ? 's' : '', \" trouv\\xE9\", exercises.length !== 1 ? 's' : '']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 7\n      }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Chargement des exercices...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: exercises.map((exercise, index) => {\n          var _exercise$user_attemp, _exercise$user_attemp2, _exercise$user_attemp3, _exercise$user_attemp4;\n          return /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            whileHover: {\n              scale: 1.02\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl mr-3\",\n                    children: getTypeIcon(exercise.exercise_type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900 dark:text-white line-clamp-2\",\n                      children: exercise.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 dark:text-gray-400\",\n                      children: exercise.course.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xl\",\n                  children: getStatusIcon(exercise)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 dark:text-gray-300 text-sm mb-4 line-clamp-3\",\n                children: exercise.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-2 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exercise.difficulty)}`,\n                  children: exercise.difficulty_display\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300\",\n                  children: exercise.type_display\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4 text-sm mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mr-1\",\n                    children: \"\\uD83C\\uDFC6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [exercise.points, \" pts\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mr-1\",\n                    children: \"\\u23F1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [Math.floor(exercise.time_limit / 60), \"min\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: \"Statut:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `font-medium ${(_exercise$user_attemp = exercise.user_attempt) !== null && _exercise$user_attemp !== void 0 && _exercise$user_attemp.completed ? exercise.user_attempt.is_correct ? 'text-green-600' : 'text-red-600' : 'text-blue-600'}`,\n                    children: getStatusText(exercise)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 19\n                }, this), ((_exercise$user_attemp2 = exercise.user_attempt) === null || _exercise$user_attemp2 === void 0 ? void 0 : _exercise$user_attemp2.completed) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2 text-xs text-gray-600 dark:text-gray-400\",\n                  children: [\"Points obtenus: \", exercise.user_attempt.points_earned, \"/\", exercise.points]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: `/exercises/${exercise.id}`,\n                className: `block w-full text-center py-3 px-4 rounded-lg font-medium transition-colors ${(_exercise$user_attemp3 = exercise.user_attempt) !== null && _exercise$user_attemp3 !== void 0 && _exercise$user_attemp3.completed ? 'bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300' : 'bg-primary-600 hover:bg-primary-700 text-white'}`,\n                children: (_exercise$user_attemp4 = exercise.user_attempt) !== null && _exercise$user_attemp4 !== void 0 && _exercise$user_attemp4.completed ? 'Revoir' : 'Commencer'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)\n          }, exercise.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), !loading && exercises.length === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"text-center py-12\",\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        transition: {\n          delay: 0.3\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDCDA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold mb-2\",\n          children: \"Aucun exercice trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-4\",\n          children: \"Essayez de modifier vos filtres ou revenez plus tard.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearFilters,\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors\",\n          children: \"Effacer les filtres\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this), totalItems > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8\",\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          currentPage: currentPage,\n          totalPages: totalPages,\n          totalItems: totalItems,\n          itemsPerPage: itemsPerPage,\n          onPageChange: handlePageChange,\n          onItemsPerPageChange: handleItemsPerPageChange,\n          showItemsPerPage: true,\n          showInfo: true,\n          itemsPerPageOptions: exercisePaginationOptions\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ExercisesPage, \"BnqML7GneqR8lpbMrbO2NVKNj4Q=\", false, function () {\n  return [useAuth, useNotifications];\n});\n_c = ExercisesPage;\nexport default ExercisesPage;\nvar _c;\n$RefreshReg$(_c, \"ExercisesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useAuth", "useNotifications", "StudentNavbar", "Pagination", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ExercisesPage", "_s", "_availableFilters$dif", "_availableFilters$typ", "user", "addNotification", "showSuccess", "title", "message", "type", "showError", "showWarning", "exercises", "setExercises", "loading", "setLoading", "filters", "setFilters", "difficulty", "course", "availableFilters", "setAvailableFilters", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "totalItems", "setTotalItems", "exercisePaginationOptions", "fetchExercises", "params", "URLSearchParams", "append", "toString", "response", "get", "data", "results", "count", "total", "error", "console", "handleFilterChange", "filterType", "value", "prev", "clearFilters", "handlePageChange", "page", "window", "scrollTo", "top", "behavior", "handleItemsPerPageChange", "newItemsPerPage", "getDifficultyColor", "getTypeIcon", "getStatusIcon", "exercise", "user_attempt", "completed", "is_correct", "getStatusText", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "totalPages", "Math", "ceil", "h1", "initial", "opacity", "y", "animate", "div", "transition", "delay", "onChange", "e", "target", "difficulties", "map", "label", "types", "onClick", "length", "index", "_exercise$user_attemp", "_exercise$user_attemp2", "_exercise$user_attemp3", "_exercise$user_attemp4", "whileHover", "scale", "exercise_type", "description", "difficulty_display", "type_display", "points", "floor", "time_limit", "points_earned", "id", "onPageChange", "onItemsPerPageChange", "showItemsPerPage", "showInfo", "itemsPerPageOptions", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/pages/ExercisesPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport StudentNavbar from '../components/StudentNavbar';\nimport Pagination from '../components/Pagination';\nimport api from '../services/api';\n\n// Types\ninterface Exercise {\n  id: number;\n  title: string;\n  description: string;\n  course: {\n    id: number;\n    title: string;\n    level: string;\n  };\n  difficulty: string;\n  difficulty_display: string;\n  exercise_type: string;\n  type_display: string;\n  points: number;\n  time_limit: number;\n  user_attempt?: {\n    completed: boolean;\n    is_correct: boolean;\n    points_earned: number;\n    time_taken: number;\n  } | null;\n  created_at: string;\n}\n\ninterface Filters {\n  difficulty: string;\n  type: string;\n  course: string;\n}\n\nconst ExercisesPage: React.FC = () => {\n  const { user } = useAuth();\n  const { addNotification } = useNotifications();\n\n  // Helper functions for notifications\n  const showSuccess = (title: string, message: string) => {\n    addNotification({ type: 'success', title, message });\n  };\n  const showError = (title: string, message: string) => {\n    addNotification({ type: 'error', title, message });\n  };\n  const showWarning = (title: string, message: string) => {\n    addNotification({ type: 'warning', title, message });\n  };\n\n  // State\n  const [exercises, setExercises] = useState<Exercise[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState<Filters>({\n    difficulty: '',\n    type: '',\n    course: ''\n  });\n  const [availableFilters, setAvailableFilters] = useState<any>({});\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(12);\n  const [totalItems, setTotalItems] = useState(0);\n\n  // Options de pagination pour les exercices\n  const exercisePaginationOptions = [6, 12, 18, 24];\n\n  // Effects\n  useEffect(() => {\n    if (user) {\n      fetchExercises();\n    }\n  }, [user, filters, currentPage, itemsPerPage]);\n\n  // API Functions\n  const fetchExercises = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      if (filters.difficulty) params.append('difficulty', filters.difficulty);\n      if (filters.type) params.append('type', filters.type);\n      if (filters.course) params.append('course', filters.course);\n\n      // Pagination parameters\n      params.append('page', currentPage.toString());\n      params.append('page_size', itemsPerPage.toString());\n\n      const response = await api.get(`/exercises/?${params.toString()}`);\n      if (response.data) {\n        setExercises(response.data.exercises || response.data.results || []);\n        setAvailableFilters(response.data.filters || {});\n        setTotalItems(response.data.count || response.data.total || 0);\n      }\n    } catch (error: any) {\n      console.error('Erreur lors de la récupération des exercices:', error);\n      showError('Erreur', 'Impossible de charger les exercices');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Event Handlers\n  const handleFilterChange = (filterType: keyof Filters, value: string) => {\n    setFilters(prev => ({\n      ...prev,\n      [filterType]: value\n    }));\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      difficulty: '',\n      type: '',\n      course: ''\n    });\n    setCurrentPage(1); // Reset to first page when clearing filters\n  };\n\n  // Pagination handlers\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const handleItemsPerPageChange = (newItemsPerPage: number) => {\n    setItemsPerPage(newItemsPerPage);\n    setCurrentPage(1); // Reset to first page when changing items per page\n  };\n\n  // Utility Functions\n  const getDifficultyColor = (difficulty: string) => {\n    switch (difficulty) {\n      case 'easy':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'medium':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'hard':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'equation':\n        return '📐';\n      case 'multiple_choice':\n        return '📝';\n      case 'calculation':\n        return '🧮';\n      case 'proof':\n        return '📋';\n      default:\n        return '📚';\n    }\n  };\n\n  const getStatusIcon = (exercise: Exercise) => {\n    if (!exercise.user_attempt) return '🆕';\n    if (exercise.user_attempt.completed) {\n      return exercise.user_attempt.is_correct ? '✅' : '❌';\n    }\n    return '⏳';\n  };\n\n  const getStatusText = (exercise: Exercise) => {\n    if (!exercise.user_attempt) return 'Nouveau';\n    if (exercise.user_attempt.completed) {\n      return exercise.user_attempt.is_correct ? 'Réussi' : 'Échoué';\n    }\n    return 'En cours';\n  };\n\n  // Loading and Error States\n  if (!user) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Exercices Interactifs</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            Vous devez être connecté pour accéder aux exercices.\n          </p>\n          <a\n            href=\"/login\"\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Se connecter\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  // Calculate total pages\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\n\n  // Main Render\n  return (\n    <>\n      <StudentNavbar />\n      <div className=\"container mx-auto px-4 py-8\">\n      <motion.h1\n        className=\"text-4xl font-bold mb-8 text-center text-primary-600\"\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        Exercices Interactifs\n      </motion.h1>\n\n      {/* Filtres */}\n      <motion.div\n        className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1 }}\n      >\n        <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n          🔍 Filtres\n        </h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n          {/* Filtre difficulté */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Difficulté\n            </label>\n            <select\n              value={filters.difficulty}\n              onChange={(e) => handleFilterChange('difficulty', e.target.value)}\n              className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"\">Toutes</option>\n              {availableFilters.difficulties?.map(([value, label]: [string, string]) => (\n                <option key={value} value={value}>{label}</option>\n              ))}\n            </select>\n          </div>\n\n          {/* Filtre type */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Type\n            </label>\n            <select\n              value={filters.type}\n              onChange={(e) => handleFilterChange('type', e.target.value)}\n              className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"\">Tous</option>\n              {availableFilters.types?.map(([value, label]: [string, string]) => (\n                <option key={value} value={value}>{label}</option>\n              ))}\n            </select>\n          </div>\n\n          {/* Bouton effacer */}\n          <div className=\"flex items-end\">\n            <button\n              onClick={clearFilters}\n              className=\"w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n            >\n              Effacer filtres\n            </button>\n          </div>\n        </div>\n        <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n          {exercises.length} exercice{exercises.length !== 1 ? 's' : ''} trouvé{exercises.length !== 1 ? 's' : ''}\n        </div>\n      </motion.div>\n\n      {/* État de chargement */}\n      {loading && (\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Chargement des exercices...</p>\n        </div>\n      )}\n\n      {/* Liste des exercices */}\n      {!loading && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {exercises.map((exercise, index) => (\n            <motion.div\n              key={exercise.id}\n              className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n              whileHover={{ scale: 1.02 }}\n            >\n              <div className=\"p-6\">\n                {/* En-tête de l'exercice */}\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-2xl mr-3\">{getTypeIcon(exercise.exercise_type)}</span>\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white line-clamp-2\">\n                        {exercise.title}\n                      </h3>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {exercise.course.title}\n                      </p>\n                    </div>\n                  </div>\n                  <span className=\"text-xl\">{getStatusIcon(exercise)}</span>\n                </div>\n\n                {/* Description */}\n                <p className=\"text-gray-700 dark:text-gray-300 text-sm mb-4 line-clamp-3\">\n                  {exercise.description}\n                </p>\n\n                {/* Badges */}\n                <div className=\"flex flex-wrap gap-2 mb-4\">\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(exercise.difficulty)}`}>\n                    {exercise.difficulty_display}\n                  </span>\n                  <span className=\"px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300\">\n                    {exercise.type_display}\n                  </span>\n                </div>\n\n                {/* Informations */}\n                <div className=\"grid grid-cols-2 gap-4 text-sm mb-4\">\n                  <div className=\"flex items-center\">\n                    <span className=\"mr-1\">🏆</span>\n                    <span>{exercise.points} pts</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span className=\"mr-1\">⏱</span>\n                    <span>{Math.floor(exercise.time_limit / 60)}min</span>\n                  </div>\n                </div>\n\n                {/* Statut et résultats */}\n                <div className=\"mb-4\">\n                  <div className=\"flex items-center justify-between text-sm\">\n                    <span className=\"font-medium\">Statut:</span>\n                    <span className={`font-medium ${\n                      exercise.user_attempt?.completed\n                        ? exercise.user_attempt.is_correct\n                          ? 'text-green-600'\n                          : 'text-red-600'\n                        : 'text-blue-600'\n                    }`}>\n                      {getStatusText(exercise)}\n                    </span>\n                  </div>\n                  {exercise.user_attempt?.completed && (\n                    <div className=\"mt-2 text-xs text-gray-600 dark:text-gray-400\">\n                      Points obtenus: {exercise.user_attempt.points_earned}/{exercise.points}\n                    </div>\n                  )}\n                </div>\n\n                {/* Bouton d'action */}\n                <a\n                  href={`/exercises/${exercise.id}`}\n                  className={`block w-full text-center py-3 px-4 rounded-lg font-medium transition-colors ${\n                    exercise.user_attempt?.completed\n                      ? 'bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300'\n                      : 'bg-primary-600 hover:bg-primary-700 text-white'\n                  }`}\n                >\n                  {exercise.user_attempt?.completed ? 'Revoir' : 'Commencer'}\n                </a>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      )}\n\n      {/* Message si aucun exercice */}\n      {!loading && exercises.length === 0 && (\n        <motion.div\n          className=\"text-center py-12\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.3 }}\n        >\n          <div className=\"text-6xl mb-4\">📚</div>\n          <h3 className=\"text-xl font-semibold mb-2\">Aucun exercice trouvé</h3>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n            Essayez de modifier vos filtres ou revenez plus tard.\n          </p>\n          <button\n            onClick={clearFilters}\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors\"\n          >\n            Effacer les filtres\n          </button>\n        </motion.div>\n      )}\n\n      {/* Pagination */}\n      {totalItems > 0 && (\n        <div className=\"mt-8\">\n          <Pagination\n            currentPage={currentPage}\n            totalPages={totalPages}\n            totalItems={totalItems}\n            itemsPerPage={itemsPerPage}\n            onPageChange={handlePageChange}\n            onItemsPerPageChange={handleItemsPerPageChange}\n            showItemsPerPage={true}\n            showInfo={true}\n            itemsPerPageOptions={exercisePaginationOptions}\n          />\n        </div>\n      )}\n    </div>\n    </>\n  );\n};\n\nexport default ExercisesPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,GAAG,MAAM,iBAAiB;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA+BA,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EACpC,MAAM;IAAEC;EAAK,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEc;EAAgB,CAAC,GAAGb,gBAAgB,CAAC,CAAC;;EAE9C;EACA,MAAMc,WAAW,GAAGA,CAACC,KAAa,EAAEC,OAAe,KAAK;IACtDH,eAAe,CAAC;MAAEI,IAAI,EAAE,SAAS;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACtD,CAAC;EACD,MAAME,SAAS,GAAGA,CAACH,KAAa,EAAEC,OAAe,KAAK;IACpDH,eAAe,CAAC;MAAEI,IAAI,EAAE,OAAO;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACpD,CAAC;EACD,MAAMG,WAAW,GAAGA,CAACJ,KAAa,EAAEC,OAAe,KAAK;IACtDH,eAAe,CAAC;MAAEI,IAAI,EAAE,SAAS;MAAEF,KAAK;MAAEC;IAAQ,CAAC,CAAC;EACtD,CAAC;;EAED;EACA,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAU;IAC9C8B,UAAU,EAAE,EAAE;IACdT,IAAI,EAAE,EAAE;IACRU,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAM,CAAC,CAAC,CAAC;;EAEjE;EACA,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAMwC,yBAAyB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;EAEjD;EACAvC,SAAS,CAAC,MAAM;IACd,IAAIe,IAAI,EAAE;MACRyB,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACzB,IAAI,EAAEY,OAAO,EAAEM,WAAW,EAAEE,YAAY,CAAC,CAAC;;EAE9C;EACA,MAAMK,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFd,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMe,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpC,IAAIf,OAAO,CAACE,UAAU,EAAEY,MAAM,CAACE,MAAM,CAAC,YAAY,EAAEhB,OAAO,CAACE,UAAU,CAAC;MACvE,IAAIF,OAAO,CAACP,IAAI,EAAEqB,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEhB,OAAO,CAACP,IAAI,CAAC;MACrD,IAAIO,OAAO,CAACG,MAAM,EAAEW,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEhB,OAAO,CAACG,MAAM,CAAC;;MAE3D;MACAW,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEV,WAAW,CAACW,QAAQ,CAAC,CAAC,CAAC;MAC7CH,MAAM,CAACE,MAAM,CAAC,WAAW,EAAER,YAAY,CAACS,QAAQ,CAAC,CAAC,CAAC;MAEnD,MAAMC,QAAQ,GAAG,MAAMvC,GAAG,CAACwC,GAAG,CAAC,eAAeL,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;MAClE,IAAIC,QAAQ,CAACE,IAAI,EAAE;QACjBvB,YAAY,CAACqB,QAAQ,CAACE,IAAI,CAACxB,SAAS,IAAIsB,QAAQ,CAACE,IAAI,CAACC,OAAO,IAAI,EAAE,CAAC;QACpEhB,mBAAmB,CAACa,QAAQ,CAACE,IAAI,CAACpB,OAAO,IAAI,CAAC,CAAC,CAAC;QAChDW,aAAa,CAACO,QAAQ,CAACE,IAAI,CAACE,KAAK,IAAIJ,QAAQ,CAACE,IAAI,CAACG,KAAK,IAAI,CAAC,CAAC;MAChE;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE9B,SAAS,CAAC,QAAQ,EAAE,qCAAqC,CAAC;IAC5D,CAAC,SAAS;MACRK,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2B,kBAAkB,GAAGA,CAACC,UAAyB,EAAEC,KAAa,KAAK;IACvE3B,UAAU,CAAC4B,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACF,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB7B,UAAU,CAAC;MACTC,UAAU,EAAE,EAAE;MACdT,IAAI,EAAE,EAAE;MACRU,MAAM,EAAE;IACV,CAAC,CAAC;IACFI,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMwB,gBAAgB,GAAIC,IAAY,IAAK;IACzCzB,cAAc,CAACyB,IAAI,CAAC;IACpBC,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;EAED,MAAMC,wBAAwB,GAAIC,eAAuB,IAAK;IAC5D7B,eAAe,CAAC6B,eAAe,CAAC;IAChC/B,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMgC,kBAAkB,GAAIrC,UAAkB,IAAK;IACjD,QAAQA,UAAU;MAChB,KAAK,MAAM;QACT,OAAO,mEAAmE;MAC5E,KAAK,QAAQ;QACX,OAAO,uEAAuE;MAChF,KAAK,MAAM;QACT,OAAO,2DAA2D;MACpE;QACE,OAAO,+DAA+D;IAC1E;EACF,CAAC;EAED,MAAMsC,WAAW,GAAI/C,IAAY,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,UAAU;QACb,OAAO,IAAI;MACb,KAAK,iBAAiB;QACpB,OAAO,IAAI;MACb,KAAK,aAAa;QAChB,OAAO,IAAI;MACb,KAAK,OAAO;QACV,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMgD,aAAa,GAAIC,QAAkB,IAAK;IAC5C,IAAI,CAACA,QAAQ,CAACC,YAAY,EAAE,OAAO,IAAI;IACvC,IAAID,QAAQ,CAACC,YAAY,CAACC,SAAS,EAAE;MACnC,OAAOF,QAAQ,CAACC,YAAY,CAACE,UAAU,GAAG,GAAG,GAAG,GAAG;IACrD;IACA,OAAO,GAAG;EACZ,CAAC;EAED,MAAMC,aAAa,GAAIJ,QAAkB,IAAK;IAC5C,IAAI,CAACA,QAAQ,CAACC,YAAY,EAAE,OAAO,SAAS;IAC5C,IAAID,QAAQ,CAACC,YAAY,CAACC,SAAS,EAAE;MACnC,OAAOF,QAAQ,CAACC,YAAY,CAACE,UAAU,GAAG,QAAQ,GAAG,QAAQ;IAC/D;IACA,OAAO,UAAU;EACnB,CAAC;;EAED;EACA,IAAI,CAACzD,IAAI,EAAE;IACT,oBACEP,OAAA;MAAKkE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CnE,OAAA;QAAKkE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BnE,OAAA;UAAIkE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClEvE,OAAA;UAAGkE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJvE,OAAA;UACEwE,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAME,UAAU,GAAGC,IAAI,CAACC,IAAI,CAAC9C,UAAU,GAAGF,YAAY,CAAC;;EAEvD;EACA,oBACE3B,OAAA,CAAAE,SAAA;IAAAiE,QAAA,gBACEnE,OAAA,CAACJ,aAAa;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjBvE,OAAA;MAAKkE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC5CnE,OAAA,CAACP,MAAM,CAACmF,EAAE;QACRV,SAAS,EAAC,sDAAsD;QAChEW,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAZ,QAAA,EAC/B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAGZvE,OAAA,CAACP,MAAM,CAACwF,GAAG;QACTf,SAAS,EAAC,yDAAyD;QACnEW,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAhB,QAAA,gBAE3BnE,OAAA;UAAIkE,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAE7D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLvE,OAAA;UAAKkE,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBAEzDnE,OAAA;YAAAmE,QAAA,gBACEnE,OAAA;cAAOkE,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAEnF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvE,OAAA;cACE+C,KAAK,EAAE5B,OAAO,CAACE,UAAW;cAC1B+D,QAAQ,EAAGC,CAAC,IAAKxC,kBAAkB,CAAC,YAAY,EAAEwC,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE;cAClEmB,SAAS,EAAC,2HAA2H;cAAAC,QAAA,gBAErInE,OAAA;gBAAQ+C,KAAK,EAAC,EAAE;gBAAAoB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,GAAAlE,qBAAA,GAC/BkB,gBAAgB,CAACgE,YAAY,cAAAlF,qBAAA,uBAA7BA,qBAAA,CAA+BmF,GAAG,CAAC,CAAC,CAACzC,KAAK,EAAE0C,KAAK,CAAmB,kBACnEzF,OAAA;gBAAoB+C,KAAK,EAAEA,KAAM;gBAAAoB,QAAA,EAAEsB;cAAK,GAA3B1C,KAAK;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA+B,CAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNvE,OAAA;YAAAmE,QAAA,gBACEnE,OAAA;cAAOkE,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAEnF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvE,OAAA;cACE+C,KAAK,EAAE5B,OAAO,CAACP,IAAK;cACpBwE,QAAQ,EAAGC,CAAC,IAAKxC,kBAAkB,CAAC,MAAM,EAAEwC,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE;cAC5DmB,SAAS,EAAC,2HAA2H;cAAAC,QAAA,gBAErInE,OAAA;gBAAQ+C,KAAK,EAAC,EAAE;gBAAAoB,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,GAAAjE,qBAAA,GAC7BiB,gBAAgB,CAACmE,KAAK,cAAApF,qBAAA,uBAAtBA,qBAAA,CAAwBkF,GAAG,CAAC,CAAC,CAACzC,KAAK,EAAE0C,KAAK,CAAmB,kBAC5DzF,OAAA;gBAAoB+C,KAAK,EAAEA,KAAM;gBAAAoB,QAAA,EAAEsB;cAAK,GAA3B1C,KAAK;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA+B,CAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNvE,OAAA;YAAKkE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BnE,OAAA;cACE2F,OAAO,EAAE1C,YAAa;cACtBiB,SAAS,EAAC,oGAAoG;cAAAC,QAAA,EAC/G;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvE,OAAA;UAAKkE,SAAS,EAAC,0CAA0C;UAAAC,QAAA,GACtDpD,SAAS,CAAC6E,MAAM,EAAC,WAAS,EAAC7E,SAAS,CAAC6E,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,YAAO,EAAC7E,SAAS,CAAC6E,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;QAAA;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGZtD,OAAO,iBACNjB,OAAA;QAAKkE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCnE,OAAA;UAAKkE,SAAS,EAAC;QAAgF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGvE,OAAA;UAAGkE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CACN,EAGA,CAACtD,OAAO,iBACPjB,OAAA;QAAKkE,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEpD,SAAS,CAACyE,GAAG,CAAC,CAAC3B,QAAQ,EAAEgC,KAAK;UAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UAAA,oBAC7BjG,OAAA,CAACP,MAAM,CAACwF,GAAG;YAETf,SAAS,EAAC,4GAA4G;YACtHW,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,KAAK,EAAEU,KAAK,GAAG;YAAI,CAAE;YACnCK,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAAAhC,QAAA,eAE5BnE,OAAA;cAAKkE,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAElBnE,OAAA;gBAAKkE,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDnE,OAAA;kBAAKkE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCnE,OAAA;oBAAMkE,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAER,WAAW,CAACE,QAAQ,CAACuC,aAAa;kBAAC;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5EvE,OAAA;oBAAAmE,QAAA,gBACEnE,OAAA;sBAAIkE,SAAS,EAAC,kEAAkE;sBAAAC,QAAA,EAC7EN,QAAQ,CAACnD;oBAAK;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC,eACLvE,OAAA;sBAAGkE,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,EACpDN,QAAQ,CAACvC,MAAM,CAACZ;oBAAK;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvE,OAAA;kBAAMkE,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAEP,aAAa,CAACC,QAAQ;gBAAC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eAGNvE,OAAA;gBAAGkE,SAAS,EAAC,4DAA4D;gBAAAC,QAAA,EACtEN,QAAQ,CAACwC;cAAW;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAGJvE,OAAA;gBAAKkE,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCnE,OAAA;kBAAMkE,SAAS,EAAE,8CAA8CR,kBAAkB,CAACG,QAAQ,CAACxC,UAAU,CAAC,EAAG;kBAAA8C,QAAA,EACtGN,QAAQ,CAACyC;gBAAkB;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACPvE,OAAA;kBAAMkE,SAAS,EAAC,0GAA0G;kBAAAC,QAAA,EACvHN,QAAQ,CAAC0C;gBAAY;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAGNvE,OAAA;gBAAKkE,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBAClDnE,OAAA;kBAAKkE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCnE,OAAA;oBAAMkE,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCvE,OAAA;oBAAAmE,QAAA,GAAON,QAAQ,CAAC2C,MAAM,EAAC,MAAI;kBAAA;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACNvE,OAAA;kBAAKkE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCnE,OAAA;oBAAMkE,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/BvE,OAAA;oBAAAmE,QAAA,GAAOO,IAAI,CAAC+B,KAAK,CAAC5C,QAAQ,CAAC6C,UAAU,GAAG,EAAE,CAAC,EAAC,KAAG;kBAAA;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNvE,OAAA;gBAAKkE,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBnE,OAAA;kBAAKkE,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,gBACxDnE,OAAA;oBAAMkE,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5CvE,OAAA;oBAAMkE,SAAS,EAAE,eACf,CAAA4B,qBAAA,GAAAjC,QAAQ,CAACC,YAAY,cAAAgC,qBAAA,eAArBA,qBAAA,CAAuB/B,SAAS,GAC5BF,QAAQ,CAACC,YAAY,CAACE,UAAU,GAC9B,gBAAgB,GAChB,cAAc,GAChB,eAAe,EAClB;oBAAAG,QAAA,EACAF,aAAa,CAACJ,QAAQ;kBAAC;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EACL,EAAAwB,sBAAA,GAAAlC,QAAQ,CAACC,YAAY,cAAAiC,sBAAA,uBAArBA,sBAAA,CAAuBhC,SAAS,kBAC/B/D,OAAA;kBAAKkE,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,GAAC,kBAC7C,EAACN,QAAQ,CAACC,YAAY,CAAC6C,aAAa,EAAC,GAAC,EAAC9C,QAAQ,CAAC2C,MAAM;gBAAA;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNvE,OAAA;gBACEwE,IAAI,EAAE,cAAcX,QAAQ,CAAC+C,EAAE,EAAG;gBAClC1C,SAAS,EAAE,+EACT,CAAA8B,sBAAA,GAAAnC,QAAQ,CAACC,YAAY,cAAAkC,sBAAA,eAArBA,sBAAA,CAAuBjC,SAAS,GAC5B,wGAAwG,GACxG,gDAAgD,EACnD;gBAAAI,QAAA,EAEF,CAAA8B,sBAAA,GAAApC,QAAQ,CAACC,YAAY,cAAAmC,sBAAA,eAArBA,sBAAA,CAAuBlC,SAAS,GAAG,QAAQ,GAAG;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC,GAnFDV,QAAQ,CAAC+C,EAAE;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoFN,CAAC;QAAA,CACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGA,CAACtD,OAAO,IAAIF,SAAS,CAAC6E,MAAM,KAAK,CAAC,iBACjC5F,OAAA,CAACP,MAAM,CAACwF,GAAG;QACTf,SAAS,EAAC,mBAAmB;QAC7BW,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBE,OAAO,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACxBI,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAhB,QAAA,gBAE3BnE,OAAA;UAAKkE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCvE,OAAA;UAAIkE,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEvE,OAAA;UAAGkE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJvE,OAAA;UACE2F,OAAO,EAAE1C,YAAa;UACtBiB,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACb,EAGA1C,UAAU,GAAG,CAAC,iBACb7B,OAAA;QAAKkE,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBnE,OAAA,CAACH,UAAU;UACT4B,WAAW,EAAEA,WAAY;UACzBgD,UAAU,EAAEA,UAAW;UACvB5C,UAAU,EAAEA,UAAW;UACvBF,YAAY,EAAEA,YAAa;UAC3BkF,YAAY,EAAE3D,gBAAiB;UAC/B4D,oBAAoB,EAAEtD,wBAAyB;UAC/CuD,gBAAgB,EAAE,IAAK;UACvBC,QAAQ,EAAE,IAAK;UACfC,mBAAmB,EAAElF;QAA0B;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAACnE,EAAA,CA3XID,aAAuB;EAAA,QACVT,OAAO,EACIC,gBAAgB;AAAA;AAAAuH,EAAA,GAFxC/G,aAAuB;AA6X7B,eAAeA,aAAa;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}