#!/usr/bin/env python3
"""
Script pour tester l'endpoint de détail d'un cours spécifique
"""

import os
import sys
import django
import requests
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from api.models import Course, CourseEnrollment
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

def get_test_token():
    """Obtenir un token de test"""
    # C<PERSON>er ou récupérer un utilisateur de test
    user, created = User.objects.get_or_create(
        username='test_student',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'Student',
            'role': 'student'
        }
    )
    
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"✅ Utilisateur de test créé: {user.username}")
    else:
        print(f"📚 Utilisateur de test existant: {user.username}")
    
    # Générer un token JWT
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    print(f"🔑 Token généré pour l'utilisateur: {user.username}")
    print(f"Token: {access_token[:50]}...")
    
    return access_token, user

def test_course_detail():
    """Tester l'endpoint de détail d'un cours"""
    print("🚀 Test de l'endpoint de détail d'un cours\n")
    
    # Obtenir un token de test
    token, user = get_test_token()
    
    # Headers pour les requêtes
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # URL de base
    base_url = 'http://localhost:8000/api'
    
    # Récupérer le premier cours
    first_course = Course.objects.filter(status='published').first()
    if not first_course:
        print("❌ Aucun cours publié trouvé!")
        return
    
    print(f"📚 Test avec le cours: {first_course.title} (ID: {first_course.id})")
    
    # Test de l'endpoint de détail
    print(f"\n🧪 Test: GET /api/courses/{first_course.id}/")
    try:
        response = requests.get(f'{base_url}/courses/{first_course.id}/', headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Succès! Détails du cours:")
            print(f"   Titre: {data.get('title')}")
            print(f"   Description: {data.get('description')[:100]}...")
            print(f"   Niveau: {data.get('level_display')}")
            print(f"   Durée: {data.get('estimated_duration')} min")
            print(f"   Inscrit: {data.get('is_enrolled')}")
            print(f"   Progression: {data.get('progress_percentage')}%")
            print(f"   Chapitres: {len(data.get('chapters', []))}")
            
            # Afficher les chapitres
            chapters = data.get('chapters', [])
            for i, chapter in enumerate(chapters[:3], 1):
                print(f"   Chapitre {i}: {chapter.get('title')}")
                lessons = chapter.get('lessons', [])
                print(f"     Leçons: {len(lessons)}")
                
        elif response.status_code == 404:
            print(f"❌ Cours non trouvé (404)")
            print(f"Réponse: {response.text}")
        elif response.status_code == 401:
            print(f"❌ Non autorisé (401) - Problème d'authentification")
            print(f"Réponse: {response.text}")
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"Réponse: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Erreur de connexion - Le serveur Django n'est pas accessible")
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")

def test_multiple_courses():
    """Tester plusieurs cours"""
    print("\n🔍 Test de plusieurs cours:")
    
    token, user = get_test_token()
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    base_url = 'http://localhost:8000/api'
    
    # Tester les 5 premiers cours
    courses = Course.objects.filter(status='published')[:5]
    
    for course in courses:
        print(f"\n📖 Test cours ID {course.id}: {course.title}")
        try:
            response = requests.get(f'{base_url}/courses/{course.id}/', headers=headers)
            if response.status_code == 200:
                print(f"   ✅ OK - Cours accessible")
            elif response.status_code == 404:
                print(f"   ❌ 404 - Cours non trouvé")
            else:
                print(f"   ⚠️ {response.status_code} - {response.text[:100]}")
        except Exception as e:
            print(f"   ❌ Erreur: {e}")

if __name__ == "__main__":
    test_course_detail()
    test_multiple_courses()
