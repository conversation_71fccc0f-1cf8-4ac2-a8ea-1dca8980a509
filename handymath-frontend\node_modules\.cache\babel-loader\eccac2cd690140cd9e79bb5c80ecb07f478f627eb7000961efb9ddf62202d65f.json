{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nimport { isFunctionNode, isOperatorNode, isParenthesisNode } from '../../../utils/is.js';\nimport { factory } from '../../../utils/factory.js';\nimport { hasOwnProperty } from '../../../utils/object.js';\nvar name = 'simplifyUtil';\nvar dependencies = ['FunctionNode', 'OperatorNode', 'SymbolNode'];\nexport var createUtil = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    FunctionNode,\n    OperatorNode,\n    SymbolNode\n  } = _ref;\n  // TODO commutative/associative properties rely on the arguments\n  // e.g. multiply is not commutative for matrices\n  // The properties should be calculated from an argument to simplify, or possibly something in math.config\n  // the other option is for typed() to specify a return type so that we can evaluate the type of arguments\n\n  /* So that properties of an operator fit on one line: */\n  var T = true;\n  var F = false;\n  var defaultName = 'defaultF';\n  var defaultContext = {\n    /*      */add: {\n      trivial: T,\n      total: T,\n      commutative: T,\n      associative: T\n    },\n    /**/unaryPlus: {\n      trivial: T,\n      total: T,\n      commutative: T,\n      associative: T\n    },\n    /* */subtract: {\n      trivial: F,\n      total: T,\n      commutative: F,\n      associative: F\n    },\n    /* */multiply: {\n      trivial: T,\n      total: T,\n      commutative: T,\n      associative: T\n    },\n    /*   */divide: {\n      trivial: F,\n      total: T,\n      commutative: F,\n      associative: F\n    },\n    /*    */paren: {\n      trivial: T,\n      total: T,\n      commutative: T,\n      associative: F\n    },\n    /* */defaultF: {\n      trivial: F,\n      total: T,\n      commutative: F,\n      associative: F\n    }\n  };\n  var realContext = {\n    divide: {\n      total: F\n    },\n    log: {\n      total: F\n    }\n  };\n  var positiveContext = {\n    subtract: {\n      total: F\n    },\n    abs: {\n      trivial: T\n    },\n    log: {\n      total: T\n    }\n  };\n  function hasProperty(nodeOrName, property) {\n    var context = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultContext;\n    var name = defaultName;\n    if (typeof nodeOrName === 'string') {\n      name = nodeOrName;\n    } else if (isOperatorNode(nodeOrName)) {\n      name = nodeOrName.fn.toString();\n    } else if (isFunctionNode(nodeOrName)) {\n      name = nodeOrName.name;\n    } else if (isParenthesisNode(nodeOrName)) {\n      name = 'paren';\n    }\n    if (hasOwnProperty(context, name)) {\n      var properties = context[name];\n      if (hasOwnProperty(properties, property)) {\n        return properties[property];\n      }\n      if (hasOwnProperty(defaultContext, name)) {\n        return defaultContext[name][property];\n      }\n    }\n    if (hasOwnProperty(context, defaultName)) {\n      var _properties = context[defaultName];\n      if (hasOwnProperty(_properties, property)) {\n        return _properties[property];\n      }\n      return defaultContext[defaultName][property];\n    }\n    /* name not found in context and context has no global default */\n    /* So use default context. */\n    if (hasOwnProperty(defaultContext, name)) {\n      var _properties2 = defaultContext[name];\n      if (hasOwnProperty(_properties2, property)) {\n        return _properties2[property];\n      }\n    }\n    return defaultContext[defaultName][property];\n  }\n  function isCommutative(node) {\n    var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultContext;\n    return hasProperty(node, 'commutative', context);\n  }\n  function isAssociative(node) {\n    var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultContext;\n    return hasProperty(node, 'associative', context);\n  }\n\n  /**\n   * Merge the given contexts, with primary overriding secondary\n   * wherever they might conflict\n   */\n  function mergeContext(primary, secondary) {\n    var merged = _objectSpread({}, primary);\n    for (var prop in secondary) {\n      if (hasOwnProperty(primary, prop)) {\n        merged[prop] = _objectSpread(_objectSpread({}, secondary[prop]), primary[prop]);\n      } else {\n        merged[prop] = secondary[prop];\n      }\n    }\n    return merged;\n  }\n\n  /**\n   * Flatten all associative operators in an expression tree.\n   * Assumes parentheses have already been removed.\n   */\n  function flatten(node, context) {\n    if (!node.args || node.args.length === 0) {\n      return node;\n    }\n    node.args = allChildren(node, context);\n    for (var i = 0; i < node.args.length; i++) {\n      flatten(node.args[i], context);\n    }\n  }\n\n  /**\n   * Get the children of a node as if it has been flattened.\n   * TODO implement for FunctionNodes\n   */\n  function allChildren(node, context) {\n    var op;\n    var children = [];\n    var _findChildren = function findChildren(node) {\n      for (var i = 0; i < node.args.length; i++) {\n        var child = node.args[i];\n        if (isOperatorNode(child) && op === child.op) {\n          _findChildren(child);\n        } else {\n          children.push(child);\n        }\n      }\n    };\n    if (isAssociative(node, context)) {\n      op = node.op;\n      _findChildren(node);\n      return children;\n    } else {\n      return node.args;\n    }\n  }\n\n  /**\n   *  Unflatten all flattened operators to a right-heavy binary tree.\n   */\n  function unflattenr(node, context) {\n    if (!node.args || node.args.length === 0) {\n      return;\n    }\n    var makeNode = createMakeNodeFunction(node);\n    var l = node.args.length;\n    for (var i = 0; i < l; i++) {\n      unflattenr(node.args[i], context);\n    }\n    if (l > 2 && isAssociative(node, context)) {\n      var curnode = node.args.pop();\n      while (node.args.length > 0) {\n        curnode = makeNode([node.args.pop(), curnode]);\n      }\n      node.args = curnode.args;\n    }\n  }\n\n  /**\n   *  Unflatten all flattened operators to a left-heavy binary tree.\n   */\n  function unflattenl(node, context) {\n    if (!node.args || node.args.length === 0) {\n      return;\n    }\n    var makeNode = createMakeNodeFunction(node);\n    var l = node.args.length;\n    for (var i = 0; i < l; i++) {\n      unflattenl(node.args[i], context);\n    }\n    if (l > 2 && isAssociative(node, context)) {\n      var curnode = node.args.shift();\n      while (node.args.length > 0) {\n        curnode = makeNode([curnode, node.args.shift()]);\n      }\n      node.args = curnode.args;\n    }\n  }\n  function createMakeNodeFunction(node) {\n    if (isOperatorNode(node)) {\n      return function (args) {\n        try {\n          return new OperatorNode(node.op, node.fn, args, node.implicit);\n        } catch (err) {\n          console.error(err);\n          return [];\n        }\n      };\n    } else {\n      return function (args) {\n        return new FunctionNode(new SymbolNode(node.name), args);\n      };\n    }\n  }\n  return {\n    createMakeNodeFunction,\n    hasProperty,\n    isCommutative,\n    isAssociative,\n    mergeContext,\n    flatten,\n    allChildren,\n    unflattenr,\n    unflattenl,\n    defaultContext,\n    realContext,\n    positiveContext\n  };\n});", "map": {"version": 3, "names": ["_defineProperty", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "isFunctionNode", "isOperatorNode", "isParenthesisNode", "factory", "hasOwnProperty", "name", "dependencies", "createUtil", "_ref", "FunctionNode", "OperatorNode", "SymbolNode", "T", "F", "defaultName", "defaultContext", "add", "trivial", "total", "commutative", "associative", "unaryPlus", "subtract", "multiply", "divide", "paren", "defaultF", "realContext", "log", "positiveContext", "abs", "hasProperty", "nodeOrName", "property", "context", "undefined", "fn", "toString", "properties", "_properties", "_properties2", "isCommutative", "node", "isAssociative", "mergeContext", "primary", "secondary", "merged", "prop", "flatten", "args", "allChildren", "i", "op", "children", "_find<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "unflattenr", "makeNode", "createMakeNodeFunction", "l", "curnode", "pop", "unflattenl", "shift", "implicit", "err", "console", "error"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/simplify/util.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { isFunctionNode, isOperatorNode, isParenthesisNode } from '../../../utils/is.js';\nimport { factory } from '../../../utils/factory.js';\nimport { hasOwnProperty } from '../../../utils/object.js';\nvar name = 'simplifyUtil';\nvar dependencies = ['FunctionNode', 'OperatorNode', 'SymbolNode'];\nexport var createUtil = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    FunctionNode,\n    OperatorNode,\n    SymbolNode\n  } = _ref;\n  // TODO commutative/associative properties rely on the arguments\n  // e.g. multiply is not commutative for matrices\n  // The properties should be calculated from an argument to simplify, or possibly something in math.config\n  // the other option is for typed() to specify a return type so that we can evaluate the type of arguments\n\n  /* So that properties of an operator fit on one line: */\n  var T = true;\n  var F = false;\n  var defaultName = 'defaultF';\n  var defaultContext = {\n    /*      */add: {\n      trivial: T,\n      total: T,\n      commutative: T,\n      associative: T\n    },\n    /**/unaryPlus: {\n      trivial: T,\n      total: T,\n      commutative: T,\n      associative: T\n    },\n    /* */subtract: {\n      trivial: F,\n      total: T,\n      commutative: F,\n      associative: F\n    },\n    /* */multiply: {\n      trivial: T,\n      total: T,\n      commutative: T,\n      associative: T\n    },\n    /*   */divide: {\n      trivial: F,\n      total: T,\n      commutative: F,\n      associative: F\n    },\n    /*    */paren: {\n      trivial: T,\n      total: T,\n      commutative: T,\n      associative: F\n    },\n    /* */defaultF: {\n      trivial: F,\n      total: T,\n      commutative: F,\n      associative: F\n    }\n  };\n  var realContext = {\n    divide: {\n      total: F\n    },\n    log: {\n      total: F\n    }\n  };\n  var positiveContext = {\n    subtract: {\n      total: F\n    },\n    abs: {\n      trivial: T\n    },\n    log: {\n      total: T\n    }\n  };\n  function hasProperty(nodeOrName, property) {\n    var context = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultContext;\n    var name = defaultName;\n    if (typeof nodeOrName === 'string') {\n      name = nodeOrName;\n    } else if (isOperatorNode(nodeOrName)) {\n      name = nodeOrName.fn.toString();\n    } else if (isFunctionNode(nodeOrName)) {\n      name = nodeOrName.name;\n    } else if (isParenthesisNode(nodeOrName)) {\n      name = 'paren';\n    }\n    if (hasOwnProperty(context, name)) {\n      var properties = context[name];\n      if (hasOwnProperty(properties, property)) {\n        return properties[property];\n      }\n      if (hasOwnProperty(defaultContext, name)) {\n        return defaultContext[name][property];\n      }\n    }\n    if (hasOwnProperty(context, defaultName)) {\n      var _properties = context[defaultName];\n      if (hasOwnProperty(_properties, property)) {\n        return _properties[property];\n      }\n      return defaultContext[defaultName][property];\n    }\n    /* name not found in context and context has no global default */\n    /* So use default context. */\n    if (hasOwnProperty(defaultContext, name)) {\n      var _properties2 = defaultContext[name];\n      if (hasOwnProperty(_properties2, property)) {\n        return _properties2[property];\n      }\n    }\n    return defaultContext[defaultName][property];\n  }\n  function isCommutative(node) {\n    var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultContext;\n    return hasProperty(node, 'commutative', context);\n  }\n  function isAssociative(node) {\n    var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultContext;\n    return hasProperty(node, 'associative', context);\n  }\n\n  /**\n   * Merge the given contexts, with primary overriding secondary\n   * wherever they might conflict\n   */\n  function mergeContext(primary, secondary) {\n    var merged = _objectSpread({}, primary);\n    for (var prop in secondary) {\n      if (hasOwnProperty(primary, prop)) {\n        merged[prop] = _objectSpread(_objectSpread({}, secondary[prop]), primary[prop]);\n      } else {\n        merged[prop] = secondary[prop];\n      }\n    }\n    return merged;\n  }\n\n  /**\n   * Flatten all associative operators in an expression tree.\n   * Assumes parentheses have already been removed.\n   */\n  function flatten(node, context) {\n    if (!node.args || node.args.length === 0) {\n      return node;\n    }\n    node.args = allChildren(node, context);\n    for (var i = 0; i < node.args.length; i++) {\n      flatten(node.args[i], context);\n    }\n  }\n\n  /**\n   * Get the children of a node as if it has been flattened.\n   * TODO implement for FunctionNodes\n   */\n  function allChildren(node, context) {\n    var op;\n    var children = [];\n    var _findChildren = function findChildren(node) {\n      for (var i = 0; i < node.args.length; i++) {\n        var child = node.args[i];\n        if (isOperatorNode(child) && op === child.op) {\n          _findChildren(child);\n        } else {\n          children.push(child);\n        }\n      }\n    };\n    if (isAssociative(node, context)) {\n      op = node.op;\n      _findChildren(node);\n      return children;\n    } else {\n      return node.args;\n    }\n  }\n\n  /**\n   *  Unflatten all flattened operators to a right-heavy binary tree.\n   */\n  function unflattenr(node, context) {\n    if (!node.args || node.args.length === 0) {\n      return;\n    }\n    var makeNode = createMakeNodeFunction(node);\n    var l = node.args.length;\n    for (var i = 0; i < l; i++) {\n      unflattenr(node.args[i], context);\n    }\n    if (l > 2 && isAssociative(node, context)) {\n      var curnode = node.args.pop();\n      while (node.args.length > 0) {\n        curnode = makeNode([node.args.pop(), curnode]);\n      }\n      node.args = curnode.args;\n    }\n  }\n\n  /**\n   *  Unflatten all flattened operators to a left-heavy binary tree.\n   */\n  function unflattenl(node, context) {\n    if (!node.args || node.args.length === 0) {\n      return;\n    }\n    var makeNode = createMakeNodeFunction(node);\n    var l = node.args.length;\n    for (var i = 0; i < l; i++) {\n      unflattenl(node.args[i], context);\n    }\n    if (l > 2 && isAssociative(node, context)) {\n      var curnode = node.args.shift();\n      while (node.args.length > 0) {\n        curnode = makeNode([curnode, node.args.shift()]);\n      }\n      node.args = curnode.args;\n    }\n  }\n  function createMakeNodeFunction(node) {\n    if (isOperatorNode(node)) {\n      return function (args) {\n        try {\n          return new OperatorNode(node.op, node.fn, args, node.implicit);\n        } catch (err) {\n          console.error(err);\n          return [];\n        }\n      };\n    } else {\n      return function (args) {\n        return new FunctionNode(new SymbolNode(node.name), args);\n      };\n    }\n  }\n  return {\n    createMakeNodeFunction,\n    hasProperty,\n    isCommutative,\n    isAssociative,\n    mergeContext,\n    flatten,\n    allChildren,\n    unflattenr,\n    unflattenl,\n    defaultContext,\n    realContext,\n    positiveContext\n  };\n});"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uCAAuC;AACnE,SAASC,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEH,eAAe,CAACE,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACa,yBAAyB,GAAGb,MAAM,CAACc,gBAAgB,CAACjB,CAAC,EAAEG,MAAM,CAACa,yBAAyB,CAACd,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACe,cAAc,CAAClB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASmB,cAAc,EAAEC,cAAc,EAAEC,iBAAiB,QAAQ,sBAAsB;AACxF,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,cAAc,QAAQ,0BAA0B;AACzD,IAAIC,IAAI,GAAG,cAAc;AACzB,IAAIC,YAAY,GAAG,CAAC,cAAc,EAAE,cAAc,EAAE,YAAY,CAAC;AACjE,OAAO,IAAIC,UAAU,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACzE,IAAI;IACFC,YAAY;IACZC,YAAY;IACZC;EACF,CAAC,GAAGH,IAAI;EACR;EACA;EACA;EACA;;EAEA;EACA,IAAII,CAAC,GAAG,IAAI;EACZ,IAAIC,CAAC,GAAG,KAAK;EACb,IAAIC,WAAW,GAAG,UAAU;EAC5B,IAAIC,cAAc,GAAG;IACnB,UAAUC,GAAG,EAAE;MACbC,OAAO,EAAEL,CAAC;MACVM,KAAK,EAAEN,CAAC;MACRO,WAAW,EAAEP,CAAC;MACdQ,WAAW,EAAER;IACf,CAAC;IACD,IAAIS,SAAS,EAAE;MACbJ,OAAO,EAAEL,CAAC;MACVM,KAAK,EAAEN,CAAC;MACRO,WAAW,EAAEP,CAAC;MACdQ,WAAW,EAAER;IACf,CAAC;IACD,KAAKU,QAAQ,EAAE;MACbL,OAAO,EAAEJ,CAAC;MACVK,KAAK,EAAEN,CAAC;MACRO,WAAW,EAAEN,CAAC;MACdO,WAAW,EAAEP;IACf,CAAC;IACD,KAAKU,QAAQ,EAAE;MACbN,OAAO,EAAEL,CAAC;MACVM,KAAK,EAAEN,CAAC;MACRO,WAAW,EAAEP,CAAC;MACdQ,WAAW,EAAER;IACf,CAAC;IACD,OAAOY,MAAM,EAAE;MACbP,OAAO,EAAEJ,CAAC;MACVK,KAAK,EAAEN,CAAC;MACRO,WAAW,EAAEN,CAAC;MACdO,WAAW,EAAEP;IACf,CAAC;IACD,QAAQY,KAAK,EAAE;MACbR,OAAO,EAAEL,CAAC;MACVM,KAAK,EAAEN,CAAC;MACRO,WAAW,EAAEP,CAAC;MACdQ,WAAW,EAAEP;IACf,CAAC;IACD,KAAKa,QAAQ,EAAE;MACbT,OAAO,EAAEJ,CAAC;MACVK,KAAK,EAAEN,CAAC;MACRO,WAAW,EAAEN,CAAC;MACdO,WAAW,EAAEP;IACf;EACF,CAAC;EACD,IAAIc,WAAW,GAAG;IAChBH,MAAM,EAAE;MACNN,KAAK,EAAEL;IACT,CAAC;IACDe,GAAG,EAAE;MACHV,KAAK,EAAEL;IACT;EACF,CAAC;EACD,IAAIgB,eAAe,GAAG;IACpBP,QAAQ,EAAE;MACRJ,KAAK,EAAEL;IACT,CAAC;IACDiB,GAAG,EAAE;MACHb,OAAO,EAAEL;IACX,CAAC;IACDgB,GAAG,EAAE;MACHV,KAAK,EAAEN;IACT;EACF,CAAC;EACD,SAASmB,WAAWA,CAACC,UAAU,EAAEC,QAAQ,EAAE;IACzC,IAAIC,OAAO,GAAGxC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKyC,SAAS,GAAGzC,SAAS,CAAC,CAAC,CAAC,GAAGqB,cAAc;IAChG,IAAIV,IAAI,GAAGS,WAAW;IACtB,IAAI,OAAOkB,UAAU,KAAK,QAAQ,EAAE;MAClC3B,IAAI,GAAG2B,UAAU;IACnB,CAAC,MAAM,IAAI/B,cAAc,CAAC+B,UAAU,CAAC,EAAE;MACrC3B,IAAI,GAAG2B,UAAU,CAACI,EAAE,CAACC,QAAQ,CAAC,CAAC;IACjC,CAAC,MAAM,IAAIrC,cAAc,CAACgC,UAAU,CAAC,EAAE;MACrC3B,IAAI,GAAG2B,UAAU,CAAC3B,IAAI;IACxB,CAAC,MAAM,IAAIH,iBAAiB,CAAC8B,UAAU,CAAC,EAAE;MACxC3B,IAAI,GAAG,OAAO;IAChB;IACA,IAAID,cAAc,CAAC8B,OAAO,EAAE7B,IAAI,CAAC,EAAE;MACjC,IAAIiC,UAAU,GAAGJ,OAAO,CAAC7B,IAAI,CAAC;MAC9B,IAAID,cAAc,CAACkC,UAAU,EAAEL,QAAQ,CAAC,EAAE;QACxC,OAAOK,UAAU,CAACL,QAAQ,CAAC;MAC7B;MACA,IAAI7B,cAAc,CAACW,cAAc,EAAEV,IAAI,CAAC,EAAE;QACxC,OAAOU,cAAc,CAACV,IAAI,CAAC,CAAC4B,QAAQ,CAAC;MACvC;IACF;IACA,IAAI7B,cAAc,CAAC8B,OAAO,EAAEpB,WAAW,CAAC,EAAE;MACxC,IAAIyB,WAAW,GAAGL,OAAO,CAACpB,WAAW,CAAC;MACtC,IAAIV,cAAc,CAACmC,WAAW,EAAEN,QAAQ,CAAC,EAAE;QACzC,OAAOM,WAAW,CAACN,QAAQ,CAAC;MAC9B;MACA,OAAOlB,cAAc,CAACD,WAAW,CAAC,CAACmB,QAAQ,CAAC;IAC9C;IACA;IACA;IACA,IAAI7B,cAAc,CAACW,cAAc,EAAEV,IAAI,CAAC,EAAE;MACxC,IAAImC,YAAY,GAAGzB,cAAc,CAACV,IAAI,CAAC;MACvC,IAAID,cAAc,CAACoC,YAAY,EAAEP,QAAQ,CAAC,EAAE;QAC1C,OAAOO,YAAY,CAACP,QAAQ,CAAC;MAC/B;IACF;IACA,OAAOlB,cAAc,CAACD,WAAW,CAAC,CAACmB,QAAQ,CAAC;EAC9C;EACA,SAASQ,aAAaA,CAACC,IAAI,EAAE;IAC3B,IAAIR,OAAO,GAAGxC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKyC,SAAS,GAAGzC,SAAS,CAAC,CAAC,CAAC,GAAGqB,cAAc;IAChG,OAAOgB,WAAW,CAACW,IAAI,EAAE,aAAa,EAAER,OAAO,CAAC;EAClD;EACA,SAASS,aAAaA,CAACD,IAAI,EAAE;IAC3B,IAAIR,OAAO,GAAGxC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKyC,SAAS,GAAGzC,SAAS,CAAC,CAAC,CAAC,GAAGqB,cAAc;IAChG,OAAOgB,WAAW,CAACW,IAAI,EAAE,aAAa,EAAER,OAAO,CAAC;EAClD;;EAEA;AACF;AACA;AACA;EACE,SAASU,YAAYA,CAACC,OAAO,EAAEC,SAAS,EAAE;IACxC,IAAIC,MAAM,GAAGtD,aAAa,CAAC,CAAC,CAAC,EAAEoD,OAAO,CAAC;IACvC,KAAK,IAAIG,IAAI,IAAIF,SAAS,EAAE;MAC1B,IAAI1C,cAAc,CAACyC,OAAO,EAAEG,IAAI,CAAC,EAAE;QACjCD,MAAM,CAACC,IAAI,CAAC,GAAGvD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqD,SAAS,CAACE,IAAI,CAAC,CAAC,EAAEH,OAAO,CAACG,IAAI,CAAC,CAAC;MACjF,CAAC,MAAM;QACLD,MAAM,CAACC,IAAI,CAAC,GAAGF,SAAS,CAACE,IAAI,CAAC;MAChC;IACF;IACA,OAAOD,MAAM;EACf;;EAEA;AACF;AACA;AACA;EACE,SAASE,OAAOA,CAACP,IAAI,EAAER,OAAO,EAAE;IAC9B,IAAI,CAACQ,IAAI,CAACQ,IAAI,IAAIR,IAAI,CAACQ,IAAI,CAACvD,MAAM,KAAK,CAAC,EAAE;MACxC,OAAO+C,IAAI;IACb;IACAA,IAAI,CAACQ,IAAI,GAAGC,WAAW,CAACT,IAAI,EAAER,OAAO,CAAC;IACtC,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,IAAI,CAACQ,IAAI,CAACvD,MAAM,EAAEyD,CAAC,EAAE,EAAE;MACzCH,OAAO,CAACP,IAAI,CAACQ,IAAI,CAACE,CAAC,CAAC,EAAElB,OAAO,CAAC;IAChC;EACF;;EAEA;AACF;AACA;AACA;EACE,SAASiB,WAAWA,CAACT,IAAI,EAAER,OAAO,EAAE;IAClC,IAAImB,EAAE;IACN,IAAIC,QAAQ,GAAG,EAAE;IACjB,IAAIC,aAAa,GAAG,SAASC,YAAYA,CAACd,IAAI,EAAE;MAC9C,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,IAAI,CAACQ,IAAI,CAACvD,MAAM,EAAEyD,CAAC,EAAE,EAAE;QACzC,IAAIK,KAAK,GAAGf,IAAI,CAACQ,IAAI,CAACE,CAAC,CAAC;QACxB,IAAInD,cAAc,CAACwD,KAAK,CAAC,IAAIJ,EAAE,KAAKI,KAAK,CAACJ,EAAE,EAAE;UAC5CE,aAAa,CAACE,KAAK,CAAC;QACtB,CAAC,MAAM;UACLH,QAAQ,CAAC/D,IAAI,CAACkE,KAAK,CAAC;QACtB;MACF;IACF,CAAC;IACD,IAAId,aAAa,CAACD,IAAI,EAAER,OAAO,CAAC,EAAE;MAChCmB,EAAE,GAAGX,IAAI,CAACW,EAAE;MACZE,aAAa,CAACb,IAAI,CAAC;MACnB,OAAOY,QAAQ;IACjB,CAAC,MAAM;MACL,OAAOZ,IAAI,CAACQ,IAAI;IAClB;EACF;;EAEA;AACF;AACA;EACE,SAASQ,UAAUA,CAAChB,IAAI,EAAER,OAAO,EAAE;IACjC,IAAI,CAACQ,IAAI,CAACQ,IAAI,IAAIR,IAAI,CAACQ,IAAI,CAACvD,MAAM,KAAK,CAAC,EAAE;MACxC;IACF;IACA,IAAIgE,QAAQ,GAAGC,sBAAsB,CAAClB,IAAI,CAAC;IAC3C,IAAImB,CAAC,GAAGnB,IAAI,CAACQ,IAAI,CAACvD,MAAM;IACxB,KAAK,IAAIyD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,CAAC,EAAET,CAAC,EAAE,EAAE;MAC1BM,UAAU,CAAChB,IAAI,CAACQ,IAAI,CAACE,CAAC,CAAC,EAAElB,OAAO,CAAC;IACnC;IACA,IAAI2B,CAAC,GAAG,CAAC,IAAIlB,aAAa,CAACD,IAAI,EAAER,OAAO,CAAC,EAAE;MACzC,IAAI4B,OAAO,GAAGpB,IAAI,CAACQ,IAAI,CAACa,GAAG,CAAC,CAAC;MAC7B,OAAOrB,IAAI,CAACQ,IAAI,CAACvD,MAAM,GAAG,CAAC,EAAE;QAC3BmE,OAAO,GAAGH,QAAQ,CAAC,CAACjB,IAAI,CAACQ,IAAI,CAACa,GAAG,CAAC,CAAC,EAAED,OAAO,CAAC,CAAC;MAChD;MACApB,IAAI,CAACQ,IAAI,GAAGY,OAAO,CAACZ,IAAI;IAC1B;EACF;;EAEA;AACF;AACA;EACE,SAASc,UAAUA,CAACtB,IAAI,EAAER,OAAO,EAAE;IACjC,IAAI,CAACQ,IAAI,CAACQ,IAAI,IAAIR,IAAI,CAACQ,IAAI,CAACvD,MAAM,KAAK,CAAC,EAAE;MACxC;IACF;IACA,IAAIgE,QAAQ,GAAGC,sBAAsB,CAAClB,IAAI,CAAC;IAC3C,IAAImB,CAAC,GAAGnB,IAAI,CAACQ,IAAI,CAACvD,MAAM;IACxB,KAAK,IAAIyD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,CAAC,EAAET,CAAC,EAAE,EAAE;MAC1BY,UAAU,CAACtB,IAAI,CAACQ,IAAI,CAACE,CAAC,CAAC,EAAElB,OAAO,CAAC;IACnC;IACA,IAAI2B,CAAC,GAAG,CAAC,IAAIlB,aAAa,CAACD,IAAI,EAAER,OAAO,CAAC,EAAE;MACzC,IAAI4B,OAAO,GAAGpB,IAAI,CAACQ,IAAI,CAACe,KAAK,CAAC,CAAC;MAC/B,OAAOvB,IAAI,CAACQ,IAAI,CAACvD,MAAM,GAAG,CAAC,EAAE;QAC3BmE,OAAO,GAAGH,QAAQ,CAAC,CAACG,OAAO,EAAEpB,IAAI,CAACQ,IAAI,CAACe,KAAK,CAAC,CAAC,CAAC,CAAC;MAClD;MACAvB,IAAI,CAACQ,IAAI,GAAGY,OAAO,CAACZ,IAAI;IAC1B;EACF;EACA,SAASU,sBAAsBA,CAAClB,IAAI,EAAE;IACpC,IAAIzC,cAAc,CAACyC,IAAI,CAAC,EAAE;MACxB,OAAO,UAAUQ,IAAI,EAAE;QACrB,IAAI;UACF,OAAO,IAAIxC,YAAY,CAACgC,IAAI,CAACW,EAAE,EAAEX,IAAI,CAACN,EAAE,EAAEc,IAAI,EAAER,IAAI,CAACwB,QAAQ,CAAC;QAChE,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC;UAClB,OAAO,EAAE;QACX;MACF,CAAC;IACH,CAAC,MAAM;MACL,OAAO,UAAUjB,IAAI,EAAE;QACrB,OAAO,IAAIzC,YAAY,CAAC,IAAIE,UAAU,CAAC+B,IAAI,CAACrC,IAAI,CAAC,EAAE6C,IAAI,CAAC;MAC1D,CAAC;IACH;EACF;EACA,OAAO;IACLU,sBAAsB;IACtB7B,WAAW;IACXU,aAAa;IACbE,aAAa;IACbC,YAAY;IACZK,OAAO;IACPE,WAAW;IACXO,UAAU;IACVM,UAAU;IACVjD,cAAc;IACdY,WAAW;IACXE;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}