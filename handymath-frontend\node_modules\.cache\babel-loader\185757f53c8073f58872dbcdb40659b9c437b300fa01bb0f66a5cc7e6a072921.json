{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\TestLoginPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestLoginPage = () => {\n  _s();\n  const [username, setUsername] = useState('lamiae');\n  const [password, setPassword] = useState('password123');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleLogin = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const success = await login(username, password);\n      if (success) {\n        navigate('/courses');\n      } else {\n        setError('Identifiants incorrects');\n      }\n    } catch (err) {\n      setError('Erreur de connexion');\n      console.error('Erreur de connexion:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAutoLogin = () => {\n    // Injecter les tokens directement\n    const tokens = {\n      authToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************.QF3vk_YT1nMibyx8ZfiKTv8S76tmyE4xadE4UOLnSjU',\n      refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************.SEycvSvgn_WyAEhtKmA4h8gpJV_rj-Dyj0viNgdrHN0',\n      user: '{\"id\": 1, \"username\": \"lamiae\", \"email\": \"<EMAIL>\", \"nom\": \"\", \"prenom\": \"\", \"role\": \"admin\"}'\n    };\n    localStorage.setItem('authToken', tokens.authToken);\n    localStorage.setItem('refreshToken', tokens.refreshToken);\n    localStorage.setItem('user', tokens.user);\n\n    // Recharger la page\n    window.location.reload();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white\",\n          children: \"Test de Connexion\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-center text-sm text-gray-600 dark:text-gray-400\",\n          children: \"Page de test pour la connexion\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"mt-8 space-y-6\",\n        onSubmit: handleLogin,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-md shadow-sm -space-y-px\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"username\",\n              className: \"sr-only\",\n              children: \"Nom d'utilisateur\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"username\",\n              name: \"username\",\n              type: \"text\",\n              required: true,\n              className: \"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n              placeholder: \"Nom d'utilisateur\",\n              value: username,\n              onChange: e => setUsername(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"sr-only\",\n              children: \"Mot de passe\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"password\",\n              name: \"password\",\n              type: \"password\",\n              required: true,\n              className: \"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\",\n              placeholder: \"Mot de passe\",\n              value: password,\n              onChange: e => setPassword(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-600 text-sm text-center\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\",\n            children: loading ? 'Connexion...' : 'Se connecter'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleAutoLogin,\n            className: \"group relative w-full flex justify-center py-2 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: \"Connexion automatique (Test)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600 dark:text-gray-400\",\n          children: \"Identifiants de test:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500 dark:text-gray-500\",\n          children: [\"Username: lamiae\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 29\n          }, this), \"Password: password123\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(TestLoginPage, \"ETMBRN5+GVlxeHe3D9LNH9P9Eyk=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = TestLoginPage;\nexport default TestLoginPage;\nvar _c;\n$RefreshReg$(_c, \"TestLoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "useNavigate", "jsxDEV", "_jsxDEV", "TestLoginPage", "_s", "username", "setUsername", "password", "setPassword", "loading", "setLoading", "error", "setError", "login", "navigate", "handleLogin", "e", "preventDefault", "success", "err", "console", "handleAutoLogin", "tokens", "authToken", "refreshToken", "user", "localStorage", "setItem", "window", "location", "reload", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "id", "name", "type", "required", "placeholder", "value", "onChange", "target", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/pages/TestLoginPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\n\nconst TestLoginPage: React.FC = () => {\n  const [username, setUsername] = useState('lamiae');\n  const [password, setPassword] = useState('password123');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  \n  const { login } = useAuth();\n  const navigate = useNavigate();\n\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const success = await login(username, password);\n      if (success) {\n        navigate('/courses');\n      } else {\n        setError('Identifiants incorrects');\n      }\n    } catch (err) {\n      setError('Erreur de connexion');\n      console.error('Erreur de connexion:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAutoLogin = () => {\n    // Injecter les tokens directement\n    const tokens = {\n      authToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************.QF3vk_YT1nMibyx8ZfiKTv8S76tmyE4xadE4UOLnSjU',\n      refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************.SEycvSvgn_WyAEhtKmA4h8gpJV_rj-Dyj0viNgdrHN0',\n      user: '{\"id\": 1, \"username\": \"lamiae\", \"email\": \"<EMAIL>\", \"nom\": \"\", \"prenom\": \"\", \"role\": \"admin\"}'\n    };\n\n    localStorage.setItem('authToken', tokens.authToken);\n    localStorage.setItem('refreshToken', tokens.refreshToken);\n    localStorage.setItem('user', tokens.user);\n    \n    // Recharger la page\n    window.location.reload();\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white\">\n            Test de Connexion\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600 dark:text-gray-400\">\n            Page de test pour la connexion\n          </p>\n        </div>\n        \n        <form className=\"mt-8 space-y-6\" onSubmit={handleLogin}>\n          <div className=\"rounded-md shadow-sm -space-y-px\">\n            <div>\n              <label htmlFor=\"username\" className=\"sr-only\">\n                Nom d'utilisateur\n              </label>\n              <input\n                id=\"username\"\n                name=\"username\"\n                type=\"text\"\n                required\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Nom d'utilisateur\"\n                value={username}\n                onChange={(e) => setUsername(e.target.value)}\n              />\n            </div>\n            <div>\n              <label htmlFor=\"password\" className=\"sr-only\">\n                Mot de passe\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                required\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Mot de passe\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n              />\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"text-red-600 text-sm text-center\">\n              {error}\n            </div>\n          )}\n\n          <div className=\"space-y-4\">\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\n            >\n              {loading ? 'Connexion...' : 'Se connecter'}\n            </button>\n            \n            <button\n              type=\"button\"\n              onClick={handleAutoLogin}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              Connexion automatique (Test)\n            </button>\n          </div>\n        </form>\n        \n        <div className=\"mt-6 text-center\">\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n            Identifiants de test:\n          </p>\n          <p className=\"text-xs text-gray-500 dark:text-gray-500\">\n            Username: lamiae<br />\n            Password: password123\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TestLoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,QAAQ,CAAC;EAClD,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,aAAa,CAAC;EACvD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM;IAAEe;EAAM,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC3B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,MAAMe,WAAW,GAAG,MAAOC,CAAkB,IAAK;IAChDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBP,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMM,OAAO,GAAG,MAAML,KAAK,CAACR,QAAQ,EAAEE,QAAQ,CAAC;MAC/C,IAAIW,OAAO,EAAE;QACXJ,QAAQ,CAAC,UAAU,CAAC;MACtB,CAAC,MAAM;QACLF,QAAQ,CAAC,yBAAyB,CAAC;MACrC;IACF,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZP,QAAQ,CAAC,qBAAqB,CAAC;MAC/BQ,OAAO,CAACT,KAAK,CAAC,sBAAsB,EAAEQ,GAAG,CAAC;IAC5C,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMW,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA,MAAMC,MAAM,GAAG;MACbC,SAAS,EAAE,qUAAqU;MAChVC,YAAY,EAAE,sUAAsU;MACpVC,IAAI,EAAE;IACR,CAAC;IAEDC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEL,MAAM,CAACC,SAAS,CAAC;IACnDG,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEL,MAAM,CAACE,YAAY,CAAC;IACzDE,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEL,MAAM,CAACG,IAAI,CAAC;;IAEzC;IACAG,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;EAED,oBACE5B,OAAA;IAAK6B,SAAS,EAAC,sGAAsG;IAAAC,QAAA,eACnH9B,OAAA;MAAK6B,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxC9B,OAAA;QAAA8B,QAAA,gBACE9B,OAAA;UAAI6B,SAAS,EAAC,wEAAwE;UAAAC,QAAA,EAAC;QAEvF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlC,OAAA;UAAG6B,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENlC,OAAA;QAAM6B,SAAS,EAAC,gBAAgB;QAACM,QAAQ,EAAEtB,WAAY;QAAAiB,QAAA,gBACrD9B,OAAA;UAAK6B,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/C9B,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAOoC,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAE9C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlC,OAAA;cACEqC,EAAE,EAAC,UAAU;cACbC,IAAI,EAAC,UAAU;cACfC,IAAI,EAAC,MAAM;cACXC,QAAQ;cACRX,SAAS,EAAC,wNAAwN;cAClOY,WAAW,EAAC,mBAAmB;cAC/BC,KAAK,EAAEvC,QAAS;cAChBwC,QAAQ,EAAG7B,CAAC,IAAKV,WAAW,CAACU,CAAC,CAAC8B,MAAM,CAACF,KAAK;YAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAOoC,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAE9C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlC,OAAA;cACEqC,EAAE,EAAC,UAAU;cACbC,IAAI,EAAC,UAAU;cACfC,IAAI,EAAC,UAAU;cACfC,QAAQ;cACRX,SAAS,EAAC,wNAAwN;cAClOY,WAAW,EAAC,cAAc;cAC1BC,KAAK,EAAErC,QAAS;cAChBsC,QAAQ,EAAG7B,CAAC,IAAKR,WAAW,CAACQ,CAAC,CAAC8B,MAAM,CAACF,KAAK;YAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELzB,KAAK,iBACJT,OAAA;UAAK6B,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAC9CrB;QAAK;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDlC,OAAA;UAAK6B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9B,OAAA;YACEuC,IAAI,EAAC,QAAQ;YACbM,QAAQ,EAAEtC,OAAQ;YAClBsB,SAAS,EAAC,mPAAmP;YAAAC,QAAA,EAE5PvB,OAAO,GAAG,cAAc,GAAG;UAAc;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eAETlC,OAAA;YACEuC,IAAI,EAAC,QAAQ;YACbO,OAAO,EAAE3B,eAAgB;YACzBU,SAAS,EAAC,2NAA2N;YAAAC,QAAA,EACtO;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEPlC,OAAA;QAAK6B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B9B,OAAA;UAAG6B,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAExD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJlC,OAAA;UAAG6B,SAAS,EAAC,0CAA0C;UAAAC,QAAA,GAAC,kBACtC,eAAA9B,OAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,yBAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChC,EAAA,CAhIID,aAAuB;EAAA,QAMTJ,OAAO,EACRC,WAAW;AAAA;AAAAiD,EAAA,GAPxB9C,aAAuB;AAkI7B,eAAeA,aAAa;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}