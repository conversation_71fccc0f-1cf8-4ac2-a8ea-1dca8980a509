import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import StudentNavbar from '../components/StudentNavbar';
import Pagination from '../components/Pagination';
import api from '../services/api';

interface Course {
  id: number;
  title: string;
  description: string;
  level: string;
  level_display: string;
  thumbnail: string;
  estimated_duration: number;
  is_featured: boolean;
  chapters_count: number;
  lessons_count: number;
  progress_percentage: number;
  is_accessible: boolean;
  is_enrolled: boolean;
  enrollment_date?: string;
  prerequisites: Array<{
    id: number;
    title: string;
    progress: number;
  }>;
  created_at: string;
}

interface Filters {
  level: string;
  featured: boolean;
}

const CoursesPage: React.FC = () => {
  const { user } = useAuth();
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<Filters>({
    level: '',
    featured: false
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(9);
  const [totalItems, setTotalItems] = useState(0);
  
  // Options de pagination pour les cours
  const coursePaginationOptions = [6, 9, 12, 18];

  useEffect(() => {
    if (user) {
      fetchCourses();
    }
  }, [user, filters, currentPage, itemsPerPage]);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (filters.level) params.append('level', filters.level);
      if (filters.featured) params.append('featured', 'true');

      // Pagination parameters
      params.append('page', currentPage.toString());
      params.append('page_size', itemsPerPage.toString());

      const response = await api.get(`/courses/?${params.toString()}`);
      if (response.data) {
        setCourses(response.data.courses || response.data.results || []);
        // Utiliser les nouvelles données de pagination du backend
        if (response.data.pagination) {
          setTotalItems(response.data.pagination.total_count);
        } else {
          setTotalItems(response.data.count || response.data.total || 0);
        }
      }
    } catch (error: any) {
      console.error('Erreur lors de la récupération des cours:', error);
      // Fallback avec des données de test si l'API n'est pas disponible
      setCourses([
        {
          id: 1,
          title: 'Algèbre de base',
          description: 'Apprenez les concepts fondamentaux de l\'algèbre avec des exercices pratiques et des exemples concrets.',
          level: 'beginner',
          level_display: 'Débutant',
          thumbnail: '📚',
          estimated_duration: 180,
          is_featured: true,
          chapters_count: 4,
          lessons_count: 12,
          progress_percentage: 45,
          is_accessible: true,
          is_enrolled: true,
          enrollment_date: '2024-01-15T10:30:00Z',
          prerequisites: [],
          created_at: '2024-01-15T10:30:00Z'
        },
        {
          id: 2,
          title: 'Géométrie euclidienne',
          description: 'Découvrez les principes de la géométrie euclidienne et ses applications pratiques.',
          level: 'intermediate',
          level_display: 'Intermédiaire',
          thumbnail: '📐',
          estimated_duration: 240,
          is_featured: false,
          chapters_count: 6,
          lessons_count: 18,
          progress_percentage: 0,
          is_accessible: true,
          is_enrolled: false,
          prerequisites: [
            { id: 1, title: 'Algèbre de base', progress: 45 }
          ],
          created_at: '2024-01-16T14:20:00Z'
        },
        {
          id: 3,
          title: 'Calcul différentiel',
          description: 'Maîtrisez les concepts du calcul différentiel et intégral.',
          level: 'advanced',
          level_display: 'Avancé',
          thumbnail: '∫',
          estimated_duration: 360,
          is_featured: true,
          chapters_count: 8,
          lessons_count: 24,
          progress_percentage: 0,
          is_accessible: false,
          is_enrolled: false,
          prerequisites: [
            { id: 1, title: 'Algèbre de base', progress: 45 }
          ],
          created_at: '2024-01-17T09:45:00Z'
        }
      ]);
      setTotalItems(3); // Nombre total de cours de test
    } finally {
      setLoading(false);
    }
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const handleEnroll = async (courseId: number, courseTitle: string) => {
    try {
      setCourses(courses.map(course => 
        course.id === courseId 
          ? { ...course, is_enrolled: true, enrollment_date: new Date().toISOString() }
          : course
      ));
      alert(`Inscription réussie au cours "${courseTitle}" !`);
    } catch (error) {
      console.error('Erreur lors de l\'inscription:', error);
      alert('Erreur lors de l\'inscription');
    }
  };

  const clearFilters = () => {
    setFilters({
      level: '',
      featured: false
    });
    setCurrentPage(1); // Reset to first page when clearing filters
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;
    }
    return `${mins}min`;
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'intermediate':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'advanced':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Cours de Mathématiques</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            Vous devez être connecté pour accéder aux cours.
          </p>
          <a
            href="/login"
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Se connecter
          </a>
        </div>
      </div>
    );
  }

  return (
    <>
    <StudentNavbar />
    <div className="container mx-auto px-4 py-8">
      {/* En-tête */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Cours de Mathématiques
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Découvrez nos cours structurés pour progresser à votre rythme
        </p>
      </div>

      {/* Filtres */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
        <div className="flex flex-wrap gap-4 items-center">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Niveau
            </label>
            <select
              value={filters.level}
              onChange={(e) => setFilters(prev => ({ ...prev, level: e.target.value }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">Tous les niveaux</option>
              <option value="beginner">Débutant</option>
              <option value="intermediate">Intermédiaire</option>
              <option value="advanced">Avancé</option>
            </select>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="featured"
              checked={filters.featured}
              onChange={(e) => setFilters(prev => ({ ...prev, featured: e.target.checked }))}
              className="mr-2"
            />
            <label htmlFor="featured" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Cours en vedette uniquement
            </label>
          </div>

          <button
            onClick={clearFilters}
            className="px-4 py-2 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors"
          >
            Effacer les filtres
          </button>
        </div>
      </div>

      {/* Loading */}
      {loading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Chargement des cours...</p>
        </div>
      )}

      {/* Cours */}
      {!loading && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {courses.map((course, index) => (
              <motion.div
                key={course.id}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                {/* Thumbnail */}
                <div className="h-32 bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center">
                  <span className="text-4xl text-white">{course.thumbnail}</span>
                </div>

                <div className="p-6">
                  {/* En-tête du cours */}
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {course.title}
                    </h3>
                    {course.is_featured && (
                      <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 rounded-full">
                        Vedette
                      </span>
                    )}
                  </div>

                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
                    {course.description}
                  </p>

                  {/* Métadonnées */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500 dark:text-gray-400">Niveau:</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`}>
                        {course.level_display}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500 dark:text-gray-400">Durée:</span>
                      <span className="text-gray-900 dark:text-white">{formatDuration(course.estimated_duration)}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500 dark:text-gray-400">Leçons:</span>
                      <span className="text-gray-900 dark:text-white">{course.lessons_count}</span>
                    </div>
                  </div>

                  {/* Progression */}
                  {course.is_enrolled && (
                    <div className="mb-4">
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span className="text-gray-500 dark:text-gray-400">Progression</span>
                        <span className="text-gray-900 dark:text-white">{course.progress_percentage}%</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${course.progress_percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex gap-2">
                    {course.is_enrolled ? (
                      <button className="flex-1 bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                        Continuer
                      </button>
                    ) : course.is_accessible ? (
                      <button
                        onClick={() => handleEnroll(course.id, course.title)}
                        className="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
                      >
                        S'inscrire
                      </button>
                    ) : (
                      <button
                        disabled
                        className="flex-1 bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 font-medium py-2 px-4 rounded-md cursor-not-allowed"
                      >
                        Prérequis manquants
                      </button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Pagination */}
          {totalItems > 0 && (
            <div className="mt-8">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                showItemsPerPage={true}
                showInfo={true}
                itemsPerPageOptions={coursePaginationOptions}
              />
            </div>
          )}
        </>
      )}
    </div>
    </>
  );
};

export default CoursesPage;
