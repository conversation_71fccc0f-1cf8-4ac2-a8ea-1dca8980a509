import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import StudentNavbar from '../components/StudentNavbar';
import Pagination from '../components/Pagination';
import api from '../services/api';

interface Course {
  id: number;
  title: string;
  description: string;
  level: string;
  level_display: string;
  estimated_duration: number;
  is_featured: boolean;
  is_enrolled: boolean;
  progress_percentage?: number;
  chapters_count: number;
  lessons_count: number;
  thumbnail: string;
  created_at: string;
}

interface Filters {
  level: string;
  featured: boolean;
  search: string;
  sortBy: 'title' | 'level' | 'estimated_duration' | 'created_at';
  sortOrder: 'asc' | 'desc';
}

const CoursesPage: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  // State management
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<Filters>({
    level: '',
    featured: false,
    search: '',
    sortBy: 'title',
    sortOrder: 'asc'
  });
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(9);
  const [totalItems, setTotalItems] = useState(0);
  const [searchValue, setSearchValue] = useState('');
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // Pagination options
  const coursePaginationOptions = [6, 9, 12, 18];

  // Fetch courses from API
  const fetchCourses = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (filters.level) params.append('level', filters.level);
      if (filters.featured) params.append('featured', 'true');
      if (filters.search) params.append('search', filters.search);
      params.append('ordering', filters.sortOrder === 'desc' ? `-${filters.sortBy}` : filters.sortBy);
      params.append('page', currentPage.toString());
      params.append('page_size', itemsPerPage.toString());

      const response = await api.get(`/courses/?${params.toString()}`);
      // L'API retourne une structure avec 'courses' et 'pagination'
      setCourses(response.data.courses || response.data.results || []);
      setTotalItems(response.data.pagination?.total_count || response.data.count || 0);
    } catch (error) {
      console.error('Error fetching courses:', error);
      setCourses([]);
      setTotalItems(0);
    } finally {
      setLoading(false);
    }
  };

  // Effects
  useEffect(() => {
    if (user) {
      fetchCourses();
    }
  }, [user, filters, currentPage, itemsPerPage]);

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  // Search handler with debounce
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    const timeout = setTimeout(() => {
      setFilters(prev => ({ ...prev, search: value }));
      setCurrentPage(1);
    }, 300);
    setSearchTimeout(timeout);
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      level: '',
      featured: false,
      search: '',
      sortBy: 'title',
      sortOrder: 'asc'
    });
    setSearchValue('');
    setCurrentPage(1);
  };

  // Enrollment handler
  const handleEnroll = async (courseId: number, courseTitle: string) => {
    try {
      await api.post(`/courses/${courseId}/enroll/`);
      // Refresh courses after enrollment
      fetchCourses();
      alert(`Vous êtes maintenant inscrit au cours: ${courseTitle}`);
    } catch (error) {
      console.error('Error enrolling in course:', error);
      alert('Erreur lors de l\'inscription au cours');
    }
  };

  // Get status badge
  const getStatusBadge = (course: Course) => {
    if (course.is_enrolled) {
      return <span className="px-2 py-1 text-xs font-semibold bg-green-100 text-green-800 rounded-full">Inscrit</span>;
    }
    if (course.is_featured) {
      return <span className="px-2 py-1 text-xs font-semibold bg-yellow-100 text-yellow-800 rounded-full">⭐ Vedette</span>;
    }
    return <span className="px-2 py-1 text-xs font-semibold bg-blue-100 text-blue-800 rounded-full">Disponible</span>;
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Accès non autorisé
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Veuillez vous connecter pour accéder aux cours.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <StudentNavbar />
      
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Découvrez nos <span className="text-blue-600">Cours</span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Explorez notre collection de cours de mathématiques conçus pour vous aider à maîtriser les concepts essentiels
          </p>
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6 mb-8"
        >
          {/* Search Bar */}
          <div className="relative mb-6">
            <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">🔍</span>
            <input
              type="text"
              placeholder="Rechercher un cours..."
              value={searchValue}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="w-full pl-12 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
            />
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
            {/* Level Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                📚 Niveau
              </label>
              <select
                value={filters.level}
                onChange={(e) => setFilters(prev => ({ ...prev, level: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Tous les niveaux</option>
                <option value="beginner">Débutant</option>
                <option value="intermediate">Intermédiaire</option>
                <option value="advanced">Avancé</option>
              </select>
            </div>

            {/* Sort Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                🔄 Trier par
              </label>
              <select
                value={filters.sortBy}
                onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="title">Titre</option>
                <option value="level">Niveau</option>
                <option value="estimated_duration">Durée</option>
                <option value="created_at">Date de création</option>
              </select>
            </div>

            {/* Sort Order */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                📊 Ordre
              </label>
              <select
                value={filters.sortOrder}
                onChange={(e) => setFilters(prev => ({ ...prev, sortOrder: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="asc">Croissant</option>
                <option value="desc">Décroissant</option>
              </select>
            </div>

            {/* Featured and Clear */}
            <div className="flex flex-col space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.featured}
                  onChange={(e) => setFilters(prev => ({ ...prev, featured: e.target.checked }))}
                  className="mr-2 rounded text-blue-600 focus:ring-blue-500"
                />
                <span className="text-yellow-500 mr-1">⭐</span>
                <span className="text-sm text-gray-700 dark:text-gray-300">Vedettes</span>
              </label>
              <button
                onClick={clearFilters}
                className="px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                Effacer
              </button>
            </div>
          </div>
        </motion.div>

        {/* Statistics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
        >
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-4 text-white">
            <div className="flex items-center">
              <span className="text-2xl mr-3">📚</span>
              <div>
                <p className="text-sm opacity-90">Total des cours</p>
                <p className="text-2xl font-bold">{totalItems}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-4 text-white">
            <div className="flex items-center">
              <span className="text-2xl mr-3">✅</span>
              <div>
                <p className="text-sm opacity-90">Cours inscrits</p>
                <p className="text-2xl font-bold">{courses.filter(c => c.is_enrolled).length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-4 text-white">
            <div className="flex items-center">
              <span className="text-2xl mr-3">📊</span>
              <div>
                <p className="text-sm opacity-90">Progrès moyen</p>
                <p className="text-2xl font-bold">
                  {courses.length > 0
                    ? Math.round(courses.reduce((acc, c) => acc + (c.progress_percentage || 0), 0) / courses.length)
                    : 0}%
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        )}

        {/* Courses Grid */}
        {!loading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"
          >
            {courses.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <p className="text-xl text-gray-600 dark:text-gray-400">
                  Aucun cours trouvé avec les critères sélectionnés.
                </p>
              </div>
            ) : (
              courses.map((course, index) => (
                <motion.div
                  key={course.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                >
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white line-clamp-2">
                        {course.title}
                      </h3>
                      {getStatusBadge(course)}
                    </div>
                    
                    <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                      {course.description}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                      <span>📊 {course.level_display}</span>
                      <span>⏱️ {Math.round(course.estimated_duration / 60)}h</span>
                    </div>
                    
                    {course.is_enrolled && course.progress_percentage !== undefined && (
                      <div className="mb-4">
                        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                          <span>Progrès</span>
                          <span>{course.progress_percentage}%</span>
                        </div>
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${course.progress_percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                    
                    <div className="flex space-x-2">
                      {!course.is_enrolled ? (
                        <button
                          onClick={() => handleEnroll(course.id, course.title)}
                          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors font-medium"
                        >
                          S'inscrire
                        </button>
                      ) : (
                        <button
                          onClick={() => navigate(`/courses/${course.id}`)}
                          className="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors font-medium"
                        >
                          Continuer
                        </button>
                      )}
                      <button
                        onClick={() => navigate(`/courses/${course.id}`)}
                        className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        Détails
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))
            )}
          </motion.div>
        )}

        {/* Pagination */}
        {!loading && courses.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mt-12"
          >
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              onPageChange={handlePageChange}
              onItemsPerPageChange={handleItemsPerPageChange}
              showItemsPerPage={true}
              showInfo={true}
              itemsPerPageOptions={coursePaginationOptions}
            />
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default CoursesPage;
