import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import StudentNavbar from '../components/StudentNavbar';
import Pagination from '../components/Pagination';

interface Course {
  id: number;
  title: string;
  description: string;
  level: string;
  level_display: string;
  thumbnail: string;
  estimated_duration: number;
  is_featured: boolean;
  chapters_count: number;
  lessons_count: number;
  progress_percentage: number;
  is_accessible: boolean;
  is_enrolled: boolean;
  enrollment_date?: string;
  prerequisites: Array<{
    id: number;
    title: string;
    progress: number;
  }>;
  created_at: string;
}

interface Filters {
  level: string;
  featured: boolean;
}

const CoursesPage: React.FC = () => {
  const { user } = useAuth();
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<Filters>({
    level: '',
    featured: false
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(9);
  const [totalItems, setTotalItems] = useState(0);

  useEffect(() => {
    if (user) {
      fetchCourses();
    }
  }, [user, filters]);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      setTimeout(() => {
        setCourses([
          {
            id: 1,
            title: 'Algèbre de base',
            description: 'Apprenez les concepts fondamentaux de l\'algèbre avec des exercices pratiques et des exemples concrets.',
            level: 'beginner',
            level_display: 'Débutant',
            thumbnail: '📚',
            estimated_duration: 180,
            is_featured: true,
            chapters_count: 4,
            lessons_count: 12,
            progress_percentage: 45,
            is_accessible: true,
            is_enrolled: true,
            enrollment_date: '2024-01-15T10:30:00Z',
            prerequisites: [],
            created_at: '2024-01-15T10:30:00Z'
          },
          {
            id: 2,
            title: 'Géométrie euclidienne',
            description: 'Découvrez les principes de la géométrie euclidienne et ses applications pratiques.',
            level: 'intermediate',
            level_display: 'Intermédiaire',
            thumbnail: '📐',
            estimated_duration: 240,
            is_featured: false,
            chapters_count: 6,
            lessons_count: 18,
            progress_percentage: 0,
            is_accessible: true,
            is_enrolled: false,
            prerequisites: [],
            created_at: '2024-01-16T14:20:00Z'
          },
          {
            id: 3,
            title: 'Calcul différentiel',
            description: 'Maîtrisez les concepts du calcul différentiel et intégral.',
            level: 'advanced',
            level_display: 'Avancé',
            thumbnail: '∫',
            estimated_duration: 360,
            is_featured: true,
            chapters_count: 8,
            lessons_count: 24,
            progress_percentage: 0,
            is_accessible: false,
            is_enrolled: false,
            prerequisites: [
              { id: 1, title: 'Algèbre de base', progress: 45 }
            ],
            created_at: '2024-01-17T09:45:00Z'
          }
        ]);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Erreur lors de la récupération des cours:', error);
      setLoading(false);
    }
  };

  const handleEnroll = async (courseId: number, courseTitle: string) => {
    try {
      setCourses(courses.map(course => 
        course.id === courseId 
          ? { ...course, is_enrolled: true, enrollment_date: new Date().toISOString() }
          : course
      ));
      alert(`Inscription réussie au cours "${courseTitle}" !`);
    } catch (error) {
      console.error('Erreur lors de l\'inscription:', error);
      alert('Erreur lors de l\'inscription');
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'intermediate':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'advanced':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;
    }
    return `${mins}min`;
  };

  const filteredCourses = courses.filter(course => {
    if (filters.level && course.level !== filters.level) return false;
    if (filters.featured && !course.is_featured) return false;
    return true;
  });

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Cours Structurés</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            Vous devez être connecté pour accéder aux cours.
          </p>
          <a
            href="/login"
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Se connecter
          </a>
        </div>
      </div>
    );
  }

  return (
    <>
      <StudentNavbar />
      <div className="container mx-auto px-4 py-8">
      <motion.h1
        className="text-4xl font-bold mb-8 text-center text-primary-600"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        Cours Structurés
      </motion.h1>

      <motion.div
        className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <h2 className="text-xl font-semibold mb-4">
          Filtres
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Niveau
            </label>
            <select
              value={filters.level}
              onChange={(e) => setFilters(prev => ({ ...prev, level: e.target.value }))}
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="">Tous les niveaux</option>
              <option value="beginner">Débutant</option>
              <option value="intermediate">Intermédiaire</option>
              <option value="advanced">Avancé</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Type
            </label>
            <label className="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700">
              <input
                type="checkbox"
                checked={filters.featured}
                onChange={(e) => setFilters(prev => ({ ...prev, featured: e.target.checked }))}
                className="mr-3"
              />
              <span className="text-gray-900 dark:text-white">Cours vedettes uniquement</span>
            </label>
          </div>
          
          <div className="flex items-end">
            <button
              onClick={() => setFilters({ level: '', featured: false })}
              className="w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
            >
              Effacer filtres
            </button>
          </div>
        </div>
        
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {filteredCourses.length} cours trouvé{filteredCourses.length !== 1 ? 's' : ''}
        </div>
      </motion.div>

      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Chargement des cours...</p>
        </div>
      ) : filteredCourses.length === 0 ? (
        <div className="text-center py-12">
          <h3 className="text-xl font-semibold mb-2">Aucun cours trouvé</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Essayez de modifier vos filtres ou revenez plus tard.
          </p>
          <button
            onClick={() => setFilters({ level: '', featured: false })}
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Voir tous les cours
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCourses.map((course, index) => (
            <motion.div
              key={course.id}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <span className="text-3xl mr-3">{course.thumbnail}</span>
                    <div>
                      <h3 className="font-semibold text-lg text-gray-900 dark:text-white">
                        {course.title}
                      </h3>
                      {course.is_featured && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                          Vedette
                        </span>
                      )}
                    </div>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`}>
                    {course.level_display}
                  </span>
                </div>

                <p className="text-gray-700 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                  {course.description}
                </p>

                <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400 mb-4">
                  <div className="flex items-center">
                    <span>{course.chapters_count} chapitres</span>
                  </div>
                  <div className="flex items-center">
                    <span>{course.lessons_count} leçons</span>
                  </div>
                  <div className="flex items-center">
                    <span className="mr-1">⏱</span>
                    <span>{formatDuration(course.estimated_duration)}</span>
                  </div>
                  <div className="flex items-center">
                    <span>{course.progress_percentage}%</span>
                  </div>
                </div>

                {course.is_enrolled && (
                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span>Progression</span>
                      <span>{course.progress_percentage}%</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${course.progress_percentage}%` }}
                      />
                    </div>
                  </div>
                )}

                {course.prerequisites.length > 0 && !course.is_accessible && (
                  <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900 rounded-lg">
                    <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                      Prérequis requis :
                    </p>
                    {course.prerequisites.map(prereq => (
                      <div key={prereq.id} className="text-xs text-yellow-700 dark:text-yellow-300">
                        • {prereq.title} ({prereq.progress}% terminé)
                      </div>
                    ))}
                  </div>
                )}

                <div className="space-y-2">
                  {!course.is_accessible ? (
                    <button
                      disabled
                      className="w-full bg-gray-400 text-white font-medium py-3 px-4 rounded-lg cursor-not-allowed"
                    >
                      Non accessible
                    </button>
                  ) : !course.is_enrolled ? (
                    <button
                      onClick={() => handleEnroll(course.id, course.title)}
                      className="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                    >
                      S'inscrire
                    </button>
                  ) : (
                    <a
                      href={`/courses/${course.id}`}
                      className="block w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg text-center transition-colors"
                    >
                      {course.progress_percentage > 0 ? 'Continuer' : 'Commencer'}
                    </a>
                  )}
                  
                  <a
                    href={`/courses/${course.id}`}
                    className="block w-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium py-2 px-4 rounded-lg text-center transition-colors"
                  >
                    Voir les détails
                  </a>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
    </>
  );
};

export default CoursesPage;
