{"ast": null, "code": "import { cbrt, expm1, isInteger, log10, log1p, log2, sign, toFixed } from '../../utils/number.js';\nvar n1 = 'number';\nvar n2 = 'number, number';\nexport function absNumber(a) {\n  return Math.abs(a);\n}\nabsNumber.signature = n1;\nexport function addNumber(a, b) {\n  return a + b;\n}\naddNumber.signature = n2;\nexport function subtractNumber(a, b) {\n  return a - b;\n}\nsubtractNumber.signature = n2;\nexport function multiplyNumber(a, b) {\n  return a * b;\n}\nmultiplyNumber.signature = n2;\nexport function divideNumber(a, b) {\n  return a / b;\n}\ndivideNumber.signature = n2;\nexport function unaryMinusNumber(x) {\n  return -x;\n}\nunaryMinusNumber.signature = n1;\nexport function unaryPlusNumber(x) {\n  return x;\n}\nunaryPlusNumber.signature = n1;\nexport function cbrtNumber(x) {\n  return cbrt(x);\n}\ncbrtNumber.signature = n1;\nexport function cubeNumber(x) {\n  return x * x * x;\n}\ncubeNumber.signature = n1;\nexport function expNumber(x) {\n  return Math.exp(x);\n}\nexpNumber.signature = n1;\nexport function expm1Number(x) {\n  return expm1(x);\n}\nexpm1Number.signature = n1;\n\n/**\n * Calculate gcd for numbers\n * @param {number} a\n * @param {number} b\n * @returns {number} Returns the greatest common denominator of a and b\n */\nexport function gcdNumber(a, b) {\n  if (!isInteger(a) || !isInteger(b)) {\n    throw new Error('Parameters in function gcd must be integer numbers');\n  }\n\n  // https://en.wikipedia.org/wiki/Euclidean_algorithm\n  var r;\n  while (b !== 0) {\n    r = a % b;\n    a = b;\n    b = r;\n  }\n  return a < 0 ? -a : a;\n}\ngcdNumber.signature = n2;\n\n/**\n * Calculate lcm for two numbers\n * @param {number} a\n * @param {number} b\n * @returns {number} Returns the least common multiple of a and b\n */\nexport function lcmNumber(a, b) {\n  if (!isInteger(a) || !isInteger(b)) {\n    throw new Error('Parameters in function lcm must be integer numbers');\n  }\n  if (a === 0 || b === 0) {\n    return 0;\n  }\n\n  // https://en.wikipedia.org/wiki/Euclidean_algorithm\n  // evaluate lcm here inline to reduce overhead\n  var t;\n  var prod = a * b;\n  while (b !== 0) {\n    t = b;\n    b = a % t;\n    a = t;\n  }\n  return Math.abs(prod / a);\n}\nlcmNumber.signature = n2;\n\n/**\n * Calculate the logarithm of a value, optionally to a given base.\n * @param {number} x\n * @param {number | null | undefined} base\n * @return {number}\n */\nexport function logNumber(x, y) {\n  if (y) {\n    return Math.log(x) / Math.log(y);\n  }\n  return Math.log(x);\n}\n\n/**\n * Calculate the 10-base logarithm of a number\n * @param {number} x\n * @return {number}\n */\nexport function log10Number(x) {\n  return log10(x);\n}\nlog10Number.signature = n1;\n\n/**\n * Calculate the 2-base logarithm of a number\n * @param {number} x\n * @return {number}\n */\nexport function log2Number(x) {\n  return log2(x);\n}\nlog2Number.signature = n1;\n\n/**\n * Calculate the natural logarithm of a `number+1`\n * @param {number} x\n * @returns {number}\n */\nexport function log1pNumber(x) {\n  return log1p(x);\n}\nlog1pNumber.signature = n1;\n\n/**\n * Calculate the modulus of two numbers\n * @param {number} x\n * @param {number} y\n * @returns {number} res\n * @private\n */\nexport function modNumber(x, y) {\n  // We don't use JavaScript's % operator here as this doesn't work\n  // correctly for x < 0 and x === 0\n  // see https://en.wikipedia.org/wiki/Modulo_operation\n  return y === 0 ? x : x - y * Math.floor(x / y);\n}\nmodNumber.signature = n2;\n\n/**\n * Calculate the nth root of a, solve x^root == a\n * http://rosettacode.org/wiki/Nth_root#JavaScript\n * @param {number} a\n * @param {number} [2] root\n * @private\n */\nexport function nthRootNumber(a) {\n  var root = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2;\n  var inv = root < 0;\n  if (inv) {\n    root = -root;\n  }\n  if (root === 0) {\n    throw new Error('Root must be non-zero');\n  }\n  if (a < 0 && Math.abs(root) % 2 !== 1) {\n    throw new Error('Root must be odd when a is negative.');\n  }\n\n  // edge cases zero and infinity\n  if (a === 0) {\n    return inv ? Infinity : 0;\n  }\n  if (!isFinite(a)) {\n    return inv ? 0 : a;\n  }\n  var x = Math.pow(Math.abs(a), 1 / root);\n  // If a < 0, we require that root is an odd integer,\n  // so (-1) ^ (1/root) = -1\n  x = a < 0 ? -x : x;\n  return inv ? 1 / x : x;\n\n  // Very nice algorithm, but fails with nthRoot(-2, 3).\n  // Newton's method has some well-known problems at times:\n  // https://en.wikipedia.org/wiki/Newton%27s_method#Failure_analysis\n  /*\n  let x = 1 // Initial guess\n  let xPrev = 1\n  let i = 0\n  const iMax = 10000\n  do {\n    const delta = (a / Math.pow(x, root - 1) - x) / root\n    xPrev = x\n    x = x + delta\n    i++\n  }\n  while (xPrev !== x && i < iMax)\n   if (xPrev !== x) {\n    throw new Error('Function nthRoot failed to converge')\n  }\n   return inv ? 1 / x : x\n  */\n}\nexport function signNumber(x) {\n  return sign(x);\n}\nsignNumber.signature = n1;\nexport function sqrtNumber(x) {\n  return Math.sqrt(x);\n}\nsqrtNumber.signature = n1;\nexport function squareNumber(x) {\n  return x * x;\n}\nsquareNumber.signature = n1;\n\n/**\n * Calculate xgcd for two numbers\n * @param {number} a\n * @param {number} b\n * @return {number} result\n * @private\n */\nexport function xgcdNumber(a, b) {\n  // source: https://en.wikipedia.org/wiki/Extended_Euclidean_algorithm\n  var t; // used to swap two variables\n  var q; // quotient\n  var r; // remainder\n  var x = 0;\n  var lastx = 1;\n  var y = 1;\n  var lasty = 0;\n  if (!isInteger(a) || !isInteger(b)) {\n    throw new Error('Parameters in function xgcd must be integer numbers');\n  }\n  while (b) {\n    q = Math.floor(a / b);\n    r = a - q * b;\n    t = x;\n    x = lastx - q * x;\n    lastx = t;\n    t = y;\n    y = lasty - q * y;\n    lasty = t;\n    a = b;\n    b = r;\n  }\n  var res;\n  if (a < 0) {\n    res = [-a, -lastx, -lasty];\n  } else {\n    res = [a, a ? lastx : 0, lasty];\n  }\n  return res;\n}\nxgcdNumber.signature = n2;\n\n/**\n * Calculates the power of x to y, x^y, for two numbers.\n * @param {number} x\n * @param {number} y\n * @return {number} res\n */\nexport function powNumber(x, y) {\n  // x^Infinity === 0 if -1 < x < 1\n  // A real number 0 is returned instead of complex(0)\n  if (x * x < 1 && y === Infinity || x * x > 1 && y === -Infinity) {\n    return 0;\n  }\n  return Math.pow(x, y);\n}\npowNumber.signature = n2;\n\n/**\n * round a number to the given number of decimals, or to zero if decimals is\n * not provided\n * @param {number} value\n * @param {number} decimals       number of decimals, between 0 and 15 (0 by default)\n * @return {number} roundedValue\n */\nexport function roundNumber(value) {\n  var decimals = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  if (!isInteger(decimals) || decimals < 0 || decimals > 15) {\n    throw new Error('Number of decimals in function round must be an integer from 0 to 15 inclusive');\n  }\n  return parseFloat(toFixed(value, decimals));\n}\n\n/**\n * Calculate the norm of a number, the absolute value.\n * @param {number} x\n * @return {number}\n */\nexport function normNumber(x) {\n  return Math.abs(x);\n}\nnormNumber.signature = n1;", "map": {"version": 3, "names": ["cbrt", "expm1", "isInteger", "log10", "log1p", "log2", "sign", "toFixed", "n1", "n2", "absNumber", "a", "Math", "abs", "signature", "addNumber", "b", "subtractNumber", "multiplyNumber", "divideNumber", "unaryMinusNumber", "x", "unaryPlusNumber", "cbrtNumber", "cubeNumber", "expNumber", "exp", "expm1Number", "gcdNumber", "Error", "r", "lcmNumber", "t", "prod", "logNumber", "y", "log", "log10Number", "log2Number", "log1pNumber", "modNumber", "floor", "nthRootNumber", "root", "arguments", "length", "undefined", "inv", "Infinity", "isFinite", "pow", "signNumber", "sqrtNumber", "sqrt", "squareNumber", "xgcdNumber", "q", "lastx", "lasty", "res", "powNumber", "roundNumber", "value", "decimals", "parseFloat", "normNumber"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/plain/number/arithmetic.js"], "sourcesContent": ["import { cbrt, expm1, isInteger, log10, log1p, log2, sign, toFixed } from '../../utils/number.js';\nvar n1 = 'number';\nvar n2 = 'number, number';\nexport function absNumber(a) {\n  return Math.abs(a);\n}\nabsNumber.signature = n1;\nexport function addNumber(a, b) {\n  return a + b;\n}\naddNumber.signature = n2;\nexport function subtractNumber(a, b) {\n  return a - b;\n}\nsubtractNumber.signature = n2;\nexport function multiplyNumber(a, b) {\n  return a * b;\n}\nmultiplyNumber.signature = n2;\nexport function divideNumber(a, b) {\n  return a / b;\n}\ndivideNumber.signature = n2;\nexport function unaryMinusNumber(x) {\n  return -x;\n}\nunaryMinusNumber.signature = n1;\nexport function unaryPlusNumber(x) {\n  return x;\n}\nunaryPlusNumber.signature = n1;\nexport function cbrtNumber(x) {\n  return cbrt(x);\n}\ncbrtNumber.signature = n1;\nexport function cubeNumber(x) {\n  return x * x * x;\n}\ncubeNumber.signature = n1;\nexport function expNumber(x) {\n  return Math.exp(x);\n}\nexpNumber.signature = n1;\nexport function expm1Number(x) {\n  return expm1(x);\n}\nexpm1Number.signature = n1;\n\n/**\n * Calculate gcd for numbers\n * @param {number} a\n * @param {number} b\n * @returns {number} Returns the greatest common denominator of a and b\n */\nexport function gcdNumber(a, b) {\n  if (!isInteger(a) || !isInteger(b)) {\n    throw new Error('Parameters in function gcd must be integer numbers');\n  }\n\n  // https://en.wikipedia.org/wiki/Euclidean_algorithm\n  var r;\n  while (b !== 0) {\n    r = a % b;\n    a = b;\n    b = r;\n  }\n  return a < 0 ? -a : a;\n}\ngcdNumber.signature = n2;\n\n/**\n * Calculate lcm for two numbers\n * @param {number} a\n * @param {number} b\n * @returns {number} Returns the least common multiple of a and b\n */\nexport function lcmNumber(a, b) {\n  if (!isInteger(a) || !isInteger(b)) {\n    throw new Error('Parameters in function lcm must be integer numbers');\n  }\n  if (a === 0 || b === 0) {\n    return 0;\n  }\n\n  // https://en.wikipedia.org/wiki/Euclidean_algorithm\n  // evaluate lcm here inline to reduce overhead\n  var t;\n  var prod = a * b;\n  while (b !== 0) {\n    t = b;\n    b = a % t;\n    a = t;\n  }\n  return Math.abs(prod / a);\n}\nlcmNumber.signature = n2;\n\n/**\n * Calculate the logarithm of a value, optionally to a given base.\n * @param {number} x\n * @param {number | null | undefined} base\n * @return {number}\n */\nexport function logNumber(x, y) {\n  if (y) {\n    return Math.log(x) / Math.log(y);\n  }\n  return Math.log(x);\n}\n\n/**\n * Calculate the 10-base logarithm of a number\n * @param {number} x\n * @return {number}\n */\nexport function log10Number(x) {\n  return log10(x);\n}\nlog10Number.signature = n1;\n\n/**\n * Calculate the 2-base logarithm of a number\n * @param {number} x\n * @return {number}\n */\nexport function log2Number(x) {\n  return log2(x);\n}\nlog2Number.signature = n1;\n\n/**\n * Calculate the natural logarithm of a `number+1`\n * @param {number} x\n * @returns {number}\n */\nexport function log1pNumber(x) {\n  return log1p(x);\n}\nlog1pNumber.signature = n1;\n\n/**\n * Calculate the modulus of two numbers\n * @param {number} x\n * @param {number} y\n * @returns {number} res\n * @private\n */\nexport function modNumber(x, y) {\n  // We don't use JavaScript's % operator here as this doesn't work\n  // correctly for x < 0 and x === 0\n  // see https://en.wikipedia.org/wiki/Modulo_operation\n  return y === 0 ? x : x - y * Math.floor(x / y);\n}\nmodNumber.signature = n2;\n\n/**\n * Calculate the nth root of a, solve x^root == a\n * http://rosettacode.org/wiki/Nth_root#JavaScript\n * @param {number} a\n * @param {number} [2] root\n * @private\n */\nexport function nthRootNumber(a) {\n  var root = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2;\n  var inv = root < 0;\n  if (inv) {\n    root = -root;\n  }\n  if (root === 0) {\n    throw new Error('Root must be non-zero');\n  }\n  if (a < 0 && Math.abs(root) % 2 !== 1) {\n    throw new Error('Root must be odd when a is negative.');\n  }\n\n  // edge cases zero and infinity\n  if (a === 0) {\n    return inv ? Infinity : 0;\n  }\n  if (!isFinite(a)) {\n    return inv ? 0 : a;\n  }\n  var x = Math.pow(Math.abs(a), 1 / root);\n  // If a < 0, we require that root is an odd integer,\n  // so (-1) ^ (1/root) = -1\n  x = a < 0 ? -x : x;\n  return inv ? 1 / x : x;\n\n  // Very nice algorithm, but fails with nthRoot(-2, 3).\n  // Newton's method has some well-known problems at times:\n  // https://en.wikipedia.org/wiki/Newton%27s_method#Failure_analysis\n  /*\n  let x = 1 // Initial guess\n  let xPrev = 1\n  let i = 0\n  const iMax = 10000\n  do {\n    const delta = (a / Math.pow(x, root - 1) - x) / root\n    xPrev = x\n    x = x + delta\n    i++\n  }\n  while (xPrev !== x && i < iMax)\n   if (xPrev !== x) {\n    throw new Error('Function nthRoot failed to converge')\n  }\n   return inv ? 1 / x : x\n  */\n}\nexport function signNumber(x) {\n  return sign(x);\n}\nsignNumber.signature = n1;\nexport function sqrtNumber(x) {\n  return Math.sqrt(x);\n}\nsqrtNumber.signature = n1;\nexport function squareNumber(x) {\n  return x * x;\n}\nsquareNumber.signature = n1;\n\n/**\n * Calculate xgcd for two numbers\n * @param {number} a\n * @param {number} b\n * @return {number} result\n * @private\n */\nexport function xgcdNumber(a, b) {\n  // source: https://en.wikipedia.org/wiki/Extended_Euclidean_algorithm\n  var t; // used to swap two variables\n  var q; // quotient\n  var r; // remainder\n  var x = 0;\n  var lastx = 1;\n  var y = 1;\n  var lasty = 0;\n  if (!isInteger(a) || !isInteger(b)) {\n    throw new Error('Parameters in function xgcd must be integer numbers');\n  }\n  while (b) {\n    q = Math.floor(a / b);\n    r = a - q * b;\n    t = x;\n    x = lastx - q * x;\n    lastx = t;\n    t = y;\n    y = lasty - q * y;\n    lasty = t;\n    a = b;\n    b = r;\n  }\n  var res;\n  if (a < 0) {\n    res = [-a, -lastx, -lasty];\n  } else {\n    res = [a, a ? lastx : 0, lasty];\n  }\n  return res;\n}\nxgcdNumber.signature = n2;\n\n/**\n * Calculates the power of x to y, x^y, for two numbers.\n * @param {number} x\n * @param {number} y\n * @return {number} res\n */\nexport function powNumber(x, y) {\n  // x^Infinity === 0 if -1 < x < 1\n  // A real number 0 is returned instead of complex(0)\n  if (x * x < 1 && y === Infinity || x * x > 1 && y === -Infinity) {\n    return 0;\n  }\n  return Math.pow(x, y);\n}\npowNumber.signature = n2;\n\n/**\n * round a number to the given number of decimals, or to zero if decimals is\n * not provided\n * @param {number} value\n * @param {number} decimals       number of decimals, between 0 and 15 (0 by default)\n * @return {number} roundedValue\n */\nexport function roundNumber(value) {\n  var decimals = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  if (!isInteger(decimals) || decimals < 0 || decimals > 15) {\n    throw new Error('Number of decimals in function round must be an integer from 0 to 15 inclusive');\n  }\n  return parseFloat(toFixed(value, decimals));\n}\n\n/**\n * Calculate the norm of a number, the absolute value.\n * @param {number} x\n * @return {number}\n */\nexport function normNumber(x) {\n  return Math.abs(x);\n}\nnormNumber.signature = n1;"], "mappings": "AAAA,SAASA,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAEC,OAAO,QAAQ,uBAAuB;AACjG,IAAIC,EAAE,GAAG,QAAQ;AACjB,IAAIC,EAAE,GAAG,gBAAgB;AACzB,OAAO,SAASC,SAASA,CAACC,CAAC,EAAE;EAC3B,OAAOC,IAAI,CAACC,GAAG,CAACF,CAAC,CAAC;AACpB;AACAD,SAAS,CAACI,SAAS,GAAGN,EAAE;AACxB,OAAO,SAASO,SAASA,CAACJ,CAAC,EAAEK,CAAC,EAAE;EAC9B,OAAOL,CAAC,GAAGK,CAAC;AACd;AACAD,SAAS,CAACD,SAAS,GAAGL,EAAE;AACxB,OAAO,SAASQ,cAAcA,CAACN,CAAC,EAAEK,CAAC,EAAE;EACnC,OAAOL,CAAC,GAAGK,CAAC;AACd;AACAC,cAAc,CAACH,SAAS,GAAGL,EAAE;AAC7B,OAAO,SAASS,cAAcA,CAACP,CAAC,EAAEK,CAAC,EAAE;EACnC,OAAOL,CAAC,GAAGK,CAAC;AACd;AACAE,cAAc,CAACJ,SAAS,GAAGL,EAAE;AAC7B,OAAO,SAASU,YAAYA,CAACR,CAAC,EAAEK,CAAC,EAAE;EACjC,OAAOL,CAAC,GAAGK,CAAC;AACd;AACAG,YAAY,CAACL,SAAS,GAAGL,EAAE;AAC3B,OAAO,SAASW,gBAAgBA,CAACC,CAAC,EAAE;EAClC,OAAO,CAACA,CAAC;AACX;AACAD,gBAAgB,CAACN,SAAS,GAAGN,EAAE;AAC/B,OAAO,SAASc,eAAeA,CAACD,CAAC,EAAE;EACjC,OAAOA,CAAC;AACV;AACAC,eAAe,CAACR,SAAS,GAAGN,EAAE;AAC9B,OAAO,SAASe,UAAUA,CAACF,CAAC,EAAE;EAC5B,OAAOrB,IAAI,CAACqB,CAAC,CAAC;AAChB;AACAE,UAAU,CAACT,SAAS,GAAGN,EAAE;AACzB,OAAO,SAASgB,UAAUA,CAACH,CAAC,EAAE;EAC5B,OAAOA,CAAC,GAAGA,CAAC,GAAGA,CAAC;AAClB;AACAG,UAAU,CAACV,SAAS,GAAGN,EAAE;AACzB,OAAO,SAASiB,SAASA,CAACJ,CAAC,EAAE;EAC3B,OAAOT,IAAI,CAACc,GAAG,CAACL,CAAC,CAAC;AACpB;AACAI,SAAS,CAACX,SAAS,GAAGN,EAAE;AACxB,OAAO,SAASmB,WAAWA,CAACN,CAAC,EAAE;EAC7B,OAAOpB,KAAK,CAACoB,CAAC,CAAC;AACjB;AACAM,WAAW,CAACb,SAAS,GAAGN,EAAE;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASoB,SAASA,CAACjB,CAAC,EAAEK,CAAC,EAAE;EAC9B,IAAI,CAACd,SAAS,CAACS,CAAC,CAAC,IAAI,CAACT,SAAS,CAACc,CAAC,CAAC,EAAE;IAClC,MAAM,IAAIa,KAAK,CAAC,oDAAoD,CAAC;EACvE;;EAEA;EACA,IAAIC,CAAC;EACL,OAAOd,CAAC,KAAK,CAAC,EAAE;IACdc,CAAC,GAAGnB,CAAC,GAAGK,CAAC;IACTL,CAAC,GAAGK,CAAC;IACLA,CAAC,GAAGc,CAAC;EACP;EACA,OAAOnB,CAAC,GAAG,CAAC,GAAG,CAACA,CAAC,GAAGA,CAAC;AACvB;AACAiB,SAAS,CAACd,SAAS,GAAGL,EAAE;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASsB,SAASA,CAACpB,CAAC,EAAEK,CAAC,EAAE;EAC9B,IAAI,CAACd,SAAS,CAACS,CAAC,CAAC,IAAI,CAACT,SAAS,CAACc,CAAC,CAAC,EAAE;IAClC,MAAM,IAAIa,KAAK,CAAC,oDAAoD,CAAC;EACvE;EACA,IAAIlB,CAAC,KAAK,CAAC,IAAIK,CAAC,KAAK,CAAC,EAAE;IACtB,OAAO,CAAC;EACV;;EAEA;EACA;EACA,IAAIgB,CAAC;EACL,IAAIC,IAAI,GAAGtB,CAAC,GAAGK,CAAC;EAChB,OAAOA,CAAC,KAAK,CAAC,EAAE;IACdgB,CAAC,GAAGhB,CAAC;IACLA,CAAC,GAAGL,CAAC,GAAGqB,CAAC;IACTrB,CAAC,GAAGqB,CAAC;EACP;EACA,OAAOpB,IAAI,CAACC,GAAG,CAACoB,IAAI,GAAGtB,CAAC,CAAC;AAC3B;AACAoB,SAAS,CAACjB,SAAS,GAAGL,EAAE;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASyB,SAASA,CAACb,CAAC,EAAEc,CAAC,EAAE;EAC9B,IAAIA,CAAC,EAAE;IACL,OAAOvB,IAAI,CAACwB,GAAG,CAACf,CAAC,CAAC,GAAGT,IAAI,CAACwB,GAAG,CAACD,CAAC,CAAC;EAClC;EACA,OAAOvB,IAAI,CAACwB,GAAG,CAACf,CAAC,CAAC;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgB,WAAWA,CAAChB,CAAC,EAAE;EAC7B,OAAOlB,KAAK,CAACkB,CAAC,CAAC;AACjB;AACAgB,WAAW,CAACvB,SAAS,GAAGN,EAAE;;AAE1B;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS8B,UAAUA,CAACjB,CAAC,EAAE;EAC5B,OAAOhB,IAAI,CAACgB,CAAC,CAAC;AAChB;AACAiB,UAAU,CAACxB,SAAS,GAAGN,EAAE;;AAEzB;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS+B,WAAWA,CAAClB,CAAC,EAAE;EAC7B,OAAOjB,KAAK,CAACiB,CAAC,CAAC;AACjB;AACAkB,WAAW,CAACzB,SAAS,GAAGN,EAAE;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgC,SAASA,CAACnB,CAAC,EAAEc,CAAC,EAAE;EAC9B;EACA;EACA;EACA,OAAOA,CAAC,KAAK,CAAC,GAAGd,CAAC,GAAGA,CAAC,GAAGc,CAAC,GAAGvB,IAAI,CAAC6B,KAAK,CAACpB,CAAC,GAAGc,CAAC,CAAC;AAChD;AACAK,SAAS,CAAC1B,SAAS,GAAGL,EAAE;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASiC,aAAaA,CAAC/B,CAAC,EAAE;EAC/B,IAAIgC,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAChF,IAAIG,GAAG,GAAGJ,IAAI,GAAG,CAAC;EAClB,IAAII,GAAG,EAAE;IACPJ,IAAI,GAAG,CAACA,IAAI;EACd;EACA,IAAIA,IAAI,KAAK,CAAC,EAAE;IACd,MAAM,IAAId,KAAK,CAAC,uBAAuB,CAAC;EAC1C;EACA,IAAIlB,CAAC,GAAG,CAAC,IAAIC,IAAI,CAACC,GAAG,CAAC8B,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IACrC,MAAM,IAAId,KAAK,CAAC,sCAAsC,CAAC;EACzD;;EAEA;EACA,IAAIlB,CAAC,KAAK,CAAC,EAAE;IACX,OAAOoC,GAAG,GAAGC,QAAQ,GAAG,CAAC;EAC3B;EACA,IAAI,CAACC,QAAQ,CAACtC,CAAC,CAAC,EAAE;IAChB,OAAOoC,GAAG,GAAG,CAAC,GAAGpC,CAAC;EACpB;EACA,IAAIU,CAAC,GAAGT,IAAI,CAACsC,GAAG,CAACtC,IAAI,CAACC,GAAG,CAACF,CAAC,CAAC,EAAE,CAAC,GAAGgC,IAAI,CAAC;EACvC;EACA;EACAtB,CAAC,GAAGV,CAAC,GAAG,CAAC,GAAG,CAACU,CAAC,GAAGA,CAAC;EAClB,OAAO0B,GAAG,GAAG,CAAC,GAAG1B,CAAC,GAAGA,CAAC;;EAEtB;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS8B,UAAUA,CAAC9B,CAAC,EAAE;EAC5B,OAAOf,IAAI,CAACe,CAAC,CAAC;AAChB;AACA8B,UAAU,CAACrC,SAAS,GAAGN,EAAE;AACzB,OAAO,SAAS4C,UAAUA,CAAC/B,CAAC,EAAE;EAC5B,OAAOT,IAAI,CAACyC,IAAI,CAAChC,CAAC,CAAC;AACrB;AACA+B,UAAU,CAACtC,SAAS,GAAGN,EAAE;AACzB,OAAO,SAAS8C,YAAYA,CAACjC,CAAC,EAAE;EAC9B,OAAOA,CAAC,GAAGA,CAAC;AACd;AACAiC,YAAY,CAACxC,SAAS,GAAGN,EAAE;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS+C,UAAUA,CAAC5C,CAAC,EAAEK,CAAC,EAAE;EAC/B;EACA,IAAIgB,CAAC,CAAC,CAAC;EACP,IAAIwB,CAAC,CAAC,CAAC;EACP,IAAI1B,CAAC,CAAC,CAAC;EACP,IAAIT,CAAC,GAAG,CAAC;EACT,IAAIoC,KAAK,GAAG,CAAC;EACb,IAAItB,CAAC,GAAG,CAAC;EACT,IAAIuB,KAAK,GAAG,CAAC;EACb,IAAI,CAACxD,SAAS,CAACS,CAAC,CAAC,IAAI,CAACT,SAAS,CAACc,CAAC,CAAC,EAAE;IAClC,MAAM,IAAIa,KAAK,CAAC,qDAAqD,CAAC;EACxE;EACA,OAAOb,CAAC,EAAE;IACRwC,CAAC,GAAG5C,IAAI,CAAC6B,KAAK,CAAC9B,CAAC,GAAGK,CAAC,CAAC;IACrBc,CAAC,GAAGnB,CAAC,GAAG6C,CAAC,GAAGxC,CAAC;IACbgB,CAAC,GAAGX,CAAC;IACLA,CAAC,GAAGoC,KAAK,GAAGD,CAAC,GAAGnC,CAAC;IACjBoC,KAAK,GAAGzB,CAAC;IACTA,CAAC,GAAGG,CAAC;IACLA,CAAC,GAAGuB,KAAK,GAAGF,CAAC,GAAGrB,CAAC;IACjBuB,KAAK,GAAG1B,CAAC;IACTrB,CAAC,GAAGK,CAAC;IACLA,CAAC,GAAGc,CAAC;EACP;EACA,IAAI6B,GAAG;EACP,IAAIhD,CAAC,GAAG,CAAC,EAAE;IACTgD,GAAG,GAAG,CAAC,CAAChD,CAAC,EAAE,CAAC8C,KAAK,EAAE,CAACC,KAAK,CAAC;EAC5B,CAAC,MAAM;IACLC,GAAG,GAAG,CAAChD,CAAC,EAAEA,CAAC,GAAG8C,KAAK,GAAG,CAAC,EAAEC,KAAK,CAAC;EACjC;EACA,OAAOC,GAAG;AACZ;AACAJ,UAAU,CAACzC,SAAS,GAAGL,EAAE;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASmD,SAASA,CAACvC,CAAC,EAAEc,CAAC,EAAE;EAC9B;EACA;EACA,IAAId,CAAC,GAAGA,CAAC,GAAG,CAAC,IAAIc,CAAC,KAAKa,QAAQ,IAAI3B,CAAC,GAAGA,CAAC,GAAG,CAAC,IAAIc,CAAC,KAAK,CAACa,QAAQ,EAAE;IAC/D,OAAO,CAAC;EACV;EACA,OAAOpC,IAAI,CAACsC,GAAG,CAAC7B,CAAC,EAAEc,CAAC,CAAC;AACvB;AACAyB,SAAS,CAAC9C,SAAS,GAAGL,EAAE;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASoD,WAAWA,CAACC,KAAK,EAAE;EACjC,IAAIC,QAAQ,GAAGnB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACpF,IAAI,CAAC1C,SAAS,CAAC6D,QAAQ,CAAC,IAAIA,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,EAAE,EAAE;IACzD,MAAM,IAAIlC,KAAK,CAAC,gFAAgF,CAAC;EACnG;EACA,OAAOmC,UAAU,CAACzD,OAAO,CAACuD,KAAK,EAAEC,QAAQ,CAAC,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,UAAUA,CAAC5C,CAAC,EAAE;EAC5B,OAAOT,IAAI,CAACC,GAAG,CAACQ,CAAC,CAAC;AACpB;AACA4C,UAAU,CAACnD,SAAS,GAAGN,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}