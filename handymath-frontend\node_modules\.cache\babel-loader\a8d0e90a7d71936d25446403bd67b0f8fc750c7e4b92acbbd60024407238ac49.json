{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { IndexDependencies } from './dependenciesIndexClass.generated.js';\nimport { compareNaturalDependencies } from './dependenciesCompareNatural.generated.js';\nimport { sizeDependencies } from './dependenciesSize.generated.js';\nimport { subsetDependencies } from './dependenciesSubset.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSetMultiplicity } from '../../factoriesAny.js';\nexport var setMultiplicityDependencies = {\n  IndexDependencies,\n  compareNaturalDependencies,\n  sizeDependencies,\n  subsetDependencies,\n  typedDependencies,\n  createSetMultiplicity\n};", "map": {"version": 3, "names": ["IndexDependencies", "compareNaturalDependencies", "sizeDependencies", "subsetDependencies", "typedDependencies", "createSetMultiplicity", "setMultiplicityDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSetMultiplicity.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { IndexDependencies } from './dependenciesIndexClass.generated.js';\nimport { compareNaturalDependencies } from './dependenciesCompareNatural.generated.js';\nimport { sizeDependencies } from './dependenciesSize.generated.js';\nimport { subsetDependencies } from './dependenciesSubset.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSetMultiplicity } from '../../factoriesAny.js';\nexport var setMultiplicityDependencies = {\n  IndexDependencies,\n  compareNaturalDependencies,\n  sizeDependencies,\n  subsetDependencies,\n  typedDependencies,\n  createSetMultiplicity\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,uCAAuC;AACzE,SAASC,0BAA0B,QAAQ,2CAA2C;AACtF,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,OAAO,IAAIC,2BAA2B,GAAG;EACvCN,iBAAiB;EACjBC,0BAA0B;EAC1BC,gBAAgB;EAChBC,kBAAkB;EAClBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}