{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * Checks if the node at w[j] is marked\n *\n * @param {Array}   w               The array\n * @param {Number}  j               The array index\n */\nexport function csMarked(w, j) {\n  // check node is marked\n  return w[j] < 0;\n}", "map": {"version": 3, "names": ["csMarked", "w", "j"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csMarked.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * Checks if the node at w[j] is marked\n *\n * @param {Array}   w               The array\n * @param {Number}  j               The array index\n */\nexport function csMarked(w, j) {\n  // check node is marked\n  return w[j] < 0;\n}"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,QAAQA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC7B;EACA,OAAOD,CAAC,CAACC,CAAC,CAAC,GAAG,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}