{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csTdfs } from './csTdfs.js';\n\n/**\n * Post order a tree of forest\n *\n * @param {Array}   parent          The tree or forest\n * @param {Number}  n               Number of columns\n */\nexport function csPost(parent, n) {\n  // check inputs\n  if (!parent) {\n    return null;\n  }\n  // vars\n  var k = 0;\n  var j;\n  // allocate result\n  var post = []; // (n)\n  // workspace, head: first n entries, next: next n entries, stack: last n entries\n  var w = []; // (3 * n)\n  var head = 0;\n  var next = n;\n  var stack = 2 * n;\n  // initialize workspace\n  for (j = 0; j < n; j++) {\n    // empty linked lists\n    w[head + j] = -1;\n  }\n  // traverse nodes in reverse order\n  for (j = n - 1; j >= 0; j--) {\n    // check j is a root\n    if (parent[j] === -1) {\n      continue;\n    }\n    // add j to list of its parent\n    w[next + j] = w[head + parent[j]];\n    w[head + parent[j]] = j;\n  }\n  // loop nodes\n  for (j = 0; j < n; j++) {\n    // skip j if it is not a root\n    if (parent[j] !== -1) {\n      continue;\n    }\n    // depth-first search\n    k = csTdfs(j, k, w, head, next, post, stack);\n  }\n  return post;\n}", "map": {"version": 3, "names": ["csTdfs", "csPost", "parent", "n", "k", "j", "post", "w", "head", "next", "stack"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csPost.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csTdfs } from './csTdfs.js';\n\n/**\n * Post order a tree of forest\n *\n * @param {Array}   parent          The tree or forest\n * @param {Number}  n               Number of columns\n */\nexport function csPost(parent, n) {\n  // check inputs\n  if (!parent) {\n    return null;\n  }\n  // vars\n  var k = 0;\n  var j;\n  // allocate result\n  var post = []; // (n)\n  // workspace, head: first n entries, next: next n entries, stack: last n entries\n  var w = []; // (3 * n)\n  var head = 0;\n  var next = n;\n  var stack = 2 * n;\n  // initialize workspace\n  for (j = 0; j < n; j++) {\n    // empty linked lists\n    w[head + j] = -1;\n  }\n  // traverse nodes in reverse order\n  for (j = n - 1; j >= 0; j--) {\n    // check j is a root\n    if (parent[j] === -1) {\n      continue;\n    }\n    // add j to list of its parent\n    w[next + j] = w[head + parent[j]];\n    w[head + parent[j]] = j;\n  }\n  // loop nodes\n  for (j = 0; j < n; j++) {\n    // skip j if it is not a root\n    if (parent[j] !== -1) {\n      continue;\n    }\n    // depth-first search\n    k = csTdfs(j, k, w, head, next, post, stack);\n  }\n  return post;\n}"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,MAAM,EAAEC,CAAC,EAAE;EAChC;EACA,IAAI,CAACD,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EACA;EACA,IAAIE,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC;EACL;EACA,IAAIC,IAAI,GAAG,EAAE,CAAC,CAAC;EACf;EACA,IAAIC,CAAC,GAAG,EAAE,CAAC,CAAC;EACZ,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,IAAI,GAAGN,CAAC;EACZ,IAAIO,KAAK,GAAG,CAAC,GAAGP,CAAC;EACjB;EACA,KAAKE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAEE,CAAC,EAAE,EAAE;IACtB;IACAE,CAAC,CAACC,IAAI,GAAGH,CAAC,CAAC,GAAG,CAAC,CAAC;EAClB;EACA;EACA,KAAKA,CAAC,GAAGF,CAAC,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC3B;IACA,IAAIH,MAAM,CAACG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MACpB;IACF;IACA;IACAE,CAAC,CAACE,IAAI,GAAGJ,CAAC,CAAC,GAAGE,CAAC,CAACC,IAAI,GAAGN,MAAM,CAACG,CAAC,CAAC,CAAC;IACjCE,CAAC,CAACC,IAAI,GAAGN,MAAM,CAACG,CAAC,CAAC,CAAC,GAAGA,CAAC;EACzB;EACA;EACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAEE,CAAC,EAAE,EAAE;IACtB;IACA,IAAIH,MAAM,CAACG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MACpB;IACF;IACA;IACAD,CAAC,GAAGJ,MAAM,CAACK,CAAC,EAAED,CAAC,EAAEG,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEH,IAAI,EAAEI,KAAK,CAAC;EAC9C;EACA,OAAOJ,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}