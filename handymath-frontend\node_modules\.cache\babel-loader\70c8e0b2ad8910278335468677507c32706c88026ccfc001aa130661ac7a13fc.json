{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { zerosDependencies } from './dependenciesZeros.generated.js';\nimport { createRightLogShift } from '../../factoriesAny.js';\nexport var rightLogShiftDependencies = {\n  DenseMatrixDependencies,\n  concatDependencies,\n  equalScalarDependencies,\n  matrixDependencies,\n  typedDependencies,\n  zerosDependencies,\n  createRightLogShift\n};", "map": {"version": 3, "names": ["DenseMatrixDependencies", "concatDependencies", "equalScalarDependencies", "matrixDependencies", "typedDependencies", "zerosDependencies", "createRightLogShift", "rightLogShiftDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesRightLogShift.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { zerosDependencies } from './dependenciesZeros.generated.js';\nimport { createRightLogShift } from '../../factoriesAny.js';\nexport var rightLogShiftDependencies = {\n  DenseMatrixDependencies,\n  concatDependencies,\n  equalScalarDependencies,\n  matrixDependencies,\n  typedDependencies,\n  zerosDependencies,\n  createRightLogShift\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,OAAO,IAAIC,yBAAyB,GAAG;EACrCP,uBAAuB;EACvBC,kBAAkB;EAClBC,uBAAuB;EACvBC,kBAAkB;EAClBC,iBAAiB;EACjBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}