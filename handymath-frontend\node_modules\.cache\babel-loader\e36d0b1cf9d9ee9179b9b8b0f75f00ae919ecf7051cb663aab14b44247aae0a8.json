{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createPlanckTemperature } from '../../factoriesAny.js';\nexport var planckTemperatureDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createPlanckTemperature\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createPlanckTemperature", "planckTemperatureDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesPlanckTemperature.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createPlanckTemperature } from '../../factoriesAny.js';\nexport var planckTemperatureDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createPlanckTemperature\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,uBAAuB,QAAQ,uBAAuB;AAC/D,OAAO,IAAIC,6BAA6B,GAAG;EACzCH,qBAAqB;EACrBC,gBAAgB;EAChBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}