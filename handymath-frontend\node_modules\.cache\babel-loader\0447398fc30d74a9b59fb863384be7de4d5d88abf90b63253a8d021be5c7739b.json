{"ast": null, "code": "export var unaryMinusDocs = {\n  name: 'unaryMinus',\n  category: 'Operators',\n  syntax: ['-x', 'unaryMinus(x)'],\n  description: 'Inverse the sign of a value. Converts booleans and strings to numbers.',\n  examples: ['-4.5', '-(-5.6)', '-\"22\"'],\n  seealso: ['add', 'subtract', 'unaryPlus']\n};", "map": {"version": 3, "names": ["unaryMinusDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/unaryMinus.js"], "sourcesContent": ["export var unaryMinusDocs = {\n  name: 'unaryMinus',\n  category: 'Operators',\n  syntax: ['-x', 'unaryMinus(x)'],\n  description: 'Inverse the sign of a value. Converts booleans and strings to numbers.',\n  examples: ['-4.5', '-(-5.6)', '-\"22\"'],\n  seealso: ['add', 'subtract', 'unaryPlus']\n};"], "mappings": "AAAA,OAAO,IAAIA,cAAc,GAAG;EAC1BC,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;EAC/BC,WAAW,EAAE,wEAAwE;EACrFC,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC;EACtCC,OAAO,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,WAAW;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}