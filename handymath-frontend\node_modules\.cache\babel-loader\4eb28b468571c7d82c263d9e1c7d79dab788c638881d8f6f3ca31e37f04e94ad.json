{"ast": null, "code": "export var powDocs = {\n  name: 'pow',\n  category: 'Operators',\n  syntax: ['x ^ y', 'pow(x, y)'],\n  description: 'Calculates the power of x to y, x^y.',\n  examples: ['2^3', '2*2*2', '1 + e ^ (pi * i)', 'pow([[1, 2], [4, 3]], 2)', 'pow([[1, 2], [4, 3]], -1)'],\n  seealso: ['multiply', 'nthRoot', 'nthRoots', 'sqrt']\n};", "map": {"version": 3, "names": ["powDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/pow.js"], "sourcesContent": ["export var powDocs = {\n  name: 'pow',\n  category: 'Operators',\n  syntax: ['x ^ y', 'pow(x, y)'],\n  description: 'Calculates the power of x to y, x^y.',\n  examples: ['2^3', '2*2*2', '1 + e ^ (pi * i)', 'pow([[1, 2], [4, 3]], 2)', 'pow([[1, 2], [4, 3]], -1)'],\n  seealso: ['multiply', 'nthRoot', 'nthRoots', 'sqrt']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC;EAC9BC,WAAW,EAAE,sCAAsC;EACnDC,QAAQ,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,kBAAkB,EAAE,0BAA0B,EAAE,2BAA2B,CAAC;EACvGC,OAAO,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM;AACrD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}