{"ast": null, "code": "export var importDocs = {\n  name: 'import',\n  category: 'Core',\n  syntax: ['import(functions)', 'import(functions, options)'],\n  description: 'Import functions or constants from an object.',\n  examples: ['import({myFn: f(x)=x^2, myConstant: 32 })', 'myFn(2)', 'myConstant'],\n  seealso: []\n};", "map": {"version": 3, "names": ["importDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/core/import.js"], "sourcesContent": ["export var importDocs = {\n  name: 'import',\n  category: 'Core',\n  syntax: ['import(functions)', 'import(functions, options)'],\n  description: 'Import functions or constants from an object.',\n  examples: ['import({myFn: f(x)=x^2, myConstant: 32 })', 'myFn(2)', 'myConstant'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,CAAC,mBAAmB,EAAE,4BAA4B,CAAC;EAC3DC,WAAW,EAAE,+CAA+C;EAC5DC,QAAQ,EAAE,CAAC,2CAA2C,EAAE,SAAS,EAAE,YAAY,CAAC;EAChFC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}