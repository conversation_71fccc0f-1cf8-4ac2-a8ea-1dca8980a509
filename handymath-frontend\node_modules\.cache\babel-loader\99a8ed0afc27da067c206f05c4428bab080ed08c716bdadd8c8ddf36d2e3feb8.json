{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createIsNumeric } from '../../factoriesAny.js';\nexport var isNumericDependencies = {\n  typedDependencies,\n  createIsNumeric\n};", "map": {"version": 3, "names": ["typedDependencies", "createIsNumeric", "isNumericDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesIsNumeric.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createIsNumeric } from '../../factoriesAny.js';\nexport var isNumericDependencies = {\n  typedDependencies,\n  createIsNumeric\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,eAAe,QAAQ,uBAAuB;AACvD,OAAO,IAAIC,qBAAqB,GAAG;EACjCF,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}