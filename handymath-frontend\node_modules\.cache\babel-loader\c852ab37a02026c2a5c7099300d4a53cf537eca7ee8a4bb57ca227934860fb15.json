{"ast": null, "code": "export var squeezeDocs = {\n  name: 'squeeze',\n  category: 'Matrix',\n  syntax: ['squeeze(x)'],\n  description: 'Remove inner and outer singleton dimensions from a matrix.',\n  examples: ['a = zeros(3,2,1)', 'size(squeeze(a))', 'b = zeros(1,1,3)', 'size(squeeze(b))'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'subset', 'trace', 'transpose', 'zeros']\n};", "map": {"version": 3, "names": ["squeezeDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/squeeze.js"], "sourcesContent": ["export var squeezeDocs = {\n  name: 'squeeze',\n  category: 'Matrix',\n  syntax: ['squeeze(x)'],\n  description: 'Remove inner and outer singleton dimensions from a matrix.',\n  examples: ['a = zeros(3,2,1)', 'size(squeeze(a))', 'b = zeros(1,1,3)', 'size(squeeze(b))'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'subset', 'trace', 'transpose', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,YAAY,CAAC;EACtBC,WAAW,EAAE,4DAA4D;EACzEC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;EAC1FC,OAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO;AACxH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}