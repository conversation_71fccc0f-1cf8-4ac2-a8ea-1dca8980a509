{"ast": null, "code": "export var intersectDocs = {\n  name: 'intersect',\n  category: 'Geometry',\n  syntax: ['intersect(expr1, expr2, expr3, expr4)', 'intersect(expr1, expr2, expr3)'],\n  description: 'Computes the intersection point of lines and/or planes.',\n  examples: ['intersect([0, 0], [10, 10], [10, 0], [0, 10])', 'intersect([1, 0, 1],  [4, -2, 2], [1, 1, 1, 6])'],\n  seealso: []\n};", "map": {"version": 3, "names": ["intersectDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/geometry/intersect.js"], "sourcesContent": ["export var intersectDocs = {\n  name: 'intersect',\n  category: 'Geometry',\n  syntax: ['intersect(expr1, expr2, expr3, expr4)', 'intersect(expr1, expr2, expr3)'],\n  description: 'Computes the intersection point of lines and/or planes.',\n  examples: ['intersect([0, 0], [10, 10], [10, 0], [0, 10])', 'intersect([1, 0, 1],  [4, -2, 2], [1, 1, 1, 6])'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,CAAC,uCAAuC,EAAE,gCAAgC,CAAC;EACnFC,WAAW,EAAE,yDAAyD;EACtEC,QAAQ,EAAE,CAAC,+CAA+C,EAAE,iDAAiD,CAAC;EAC9GC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}