{"ast": null, "code": "export var countDocs = {\n  name: 'count',\n  category: 'Matrix',\n  syntax: ['count(x)'],\n  description: 'Count the number of elements of a matrix, array or string.',\n  examples: ['a = [1, 2; 3, 4; 5, 6]', 'count(a)', 'size(a)', 'count(\"hello world\")'],\n  seealso: ['size']\n};", "map": {"version": 3, "names": ["countDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/count.js"], "sourcesContent": ["export var countDocs = {\n  name: 'count',\n  category: 'Matrix',\n  syntax: ['count(x)'],\n  description: 'Count the number of elements of a matrix, array or string.',\n  examples: ['a = [1, 2; 3, 4; 5, 6]', 'count(a)', 'size(a)', 'count(\"hello world\")'],\n  seealso: ['size']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,4DAA4D;EACzEC,QAAQ,EAAE,CAAC,wBAAwB,EAAE,UAAU,EAAE,SAAS,EAAE,sBAAsB,CAAC;EACnFC,OAAO,EAAE,CAAC,MAAM;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}