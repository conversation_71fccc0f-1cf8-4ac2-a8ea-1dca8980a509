{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { createTau } from '../../factoriesAny.js';\nexport var tauDependencies = {\n  BigNumberDependencies,\n  createTau\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "createTau", "tauDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesTau.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { createTau } from '../../factoriesAny.js';\nexport var tauDependencies = {\n  BigNumberDependencies,\n  createTau\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAO,IAAIC,eAAe,GAAG;EAC3BF,qBAAqB;EACrBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}