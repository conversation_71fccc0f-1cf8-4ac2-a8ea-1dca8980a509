{"ast": null, "code": "export var indexDocs = {\n  name: 'index',\n  category: 'Construction',\n  syntax: ['[start]', '[start:end]', '[start:step:end]', '[start1, start 2, ...]', '[start1:end1, start2:end2, ...]', '[start1:step1:end1, start2:step2:end2, ...]'],\n  description: 'Create an index to get or replace a subset of a matrix',\n  examples: ['A = [1, 2, 3; 4, 5, 6]', 'A[1, :]', 'A[1, 2] = 50', 'A[1:2, 1:2] = 1', 'B = [1, 2, 3]', 'B[B>1 and B<3]'],\n  seealso: ['bignumber', 'boolean', 'complex', 'matrix', 'number', 'range', 'string', 'unit']\n};", "map": {"version": 3, "names": ["indexDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/construction/index.js"], "sourcesContent": ["export var indexDocs = {\n  name: 'index',\n  category: 'Construction',\n  syntax: ['[start]', '[start:end]', '[start:step:end]', '[start1, start 2, ...]', '[start1:end1, start2:end2, ...]', '[start1:step1:end1, start2:step2:end2, ...]'],\n  description: 'Create an index to get or replace a subset of a matrix',\n  examples: ['A = [1, 2, 3; 4, 5, 6]', 'A[1, :]', 'A[1, 2] = 50', 'A[1:2, 1:2] = 1', 'B = [1, 2, 3]', 'B[B>1 and B<3]'],\n  seealso: ['bignumber', 'boolean', 'complex', 'matrix', 'number', 'range', 'string', 'unit']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,kBAAkB,EAAE,wBAAwB,EAAE,iCAAiC,EAAE,6CAA6C,CAAC;EAClKC,WAAW,EAAE,wDAAwD;EACrEC,QAAQ,EAAE,CAAC,wBAAwB,EAAE,SAAS,EAAE,cAAc,EAAE,iBAAiB,EAAE,eAAe,EAAE,gBAAgB,CAAC;EACrHC,OAAO,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM;AAC5F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}