{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { dotDivideDependencies } from './dependenciesDotDivide.generated.js';\nimport { isNumericDependencies } from './dependenciesIsNumeric.generated.js';\nimport { logDependencies } from './dependenciesLog.generated.js';\nimport { mapDependencies } from './dependenciesMap.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { sumDependencies } from './dependenciesSum.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createKldivergence } from '../../factoriesAny.js';\nexport var kldivergenceDependencies = {\n  divideDependencies,\n  dotDivideDependencies,\n  isNumericDependencies,\n  logDependencies,\n  mapDependencies,\n  matrixDependencies,\n  multiplyDependencies,\n  sumDependencies,\n  typedDependencies,\n  createKldivergence\n};", "map": {"version": 3, "names": ["divideDependencies", "dotDivideDependencies", "isNumericDependencies", "logDependencies", "mapDependencies", "matrixDependencies", "multiplyDependencies", "sumDependencies", "typedDependencies", "createKldivergence", "kldivergenceDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesKldivergence.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { dotDivideDependencies } from './dependenciesDotDivide.generated.js';\nimport { isNumericDependencies } from './dependenciesIsNumeric.generated.js';\nimport { logDependencies } from './dependenciesLog.generated.js';\nimport { mapDependencies } from './dependenciesMap.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { sumDependencies } from './dependenciesSum.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createKldivergence } from '../../factoriesAny.js';\nexport var kldivergenceDependencies = {\n  divideDependencies,\n  dotDivideDependencies,\n  isNumericDependencies,\n  logDependencies,\n  mapDependencies,\n  matrixDependencies,\n  multiplyDependencies,\n  sumDependencies,\n  typedDependencies,\n  createKldivergence\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,OAAO,IAAIC,wBAAwB,GAAG;EACpCV,kBAAkB;EAClBC,qBAAqB;EACrBC,qBAAqB;EACrBC,eAAe;EACfC,eAAe;EACfC,kBAAkB;EAClBC,oBAAoB;EACpBC,eAAe;EACfC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}