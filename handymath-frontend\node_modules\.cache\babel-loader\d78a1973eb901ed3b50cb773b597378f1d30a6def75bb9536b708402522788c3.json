{"ast": null, "code": "var n1 = 'number';\nvar n2 = 'number, number';\nexport function notNumber(x) {\n  return !x;\n}\nnotNumber.signature = n1;\nexport function orNumber(x, y) {\n  return !!(x || y);\n}\norNumber.signature = n2;\nexport function xorNumber(x, y) {\n  return !!x !== !!y;\n}\nxorNumber.signature = n2;\nexport function andNumber(x, y) {\n  return !!(x && y);\n}\nandNumber.signature = n2;", "map": {"version": 3, "names": ["n1", "n2", "notNumber", "x", "signature", "orNumber", "y", "xorNumber", "andNumber"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/plain/number/logical.js"], "sourcesContent": ["var n1 = 'number';\nvar n2 = 'number, number';\nexport function notNumber(x) {\n  return !x;\n}\nnotNumber.signature = n1;\nexport function orNumber(x, y) {\n  return !!(x || y);\n}\norNumber.signature = n2;\nexport function xorNumber(x, y) {\n  return !!x !== !!y;\n}\nxorNumber.signature = n2;\nexport function andNumber(x, y) {\n  return !!(x && y);\n}\nandNumber.signature = n2;"], "mappings": "AAAA,IAAIA,EAAE,GAAG,QAAQ;AACjB,IAAIC,EAAE,GAAG,gBAAgB;AACzB,OAAO,SAASC,SAASA,CAACC,CAAC,EAAE;EAC3B,OAAO,CAACA,CAAC;AACX;AACAD,SAAS,CAACE,SAAS,GAAGJ,EAAE;AACxB,OAAO,SAASK,QAAQA,CAACF,CAAC,EAAEG,CAAC,EAAE;EAC7B,OAAO,CAAC,EAAEH,CAAC,IAAIG,CAAC,CAAC;AACnB;AACAD,QAAQ,CAACD,SAAS,GAAGH,EAAE;AACvB,OAAO,SAASM,SAASA,CAACJ,CAAC,EAAEG,CAAC,EAAE;EAC9B,OAAO,CAAC,CAACH,CAAC,KAAK,CAAC,CAACG,CAAC;AACpB;AACAC,SAAS,CAACH,SAAS,GAAGH,EAAE;AACxB,OAAO,SAASO,SAASA,CAACL,CAAC,EAAEG,CAAC,EAAE;EAC9B,OAAO,CAAC,EAAEH,CAAC,IAAIG,CAAC,CAAC;AACnB;AACAE,SAAS,CAACJ,SAAS,GAAGH,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}