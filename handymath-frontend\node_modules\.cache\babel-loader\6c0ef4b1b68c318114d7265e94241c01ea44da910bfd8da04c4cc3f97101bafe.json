{"ast": null, "code": "export var sechDocs = {\n  name: 'sech',\n  category: 'Trigonometry',\n  syntax: ['sech(x)'],\n  description: 'Compute the hyperbolic secant of x in radians. Defined as 1/cosh(x)',\n  examples: ['sech(2)', '1 / cosh(2)'],\n  seealso: ['coth', 'csch', 'cosh']\n};", "map": {"version": 3, "names": ["sechDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/sech.js"], "sourcesContent": ["export var sechDocs = {\n  name: 'sech',\n  category: 'Trigonometry',\n  syntax: ['sech(x)'],\n  description: 'Compute the hyperbolic secant of x in radians. Defined as 1/cosh(x)',\n  examples: ['sech(2)', '1 / cosh(2)'],\n  seealso: ['coth', 'csch', 'cosh']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,qEAAqE;EAClFC,QAAQ,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;EACpCC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}