import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { NotificationProvider } from './components/NotificationSystem';
import ProtectedRoute from './components/ProtectedRoute';
import HomePage from './pages/HomePage';
import SolverPage from './pages/SolverPage';
import VisualizerPage from './pages/VisualizerPage';
import ExercisesPage from './pages/ExercisesPage';
import ExerciseDetailPage from './pages/ExerciseDetailPage';
import ProgressPage from './pages/ProgressPage';
import CoursesPage from './pages/CoursesPage';
import CourseDetailPage from './pages/CourseDetailPage';
import LessonDetailPage from './pages/LessonDetailPage';
import TestLoginPage from './pages/TestLoginPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';

import StudentDashboard from './pages/student/StudentDashboard';
import AdminDashboard from './pages/AdminDashboard';
import AdminUsers from './pages/AdminUsers';
import AdminCourses from './pages/AdminCourses';
import AdminExercises from './pages/AdminExercises';
import AdminAnalytics from './pages/AdminAnalytics';
import ContactMessagesPage from './pages/admin/ContactMessagesPage';
import ProfilePage from './pages/ProfilePage';
import SettingsPage from './pages/SettingsPage';
import ContactPage from './pages/ContactPage';
import AboutPage from './pages/AboutPage';
import NotFoundPage from './pages/errors/NotFoundPage';
import Footer from './components/Footer';


import axios from 'axios';

// Configuration globale d'axios
axios.defaults.baseURL = 'http://localhost:8000';  // URL de base pour toutes les requêtes

// Intercepteur pour ajouter le token d'authentification
axios.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs de réponse
axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('Erreur Axios:', error);
    if (error.response) {
      console.error('Données de réponse:', error.response.data);
      console.error('Statut:', error.response.status);
    }
    return Promise.reject(error);
  }
);

function App() {
  return (
    <ThemeProvider>
      <NotificationProvider>
        <AuthProvider>
          <Router>
            <div className="min-h-screen flex flex-col">
            <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/solver" element={<SolverPage />} />
          <Route path="/visualizer" element={<VisualizerPage />} />
          <Route path="/exercises" element={<ExercisesPage />} />
          <Route path="/exercises/:id" element={<ExerciseDetailPage />} />
          <Route path="/progress" element={<ProgressPage />} />
          <Route path="/courses" element={<CoursesPage />} />
          <Route path="/courses/:id" element={<CourseDetailPage />} />
          <Route path="/lessons/:id" element={<LessonDetailPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/test-login" element={<TestLoginPage />} />
          <Route path="/register" element={<RegisterPage />} />

          {/* Routes protégées */}
          <Route
            path="/etudiant/dashboard"
            element={
              <ProtectedRoute roles={['student']}>
                <StudentDashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin"
            element={
              <ProtectedRoute roles={['admin']}>
                <AdminDashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/dashboard"
            element={
              <ProtectedRoute roles={['admin']}>
                <AdminDashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/users"
            element={
              <ProtectedRoute roles={['admin']}>
                <AdminUsers />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/courses"
            element={
              <ProtectedRoute roles={['admin']}>
                <AdminCourses />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/exercises"
            element={
              <ProtectedRoute roles={['admin']}>
                <AdminExercises />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/analytics"
            element={
              <ProtectedRoute roles={['admin']}>
                <AdminAnalytics />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/contact"
            element={
              <ProtectedRoute roles={['admin']}>
                <ContactMessagesPage />
              </ProtectedRoute>
            }
          />

          {/* Pages utilisateur protégées */}
          <Route
            path="/profile"
            element={
              <ProtectedRoute roles={['student', 'admin']}>
                <ProfilePage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/settings"
            element={
              <ProtectedRoute roles={['student', 'admin']}>
                <SettingsPage />
              </ProtectedRoute>
            }
          />

          {/* Pages publiques */}
          <Route path="/contact" element={<ContactPage />} />
          <Route path="/about" element={<AboutPage />} />

          {/* Page 404 - doit être en dernier */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
        <Footer />
        </div>
        </Router>
      </AuthProvider>
    </NotificationProvider>
    </ThemeProvider>
  );
}

export default App;

