{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo09xS0Sf';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createMatAlgo09xS0Sf = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix A and invokes the callback function f(Aij, Bij).\n   * Callback function invoked NZA times, number of nonzero elements in A.\n   *\n   *\n   *          ┌  f(Aij, Bij)  ; A(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  0            ; otherwise\n   *\n   *\n   * @param {Matrix}   a                 The SparseMatrix instance (A)\n   * @param {Matrix}   b                 The SparseMatrix instance (B)\n   * @param {Function} callback          The f(Aij,Bij) operation to invoke\n   *\n   * @return {Matrix}                    SparseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97620294\n   */\n  return function matAlgo09xS0Sf(a, b, callback) {\n    // sparse matrix arrays\n    var avalues = a._values;\n    var aindex = a._index;\n    var aptr = a._ptr;\n    var asize = a._size;\n    var adt = a._datatype || a._data === undefined ? a._datatype : a.getDataType();\n    // sparse matrix arrays\n    var bvalues = b._values;\n    var bindex = b._index;\n    var bptr = b._ptr;\n    var bsize = b._size;\n    var bdt = b._datatype || b._data === undefined ? b._datatype : b.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      // datatype\n      dt = adt;\n      // find signature that matches (dt, dt)\n      eq = typed.find(equalScalar, [dt, dt]);\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays\n    var cvalues = avalues && bvalues ? [] : undefined;\n    var cindex = [];\n    var cptr = [];\n\n    // workspaces\n    var x = cvalues ? [] : undefined;\n    // marks indicating we have a value in x for a given column\n    var w = [];\n\n    // vars\n    var i, j, k, k0, k1;\n\n    // loop columns\n    for (j = 0; j < columns; j++) {\n      // update cptr\n      cptr[j] = cindex.length;\n      // column mark\n      var mark = j + 1;\n      // check we need to process values\n      if (x) {\n        // loop B(:,j)\n        for (k0 = bptr[j], k1 = bptr[j + 1], k = k0; k < k1; k++) {\n          // row\n          i = bindex[k];\n          // update workspace\n          w[i] = mark;\n          x[i] = bvalues[k];\n        }\n      }\n      // loop A(:,j)\n      for (k0 = aptr[j], k1 = aptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        i = aindex[k];\n        // check we need to process values\n        if (x) {\n          // b value @ i,j\n          var vb = w[i] === mark ? x[i] : zero;\n          // invoke f\n          var vc = cf(avalues[k], vb);\n          // check zero value\n          if (!eq(vc, zero)) {\n            // push index\n            cindex.push(i);\n            // push value\n            cvalues.push(vc);\n          }\n        } else {\n          // push index\n          cindex.push(i);\n        }\n      }\n    }\n    // update cptr\n    cptr[columns] = cindex.length;\n\n    // return sparse matrix\n    return a.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === a._datatype && bdt === b._datatype ? dt : undefined\n    });\n  };\n});", "map": {"version": 3, "names": ["factory", "DimensionError", "name", "dependencies", "createMatAlgo09xS0Sf", "_ref", "typed", "equalScalar", "matAlgo09xS0Sf", "a", "b", "callback", "avalues", "_values", "aindex", "_index", "aptr", "_ptr", "asize", "_size", "adt", "_datatype", "_data", "undefined", "getDataType", "bvalues", "bindex", "bptr", "bsize", "bdt", "length", "RangeError", "rows", "columns", "dt", "eq", "zero", "cf", "find", "convert", "cvalues", "cindex", "cptr", "x", "w", "i", "j", "k", "k0", "k1", "mark", "vb", "vc", "push", "createSparseMatrix", "values", "index", "ptr", "size", "datatype"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/utils/matAlgo09xS0Sf.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo09xS0Sf';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createMatAlgo09xS0Sf = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix A and invokes the callback function f(Aij, Bij).\n   * Callback function invoked NZA times, number of nonzero elements in A.\n   *\n   *\n   *          ┌  f(Aij, Bij)  ; A(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  0            ; otherwise\n   *\n   *\n   * @param {Matrix}   a                 The SparseMatrix instance (A)\n   * @param {Matrix}   b                 The SparseMatrix instance (B)\n   * @param {Function} callback          The f(Aij,Bij) operation to invoke\n   *\n   * @return {Matrix}                    SparseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97620294\n   */\n  return function matAlgo09xS0Sf(a, b, callback) {\n    // sparse matrix arrays\n    var avalues = a._values;\n    var aindex = a._index;\n    var aptr = a._ptr;\n    var asize = a._size;\n    var adt = a._datatype || a._data === undefined ? a._datatype : a.getDataType();\n    // sparse matrix arrays\n    var bvalues = b._values;\n    var bindex = b._index;\n    var bptr = b._ptr;\n    var bsize = b._size;\n    var bdt = b._datatype || b._data === undefined ? b._datatype : b.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      // datatype\n      dt = adt;\n      // find signature that matches (dt, dt)\n      eq = typed.find(equalScalar, [dt, dt]);\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays\n    var cvalues = avalues && bvalues ? [] : undefined;\n    var cindex = [];\n    var cptr = [];\n\n    // workspaces\n    var x = cvalues ? [] : undefined;\n    // marks indicating we have a value in x for a given column\n    var w = [];\n\n    // vars\n    var i, j, k, k0, k1;\n\n    // loop columns\n    for (j = 0; j < columns; j++) {\n      // update cptr\n      cptr[j] = cindex.length;\n      // column mark\n      var mark = j + 1;\n      // check we need to process values\n      if (x) {\n        // loop B(:,j)\n        for (k0 = bptr[j], k1 = bptr[j + 1], k = k0; k < k1; k++) {\n          // row\n          i = bindex[k];\n          // update workspace\n          w[i] = mark;\n          x[i] = bvalues[k];\n        }\n      }\n      // loop A(:,j)\n      for (k0 = aptr[j], k1 = aptr[j + 1], k = k0; k < k1; k++) {\n        // row\n        i = aindex[k];\n        // check we need to process values\n        if (x) {\n          // b value @ i,j\n          var vb = w[i] === mark ? x[i] : zero;\n          // invoke f\n          var vc = cf(avalues[k], vb);\n          // check zero value\n          if (!eq(vc, zero)) {\n            // push index\n            cindex.push(i);\n            // push value\n            cvalues.push(vc);\n          }\n        } else {\n          // push index\n          cindex.push(i);\n        }\n      }\n    }\n    // update cptr\n    cptr[columns] = cindex.length;\n\n    // return sparse matrix\n    return a.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === a._datatype && bdt === b._datatype ? dt : undefined\n    });\n  };\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,IAAIC,IAAI,GAAG,gBAAgB;AAC3B,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC;AAC3C,OAAO,IAAIC,oBAAoB,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACnF,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASG,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAE;IAC7C;IACA,IAAIC,OAAO,GAAGH,CAAC,CAACI,OAAO;IACvB,IAAIC,MAAM,GAAGL,CAAC,CAACM,MAAM;IACrB,IAAIC,IAAI,GAAGP,CAAC,CAACQ,IAAI;IACjB,IAAIC,KAAK,GAAGT,CAAC,CAACU,KAAK;IACnB,IAAIC,GAAG,GAAGX,CAAC,CAACY,SAAS,IAAIZ,CAAC,CAACa,KAAK,KAAKC,SAAS,GAAGd,CAAC,CAACY,SAAS,GAAGZ,CAAC,CAACe,WAAW,CAAC,CAAC;IAC9E;IACA,IAAIC,OAAO,GAAGf,CAAC,CAACG,OAAO;IACvB,IAAIa,MAAM,GAAGhB,CAAC,CAACK,MAAM;IACrB,IAAIY,IAAI,GAAGjB,CAAC,CAACO,IAAI;IACjB,IAAIW,KAAK,GAAGlB,CAAC,CAACS,KAAK;IACnB,IAAIU,GAAG,GAAGnB,CAAC,CAACW,SAAS,IAAIX,CAAC,CAACY,KAAK,KAAKC,SAAS,GAAGb,CAAC,CAACW,SAAS,GAAGX,CAAC,CAACc,WAAW,CAAC,CAAC;;IAE9E;IACA,IAAIN,KAAK,CAACY,MAAM,KAAKF,KAAK,CAACE,MAAM,EAAE;MACjC,MAAM,IAAI7B,cAAc,CAACiB,KAAK,CAACY,MAAM,EAAEF,KAAK,CAACE,MAAM,CAAC;IACtD;;IAEA;IACA,IAAIZ,KAAK,CAAC,CAAC,CAAC,KAAKU,KAAK,CAAC,CAAC,CAAC,IAAIV,KAAK,CAAC,CAAC,CAAC,KAAKU,KAAK,CAAC,CAAC,CAAC,EAAE;MAClD,MAAM,IAAIG,UAAU,CAAC,gCAAgC,GAAGb,KAAK,GAAG,yBAAyB,GAAGU,KAAK,GAAG,GAAG,CAAC;IAC1G;;IAEA;IACA,IAAII,IAAI,GAAGd,KAAK,CAAC,CAAC,CAAC;IACnB,IAAIe,OAAO,GAAGf,KAAK,CAAC,CAAC,CAAC;;IAEtB;IACA,IAAIgB,EAAE;IACN;IACA,IAAIC,EAAE,GAAG5B,WAAW;IACpB;IACA,IAAI6B,IAAI,GAAG,CAAC;IACZ;IACA,IAAIC,EAAE,GAAG1B,QAAQ;;IAEjB;IACA,IAAI,OAAOS,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAKS,GAAG,IAAIT,GAAG,KAAK,OAAO,EAAE;MAC7D;MACAc,EAAE,GAAGd,GAAG;MACR;MACAe,EAAE,GAAG7B,KAAK,CAACgC,IAAI,CAAC/B,WAAW,EAAE,CAAC2B,EAAE,EAAEA,EAAE,CAAC,CAAC;MACtC;MACAE,IAAI,GAAG9B,KAAK,CAACiC,OAAO,CAAC,CAAC,EAAEL,EAAE,CAAC;MAC3B;MACAG,EAAE,GAAG/B,KAAK,CAACgC,IAAI,CAAC3B,QAAQ,EAAE,CAACuB,EAAE,EAAEA,EAAE,CAAC,CAAC;IACrC;;IAEA;IACA,IAAIM,OAAO,GAAG5B,OAAO,IAAIa,OAAO,GAAG,EAAE,GAAGF,SAAS;IACjD,IAAIkB,MAAM,GAAG,EAAE;IACf,IAAIC,IAAI,GAAG,EAAE;;IAEb;IACA,IAAIC,CAAC,GAAGH,OAAO,GAAG,EAAE,GAAGjB,SAAS;IAChC;IACA,IAAIqB,CAAC,GAAG,EAAE;;IAEV;IACA,IAAIC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE;;IAEnB;IACA,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,OAAO,EAAEa,CAAC,EAAE,EAAE;MAC5B;MACAJ,IAAI,CAACI,CAAC,CAAC,GAAGL,MAAM,CAACX,MAAM;MACvB;MACA,IAAIoB,IAAI,GAAGJ,CAAC,GAAG,CAAC;MAChB;MACA,IAAIH,CAAC,EAAE;QACL;QACA,KAAKK,EAAE,GAAGrB,IAAI,CAACmB,CAAC,CAAC,EAAEG,EAAE,GAAGtB,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,GAAGC,EAAE,EAAED,CAAC,GAAGE,EAAE,EAAEF,CAAC,EAAE,EAAE;UACxD;UACAF,CAAC,GAAGnB,MAAM,CAACqB,CAAC,CAAC;UACb;UACAH,CAAC,CAACC,CAAC,CAAC,GAAGK,IAAI;UACXP,CAAC,CAACE,CAAC,CAAC,GAAGpB,OAAO,CAACsB,CAAC,CAAC;QACnB;MACF;MACA;MACA,KAAKC,EAAE,GAAGhC,IAAI,CAAC8B,CAAC,CAAC,EAAEG,EAAE,GAAGjC,IAAI,CAAC8B,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,GAAGC,EAAE,EAAED,CAAC,GAAGE,EAAE,EAAEF,CAAC,EAAE,EAAE;QACxD;QACAF,CAAC,GAAG/B,MAAM,CAACiC,CAAC,CAAC;QACb;QACA,IAAIJ,CAAC,EAAE;UACL;UACA,IAAIQ,EAAE,GAAGP,CAAC,CAACC,CAAC,CAAC,KAAKK,IAAI,GAAGP,CAAC,CAACE,CAAC,CAAC,GAAGT,IAAI;UACpC;UACA,IAAIgB,EAAE,GAAGf,EAAE,CAACzB,OAAO,CAACmC,CAAC,CAAC,EAAEI,EAAE,CAAC;UAC3B;UACA,IAAI,CAAChB,EAAE,CAACiB,EAAE,EAAEhB,IAAI,CAAC,EAAE;YACjB;YACAK,MAAM,CAACY,IAAI,CAACR,CAAC,CAAC;YACd;YACAL,OAAO,CAACa,IAAI,CAACD,EAAE,CAAC;UAClB;QACF,CAAC,MAAM;UACL;UACAX,MAAM,CAACY,IAAI,CAACR,CAAC,CAAC;QAChB;MACF;IACF;IACA;IACAH,IAAI,CAACT,OAAO,CAAC,GAAGQ,MAAM,CAACX,MAAM;;IAE7B;IACA,OAAOrB,CAAC,CAAC6C,kBAAkB,CAAC;MAC1BC,MAAM,EAAEf,OAAO;MACfgB,KAAK,EAAEf,MAAM;MACbgB,GAAG,EAAEf,IAAI;MACTgB,IAAI,EAAE,CAAC1B,IAAI,EAAEC,OAAO,CAAC;MACrB0B,QAAQ,EAAEvC,GAAG,KAAKX,CAAC,CAACY,SAAS,IAAIQ,GAAG,KAAKnB,CAAC,CAACW,SAAS,GAAGa,EAAE,GAAGX;IAC9D,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}