{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createOr } from '../../factoriesAny.js';\nexport var orDependencies = {\n  DenseMatrixDependencies,\n  concatDependencies,\n  equalScalarDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createOr\n};", "map": {"version": 3, "names": ["DenseMatrixDependencies", "concatDependencies", "equalScalarDependencies", "matrixDependencies", "typedDependencies", "createOr", "orDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesOr.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createOr } from '../../factoriesAny.js';\nexport var orDependencies = {\n  DenseMatrixDependencies,\n  concatDependencies,\n  equalScalarDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createOr\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,OAAO,IAAIC,cAAc,GAAG;EAC1BN,uBAAuB;EACvBC,kBAAkB;EAClBC,uBAAuB;EACvBC,kBAAkB;EAClBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}