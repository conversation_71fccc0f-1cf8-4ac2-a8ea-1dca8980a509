{"ast": null, "code": "export var rotationMatrixDocs = {\n  name: 'rotationMatrix',\n  category: 'Matrix',\n  syntax: ['rotationMatrix(theta)', 'rotationMatrix(theta, v)', 'rotationMatrix(theta, v, format)'],\n  description: 'Returns a 2-D rotation matrix (2x2) for a given angle (in radians). ' + 'Returns a 2-D rotation matrix (3x3) of a given angle (in radians) around given axis.',\n  examples: ['rotationMatrix(pi / 2)', 'rotationMatrix(unit(\"45deg\"), [0, 0, 1])', 'rotationMatrix(1, matrix([0, 0, 1]), \"sparse\")'],\n  seealso: ['cos', 'sin']\n};", "map": {"version": 3, "names": ["rotationMatrixDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/rotationMatrix.js"], "sourcesContent": ["export var rotationMatrixDocs = {\n  name: 'rotationMatrix',\n  category: 'Matrix',\n  syntax: ['rotationMatrix(theta)', 'rotationMatrix(theta, v)', 'rotationMatrix(theta, v, format)'],\n  description: 'Returns a 2-D rotation matrix (2x2) for a given angle (in radians). ' + 'Returns a 2-D rotation matrix (3x3) of a given angle (in radians) around given axis.',\n  examples: ['rotationMatrix(pi / 2)', 'rotationMatrix(unit(\"45deg\"), [0, 0, 1])', 'rotationMatrix(1, matrix([0, 0, 1]), \"sparse\")'],\n  seealso: ['cos', 'sin']\n};"], "mappings": "AAAA,OAAO,IAAIA,kBAAkB,GAAG;EAC9BC,IAAI,EAAE,gBAAgB;EACtBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,uBAAuB,EAAE,0BAA0B,EAAE,kCAAkC,CAAC;EACjGC,WAAW,EAAE,sEAAsE,GAAG,sFAAsF;EAC5KC,QAAQ,EAAE,CAAC,wBAAwB,EAAE,0CAA0C,EAAE,gDAAgD,CAAC;EAClIC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}