{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport { NotificationProvider } from './components/NotificationSystem';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport HomePage from './pages/HomePage';\nimport SolverPage from './pages/SolverPage';\nimport VisualizerPage from './pages/VisualizerPage';\nimport ExercisesPage from './pages/ExercisesPage';\nimport ExerciseDetailPage from './pages/ExerciseDetailPage';\nimport ProgressPage from './pages/ProgressPage';\nimport CoursesPage from './pages/CoursesPage';\nimport CourseDetailPage from './pages/CourseDetailPage';\nimport LessonDetailPage from './pages/LessonDetailPage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\nimport StudentDashboard from './pages/student/StudentDashboard';\nimport AdminDashboard from './pages/AdminDashboard';\nimport AdminUsers from './pages/AdminUsers';\nimport AdminCourses from './pages/AdminCourses';\nimport AdminExercises from './pages/AdminExercises';\nimport AdminAnalytics from './pages/AdminAnalytics';\nimport ContactMessagesPage from './pages/admin/ContactMessagesPage';\nimport ProfilePage from './pages/ProfilePage';\nimport SettingsPage from './pages/SettingsPage';\nimport ContactPage from './pages/ContactPage';\nimport AboutPage from './pages/AboutPage';\nimport NotFoundPage from './pages/errors/NotFoundPage';\nimport Footer from './components/Footer';\nimport axios from 'axios';\n\n// Configuration globale d'axios\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\naxios.defaults.baseURL = 'http://localhost:8000'; // URL de base pour toutes les requêtes\n\n// Intercepteur pour ajouter le token d'authentification\naxios.interceptors.request.use(config => {\n  const token = localStorage.getItem('authToken');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Intercepteur pour gérer les erreurs de réponse\naxios.interceptors.response.use(response => {\n  return response;\n}, error => {\n  console.error('Erreur Axios:', error);\n  if (error.response) {\n    console.error('Données de réponse:', error.response.data);\n    console.error('Statut:', error.response.status);\n  }\n  return Promise.reject(error);\n});\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    children: /*#__PURE__*/_jsxDEV(NotificationProvider, {\n      children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n        children: /*#__PURE__*/_jsxDEV(Router, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"min-h-screen flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 36\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/solver\",\n                element: /*#__PURE__*/_jsxDEV(SolverPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/visualizer\",\n                element: /*#__PURE__*/_jsxDEV(VisualizerPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 46\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/exercises\",\n                element: /*#__PURE__*/_jsxDEV(ExercisesPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/exercises/:id\",\n                element: /*#__PURE__*/_jsxDEV(ExerciseDetailPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/progress\",\n                element: /*#__PURE__*/_jsxDEV(ProgressPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 44\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/courses\",\n                element: /*#__PURE__*/_jsxDEV(CoursesPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 43\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/courses/:id\",\n                element: /*#__PURE__*/_jsxDEV(CourseDetailPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 47\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/lessons/:id\",\n                element: /*#__PURE__*/_jsxDEV(LessonDetailPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 47\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/login\",\n                element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/register\",\n                element: /*#__PURE__*/_jsxDEV(RegisterPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 44\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/etudiant/dashboard\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  roles: ['student'],\n                  children: /*#__PURE__*/_jsxDEV(StudentDashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 94,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/admin\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  roles: ['admin'],\n                  children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 102,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/admin/dashboard\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  roles: ['admin'],\n                  children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/admin/users\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  roles: ['admin'],\n                  children: /*#__PURE__*/_jsxDEV(AdminUsers, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/admin/courses\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  roles: ['admin'],\n                  children: /*#__PURE__*/_jsxDEV(AdminCourses, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 126,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/admin/exercises\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  roles: ['admin'],\n                  children: /*#__PURE__*/_jsxDEV(AdminExercises, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/admin/analytics\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  roles: ['admin'],\n                  children: /*#__PURE__*/_jsxDEV(AdminAnalytics, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/admin/contact\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  roles: ['admin'],\n                  children: /*#__PURE__*/_jsxDEV(ContactMessagesPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/profile\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  roles: ['student', 'admin'],\n                  children: /*#__PURE__*/_jsxDEV(ProfilePage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/settings\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  roles: ['student', 'admin'],\n                  children: /*#__PURE__*/_jsxDEV(SettingsPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/contact\",\n                element: /*#__PURE__*/_jsxDEV(ContactPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 43\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/about\",\n                element: /*#__PURE__*/_jsxDEV(AboutPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"*\",\n                element: /*#__PURE__*/_jsxDEV(NotFoundPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 36\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "ThemeProvider", "NotificationProvider", "ProtectedRoute", "HomePage", "SolverPage", "VisualizerPage", "ExercisesPage", "ExerciseDetailPage", "ProgressPage", "CoursesPage", "CourseDetailPage", "LessonDetailPage", "LoginPage", "RegisterPage", "StudentDashboard", "AdminDashboard", "AdminUsers", "AdminCourses", "AdminExercises", "AdminAnalytics", "ContactMessagesPage", "ProfilePage", "SettingsPage", "ContactPage", "AboutPage", "NotFoundPage", "Footer", "axios", "jsxDEV", "_jsxDEV", "defaults", "baseURL", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "response", "console", "data", "status", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "roles", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport { NotificationProvider } from './components/NotificationSystem';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport HomePage from './pages/HomePage';\nimport SolverPage from './pages/SolverPage';\nimport VisualizerPage from './pages/VisualizerPage';\nimport ExercisesPage from './pages/ExercisesPage';\nimport ExerciseDetailPage from './pages/ExerciseDetailPage';\nimport ProgressPage from './pages/ProgressPage';\nimport CoursesPage from './pages/CoursesPage';\nimport CourseDetailPage from './pages/CourseDetailPage';\nimport LessonDetailPage from './pages/LessonDetailPage';\nimport TestLoginPage from './pages/TestLoginPage';\nimport LoginPage from './pages/LoginPage';\nimport RegisterPage from './pages/RegisterPage';\n\nimport StudentDashboard from './pages/student/StudentDashboard';\nimport AdminDashboard from './pages/AdminDashboard';\nimport AdminUsers from './pages/AdminUsers';\nimport AdminCourses from './pages/AdminCourses';\nimport AdminExercises from './pages/AdminExercises';\nimport AdminAnalytics from './pages/AdminAnalytics';\nimport ContactMessagesPage from './pages/admin/ContactMessagesPage';\nimport ProfilePage from './pages/ProfilePage';\nimport SettingsPage from './pages/SettingsPage';\nimport ContactPage from './pages/ContactPage';\nimport AboutPage from './pages/AboutPage';\nimport NotFoundPage from './pages/errors/NotFoundPage';\nimport Footer from './components/Footer';\n\n\nimport axios from 'axios';\n\n// Configuration globale d'axios\naxios.defaults.baseURL = 'http://localhost:8000';  // URL de base pour toutes les requêtes\n\n// Intercepteur pour ajouter le token d'authentification\naxios.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('authToken');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Intercepteur pour gérer les erreurs de réponse\naxios.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    console.error('Erreur Axios:', error);\n    if (error.response) {\n      console.error('Données de réponse:', error.response.data);\n      console.error('Statut:', error.response.status);\n    }\n    return Promise.reject(error);\n  }\n);\n\nfunction App() {\n  return (\n    <ThemeProvider>\n      <NotificationProvider>\n        <AuthProvider>\n          <Router>\n            <div className=\"min-h-screen flex flex-col\">\n            <Routes>\n          <Route path=\"/\" element={<HomePage />} />\n          <Route path=\"/solver\" element={<SolverPage />} />\n          <Route path=\"/visualizer\" element={<VisualizerPage />} />\n          <Route path=\"/exercises\" element={<ExercisesPage />} />\n          <Route path=\"/exercises/:id\" element={<ExerciseDetailPage />} />\n          <Route path=\"/progress\" element={<ProgressPage />} />\n          <Route path=\"/courses\" element={<CoursesPage />} />\n          <Route path=\"/courses/:id\" element={<CourseDetailPage />} />\n          <Route path=\"/lessons/:id\" element={<LessonDetailPage />} />\n          <Route path=\"/login\" element={<LoginPage />} />\n          <Route path=\"/register\" element={<RegisterPage />} />\n\n          {/* Routes protégées */}\n          <Route\n            path=\"/etudiant/dashboard\"\n            element={\n              <ProtectedRoute roles={['student']}>\n                <StudentDashboard />\n              </ProtectedRoute>\n            }\n          />\n          <Route\n            path=\"/admin\"\n            element={\n              <ProtectedRoute roles={['admin']}>\n                <AdminDashboard />\n              </ProtectedRoute>\n            }\n          />\n          <Route\n            path=\"/admin/dashboard\"\n            element={\n              <ProtectedRoute roles={['admin']}>\n                <AdminDashboard />\n              </ProtectedRoute>\n            }\n          />\n          <Route\n            path=\"/admin/users\"\n            element={\n              <ProtectedRoute roles={['admin']}>\n                <AdminUsers />\n              </ProtectedRoute>\n            }\n          />\n          <Route\n            path=\"/admin/courses\"\n            element={\n              <ProtectedRoute roles={['admin']}>\n                <AdminCourses />\n              </ProtectedRoute>\n            }\n          />\n          <Route\n            path=\"/admin/exercises\"\n            element={\n              <ProtectedRoute roles={['admin']}>\n                <AdminExercises />\n              </ProtectedRoute>\n            }\n          />\n          <Route\n            path=\"/admin/analytics\"\n            element={\n              <ProtectedRoute roles={['admin']}>\n                <AdminAnalytics />\n              </ProtectedRoute>\n            }\n          />\n          <Route\n            path=\"/admin/contact\"\n            element={\n              <ProtectedRoute roles={['admin']}>\n                <ContactMessagesPage />\n              </ProtectedRoute>\n            }\n          />\n\n          {/* Pages utilisateur protégées */}\n          <Route\n            path=\"/profile\"\n            element={\n              <ProtectedRoute roles={['student', 'admin']}>\n                <ProfilePage />\n              </ProtectedRoute>\n            }\n          />\n          <Route\n            path=\"/settings\"\n            element={\n              <ProtectedRoute roles={['student', 'admin']}>\n                <SettingsPage />\n              </ProtectedRoute>\n            }\n          />\n\n          {/* Pages publiques */}\n          <Route path=\"/contact\" element={<ContactPage />} />\n          <Route path=\"/about\" element={<AboutPage />} />\n\n          {/* Page 404 - doit être en dernier */}\n          <Route path=\"*\" element={<NotFoundPage />} />\n        </Routes>\n        <Footer />\n        </div>\n        </Router>\n      </AuthProvider>\n    </NotificationProvider>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,gBAAgB,MAAM,0BAA0B;AAEvD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAE/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,6BAA6B;AACtD,OAAOC,MAAM,MAAM,qBAAqB;AAGxC,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAF,KAAK,CAACG,QAAQ,CAACC,OAAO,GAAG,uBAAuB,CAAC,CAAE;;AAEnD;AACAJ,KAAK,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAC3BC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC/C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAM,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAd,KAAK,CAACK,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC5BU,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACTI,OAAO,CAACJ,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;EACrC,IAAIA,KAAK,CAACG,QAAQ,EAAE;IAClBC,OAAO,CAACJ,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAACG,QAAQ,CAACE,IAAI,CAAC;IACzDD,OAAO,CAACJ,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACG,QAAQ,CAACG,MAAM,CAAC;EACjD;EACA,OAAOL,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,SAASO,GAAGA,CAAA,EAAG;EACb,oBACEnB,OAAA,CAAC7B,aAAa;IAAAiD,QAAA,eACZpB,OAAA,CAAC5B,oBAAoB;MAAAgD,QAAA,eACnBpB,OAAA,CAAC9B,YAAY;QAAAkD,QAAA,eACXpB,OAAA,CAACjC,MAAM;UAAAqD,QAAA,eACLpB,OAAA;YAAKqB,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBAC3CpB,OAAA,CAAChC,MAAM;cAAAoD,QAAA,gBACTpB,OAAA,CAAC/B,KAAK;gBAACqD,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAEvB,OAAA,CAAC1B,QAAQ;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC3B,OAAA,CAAC/B,KAAK;gBAACqD,IAAI,EAAC,SAAS;gBAACC,OAAO,eAAEvB,OAAA,CAACzB,UAAU;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjD3B,OAAA,CAAC/B,KAAK;gBAACqD,IAAI,EAAC,aAAa;gBAACC,OAAO,eAAEvB,OAAA,CAACxB,cAAc;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzD3B,OAAA,CAAC/B,KAAK;gBAACqD,IAAI,EAAC,YAAY;gBAACC,OAAO,eAAEvB,OAAA,CAACvB,aAAa;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvD3B,OAAA,CAAC/B,KAAK;gBAACqD,IAAI,EAAC,gBAAgB;gBAACC,OAAO,eAAEvB,OAAA,CAACtB,kBAAkB;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChE3B,OAAA,CAAC/B,KAAK;gBAACqD,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAEvB,OAAA,CAACrB,YAAY;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrD3B,OAAA,CAAC/B,KAAK;gBAACqD,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAEvB,OAAA,CAACpB,WAAW;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnD3B,OAAA,CAAC/B,KAAK;gBAACqD,IAAI,EAAC,cAAc;gBAACC,OAAO,eAAEvB,OAAA,CAACnB,gBAAgB;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5D3B,OAAA,CAAC/B,KAAK;gBAACqD,IAAI,EAAC,cAAc;gBAACC,OAAO,eAAEvB,OAAA,CAAClB,gBAAgB;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5D3B,OAAA,CAAC/B,KAAK;gBAACqD,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAEvB,OAAA,CAACjB,SAAS;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/C3B,OAAA,CAAC/B,KAAK;gBAACqD,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAEvB,OAAA,CAAChB,YAAY;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAGrD3B,OAAA,CAAC/B,KAAK;gBACJqD,IAAI,EAAC,qBAAqB;gBAC1BC,OAAO,eACLvB,OAAA,CAAC3B,cAAc;kBAACuD,KAAK,EAAE,CAAC,SAAS,CAAE;kBAAAR,QAAA,eACjCpB,OAAA,CAACf,gBAAgB;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF3B,OAAA,CAAC/B,KAAK;gBACJqD,IAAI,EAAC,QAAQ;gBACbC,OAAO,eACLvB,OAAA,CAAC3B,cAAc;kBAACuD,KAAK,EAAE,CAAC,OAAO,CAAE;kBAAAR,QAAA,eAC/BpB,OAAA,CAACd,cAAc;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF3B,OAAA,CAAC/B,KAAK;gBACJqD,IAAI,EAAC,kBAAkB;gBACvBC,OAAO,eACLvB,OAAA,CAAC3B,cAAc;kBAACuD,KAAK,EAAE,CAAC,OAAO,CAAE;kBAAAR,QAAA,eAC/BpB,OAAA,CAACd,cAAc;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF3B,OAAA,CAAC/B,KAAK;gBACJqD,IAAI,EAAC,cAAc;gBACnBC,OAAO,eACLvB,OAAA,CAAC3B,cAAc;kBAACuD,KAAK,EAAE,CAAC,OAAO,CAAE;kBAAAR,QAAA,eAC/BpB,OAAA,CAACb,UAAU;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF3B,OAAA,CAAC/B,KAAK;gBACJqD,IAAI,EAAC,gBAAgB;gBACrBC,OAAO,eACLvB,OAAA,CAAC3B,cAAc;kBAACuD,KAAK,EAAE,CAAC,OAAO,CAAE;kBAAAR,QAAA,eAC/BpB,OAAA,CAACZ,YAAY;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF3B,OAAA,CAAC/B,KAAK;gBACJqD,IAAI,EAAC,kBAAkB;gBACvBC,OAAO,eACLvB,OAAA,CAAC3B,cAAc;kBAACuD,KAAK,EAAE,CAAC,OAAO,CAAE;kBAAAR,QAAA,eAC/BpB,OAAA,CAACX,cAAc;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF3B,OAAA,CAAC/B,KAAK;gBACJqD,IAAI,EAAC,kBAAkB;gBACvBC,OAAO,eACLvB,OAAA,CAAC3B,cAAc;kBAACuD,KAAK,EAAE,CAAC,OAAO,CAAE;kBAAAR,QAAA,eAC/BpB,OAAA,CAACV,cAAc;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF3B,OAAA,CAAC/B,KAAK;gBACJqD,IAAI,EAAC,gBAAgB;gBACrBC,OAAO,eACLvB,OAAA,CAAC3B,cAAc;kBAACuD,KAAK,EAAE,CAAC,OAAO,CAAE;kBAAAR,QAAA,eAC/BpB,OAAA,CAACT,mBAAmB;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGF3B,OAAA,CAAC/B,KAAK;gBACJqD,IAAI,EAAC,UAAU;gBACfC,OAAO,eACLvB,OAAA,CAAC3B,cAAc;kBAACuD,KAAK,EAAE,CAAC,SAAS,EAAE,OAAO,CAAE;kBAAAR,QAAA,eAC1CpB,OAAA,CAACR,WAAW;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF3B,OAAA,CAAC/B,KAAK;gBACJqD,IAAI,EAAC,WAAW;gBAChBC,OAAO,eACLvB,OAAA,CAAC3B,cAAc;kBAACuD,KAAK,EAAE,CAAC,SAAS,EAAE,OAAO,CAAE;kBAAAR,QAAA,eAC1CpB,OAAA,CAACP,YAAY;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGF3B,OAAA,CAAC/B,KAAK;gBAACqD,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAEvB,OAAA,CAACN,WAAW;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnD3B,OAAA,CAAC/B,KAAK;gBAACqD,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAEvB,OAAA,CAACL,SAAS;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAG/C3B,OAAA,CAAC/B,KAAK;gBAACqD,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAEvB,OAAA,CAACJ,YAAY;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACT3B,OAAA,CAACH,MAAM;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEpB;AAACE,EAAA,GAtHQV,GAAG;AAwHZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}