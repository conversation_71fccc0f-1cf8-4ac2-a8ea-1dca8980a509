{"ast": null, "code": "export var cosDocs = {\n  name: 'cos',\n  category: 'Trigonometry',\n  syntax: ['cos(x)'],\n  description: 'Compute the cosine of x in radians.',\n  examples: ['cos(2)', 'cos(pi / 4) ^ 2', 'cos(180 deg)', 'cos(60 deg)', 'sin(0.2)^2 + cos(0.2)^2'],\n  seealso: ['acos', 'sin', 'tan']\n};", "map": {"version": 3, "names": ["cosDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/cos.js"], "sourcesContent": ["export var cosDocs = {\n  name: 'cos',\n  category: 'Trigonometry',\n  syntax: ['cos(x)'],\n  description: 'Compute the cosine of x in radians.',\n  examples: ['cos(2)', 'cos(pi / 4) ^ 2', 'cos(180 deg)', 'cos(60 deg)', 'sin(0.2)^2 + cos(0.2)^2'],\n  seealso: ['acos', 'sin', 'tan']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,qCAAqC;EAClDC,QAAQ,EAAE,CAAC,QAAQ,EAAE,iBAAiB,EAAE,cAAc,EAAE,aAAa,EAAE,yBAAyB,CAAC;EACjGC,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}