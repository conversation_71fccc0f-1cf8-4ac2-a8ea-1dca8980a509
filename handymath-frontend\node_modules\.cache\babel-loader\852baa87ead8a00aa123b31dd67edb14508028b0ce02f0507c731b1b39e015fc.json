{"ast": null, "code": "export var columnDocs = {\n  name: 'column',\n  category: 'Matrix',\n  syntax: ['column(x, index)'],\n  description: 'Return a column from a matrix or array.',\n  examples: ['A = [[1, 2], [3, 4]]', 'column(A, 1)', 'column(A, 2)'],\n  seealso: ['row', 'matrixFromColumns']\n};", "map": {"version": 3, "names": ["columnDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/column.js"], "sourcesContent": ["export var columnDocs = {\n  name: 'column',\n  category: 'Matrix',\n  syntax: ['column(x, index)'],\n  description: 'Return a column from a matrix or array.',\n  examples: ['A = [[1, 2], [3, 4]]', 'column(A, 1)', 'column(A, 2)'],\n  seealso: ['row', 'matrixFromColumns']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,kBAAkB,CAAC;EAC5BC,WAAW,EAAE,yCAAyC;EACtDC,QAAQ,EAAE,CAAC,sBAAsB,EAAE,cAAc,EAAE,cAAc,CAAC;EAClEC,OAAO,EAAE,CAAC,KAAK,EAAE,mBAAmB;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}