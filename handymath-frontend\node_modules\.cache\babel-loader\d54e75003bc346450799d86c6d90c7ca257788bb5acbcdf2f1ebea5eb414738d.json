{"ast": null, "code": "export var isNegativeDocs = {\n  name: 'isNegative',\n  category: 'Utils',\n  syntax: ['isNegative(x)'],\n  description: 'Test whether a value is negative: smaller than zero.',\n  examples: ['isNegative(2)', 'isNegative(0)', 'isNegative(-4)', 'isNegative([3, 0.5, -2])'],\n  seealso: ['isInteger', 'isNumeric', 'isPositive', 'isZero']\n};", "map": {"version": 3, "names": ["isNegativeDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/isNegative.js"], "sourcesContent": ["export var isNegativeDocs = {\n  name: 'isNegative',\n  category: 'Utils',\n  syntax: ['isNegative(x)'],\n  description: 'Test whether a value is negative: smaller than zero.',\n  examples: ['isNegative(2)', 'isNegative(0)', 'isNegative(-4)', 'isNegative([3, 0.5, -2])'],\n  seealso: ['isInteger', 'isNumeric', 'isPositive', 'isZero']\n};"], "mappings": "AAAA,OAAO,IAAIA,cAAc,GAAG;EAC1BC,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,eAAe,CAAC;EACzBC,WAAW,EAAE,sDAAsD;EACnEC,QAAQ,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,0BAA0B,CAAC;EAC1FC,OAAO,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ;AAC5D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}