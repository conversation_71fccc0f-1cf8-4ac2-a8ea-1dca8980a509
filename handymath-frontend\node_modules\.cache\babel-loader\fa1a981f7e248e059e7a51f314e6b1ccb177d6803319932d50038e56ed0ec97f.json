{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\CourseDetailPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CourseDetailPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useAuth();\n  const {\n    showSuccess,\n    showError\n  } = useNotifications();\n  const [course, setCourse] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [enrolling, setEnrolling] = useState(false);\n  useEffect(() => {\n    if (user && id) {\n      fetchCourse();\n    }\n  }, [user, id]);\n  const fetchCourse = async () => {\n    try {\n      setLoading(true);\n      setTimeout(() => {\n        setCourse({\n          id: parseInt(id || '1'),\n          title: 'Algèbre de base',\n          description: 'Apprenez les concepts fondamentaux de l\\'algèbre avec des exercices pratiques et des exemples concrets.',\n          level: 'beginner',\n          level_display: 'Débutant',\n          thumbnail: '📚',\n          estimated_duration: 180,\n          progress_percentage: 45,\n          is_enrolled: true,\n          enrollment_date: '2024-01-15T10:30:00Z',\n          chapters: [{\n            id: 1,\n            title: 'Introduction à l\\'algèbre',\n            description: 'Les bases de l\\'algèbre et les variables',\n            order: 1,\n            progress_percentage: 100,\n            lessons: [{\n              id: 1,\n              title: 'Qu\\'est-ce que l\\'algèbre ?',\n              lesson_type: 'theory',\n              type_display: 'Théorie',\n              order: 1,\n              estimated_duration: 15,\n              is_accessible: true,\n              is_completed: true,\n              time_spent: 18\n            }, {\n              id: 2,\n              title: 'Les variables et les constantes',\n              lesson_type: 'theory',\n              type_display: 'Théorie',\n              order: 2,\n              estimated_duration: 20,\n              is_accessible: true,\n              is_completed: true,\n              time_spent: 22\n            }]\n          }, {\n            id: 2,\n            title: 'Équations du premier degré',\n            description: 'Résolution d\\'équations simples',\n            order: 2,\n            progress_percentage: 60,\n            lessons: [{\n              id: 3,\n              title: 'Résoudre x + 5 = 12',\n              lesson_type: 'example',\n              type_display: 'Exemple',\n              order: 1,\n              estimated_duration: 25,\n              is_accessible: true,\n              is_completed: true,\n              time_spent: 30,\n              exercise_id: 1\n            }, {\n              id: 4,\n              title: 'Équations avec multiplication',\n              lesson_type: 'theory',\n              type_display: 'Théorie',\n              order: 2,\n              estimated_duration: 30,\n              is_accessible: true,\n              is_completed: false,\n              time_spent: 0\n            }]\n          }],\n          prerequisites: [],\n          created_at: '2024-01-15T10:30:00Z'\n        });\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      console.error('Erreur lors de la récupération du cours:', error);\n      setLoading(false);\n    }\n  };\n  const handleEnroll = async () => {\n    if (!course) return;\n    try {\n      setEnrolling(true);\n      setTimeout(() => {\n        setCourse({\n          ...course,\n          is_enrolled: true\n        });\n        setEnrolling(false);\n      }, 1000);\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      setEnrolling(false);\n    }\n  };\n  const getLevelColor = level => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n  const formatDuration = minutes => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Acc\\xE8s restreint\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Vous devez \\xEAtre connect\\xE9 pour acc\\xE9der \\xE0 ce cours.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Chargement du cours...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this);\n  }\n  if (!course) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Cours non trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Le cours demand\\xE9 n'existe pas ou n'est plus disponible.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/courses'),\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Retour aux cours\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => navigate('/courses'),\n      className: \"mb-6 flex items-center text-primary-600 hover:text-primary-700 transition-colors\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"mr-2\",\n        children: \"\\u2190\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), \"Retour aux cours\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start justify-between mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-4xl mr-4\",\n              children: course.thumbnail\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                children: course.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-block px-3 py-1 rounded-full text-sm font-medium mt-2 ${getLevelColor(course.level)}`,\n                children: course.level_display\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-700 dark:text-gray-300 mb-6\",\n          children: course.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Chapitres:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 21\n              }, this), \" \", course.chapters.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Le\\xE7ons:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 21\n              }, this), \" \", course.chapters.reduce((total, chapter) => total + chapter.lessons.length, 0)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2\",\n              children: \"\\u23F1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Dur\\xE9e:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 21\n              }, this), \" \", formatDuration(course.estimated_duration)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Progression:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 21\n              }, this), \" \", course.progress_percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), course.is_enrolled && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-sm mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"Progression du cours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [course.progress_percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-primary-600 h-3 rounded-full transition-all duration-300\",\n              style: {\n                width: `${course.progress_percentage}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), !course.is_enrolled && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleEnroll,\n            disabled: enrolling,\n            className: \"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-medium py-3 px-8 rounded-lg transition-colors\",\n            children: enrolling ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 21\n              }, this), \"Inscription...\"]\n            }, void 0, true) : 'S\\'inscrire au cours'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), course.is_enrolled && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: course.chapters.map((chapter, chapterIndex) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: (chapterIndex + 1) * 0.1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n              children: [\"Chapitre \", chapter.order, \": \", chapter.title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600 dark:text-gray-400\",\n              children: [chapter.progress_percentage, \"% termin\\xE9\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 17\n          }, this), chapter.description && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-700 dark:text-gray-300 mb-4\",\n            children: chapter.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-green-500 h-2 rounded-full transition-all duration-300\",\n                style: {\n                  width: `${chapter.progress_percentage}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: chapter.lessons.map(lesson => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center justify-between p-3 rounded-lg border ${lesson.is_completed ? 'bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700' : lesson.is_accessible ? 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600' : 'bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 opacity-60'} transition-colors`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xl mr-3\",\n                  children: lesson.is_completed ? '✅' : lesson.is_accessible ? '📖' : '🔒'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-gray-900 dark:text-white\",\n                    children: lesson.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center text-sm text-gray-600 dark:text-gray-400\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"mr-3\",\n                      children: lesson.type_display\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 382,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"mr-3\",\n                      children: [\"\\u23F1\", lesson.estimated_duration, \"min\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 383,\n                      columnNumber: 29\n                    }, this), lesson.time_spent > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [Math.floor(lesson.time_spent / 60), \"min pass\\xE9es\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: lesson.is_accessible ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => navigate(`/lessons/${lesson.id}`),\n                    className: \"bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors\",\n                    children: lesson.is_completed ? 'Revoir' : 'Commencer'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 29\n                  }, this), lesson.exercise_id && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => navigate(`/exercises/${lesson.exercise_id}`),\n                    className: \"bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors\",\n                    children: \"Exercice\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 31\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-500 dark:text-gray-400\",\n                  children: \"Terminez les le\\xE7ons pr\\xE9c\\xE9dentes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 23\n              }, this)]\n            }, lesson.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 17\n          }, this)]\n        }, chapter.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 243,\n    columnNumber: 5\n  }, this);\n};\n_s(CourseDetailPage, \"jEsl00RHBCKrGrlDvXmUXP8m3BY=\", false, function () {\n  return [useParams, useNavigate, useAuth, useNotifications];\n});\n_c = CourseDetailPage;\nexport default CourseDetailPage;\nvar _c;\n$RefreshReg$(_c, \"CourseDetailPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "motion", "useAuth", "useNotifications", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CourseDetailPage", "_s", "id", "navigate", "user", "showSuccess", "showError", "course", "setCourse", "loading", "setLoading", "enrolling", "setEnrolling", "fetchCourse", "setTimeout", "parseInt", "title", "description", "level", "level_display", "thumbnail", "estimated_duration", "progress_percentage", "is_enrolled", "enrollment_date", "chapters", "order", "lessons", "lesson_type", "type_display", "is_accessible", "is_completed", "time_spent", "exercise_id", "prerequisites", "created_at", "error", "console", "handleEnroll", "getLevelColor", "formatDuration", "minutes", "hours", "Math", "floor", "mins", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "div", "initial", "opacity", "y", "animate", "length", "reduce", "total", "chapter", "style", "width", "disabled", "map", "chapterIndex", "transition", "delay", "lesson", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/pages/CourseDetailPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport api from '../services/api';\n\ninterface Lesson {\n  id: number;\n  title: string;\n  lesson_type: string;\n  type_display: string;\n  order: number;\n  estimated_duration: number;\n  is_accessible: boolean;\n  is_completed: boolean;\n  time_spent: number;\n  exercise_id?: number;\n}\n\ninterface Chapter {\n  id: number;\n  title: string;\n  description: string;\n  order: number;\n  progress_percentage: number;\n  lessons: Lesson[];\n}\n\ninterface Course {\n  id: number;\n  title: string;\n  description: string;\n  level: string;\n  level_display: string;\n  thumbnail: string;\n  estimated_duration: number;\n  progress_percentage: number;\n  is_enrolled: boolean;\n  enrollment_date?: string;\n  chapters: Chapter[];\n  prerequisites: Array<{\n    id: number;\n    title: string;\n    progress: number;\n  }>;\n  created_at: string;\n}\n\nconst CourseDetailPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const { user } = useAuth();\n  const { showSuccess, showError } = useNotifications();\n  const [course, setCourse] = useState<Course | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [enrolling, setEnrolling] = useState(false);\n\n  useEffect(() => {\n    if (user && id) {\n      fetchCourse();\n    }\n  }, [user, id]);\n\n  const fetchCourse = async () => {\n    try {\n      setLoading(true);\n      setTimeout(() => {\n        setCourse({\n          id: parseInt(id || '1'),\n          title: 'Algèbre de base',\n          description: 'Apprenez les concepts fondamentaux de l\\'algèbre avec des exercices pratiques et des exemples concrets.',\n          level: 'beginner',\n          level_display: 'Débutant',\n          thumbnail: '📚',\n          estimated_duration: 180,\n          progress_percentage: 45,\n          is_enrolled: true,\n          enrollment_date: '2024-01-15T10:30:00Z',\n          chapters: [\n            {\n              id: 1,\n              title: 'Introduction à l\\'algèbre',\n              description: 'Les bases de l\\'algèbre et les variables',\n              order: 1,\n              progress_percentage: 100,\n              lessons: [\n                {\n                  id: 1,\n                  title: 'Qu\\'est-ce que l\\'algèbre ?',\n                  lesson_type: 'theory',\n                  type_display: 'Théorie',\n                  order: 1,\n                  estimated_duration: 15,\n                  is_accessible: true,\n                  is_completed: true,\n                  time_spent: 18,\n                },\n                {\n                  id: 2,\n                  title: 'Les variables et les constantes',\n                  lesson_type: 'theory',\n                  type_display: 'Théorie',\n                  order: 2,\n                  estimated_duration: 20,\n                  is_accessible: true,\n                  is_completed: true,\n                  time_spent: 22,\n                }\n              ]\n            },\n            {\n              id: 2,\n              title: 'Équations du premier degré',\n              description: 'Résolution d\\'équations simples',\n              order: 2,\n              progress_percentage: 60,\n              lessons: [\n                {\n                  id: 3,\n                  title: 'Résoudre x + 5 = 12',\n                  lesson_type: 'example',\n                  type_display: 'Exemple',\n                  order: 1,\n                  estimated_duration: 25,\n                  is_accessible: true,\n                  is_completed: true,\n                  time_spent: 30,\n                  exercise_id: 1\n                },\n                {\n                  id: 4,\n                  title: 'Équations avec multiplication',\n                  lesson_type: 'theory',\n                  type_display: 'Théorie',\n                  order: 2,\n                  estimated_duration: 30,\n                  is_accessible: true,\n                  is_completed: false,\n                  time_spent: 0,\n                }\n              ]\n            }\n          ],\n          prerequisites: [],\n          created_at: '2024-01-15T10:30:00Z'\n        });\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      console.error('Erreur lors de la récupération du cours:', error);\n      setLoading(false);\n    }\n  };\n\n  const handleEnroll = async () => {\n    if (!course) return;\n    \n    try {\n      setEnrolling(true);\n      setTimeout(() => {\n        setCourse({ ...course, is_enrolled: true });\n        setEnrolling(false);\n      }, 1000);\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      setEnrolling(false);\n    }\n  };\n\n  const getLevelColor = (level: string) => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n\n  const formatDuration = (minutes: number) => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n\n  if (!user) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Accès restreint</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            Vous devez être connecté pour accéder à ce cours.\n          </p>\n          <a\n            href=\"/login\"\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Se connecter\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Chargement du cours...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!course) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Cours non trouvé</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            Le cours demandé n'existe pas ou n'est plus disponible.\n          </p>\n          <button\n            onClick={() => navigate('/courses')}\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Retour aux cours\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <button\n        onClick={() => navigate('/courses')}\n        className=\"mb-6 flex items-center text-primary-600 hover:text-primary-700 transition-colors\"\n      >\n        <span className=\"mr-2\">←</span>\n        Retour aux cours\n      </button>\n\n      <div className=\"max-w-4xl mx-auto\">\n        <motion.div\n          className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n        >\n          <div className=\"flex items-start justify-between mb-4\">\n            <div className=\"flex items-center\">\n              <span className=\"text-4xl mr-4\">{course.thumbnail}</span>\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                  {course.title}\n                </h1>\n                <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium mt-2 ${getLevelColor(course.level)}`}>\n                  {course.level_display}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          <p className=\"text-gray-700 dark:text-gray-300 mb-6\">\n            {course.description}\n          </p>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm mb-6\">\n            <div className=\"flex items-center\">\n              <span><strong>Chapitres:</strong> {course.chapters.length}</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span><strong>Leçons:</strong> {course.chapters.reduce((total, chapter) => total + chapter.lessons.length, 0)}</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span className=\"mr-2\">⏱</span>\n              <span><strong>Durée:</strong> {formatDuration(course.estimated_duration)}</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span><strong>Progression:</strong> {course.progress_percentage}%</span>\n            </div>\n          </div>\n\n          {course.is_enrolled && (\n            <div className=\"mb-6\">\n              <div className=\"flex justify-between text-sm mb-2\">\n                <span className=\"font-medium\">Progression du cours</span>\n                <span>{course.progress_percentage}%</span>\n              </div>\n              <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3\">\n                <div\n                  className=\"bg-primary-600 h-3 rounded-full transition-all duration-300\"\n                  style={{ width: `${course.progress_percentage}%` }}\n                />\n              </div>\n            </div>\n          )}\n\n          {!course.is_enrolled && (\n            <div className=\"text-center\">\n              <button\n                onClick={handleEnroll}\n                disabled={enrolling}\n                className=\"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-medium py-3 px-8 rounded-lg transition-colors\"\n              >\n                {enrolling ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2\"></div>\n                    Inscription...\n                  </>\n                ) : (\n                  'S\\'inscrire au cours'\n                )}\n              </button>\n            </div>\n          )}\n        </motion.div>\n\n        {course.is_enrolled && (\n          <div className=\"space-y-6\">\n            {course.chapters.map((chapter, chapterIndex) => (\n              <motion.div\n                key={chapter.id}\n                className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: (chapterIndex + 1) * 0.1 }}\n              >\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                    Chapitre {chapter.order}: {chapter.title}\n                  </h2>\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {chapter.progress_percentage}% terminé\n                  </span>\n                </div>\n\n                {chapter.description && (\n                  <p className=\"text-gray-700 dark:text-gray-300 mb-4\">\n                    {chapter.description}\n                  </p>\n                )}\n\n                <div className=\"mb-4\">\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div\n                      className=\"bg-green-500 h-2 rounded-full transition-all duration-300\"\n                      style={{ width: `${chapter.progress_percentage}%` }}\n                    />\n                  </div>\n                </div>\n\n                <div className=\"space-y-3\">\n                  {chapter.lessons.map((lesson) => (\n                    <div\n                      key={lesson.id}\n                      className={`flex items-center justify-between p-3 rounded-lg border ${\n                        lesson.is_completed\n                          ? 'bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700'\n                          : lesson.is_accessible\n                          ? 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'\n                          : 'bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 opacity-60'\n                      } transition-colors`}\n                    >\n                      <div className=\"flex items-center\">\n                        <span className=\"text-xl mr-3\">\n                          {lesson.is_completed ? '✅' : lesson.is_accessible ? '📖' : '🔒'}\n                        </span>\n                        <div>\n                          <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                            {lesson.title}\n                          </h4>\n                          <div className=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                            <span className=\"mr-3\">{lesson.type_display}</span>\n                            <span className=\"mr-3\">⏱{lesson.estimated_duration}min</span>\n                            {lesson.time_spent > 0 && (\n                              <span>{Math.floor(lesson.time_spent / 60)}min passées</span>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center space-x-2\">\n                        {lesson.is_accessible ? (\n                          <>\n                            <button\n                              onClick={() => navigate(`/lessons/${lesson.id}`)}\n                              className=\"bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors\"\n                            >\n                              {lesson.is_completed ? 'Revoir' : 'Commencer'}\n                            </button>\n                            {lesson.exercise_id && (\n                              <button\n                                onClick={() => navigate(`/exercises/${lesson.exercise_id}`)}\n                                className=\"bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors\"\n                              >\n                                Exercice\n                              </button>\n                            )}\n                          </>\n                        ) : (\n                          <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                            Terminez les leçons précédentes\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CourseDetailPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,gBAAgB,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA6CpE,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAG,CAAC,GAAGX,SAAS,CAAiB,CAAC;EAC1C,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY;EAAK,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEW,WAAW;IAAEC;EAAU,CAAC,GAAGX,gBAAgB,CAAC,CAAC;EACrD,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAgB,IAAI,CAAC;EACzD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACd,IAAIc,IAAI,IAAIF,EAAE,EAAE;MACdW,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACT,IAAI,EAAEF,EAAE,CAAC,CAAC;EAEd,MAAMW,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChBI,UAAU,CAAC,MAAM;QACfN,SAAS,CAAC;UACRN,EAAE,EAAEa,QAAQ,CAACb,EAAE,IAAI,GAAG,CAAC;UACvBc,KAAK,EAAE,iBAAiB;UACxBC,WAAW,EAAE,yGAAyG;UACtHC,KAAK,EAAE,UAAU;UACjBC,aAAa,EAAE,UAAU;UACzBC,SAAS,EAAE,IAAI;UACfC,kBAAkB,EAAE,GAAG;UACvBC,mBAAmB,EAAE,EAAE;UACvBC,WAAW,EAAE,IAAI;UACjBC,eAAe,EAAE,sBAAsB;UACvCC,QAAQ,EAAE,CACR;YACEvB,EAAE,EAAE,CAAC;YACLc,KAAK,EAAE,2BAA2B;YAClCC,WAAW,EAAE,0CAA0C;YACvDS,KAAK,EAAE,CAAC;YACRJ,mBAAmB,EAAE,GAAG;YACxBK,OAAO,EAAE,CACP;cACEzB,EAAE,EAAE,CAAC;cACLc,KAAK,EAAE,6BAA6B;cACpCY,WAAW,EAAE,QAAQ;cACrBC,YAAY,EAAE,SAAS;cACvBH,KAAK,EAAE,CAAC;cACRL,kBAAkB,EAAE,EAAE;cACtBS,aAAa,EAAE,IAAI;cACnBC,YAAY,EAAE,IAAI;cAClBC,UAAU,EAAE;YACd,CAAC,EACD;cACE9B,EAAE,EAAE,CAAC;cACLc,KAAK,EAAE,iCAAiC;cACxCY,WAAW,EAAE,QAAQ;cACrBC,YAAY,EAAE,SAAS;cACvBH,KAAK,EAAE,CAAC;cACRL,kBAAkB,EAAE,EAAE;cACtBS,aAAa,EAAE,IAAI;cACnBC,YAAY,EAAE,IAAI;cAClBC,UAAU,EAAE;YACd,CAAC;UAEL,CAAC,EACD;YACE9B,EAAE,EAAE,CAAC;YACLc,KAAK,EAAE,4BAA4B;YACnCC,WAAW,EAAE,iCAAiC;YAC9CS,KAAK,EAAE,CAAC;YACRJ,mBAAmB,EAAE,EAAE;YACvBK,OAAO,EAAE,CACP;cACEzB,EAAE,EAAE,CAAC;cACLc,KAAK,EAAE,qBAAqB;cAC5BY,WAAW,EAAE,SAAS;cACtBC,YAAY,EAAE,SAAS;cACvBH,KAAK,EAAE,CAAC;cACRL,kBAAkB,EAAE,EAAE;cACtBS,aAAa,EAAE,IAAI;cACnBC,YAAY,EAAE,IAAI;cAClBC,UAAU,EAAE,EAAE;cACdC,WAAW,EAAE;YACf,CAAC,EACD;cACE/B,EAAE,EAAE,CAAC;cACLc,KAAK,EAAE,+BAA+B;cACtCY,WAAW,EAAE,QAAQ;cACrBC,YAAY,EAAE,SAAS;cACvBH,KAAK,EAAE,CAAC;cACRL,kBAAkB,EAAE,EAAE;cACtBS,aAAa,EAAE,IAAI;cACnBC,YAAY,EAAE,KAAK;cACnBC,UAAU,EAAE;YACd,CAAC;UAEL,CAAC,CACF;UACDE,aAAa,EAAE,EAAE;UACjBC,UAAU,EAAE;QACd,CAAC,CAAC;QACFzB,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAC/B,MAAM,EAAE;IAEb,IAAI;MACFK,YAAY,CAAC,IAAI,CAAC;MAClBE,UAAU,CAAC,MAAM;QACfN,SAAS,CAAC;UAAE,GAAGD,MAAM;UAAEgB,WAAW,EAAE;QAAK,CAAC,CAAC;QAC3CX,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDxB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM2B,aAAa,GAAIrB,KAAa,IAAK;IACvC,QAAQA,KAAK;MACX,KAAK,UAAU;QACb,OAAO,mEAAmE;MAC5E,KAAK,cAAc;QACjB,OAAO,uEAAuE;MAChF,KAAK,UAAU;QACb,OAAO,2DAA2D;MACpE;QACE,OAAO,+DAA+D;IAC1E;EACF,CAAC;EAED,MAAMsB,cAAc,GAAIC,OAAe,IAAK;IAC1C,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIG,IAAI,GAAG,CAAC,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE,EAAE;IACpD;IACA,OAAO,GAAGA,IAAI,KAAK;EACrB,CAAC;EAED,IAAI,CAACzC,IAAI,EAAE;IACT,oBACEP,OAAA;MAAKiD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1ClD,OAAA;QAAKiD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlD,OAAA;UAAIiD,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5DtD,OAAA;UAAGiD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJtD,OAAA;UACEuD,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI1C,OAAO,EAAE;IACX,oBACEZ,OAAA;MAAKiD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1ClD,OAAA;QAAKiD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChClD,OAAA;UAAKiD,SAAS,EAAC;QAAgF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGtD,OAAA;UAAGiD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAC5C,MAAM,EAAE;IACX,oBACEV,OAAA;MAAKiD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1ClD,OAAA;QAAKiD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlD,OAAA;UAAIiD,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DtD,OAAA;UAAGiD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJtD,OAAA;UACEwD,OAAO,EAAEA,CAAA,KAAMlD,QAAQ,CAAC,UAAU,CAAE;UACpC2C,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtD,OAAA;IAAKiD,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1ClD,OAAA;MACEwD,OAAO,EAAEA,CAAA,KAAMlD,QAAQ,CAAC,UAAU,CAAE;MACpC2C,SAAS,EAAC,kFAAkF;MAAAC,QAAA,gBAE5FlD,OAAA;QAAMiD,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,oBAEjC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAETtD,OAAA;MAAKiD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChClD,OAAA,CAACJ,MAAM,CAAC6D,GAAG;QACTR,SAAS,EAAC,yDAAyD;QACnES,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAV,QAAA,gBAE9BlD,OAAA;UAAKiD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDlD,OAAA;YAAKiD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChClD,OAAA;cAAMiD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAExC,MAAM,CAACa;YAAS;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzDtD,OAAA;cAAAkD,QAAA,gBACElD,OAAA;gBAAIiD,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,EAC7DxC,MAAM,CAACS;cAAK;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACLtD,OAAA;gBAAMiD,SAAS,EAAE,gEAAgEP,aAAa,CAAChC,MAAM,CAACW,KAAK,CAAC,EAAG;gBAAA6B,QAAA,EAC5GxC,MAAM,CAACY;cAAa;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtD,OAAA;UAAGiD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACjDxC,MAAM,CAACU;QAAW;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eAEJtD,OAAA;UAAKiD,SAAS,EAAC,oDAAoD;UAAAC,QAAA,gBACjElD,OAAA;YAAKiD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChClD,OAAA;cAAAkD,QAAA,gBAAMlD,OAAA;gBAAAkD,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC5C,MAAM,CAACkB,QAAQ,CAACkC,MAAM;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACNtD,OAAA;YAAKiD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChClD,OAAA;cAAAkD,QAAA,gBAAMlD,OAAA;gBAAAkD,QAAA,EAAQ;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC5C,MAAM,CAACkB,QAAQ,CAACmC,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAKD,KAAK,GAAGC,OAAO,CAACnC,OAAO,CAACgC,MAAM,EAAE,CAAC,CAAC;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClH,CAAC,eACNtD,OAAA;YAAKiD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChClD,OAAA;cAAMiD,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/BtD,OAAA;cAAAkD,QAAA,gBAAMlD,OAAA;gBAAAkD,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACX,cAAc,CAACjC,MAAM,CAACc,kBAAkB,CAAC;YAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eACNtD,OAAA;YAAKiD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChClD,OAAA;cAAAkD,QAAA,gBAAMlD,OAAA;gBAAAkD,QAAA,EAAQ;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC5C,MAAM,CAACe,mBAAmB,EAAC,GAAC;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL5C,MAAM,CAACgB,WAAW,iBACjB1B,OAAA;UAAKiD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlD,OAAA;YAAKiD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDlD,OAAA;cAAMiD,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzDtD,OAAA;cAAAkD,QAAA,GAAOxC,MAAM,CAACe,mBAAmB,EAAC,GAAC;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNtD,OAAA;YAAKiD,SAAS,EAAC,sDAAsD;YAAAC,QAAA,eACnElD,OAAA;cACEiD,SAAS,EAAC,6DAA6D;cACvEiB,KAAK,EAAE;gBAAEC,KAAK,EAAE,GAAGzD,MAAM,CAACe,mBAAmB;cAAI;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA,CAAC5C,MAAM,CAACgB,WAAW,iBAClB1B,OAAA;UAAKiD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BlD,OAAA;YACEwD,OAAO,EAAEf,YAAa;YACtB2B,QAAQ,EAAEtD,SAAU;YACpBmC,SAAS,EAAC,2HAA2H;YAAAC,QAAA,EAEpIpC,SAAS,gBACRd,OAAA,CAAAE,SAAA;cAAAgD,QAAA,gBACElD,OAAA;gBAAKiD,SAAS,EAAC;cAA6E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,kBAErG;YAAA,eAAE,CAAC,GAEH;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,EAEZ5C,MAAM,CAACgB,WAAW,iBACjB1B,OAAA;QAAKiD,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBxC,MAAM,CAACkB,QAAQ,CAACyC,GAAG,CAAC,CAACJ,OAAO,EAAEK,YAAY,kBACzCtE,OAAA,CAACJ,MAAM,CAAC6D,GAAG;UAETR,SAAS,EAAC,oDAAoD;UAC9DS,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BW,UAAU,EAAE;YAAEC,KAAK,EAAE,CAACF,YAAY,GAAG,CAAC,IAAI;UAAI,CAAE;UAAApB,QAAA,gBAEhDlD,OAAA;YAAKiD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDlD,OAAA;cAAIiD,SAAS,EAAC,qDAAqD;cAAAC,QAAA,GAAC,WACzD,EAACe,OAAO,CAACpC,KAAK,EAAC,IAAE,EAACoC,OAAO,CAAC9C,KAAK;YAAA;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACLtD,OAAA;cAAMiD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,GACvDe,OAAO,CAACxC,mBAAmB,EAAC,cAC/B;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAELW,OAAO,CAAC7C,WAAW,iBAClBpB,OAAA;YAAGiD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACjDe,OAAO,CAAC7C;UAAW;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CACJ,eAEDtD,OAAA;YAAKiD,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBlD,OAAA;cAAKiD,SAAS,EAAC,sDAAsD;cAAAC,QAAA,eACnElD,OAAA;gBACEiD,SAAS,EAAC,2DAA2D;gBACrEiB,KAAK,EAAE;kBAAEC,KAAK,EAAE,GAAGF,OAAO,CAACxC,mBAAmB;gBAAI;cAAE;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtD,OAAA;YAAKiD,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBe,OAAO,CAACnC,OAAO,CAACuC,GAAG,CAAEI,MAAM,iBAC1BzE,OAAA;cAEEiD,SAAS,EAAE,2DACTwB,MAAM,CAACvC,YAAY,GACf,sEAAsE,GACtEuC,MAAM,CAACxC,aAAa,GACpB,2GAA2G,GAC3G,8EAA8E,oBAC/D;cAAAiB,QAAA,gBAErBlD,OAAA;gBAAKiD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChClD,OAAA;kBAAMiD,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAC3BuB,MAAM,CAACvC,YAAY,GAAG,GAAG,GAAGuC,MAAM,CAACxC,aAAa,GAAG,IAAI,GAAG;gBAAI;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACPtD,OAAA;kBAAAkD,QAAA,gBACElD,OAAA;oBAAIiD,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EACtDuB,MAAM,CAACtD;kBAAK;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACLtD,OAAA;oBAAKiD,SAAS,EAAC,4DAA4D;oBAAAC,QAAA,gBACzElD,OAAA;sBAAMiD,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAAEuB,MAAM,CAACzC;oBAAY;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACnDtD,OAAA;sBAAMiD,SAAS,EAAC,MAAM;sBAAAC,QAAA,GAAC,QAAC,EAACuB,MAAM,CAACjD,kBAAkB,EAAC,KAAG;oBAAA;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAC5DmB,MAAM,CAACtC,UAAU,GAAG,CAAC,iBACpBnC,OAAA;sBAAAkD,QAAA,GAAOJ,IAAI,CAACC,KAAK,CAAC0B,MAAM,CAACtC,UAAU,GAAG,EAAE,CAAC,EAAC,gBAAW;oBAAA;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAC5D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtD,OAAA;gBAAKiD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EACzCuB,MAAM,CAACxC,aAAa,gBACnBjC,OAAA,CAAAE,SAAA;kBAAAgD,QAAA,gBACElD,OAAA;oBACEwD,OAAO,EAAEA,CAAA,KAAMlD,QAAQ,CAAC,YAAYmE,MAAM,CAACpE,EAAE,EAAE,CAAE;oBACjD4C,SAAS,EAAC,2GAA2G;oBAAAC,QAAA,EAEpHuB,MAAM,CAACvC,YAAY,GAAG,QAAQ,GAAG;kBAAW;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,EACRmB,MAAM,CAACrC,WAAW,iBACjBpC,OAAA;oBACEwD,OAAO,EAAEA,CAAA,KAAMlD,QAAQ,CAAC,cAAcmE,MAAM,CAACrC,WAAW,EAAE,CAAE;oBAC5Da,SAAS,EAAC,uGAAuG;oBAAAC,QAAA,EAClH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA,eACD,CAAC,gBAEHtD,OAAA;kBAAMiD,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACP;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GAlDDmB,MAAM,CAACpE,EAAE;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDX,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,GAtFDW,OAAO,CAAC5D,EAAE;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuFL,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CAvXID,gBAA0B;EAAA,QACfT,SAAS,EACPC,WAAW,EACXE,OAAO,EACWC,gBAAgB;AAAA;AAAA4E,EAAA,GAJ/CvE,gBAA0B;AAyXhC,eAAeA,gBAAgB;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}