{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { SparseMatrixDependencies } from './dependenciesSparseMatrixClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSparse } from '../../factoriesAny.js';\nexport var sparseDependencies = {\n  SparseMatrixDependencies,\n  typedDependencies,\n  createSparse\n};", "map": {"version": 3, "names": ["SparseMatrixDependencies", "typedDependencies", "createSparse", "sparseDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSparse.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { SparseMatrixDependencies } from './dependenciesSparseMatrixClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSparse } from '../../factoriesAny.js';\nexport var sparseDependencies = {\n  SparseMatrixDependencies,\n  typedDependencies,\n  createSparse\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,wBAAwB,QAAQ,8CAA8C;AACvF,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAO,IAAIC,kBAAkB,GAAG;EAC9BH,wBAAwB;EACxBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}