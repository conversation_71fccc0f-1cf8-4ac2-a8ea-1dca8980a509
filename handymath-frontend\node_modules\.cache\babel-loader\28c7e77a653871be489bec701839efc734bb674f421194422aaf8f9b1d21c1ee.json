{"ast": null, "code": "/*!\r\n *  decimal.js v10.5.0\r\n *  An arbitrary-precision Decimal type for JavaScript.\r\n *  https://github.com/MikeMcl/decimal.js\r\n *  Copyright (c) 2025 <PERSON> <<EMAIL>>\r\n *  MIT Licence\r\n */\n\n// -----------------------------------  EDITABLE DEFAULTS  ------------------------------------ //\n\n// The maximum exponent magnitude.\n// The limit on the value of `toExpNeg`, `toExpPos`, `minE` and `maxE`.\nvar EXP_LIMIT = 9e15,\n  // 0 to 9e15\n\n  // The limit on the value of `precision`, and on the value of the first argument to\n  // `toDecimalPlaces`, `toExponential`, `toFixed`, `toPrecision` and `toSignificantDigits`.\n  MAX_DIGITS = 1e9,\n  // 0 to 1e9\n\n  // Base conversion alphabet.\n  NUMERALS = '0123456789abcdef',\n  // The natural logarithm of 10 (1025 digits).\n  LN10 = '2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058',\n  // Pi (1025 digits).\n  PI = '3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789',\n  // The initial configuration properties of the Decimal constructor.\n  DEFAULTS = {\n    // These values must be integers within the stated ranges (inclusive).\n    // Most of these values can be changed at run-time using the `Decimal.config` method.\n\n    // The maximum number of significant digits of the result of a calculation or base conversion.\n    // E.g. `Decimal.config({ precision: 20 });`\n    precision: 20,\n    // 1 to MAX_DIGITS\n\n    // The rounding mode used when rounding to `precision`.\n    //\n    // ROUND_UP         0 Away from zero.\n    // ROUND_DOWN       1 Towards zero.\n    // ROUND_CEIL       2 Towards +Infinity.\n    // ROUND_FLOOR      3 Towards -Infinity.\n    // ROUND_HALF_UP    4 Towards nearest neighbour. If equidistant, up.\n    // ROUND_HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\n    // ROUND_HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\n    // ROUND_HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\n    // ROUND_HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\n    //\n    // E.g.\n    // `Decimal.rounding = 4;`\n    // `Decimal.rounding = Decimal.ROUND_HALF_UP;`\n    rounding: 4,\n    // 0 to 8\n\n    // The modulo mode used when calculating the modulus: a mod n.\n    // The quotient (q = a / n) is calculated according to the corresponding rounding mode.\n    // The remainder (r) is calculated as: r = a - n * q.\n    //\n    // UP         0 The remainder is positive if the dividend is negative, else is negative.\n    // DOWN       1 The remainder has the same sign as the dividend (JavaScript %).\n    // FLOOR      3 The remainder has the same sign as the divisor (Python %).\n    // HALF_EVEN  6 The IEEE 754 remainder function.\n    // EUCLID     9 Euclidian division. q = sign(n) * floor(a / abs(n)). Always positive.\n    //\n    // Truncated division (1), floored division (3), the IEEE 754 remainder (6), and Euclidian\n    // division (9) are commonly used for the modulus operation. The other rounding modes can also\n    // be used, but they may not give useful results.\n    modulo: 1,\n    // 0 to 9\n\n    // The exponent value at and beneath which `toString` returns exponential notation.\n    // JavaScript numbers: -7\n    toExpNeg: -7,\n    // 0 to -EXP_LIMIT\n\n    // The exponent value at and above which `toString` returns exponential notation.\n    // JavaScript numbers: 21\n    toExpPos: 21,\n    // 0 to EXP_LIMIT\n\n    // The minimum exponent value, beneath which underflow to zero occurs.\n    // JavaScript numbers: -324  (5e-324)\n    minE: -EXP_LIMIT,\n    // -1 to -EXP_LIMIT\n\n    // The maximum exponent value, above which overflow to Infinity occurs.\n    // JavaScript numbers: 308  (1.7976931348623157e+308)\n    maxE: EXP_LIMIT,\n    // 1 to EXP_LIMIT\n\n    // Whether to use cryptographically-secure random number generation, if available.\n    crypto: false // true/false\n  },\n  // ----------------------------------- END OF EDITABLE DEFAULTS ------------------------------- //\n\n  inexact,\n  quadrant,\n  external = true,\n  decimalError = '[DecimalError] ',\n  invalidArgument = decimalError + 'Invalid argument: ',\n  precisionLimitExceeded = decimalError + 'Precision limit exceeded',\n  cryptoUnavailable = decimalError + 'crypto unavailable',\n  tag = '[object Decimal]',\n  mathfloor = Math.floor,\n  mathpow = Math.pow,\n  isBinary = /^0b([01]+(\\.[01]*)?|\\.[01]+)(p[+-]?\\d+)?$/i,\n  isHex = /^0x([0-9a-f]+(\\.[0-9a-f]*)?|\\.[0-9a-f]+)(p[+-]?\\d+)?$/i,\n  isOctal = /^0o([0-7]+(\\.[0-7]*)?|\\.[0-7]+)(p[+-]?\\d+)?$/i,\n  isDecimal = /^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i,\n  BASE = 1e7,\n  LOG_BASE = 7,\n  MAX_SAFE_INTEGER = 9007199254740991,\n  LN10_PRECISION = LN10.length - 1,\n  PI_PRECISION = PI.length - 1,\n  // Decimal.prototype object\n  P = {\n    toStringTag: tag\n  };\n\n// Decimal prototype methods\n\n/*\r\n *  absoluteValue             abs\r\n *  ceil\r\n *  clampedTo                 clamp\r\n *  comparedTo                cmp\r\n *  cosine                    cos\r\n *  cubeRoot                  cbrt\r\n *  decimalPlaces             dp\r\n *  dividedBy                 div\r\n *  dividedToIntegerBy        divToInt\r\n *  equals                    eq\r\n *  floor\r\n *  greaterThan               gt\r\n *  greaterThanOrEqualTo      gte\r\n *  hyperbolicCosine          cosh\r\n *  hyperbolicSine            sinh\r\n *  hyperbolicTangent         tanh\r\n *  inverseCosine             acos\r\n *  inverseHyperbolicCosine   acosh\r\n *  inverseHyperbolicSine     asinh\r\n *  inverseHyperbolicTangent  atanh\r\n *  inverseSine               asin\r\n *  inverseTangent            atan\r\n *  isFinite\r\n *  isInteger                 isInt\r\n *  isNaN\r\n *  isNegative                isNeg\r\n *  isPositive                isPos\r\n *  isZero\r\n *  lessThan                  lt\r\n *  lessThanOrEqualTo         lte\r\n *  logarithm                 log\r\n *  [maximum]                 [max]\r\n *  [minimum]                 [min]\r\n *  minus                     sub\r\n *  modulo                    mod\r\n *  naturalExponential        exp\r\n *  naturalLogarithm          ln\r\n *  negated                   neg\r\n *  plus                      add\r\n *  precision                 sd\r\n *  round\r\n *  sine                      sin\r\n *  squareRoot                sqrt\r\n *  tangent                   tan\r\n *  times                     mul\r\n *  toBinary\r\n *  toDecimalPlaces           toDP\r\n *  toExponential\r\n *  toFixed\r\n *  toFraction\r\n *  toHexadecimal             toHex\r\n *  toNearest\r\n *  toNumber\r\n *  toOctal\r\n *  toPower                   pow\r\n *  toPrecision\r\n *  toSignificantDigits       toSD\r\n *  toString\r\n *  truncated                 trunc\r\n *  valueOf                   toJSON\r\n */\n\n/*\r\n * Return a new Decimal whose value is the absolute value of this Decimal.\r\n *\r\n */\nP.absoluteValue = P.abs = function () {\n  var x = new this.constructor(this);\n  if (x.s < 0) x.s = 1;\n  return finalise(x);\n};\n\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number in the\r\n * direction of positive Infinity.\r\n *\r\n */\nP.ceil = function () {\n  return finalise(new this.constructor(this), this.e + 1, 2);\n};\n\n/*\r\n * Return a new Decimal whose value is the value of this Decimal clamped to the range\r\n * delineated by `min` and `max`.\r\n *\r\n * min {number|string|bigint|Decimal}\r\n * max {number|string|bigint|Decimal}\r\n *\r\n */\nP.clampedTo = P.clamp = function (min, max) {\n  var k,\n    x = this,\n    Ctor = x.constructor;\n  min = new Ctor(min);\n  max = new Ctor(max);\n  if (!min.s || !max.s) return new Ctor(NaN);\n  if (min.gt(max)) throw Error(invalidArgument + max);\n  k = x.cmp(min);\n  return k < 0 ? min : x.cmp(max) > 0 ? max : new Ctor(x);\n};\n\n/*\r\n * Return\r\n *   1    if the value of this Decimal is greater than the value of `y`,\r\n *  -1    if the value of this Decimal is less than the value of `y`,\r\n *   0    if they have the same value,\r\n *   NaN  if the value of either Decimal is NaN.\r\n *\r\n */\nP.comparedTo = P.cmp = function (y) {\n  var i,\n    j,\n    xdL,\n    ydL,\n    x = this,\n    xd = x.d,\n    yd = (y = new x.constructor(y)).d,\n    xs = x.s,\n    ys = y.s;\n\n  // Either NaN or ±Infinity?\n  if (!xd || !yd) {\n    return !xs || !ys ? NaN : xs !== ys ? xs : xd === yd ? 0 : !xd ^ xs < 0 ? 1 : -1;\n  }\n\n  // Either zero?\n  if (!xd[0] || !yd[0]) return xd[0] ? xs : yd[0] ? -ys : 0;\n\n  // Signs differ?\n  if (xs !== ys) return xs;\n\n  // Compare exponents.\n  if (x.e !== y.e) return x.e > y.e ^ xs < 0 ? 1 : -1;\n  xdL = xd.length;\n  ydL = yd.length;\n\n  // Compare digit by digit.\n  for (i = 0, j = xdL < ydL ? xdL : ydL; i < j; ++i) {\n    if (xd[i] !== yd[i]) return xd[i] > yd[i] ^ xs < 0 ? 1 : -1;\n  }\n\n  // Compare lengths.\n  return xdL === ydL ? 0 : xdL > ydL ^ xs < 0 ? 1 : -1;\n};\n\n/*\r\n * Return a new Decimal whose value is the cosine of the value in radians of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-1, 1]\r\n *\r\n * cos(0)         = 1\r\n * cos(-0)        = 1\r\n * cos(Infinity)  = NaN\r\n * cos(-Infinity) = NaN\r\n * cos(NaN)       = NaN\r\n *\r\n */\nP.cosine = P.cos = function () {\n  var pr,\n    rm,\n    x = this,\n    Ctor = x.constructor;\n  if (!x.d) return new Ctor(NaN);\n\n  // cos(0) = cos(-0) = 1\n  if (!x.d[0]) return new Ctor(1);\n  pr = Ctor.precision;\n  rm = Ctor.rounding;\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + LOG_BASE;\n  Ctor.rounding = 1;\n  x = cosine(Ctor, toLessThanHalfPi(Ctor, x));\n  Ctor.precision = pr;\n  Ctor.rounding = rm;\n  return finalise(quadrant == 2 || quadrant == 3 ? x.neg() : x, pr, rm, true);\n};\n\n/*\r\n *\r\n * Return a new Decimal whose value is the cube root of the value of this Decimal, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n *  cbrt(0)  =  0\r\n *  cbrt(-0) = -0\r\n *  cbrt(1)  =  1\r\n *  cbrt(-1) = -1\r\n *  cbrt(N)  =  N\r\n *  cbrt(-I) = -I\r\n *  cbrt(I)  =  I\r\n *\r\n * Math.cbrt(x) = (x < 0 ? -Math.pow(-x, 1/3) : Math.pow(x, 1/3))\r\n *\r\n */\nP.cubeRoot = P.cbrt = function () {\n  var e,\n    m,\n    n,\n    r,\n    rep,\n    s,\n    sd,\n    t,\n    t3,\n    t3plusx,\n    x = this,\n    Ctor = x.constructor;\n  if (!x.isFinite() || x.isZero()) return new Ctor(x);\n  external = false;\n\n  // Initial estimate.\n  s = x.s * mathpow(x.s * x, 1 / 3);\n\n  // Math.cbrt underflow/overflow?\n  // Pass x to Math.pow as integer, then adjust the exponent of the result.\n  if (!s || Math.abs(s) == 1 / 0) {\n    n = digitsToString(x.d);\n    e = x.e;\n\n    // Adjust n exponent so it is a multiple of 3 away from x exponent.\n    if (s = (e - n.length + 1) % 3) n += s == 1 || s == -2 ? '0' : '00';\n    s = mathpow(n, 1 / 3);\n\n    // Rarely, e may be one less than the result exponent value.\n    e = mathfloor((e + 1) / 3) - (e % 3 == (e < 0 ? -1 : 2));\n    if (s == 1 / 0) {\n      n = '5e' + e;\n    } else {\n      n = s.toExponential();\n      n = n.slice(0, n.indexOf('e') + 1) + e;\n    }\n    r = new Ctor(n);\n    r.s = x.s;\n  } else {\n    r = new Ctor(s.toString());\n  }\n  sd = (e = Ctor.precision) + 3;\n\n  // Halley's method.\n  // TODO? Compare Newton's method.\n  for (;;) {\n    t = r;\n    t3 = t.times(t).times(t);\n    t3plusx = t3.plus(x);\n    r = divide(t3plusx.plus(x).times(t), t3plusx.plus(t3), sd + 2, 1);\n\n    // TODO? Replace with for-loop and checkRoundingDigits.\n    if (digitsToString(t.d).slice(0, sd) === (n = digitsToString(r.d)).slice(0, sd)) {\n      n = n.slice(sd - 3, sd + 1);\n\n      // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or 4999\n      // , i.e. approaching a rounding boundary, continue the iteration.\n      if (n == '9999' || !rep && n == '4999') {\n        // On the first iteration only, check to see if rounding up gives the exact result as the\n        // nines may infinitely repeat.\n        if (!rep) {\n          finalise(t, e + 1, 0);\n          if (t.times(t).times(t).eq(x)) {\n            r = t;\n            break;\n          }\n        }\n        sd += 4;\n        rep = 1;\n      } else {\n        // If the rounding digits are null, 0{0,4} or 50{0,3}, check for an exact result.\n        // If not, then there are further digits and m will be truthy.\n        if (!+n || !+n.slice(1) && n.charAt(0) == '5') {\n          // Truncate to the first rounding digit.\n          finalise(r, e + 1, 1);\n          m = !r.times(r).times(r).eq(x);\n        }\n        break;\n      }\n    }\n  }\n  external = true;\n  return finalise(r, e, Ctor.rounding, m);\n};\n\n/*\r\n * Return the number of decimal places of the value of this Decimal.\r\n *\r\n */\nP.decimalPlaces = P.dp = function () {\n  var w,\n    d = this.d,\n    n = NaN;\n  if (d) {\n    w = d.length - 1;\n    n = (w - mathfloor(this.e / LOG_BASE)) * LOG_BASE;\n\n    // Subtract the number of trailing zeros of the last word.\n    w = d[w];\n    if (w) for (; w % 10 == 0; w /= 10) n--;\n    if (n < 0) n = 0;\n  }\n  return n;\n};\n\n/*\r\n *  n / 0 = I\r\n *  n / N = N\r\n *  n / I = 0\r\n *  0 / n = 0\r\n *  0 / 0 = N\r\n *  0 / N = N\r\n *  0 / I = 0\r\n *  N / n = N\r\n *  N / 0 = N\r\n *  N / N = N\r\n *  N / I = N\r\n *  I / n = I\r\n *  I / 0 = I\r\n *  I / N = N\r\n *  I / I = N\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal divided by `y`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n */\nP.dividedBy = P.div = function (y) {\n  return divide(this, new this.constructor(y));\n};\n\n/*\r\n * Return a new Decimal whose value is the integer part of dividing the value of this Decimal\r\n * by the value of `y`, rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n */\nP.dividedToIntegerBy = P.divToInt = function (y) {\n  var x = this,\n    Ctor = x.constructor;\n  return finalise(divide(x, new Ctor(y), 0, 1, 1), Ctor.precision, Ctor.rounding);\n};\n\n/*\r\n * Return true if the value of this Decimal is equal to the value of `y`, otherwise return false.\r\n *\r\n */\nP.equals = P.eq = function (y) {\n  return this.cmp(y) === 0;\n};\n\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number in the\r\n * direction of negative Infinity.\r\n *\r\n */\nP.floor = function () {\n  return finalise(new this.constructor(this), this.e + 1, 3);\n};\n\n/*\r\n * Return true if the value of this Decimal is greater than the value of `y`, otherwise return\r\n * false.\r\n *\r\n */\nP.greaterThan = P.gt = function (y) {\n  return this.cmp(y) > 0;\n};\n\n/*\r\n * Return true if the value of this Decimal is greater than or equal to the value of `y`,\r\n * otherwise return false.\r\n *\r\n */\nP.greaterThanOrEqualTo = P.gte = function (y) {\n  var k = this.cmp(y);\n  return k == 1 || k === 0;\n};\n\n/*\r\n * Return a new Decimal whose value is the hyperbolic cosine of the value in radians of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [1, Infinity]\r\n *\r\n * cosh(x) = 1 + x^2/2! + x^4/4! + x^6/6! + ...\r\n *\r\n * cosh(0)         = 1\r\n * cosh(-0)        = 1\r\n * cosh(Infinity)  = Infinity\r\n * cosh(-Infinity) = Infinity\r\n * cosh(NaN)       = NaN\r\n *\r\n *  x        time taken (ms)   result\r\n * 1000      9                 9.8503555700852349694e+433\r\n * 10000     25                4.4034091128314607936e+4342\r\n * 100000    171               1.4033316802130615897e+43429\r\n * 1000000   3817              1.5166076984010437725e+434294\r\n * 10000000  abandoned after 2 minute wait\r\n *\r\n * TODO? Compare performance of cosh(x) = 0.5 * (exp(x) + exp(-x))\r\n *\r\n */\nP.hyperbolicCosine = P.cosh = function () {\n  var k,\n    n,\n    pr,\n    rm,\n    len,\n    x = this,\n    Ctor = x.constructor,\n    one = new Ctor(1);\n  if (!x.isFinite()) return new Ctor(x.s ? 1 / 0 : NaN);\n  if (x.isZero()) return one;\n  pr = Ctor.precision;\n  rm = Ctor.rounding;\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + 4;\n  Ctor.rounding = 1;\n  len = x.d.length;\n\n  // Argument reduction: cos(4x) = 1 - 8cos^2(x) + 8cos^4(x) + 1\n  // i.e. cos(x) = 1 - cos^2(x/4)(8 - 8cos^2(x/4))\n\n  // Estimate the optimum number of times to use the argument reduction.\n  // TODO? Estimation reused from cosine() and may not be optimal here.\n  if (len < 32) {\n    k = Math.ceil(len / 3);\n    n = (1 / tinyPow(4, k)).toString();\n  } else {\n    k = 16;\n    n = '2.3283064365386962890625e-10';\n  }\n  x = taylorSeries(Ctor, 1, x.times(n), new Ctor(1), true);\n\n  // Reverse argument reduction\n  var cosh2_x,\n    i = k,\n    d8 = new Ctor(8);\n  for (; i--;) {\n    cosh2_x = x.times(x);\n    x = one.minus(cosh2_x.times(d8.minus(cosh2_x.times(d8))));\n  }\n  return finalise(x, Ctor.precision = pr, Ctor.rounding = rm, true);\n};\n\n/*\r\n * Return a new Decimal whose value is the hyperbolic sine of the value in radians of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * sinh(x) = x + x^3/3! + x^5/5! + x^7/7! + ...\r\n *\r\n * sinh(0)         = 0\r\n * sinh(-0)        = -0\r\n * sinh(Infinity)  = Infinity\r\n * sinh(-Infinity) = -Infinity\r\n * sinh(NaN)       = NaN\r\n *\r\n * x        time taken (ms)\r\n * 10       2 ms\r\n * 100      5 ms\r\n * 1000     14 ms\r\n * 10000    82 ms\r\n * 100000   886 ms            1.4033316802130615897e+43429\r\n * 200000   2613 ms\r\n * 300000   5407 ms\r\n * 400000   8824 ms\r\n * 500000   13026 ms          8.7080643612718084129e+217146\r\n * 1000000  48543 ms\r\n *\r\n * TODO? Compare performance of sinh(x) = 0.5 * (exp(x) - exp(-x))\r\n *\r\n */\nP.hyperbolicSine = P.sinh = function () {\n  var k,\n    pr,\n    rm,\n    len,\n    x = this,\n    Ctor = x.constructor;\n  if (!x.isFinite() || x.isZero()) return new Ctor(x);\n  pr = Ctor.precision;\n  rm = Ctor.rounding;\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + 4;\n  Ctor.rounding = 1;\n  len = x.d.length;\n  if (len < 3) {\n    x = taylorSeries(Ctor, 2, x, x, true);\n  } else {\n    // Alternative argument reduction: sinh(3x) = sinh(x)(3 + 4sinh^2(x))\n    // i.e. sinh(x) = sinh(x/3)(3 + 4sinh^2(x/3))\n    // 3 multiplications and 1 addition\n\n    // Argument reduction: sinh(5x) = sinh(x)(5 + sinh^2(x)(20 + 16sinh^2(x)))\n    // i.e. sinh(x) = sinh(x/5)(5 + sinh^2(x/5)(20 + 16sinh^2(x/5)))\n    // 4 multiplications and 2 additions\n\n    // Estimate the optimum number of times to use the argument reduction.\n    k = 1.4 * Math.sqrt(len);\n    k = k > 16 ? 16 : k | 0;\n    x = x.times(1 / tinyPow(5, k));\n    x = taylorSeries(Ctor, 2, x, x, true);\n\n    // Reverse argument reduction\n    var sinh2_x,\n      d5 = new Ctor(5),\n      d16 = new Ctor(16),\n      d20 = new Ctor(20);\n    for (; k--;) {\n      sinh2_x = x.times(x);\n      x = x.times(d5.plus(sinh2_x.times(d16.times(sinh2_x).plus(d20))));\n    }\n  }\n  Ctor.precision = pr;\n  Ctor.rounding = rm;\n  return finalise(x, pr, rm, true);\n};\n\n/*\r\n * Return a new Decimal whose value is the hyperbolic tangent of the value in radians of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-1, 1]\r\n *\r\n * tanh(x) = sinh(x) / cosh(x)\r\n *\r\n * tanh(0)         = 0\r\n * tanh(-0)        = -0\r\n * tanh(Infinity)  = 1\r\n * tanh(-Infinity) = -1\r\n * tanh(NaN)       = NaN\r\n *\r\n */\nP.hyperbolicTangent = P.tanh = function () {\n  var pr,\n    rm,\n    x = this,\n    Ctor = x.constructor;\n  if (!x.isFinite()) return new Ctor(x.s);\n  if (x.isZero()) return new Ctor(x);\n  pr = Ctor.precision;\n  rm = Ctor.rounding;\n  Ctor.precision = pr + 7;\n  Ctor.rounding = 1;\n  return divide(x.sinh(), x.cosh(), Ctor.precision = pr, Ctor.rounding = rm);\n};\n\n/*\r\n * Return a new Decimal whose value is the arccosine (inverse cosine) in radians of the value of\r\n * this Decimal.\r\n *\r\n * Domain: [-1, 1]\r\n * Range: [0, pi]\r\n *\r\n * acos(x) = pi/2 - asin(x)\r\n *\r\n * acos(0)       = pi/2\r\n * acos(-0)      = pi/2\r\n * acos(1)       = 0\r\n * acos(-1)      = pi\r\n * acos(1/2)     = pi/3\r\n * acos(-1/2)    = 2*pi/3\r\n * acos(|x| > 1) = NaN\r\n * acos(NaN)     = NaN\r\n *\r\n */\nP.inverseCosine = P.acos = function () {\n  var x = this,\n    Ctor = x.constructor,\n    k = x.abs().cmp(1),\n    pr = Ctor.precision,\n    rm = Ctor.rounding;\n  if (k !== -1) {\n    return k === 0\n    // |x| is 1\n    ? x.isNeg() ? getPi(Ctor, pr, rm) : new Ctor(0)\n    // |x| > 1 or x is NaN\n    : new Ctor(NaN);\n  }\n  if (x.isZero()) return getPi(Ctor, pr + 4, rm).times(0.5);\n\n  // TODO? Special case acos(0.5) = pi/3 and acos(-0.5) = 2*pi/3\n\n  Ctor.precision = pr + 6;\n  Ctor.rounding = 1;\n\n  // See https://github.com/MikeMcl/decimal.js/pull/217\n  x = new Ctor(1).minus(x).div(x.plus(1)).sqrt().atan();\n  Ctor.precision = pr;\n  Ctor.rounding = rm;\n  return x.times(2);\n};\n\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic cosine in radians of the\r\n * value of this Decimal.\r\n *\r\n * Domain: [1, Infinity]\r\n * Range: [0, Infinity]\r\n *\r\n * acosh(x) = ln(x + sqrt(x^2 - 1))\r\n *\r\n * acosh(x < 1)     = NaN\r\n * acosh(NaN)       = NaN\r\n * acosh(Infinity)  = Infinity\r\n * acosh(-Infinity) = NaN\r\n * acosh(0)         = NaN\r\n * acosh(-0)        = NaN\r\n * acosh(1)         = 0\r\n * acosh(-1)        = NaN\r\n *\r\n */\nP.inverseHyperbolicCosine = P.acosh = function () {\n  var pr,\n    rm,\n    x = this,\n    Ctor = x.constructor;\n  if (x.lte(1)) return new Ctor(x.eq(1) ? 0 : NaN);\n  if (!x.isFinite()) return new Ctor(x);\n  pr = Ctor.precision;\n  rm = Ctor.rounding;\n  Ctor.precision = pr + Math.max(Math.abs(x.e), x.sd()) + 4;\n  Ctor.rounding = 1;\n  external = false;\n  x = x.times(x).minus(1).sqrt().plus(x);\n  external = true;\n  Ctor.precision = pr;\n  Ctor.rounding = rm;\n  return x.ln();\n};\n\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic sine in radians of the value\r\n * of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * asinh(x) = ln(x + sqrt(x^2 + 1))\r\n *\r\n * asinh(NaN)       = NaN\r\n * asinh(Infinity)  = Infinity\r\n * asinh(-Infinity) = -Infinity\r\n * asinh(0)         = 0\r\n * asinh(-0)        = -0\r\n *\r\n */\nP.inverseHyperbolicSine = P.asinh = function () {\n  var pr,\n    rm,\n    x = this,\n    Ctor = x.constructor;\n  if (!x.isFinite() || x.isZero()) return new Ctor(x);\n  pr = Ctor.precision;\n  rm = Ctor.rounding;\n  Ctor.precision = pr + 2 * Math.max(Math.abs(x.e), x.sd()) + 6;\n  Ctor.rounding = 1;\n  external = false;\n  x = x.times(x).plus(1).sqrt().plus(x);\n  external = true;\n  Ctor.precision = pr;\n  Ctor.rounding = rm;\n  return x.ln();\n};\n\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic tangent in radians of the\r\n * value of this Decimal.\r\n *\r\n * Domain: [-1, 1]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * atanh(x) = 0.5 * ln((1 + x) / (1 - x))\r\n *\r\n * atanh(|x| > 1)   = NaN\r\n * atanh(NaN)       = NaN\r\n * atanh(Infinity)  = NaN\r\n * atanh(-Infinity) = NaN\r\n * atanh(0)         = 0\r\n * atanh(-0)        = -0\r\n * atanh(1)         = Infinity\r\n * atanh(-1)        = -Infinity\r\n *\r\n */\nP.inverseHyperbolicTangent = P.atanh = function () {\n  var pr,\n    rm,\n    wpr,\n    xsd,\n    x = this,\n    Ctor = x.constructor;\n  if (!x.isFinite()) return new Ctor(NaN);\n  if (x.e >= 0) return new Ctor(x.abs().eq(1) ? x.s / 0 : x.isZero() ? x : NaN);\n  pr = Ctor.precision;\n  rm = Ctor.rounding;\n  xsd = x.sd();\n  if (Math.max(xsd, pr) < 2 * -x.e - 1) return finalise(new Ctor(x), pr, rm, true);\n  Ctor.precision = wpr = xsd - x.e;\n  x = divide(x.plus(1), new Ctor(1).minus(x), wpr + pr, 1);\n  Ctor.precision = pr + 4;\n  Ctor.rounding = 1;\n  x = x.ln();\n  Ctor.precision = pr;\n  Ctor.rounding = rm;\n  return x.times(0.5);\n};\n\n/*\r\n * Return a new Decimal whose value is the arcsine (inverse sine) in radians of the value of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-pi/2, pi/2]\r\n *\r\n * asin(x) = 2*atan(x/(1 + sqrt(1 - x^2)))\r\n *\r\n * asin(0)       = 0\r\n * asin(-0)      = -0\r\n * asin(1/2)     = pi/6\r\n * asin(-1/2)    = -pi/6\r\n * asin(1)       = pi/2\r\n * asin(-1)      = -pi/2\r\n * asin(|x| > 1) = NaN\r\n * asin(NaN)     = NaN\r\n *\r\n * TODO? Compare performance of Taylor series.\r\n *\r\n */\nP.inverseSine = P.asin = function () {\n  var halfPi,\n    k,\n    pr,\n    rm,\n    x = this,\n    Ctor = x.constructor;\n  if (x.isZero()) return new Ctor(x);\n  k = x.abs().cmp(1);\n  pr = Ctor.precision;\n  rm = Ctor.rounding;\n  if (k !== -1) {\n    // |x| is 1\n    if (k === 0) {\n      halfPi = getPi(Ctor, pr + 4, rm).times(0.5);\n      halfPi.s = x.s;\n      return halfPi;\n    }\n\n    // |x| > 1 or x is NaN\n    return new Ctor(NaN);\n  }\n\n  // TODO? Special case asin(1/2) = pi/6 and asin(-1/2) = -pi/6\n\n  Ctor.precision = pr + 6;\n  Ctor.rounding = 1;\n  x = x.div(new Ctor(1).minus(x.times(x)).sqrt().plus(1)).atan();\n  Ctor.precision = pr;\n  Ctor.rounding = rm;\n  return x.times(2);\n};\n\n/*\r\n * Return a new Decimal whose value is the arctangent (inverse tangent) in radians of the value\r\n * of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-pi/2, pi/2]\r\n *\r\n * atan(x) = x - x^3/3 + x^5/5 - x^7/7 + ...\r\n *\r\n * atan(0)         = 0\r\n * atan(-0)        = -0\r\n * atan(1)         = pi/4\r\n * atan(-1)        = -pi/4\r\n * atan(Infinity)  = pi/2\r\n * atan(-Infinity) = -pi/2\r\n * atan(NaN)       = NaN\r\n *\r\n */\nP.inverseTangent = P.atan = function () {\n  var i,\n    j,\n    k,\n    n,\n    px,\n    t,\n    r,\n    wpr,\n    x2,\n    x = this,\n    Ctor = x.constructor,\n    pr = Ctor.precision,\n    rm = Ctor.rounding;\n  if (!x.isFinite()) {\n    if (!x.s) return new Ctor(NaN);\n    if (pr + 4 <= PI_PRECISION) {\n      r = getPi(Ctor, pr + 4, rm).times(0.5);\n      r.s = x.s;\n      return r;\n    }\n  } else if (x.isZero()) {\n    return new Ctor(x);\n  } else if (x.abs().eq(1) && pr + 4 <= PI_PRECISION) {\n    r = getPi(Ctor, pr + 4, rm).times(0.25);\n    r.s = x.s;\n    return r;\n  }\n  Ctor.precision = wpr = pr + 10;\n  Ctor.rounding = 1;\n\n  // TODO? if (x >= 1 && pr <= PI_PRECISION) atan(x) = halfPi * x.s - atan(1 / x);\n\n  // Argument reduction\n  // Ensure |x| < 0.42\n  // atan(x) = 2 * atan(x / (1 + sqrt(1 + x^2)))\n\n  k = Math.min(28, wpr / LOG_BASE + 2 | 0);\n  for (i = k; i; --i) x = x.div(x.times(x).plus(1).sqrt().plus(1));\n  external = false;\n  j = Math.ceil(wpr / LOG_BASE);\n  n = 1;\n  x2 = x.times(x);\n  r = new Ctor(x);\n  px = x;\n\n  // atan(x) = x - x^3/3 + x^5/5 - x^7/7 + ...\n  for (; i !== -1;) {\n    px = px.times(x2);\n    t = r.minus(px.div(n += 2));\n    px = px.times(x2);\n    r = t.plus(px.div(n += 2));\n    if (r.d[j] !== void 0) for (i = j; r.d[i] === t.d[i] && i--;);\n  }\n  if (k) r = r.times(2 << k - 1);\n  external = true;\n  return finalise(r, Ctor.precision = pr, Ctor.rounding = rm, true);\n};\n\n/*\r\n * Return true if the value of this Decimal is a finite number, otherwise return false.\r\n *\r\n */\nP.isFinite = function () {\n  return !!this.d;\n};\n\n/*\r\n * Return true if the value of this Decimal is an integer, otherwise return false.\r\n *\r\n */\nP.isInteger = P.isInt = function () {\n  return !!this.d && mathfloor(this.e / LOG_BASE) > this.d.length - 2;\n};\n\n/*\r\n * Return true if the value of this Decimal is NaN, otherwise return false.\r\n *\r\n */\nP.isNaN = function () {\n  return !this.s;\n};\n\n/*\r\n * Return true if the value of this Decimal is negative, otherwise return false.\r\n *\r\n */\nP.isNegative = P.isNeg = function () {\n  return this.s < 0;\n};\n\n/*\r\n * Return true if the value of this Decimal is positive, otherwise return false.\r\n *\r\n */\nP.isPositive = P.isPos = function () {\n  return this.s > 0;\n};\n\n/*\r\n * Return true if the value of this Decimal is 0 or -0, otherwise return false.\r\n *\r\n */\nP.isZero = function () {\n  return !!this.d && this.d[0] === 0;\n};\n\n/*\r\n * Return true if the value of this Decimal is less than `y`, otherwise return false.\r\n *\r\n */\nP.lessThan = P.lt = function (y) {\n  return this.cmp(y) < 0;\n};\n\n/*\r\n * Return true if the value of this Decimal is less than or equal to `y`, otherwise return false.\r\n *\r\n */\nP.lessThanOrEqualTo = P.lte = function (y) {\n  return this.cmp(y) < 1;\n};\n\n/*\r\n * Return the logarithm of the value of this Decimal to the specified base, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * If no base is specified, return log[10](arg).\r\n *\r\n * log[base](arg) = ln(arg) / ln(base)\r\n *\r\n * The result will always be correctly rounded if the base of the log is 10, and 'almost always'\r\n * otherwise:\r\n *\r\n * Depending on the rounding mode, the result may be incorrectly rounded if the first fifteen\r\n * rounding digits are [49]99999999999999 or [50]00000000000000. In that case, the maximum error\r\n * between the result and the correctly rounded result will be one ulp (unit in the last place).\r\n *\r\n * log[-b](a)       = NaN\r\n * log[0](a)        = NaN\r\n * log[1](a)        = NaN\r\n * log[NaN](a)      = NaN\r\n * log[Infinity](a) = NaN\r\n * log[b](0)        = -Infinity\r\n * log[b](-0)       = -Infinity\r\n * log[b](-a)       = NaN\r\n * log[b](1)        = 0\r\n * log[b](Infinity) = Infinity\r\n * log[b](NaN)      = NaN\r\n *\r\n * [base] {number|string|bigint|Decimal} The base of the logarithm.\r\n *\r\n */\nP.logarithm = P.log = function (base) {\n  var isBase10,\n    d,\n    denominator,\n    k,\n    inf,\n    num,\n    sd,\n    r,\n    arg = this,\n    Ctor = arg.constructor,\n    pr = Ctor.precision,\n    rm = Ctor.rounding,\n    guard = 5;\n\n  // Default base is 10.\n  if (base == null) {\n    base = new Ctor(10);\n    isBase10 = true;\n  } else {\n    base = new Ctor(base);\n    d = base.d;\n\n    // Return NaN if base is negative, or non-finite, or is 0 or 1.\n    if (base.s < 0 || !d || !d[0] || base.eq(1)) return new Ctor(NaN);\n    isBase10 = base.eq(10);\n  }\n  d = arg.d;\n\n  // Is arg negative, non-finite, 0 or 1?\n  if (arg.s < 0 || !d || !d[0] || arg.eq(1)) {\n    return new Ctor(d && !d[0] ? -1 / 0 : arg.s != 1 ? NaN : d ? 0 : 1 / 0);\n  }\n\n  // The result will have a non-terminating decimal expansion if base is 10 and arg is not an\n  // integer power of 10.\n  if (isBase10) {\n    if (d.length > 1) {\n      inf = true;\n    } else {\n      for (k = d[0]; k % 10 === 0;) k /= 10;\n      inf = k !== 1;\n    }\n  }\n  external = false;\n  sd = pr + guard;\n  num = naturalLogarithm(arg, sd);\n  denominator = isBase10 ? getLn10(Ctor, sd + 10) : naturalLogarithm(base, sd);\n\n  // The result will have 5 rounding digits.\n  r = divide(num, denominator, sd, 1);\n\n  // If at a rounding boundary, i.e. the result's rounding digits are [49]9999 or [50]0000,\n  // calculate 10 further digits.\n  //\n  // If the result is known to have an infinite decimal expansion, repeat this until it is clear\n  // that the result is above or below the boundary. Otherwise, if after calculating the 10\n  // further digits, the last 14 are nines, round up and assume the result is exact.\n  // Also assume the result is exact if the last 14 are zero.\n  //\n  // Example of a result that will be incorrectly rounded:\n  // log[1048576](4503599627370502) = 2.60000000000000009610279511444746...\n  // The above result correctly rounded using ROUND_CEIL to 1 decimal place should be 2.7, but it\n  // will be given as 2.6 as there are 15 zeros immediately after the requested decimal place, so\n  // the exact result would be assumed to be 2.6, which rounded using ROUND_CEIL to 1 decimal\n  // place is still 2.6.\n  if (checkRoundingDigits(r.d, k = pr, rm)) {\n    do {\n      sd += 10;\n      num = naturalLogarithm(arg, sd);\n      denominator = isBase10 ? getLn10(Ctor, sd + 10) : naturalLogarithm(base, sd);\n      r = divide(num, denominator, sd, 1);\n      if (!inf) {\n        // Check for 14 nines from the 2nd rounding digit, as the first may be 4.\n        if (+digitsToString(r.d).slice(k + 1, k + 15) + 1 == 1e14) {\n          r = finalise(r, pr + 1, 0);\n        }\n        break;\n      }\n    } while (checkRoundingDigits(r.d, k += 10, rm));\n  }\n  external = true;\n  return finalise(r, pr, rm);\n};\n\n/*\r\n * Return a new Decimal whose value is the maximum of the arguments and the value of this Decimal.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\nP.max = function () {\r\n  Array.prototype.push.call(arguments, this);\r\n  return maxOrMin(this.constructor, arguments, -1);\r\n};\r\n */\n\n/*\r\n * Return a new Decimal whose value is the minimum of the arguments and the value of this Decimal.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\nP.min = function () {\r\n  Array.prototype.push.call(arguments, this);\r\n  return maxOrMin(this.constructor, arguments, 1);\r\n};\r\n */\n\n/*\r\n *  n - 0 = n\r\n *  n - N = N\r\n *  n - I = -I\r\n *  0 - n = -n\r\n *  0 - 0 = 0\r\n *  0 - N = N\r\n *  0 - I = -I\r\n *  N - n = N\r\n *  N - 0 = N\r\n *  N - N = N\r\n *  N - I = N\r\n *  I - n = I\r\n *  I - 0 = I\r\n *  I - N = N\r\n *  I - I = N\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal minus `y`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n */\nP.minus = P.sub = function (y) {\n  var d,\n    e,\n    i,\n    j,\n    k,\n    len,\n    pr,\n    rm,\n    xd,\n    xe,\n    xLTy,\n    yd,\n    x = this,\n    Ctor = x.constructor;\n  y = new Ctor(y);\n\n  // If either is not finite...\n  if (!x.d || !y.d) {\n    // Return NaN if either is NaN.\n    if (!x.s || !y.s) y = new Ctor(NaN);\n\n    // Return y negated if x is finite and y is ±Infinity.\n    else if (x.d) y.s = -y.s;\n\n    // Return x if y is finite and x is ±Infinity.\n    // Return x if both are ±Infinity with different signs.\n    // Return NaN if both are ±Infinity with the same sign.\n    else y = new Ctor(y.d || x.s !== y.s ? x : NaN);\n    return y;\n  }\n\n  // If signs differ...\n  if (x.s != y.s) {\n    y.s = -y.s;\n    return x.plus(y);\n  }\n  xd = x.d;\n  yd = y.d;\n  pr = Ctor.precision;\n  rm = Ctor.rounding;\n\n  // If either is zero...\n  if (!xd[0] || !yd[0]) {\n    // Return y negated if x is zero and y is non-zero.\n    if (yd[0]) y.s = -y.s;\n\n    // Return x if y is zero and x is non-zero.\n    else if (xd[0]) y = new Ctor(x);\n\n    // Return zero if both are zero.\n    // From IEEE 754 (2008) 6.3: 0 - 0 = -0 - -0 = -0 when rounding to -Infinity.\n    else return new Ctor(rm === 3 ? -0 : 0);\n    return external ? finalise(y, pr, rm) : y;\n  }\n\n  // x and y are finite, non-zero numbers with the same sign.\n\n  // Calculate base 1e7 exponents.\n  e = mathfloor(y.e / LOG_BASE);\n  xe = mathfloor(x.e / LOG_BASE);\n  xd = xd.slice();\n  k = xe - e;\n\n  // If base 1e7 exponents differ...\n  if (k) {\n    xLTy = k < 0;\n    if (xLTy) {\n      d = xd;\n      k = -k;\n      len = yd.length;\n    } else {\n      d = yd;\n      e = xe;\n      len = xd.length;\n    }\n\n    // Numbers with massively different exponents would result in a very high number of\n    // zeros needing to be prepended, but this can be avoided while still ensuring correct\n    // rounding by limiting the number of zeros to `Math.ceil(pr / LOG_BASE) + 2`.\n    i = Math.max(Math.ceil(pr / LOG_BASE), len) + 2;\n    if (k > i) {\n      k = i;\n      d.length = 1;\n    }\n\n    // Prepend zeros to equalise exponents.\n    d.reverse();\n    for (i = k; i--;) d.push(0);\n    d.reverse();\n\n    // Base 1e7 exponents equal.\n  } else {\n    // Check digits to determine which is the bigger number.\n\n    i = xd.length;\n    len = yd.length;\n    xLTy = i < len;\n    if (xLTy) len = i;\n    for (i = 0; i < len; i++) {\n      if (xd[i] != yd[i]) {\n        xLTy = xd[i] < yd[i];\n        break;\n      }\n    }\n    k = 0;\n  }\n  if (xLTy) {\n    d = xd;\n    xd = yd;\n    yd = d;\n    y.s = -y.s;\n  }\n  len = xd.length;\n\n  // Append zeros to `xd` if shorter.\n  // Don't add zeros to `yd` if shorter as subtraction only needs to start at `yd` length.\n  for (i = yd.length - len; i > 0; --i) xd[len++] = 0;\n\n  // Subtract yd from xd.\n  for (i = yd.length; i > k;) {\n    if (xd[--i] < yd[i]) {\n      for (j = i; j && xd[--j] === 0;) xd[j] = BASE - 1;\n      --xd[j];\n      xd[i] += BASE;\n    }\n    xd[i] -= yd[i];\n  }\n\n  // Remove trailing zeros.\n  for (; xd[--len] === 0;) xd.pop();\n\n  // Remove leading zeros and adjust exponent accordingly.\n  for (; xd[0] === 0; xd.shift()) --e;\n\n  // Zero?\n  if (!xd[0]) return new Ctor(rm === 3 ? -0 : 0);\n  y.d = xd;\n  y.e = getBase10Exponent(xd, e);\n  return external ? finalise(y, pr, rm) : y;\n};\n\n/*\r\n *   n % 0 =  N\r\n *   n % N =  N\r\n *   n % I =  n\r\n *   0 % n =  0\r\n *  -0 % n = -0\r\n *   0 % 0 =  N\r\n *   0 % N =  N\r\n *   0 % I =  0\r\n *   N % n =  N\r\n *   N % 0 =  N\r\n *   N % N =  N\r\n *   N % I =  N\r\n *   I % n =  N\r\n *   I % 0 =  N\r\n *   I % N =  N\r\n *   I % I =  N\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal modulo `y`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * The result depends on the modulo mode.\r\n *\r\n */\nP.modulo = P.mod = function (y) {\n  var q,\n    x = this,\n    Ctor = x.constructor;\n  y = new Ctor(y);\n\n  // Return NaN if x is ±Infinity or NaN, or y is NaN or ±0.\n  if (!x.d || !y.s || y.d && !y.d[0]) return new Ctor(NaN);\n\n  // Return x if y is ±Infinity or x is ±0.\n  if (!y.d || x.d && !x.d[0]) {\n    return finalise(new Ctor(x), Ctor.precision, Ctor.rounding);\n  }\n\n  // Prevent rounding of intermediate calculations.\n  external = false;\n  if (Ctor.modulo == 9) {\n    // Euclidian division: q = sign(y) * floor(x / abs(y))\n    // result = x - q * y    where  0 <= result < abs(y)\n    q = divide(x, y.abs(), 0, 3, 1);\n    q.s *= y.s;\n  } else {\n    q = divide(x, y, 0, Ctor.modulo, 1);\n  }\n  q = q.times(y);\n  external = true;\n  return x.minus(q);\n};\n\n/*\r\n * Return a new Decimal whose value is the natural exponential of the value of this Decimal,\r\n * i.e. the base e raised to the power the value of this Decimal, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n */\nP.naturalExponential = P.exp = function () {\n  return naturalExponential(this);\n};\n\n/*\r\n * Return a new Decimal whose value is the natural logarithm of the value of this Decimal,\r\n * rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n */\nP.naturalLogarithm = P.ln = function () {\n  return naturalLogarithm(this);\n};\n\n/*\r\n * Return a new Decimal whose value is the value of this Decimal negated, i.e. as if multiplied by\r\n * -1.\r\n *\r\n */\nP.negated = P.neg = function () {\n  var x = new this.constructor(this);\n  x.s = -x.s;\n  return finalise(x);\n};\n\n/*\r\n *  n + 0 = n\r\n *  n + N = N\r\n *  n + I = I\r\n *  0 + n = n\r\n *  0 + 0 = 0\r\n *  0 + N = N\r\n *  0 + I = I\r\n *  N + n = N\r\n *  N + 0 = N\r\n *  N + N = N\r\n *  N + I = N\r\n *  I + n = I\r\n *  I + 0 = I\r\n *  I + N = N\r\n *  I + I = I\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal plus `y`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n */\nP.plus = P.add = function (y) {\n  var carry,\n    d,\n    e,\n    i,\n    k,\n    len,\n    pr,\n    rm,\n    xd,\n    yd,\n    x = this,\n    Ctor = x.constructor;\n  y = new Ctor(y);\n\n  // If either is not finite...\n  if (!x.d || !y.d) {\n    // Return NaN if either is NaN.\n    if (!x.s || !y.s) y = new Ctor(NaN);\n\n    // Return x if y is finite and x is ±Infinity.\n    // Return x if both are ±Infinity with the same sign.\n    // Return NaN if both are ±Infinity with different signs.\n    // Return y if x is finite and y is ±Infinity.\n    else if (!x.d) y = new Ctor(y.d || x.s === y.s ? x : NaN);\n    return y;\n  }\n\n  // If signs differ...\n  if (x.s != y.s) {\n    y.s = -y.s;\n    return x.minus(y);\n  }\n  xd = x.d;\n  yd = y.d;\n  pr = Ctor.precision;\n  rm = Ctor.rounding;\n\n  // If either is zero...\n  if (!xd[0] || !yd[0]) {\n    // Return x if y is zero.\n    // Return y if y is non-zero.\n    if (!yd[0]) y = new Ctor(x);\n    return external ? finalise(y, pr, rm) : y;\n  }\n\n  // x and y are finite, non-zero numbers with the same sign.\n\n  // Calculate base 1e7 exponents.\n  k = mathfloor(x.e / LOG_BASE);\n  e = mathfloor(y.e / LOG_BASE);\n  xd = xd.slice();\n  i = k - e;\n\n  // If base 1e7 exponents differ...\n  if (i) {\n    if (i < 0) {\n      d = xd;\n      i = -i;\n      len = yd.length;\n    } else {\n      d = yd;\n      e = k;\n      len = xd.length;\n    }\n\n    // Limit number of zeros prepended to max(ceil(pr / LOG_BASE), len) + 1.\n    k = Math.ceil(pr / LOG_BASE);\n    len = k > len ? k + 1 : len + 1;\n    if (i > len) {\n      i = len;\n      d.length = 1;\n    }\n\n    // Prepend zeros to equalise exponents. Note: Faster to use reverse then do unshifts.\n    d.reverse();\n    for (; i--;) d.push(0);\n    d.reverse();\n  }\n  len = xd.length;\n  i = yd.length;\n\n  // If yd is longer than xd, swap xd and yd so xd points to the longer array.\n  if (len - i < 0) {\n    i = len;\n    d = yd;\n    yd = xd;\n    xd = d;\n  }\n\n  // Only start adding at yd.length - 1 as the further digits of xd can be left as they are.\n  for (carry = 0; i;) {\n    carry = (xd[--i] = xd[i] + yd[i] + carry) / BASE | 0;\n    xd[i] %= BASE;\n  }\n  if (carry) {\n    xd.unshift(carry);\n    ++e;\n  }\n\n  // Remove trailing zeros.\n  // No need to check for zero, as +x + +y != 0 && -x + -y != 0\n  for (len = xd.length; xd[--len] == 0;) xd.pop();\n  y.d = xd;\n  y.e = getBase10Exponent(xd, e);\n  return external ? finalise(y, pr, rm) : y;\n};\n\n/*\r\n * Return the number of significant digits of the value of this Decimal.\r\n *\r\n * [z] {boolean|number} Whether to count integer-part trailing zeros: true, false, 1 or 0.\r\n *\r\n */\nP.precision = P.sd = function (z) {\n  var k,\n    x = this;\n  if (z !== void 0 && z !== !!z && z !== 1 && z !== 0) throw Error(invalidArgument + z);\n  if (x.d) {\n    k = getPrecision(x.d);\n    if (z && x.e + 1 > k) k = x.e + 1;\n  } else {\n    k = NaN;\n  }\n  return k;\n};\n\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number using\r\n * rounding mode `rounding`.\r\n *\r\n */\nP.round = function () {\n  var x = this,\n    Ctor = x.constructor;\n  return finalise(new Ctor(x), x.e + 1, Ctor.rounding);\n};\n\n/*\r\n * Return a new Decimal whose value is the sine of the value in radians of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-1, 1]\r\n *\r\n * sin(x) = x - x^3/3! + x^5/5! - ...\r\n *\r\n * sin(0)         = 0\r\n * sin(-0)        = -0\r\n * sin(Infinity)  = NaN\r\n * sin(-Infinity) = NaN\r\n * sin(NaN)       = NaN\r\n *\r\n */\nP.sine = P.sin = function () {\n  var pr,\n    rm,\n    x = this,\n    Ctor = x.constructor;\n  if (!x.isFinite()) return new Ctor(NaN);\n  if (x.isZero()) return new Ctor(x);\n  pr = Ctor.precision;\n  rm = Ctor.rounding;\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + LOG_BASE;\n  Ctor.rounding = 1;\n  x = sine(Ctor, toLessThanHalfPi(Ctor, x));\n  Ctor.precision = pr;\n  Ctor.rounding = rm;\n  return finalise(quadrant > 2 ? x.neg() : x, pr, rm, true);\n};\n\n/*\r\n * Return a new Decimal whose value is the square root of this Decimal, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n *  sqrt(-n) =  N\r\n *  sqrt(N)  =  N\r\n *  sqrt(-I) =  N\r\n *  sqrt(I)  =  I\r\n *  sqrt(0)  =  0\r\n *  sqrt(-0) = -0\r\n *\r\n */\nP.squareRoot = P.sqrt = function () {\n  var m,\n    n,\n    sd,\n    r,\n    rep,\n    t,\n    x = this,\n    d = x.d,\n    e = x.e,\n    s = x.s,\n    Ctor = x.constructor;\n\n  // Negative/NaN/Infinity/zero?\n  if (s !== 1 || !d || !d[0]) {\n    return new Ctor(!s || s < 0 && (!d || d[0]) ? NaN : d ? x : 1 / 0);\n  }\n  external = false;\n\n  // Initial estimate.\n  s = Math.sqrt(+x);\n\n  // Math.sqrt underflow/overflow?\n  // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\n  if (s == 0 || s == 1 / 0) {\n    n = digitsToString(d);\n    if ((n.length + e) % 2 == 0) n += '0';\n    s = Math.sqrt(n);\n    e = mathfloor((e + 1) / 2) - (e < 0 || e % 2);\n    if (s == 1 / 0) {\n      n = '5e' + e;\n    } else {\n      n = s.toExponential();\n      n = n.slice(0, n.indexOf('e') + 1) + e;\n    }\n    r = new Ctor(n);\n  } else {\n    r = new Ctor(s.toString());\n  }\n  sd = (e = Ctor.precision) + 3;\n\n  // Newton-Raphson iteration.\n  for (;;) {\n    t = r;\n    r = t.plus(divide(x, t, sd + 2, 1)).times(0.5);\n\n    // TODO? Replace with for-loop and checkRoundingDigits.\n    if (digitsToString(t.d).slice(0, sd) === (n = digitsToString(r.d)).slice(0, sd)) {\n      n = n.slice(sd - 3, sd + 1);\n\n      // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or\n      // 4999, i.e. approaching a rounding boundary, continue the iteration.\n      if (n == '9999' || !rep && n == '4999') {\n        // On the first iteration only, check to see if rounding up gives the exact result as the\n        // nines may infinitely repeat.\n        if (!rep) {\n          finalise(t, e + 1, 0);\n          if (t.times(t).eq(x)) {\n            r = t;\n            break;\n          }\n        }\n        sd += 4;\n        rep = 1;\n      } else {\n        // If the rounding digits are null, 0{0,4} or 50{0,3}, check for an exact result.\n        // If not, then there are further digits and m will be truthy.\n        if (!+n || !+n.slice(1) && n.charAt(0) == '5') {\n          // Truncate to the first rounding digit.\n          finalise(r, e + 1, 1);\n          m = !r.times(r).eq(x);\n        }\n        break;\n      }\n    }\n  }\n  external = true;\n  return finalise(r, e, Ctor.rounding, m);\n};\n\n/*\r\n * Return a new Decimal whose value is the tangent of the value in radians of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * tan(0)         = 0\r\n * tan(-0)        = -0\r\n * tan(Infinity)  = NaN\r\n * tan(-Infinity) = NaN\r\n * tan(NaN)       = NaN\r\n *\r\n */\nP.tangent = P.tan = function () {\n  var pr,\n    rm,\n    x = this,\n    Ctor = x.constructor;\n  if (!x.isFinite()) return new Ctor(NaN);\n  if (x.isZero()) return new Ctor(x);\n  pr = Ctor.precision;\n  rm = Ctor.rounding;\n  Ctor.precision = pr + 10;\n  Ctor.rounding = 1;\n  x = x.sin();\n  x.s = 1;\n  x = divide(x, new Ctor(1).minus(x.times(x)).sqrt(), pr + 10, 0);\n  Ctor.precision = pr;\n  Ctor.rounding = rm;\n  return finalise(quadrant == 2 || quadrant == 4 ? x.neg() : x, pr, rm, true);\n};\n\n/*\r\n *  n * 0 = 0\r\n *  n * N = N\r\n *  n * I = I\r\n *  0 * n = 0\r\n *  0 * 0 = 0\r\n *  0 * N = N\r\n *  0 * I = N\r\n *  N * n = N\r\n *  N * 0 = N\r\n *  N * N = N\r\n *  N * I = N\r\n *  I * n = I\r\n *  I * 0 = N\r\n *  I * N = N\r\n *  I * I = I\r\n *\r\n * Return a new Decimal whose value is this Decimal times `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n */\nP.times = P.mul = function (y) {\n  var carry,\n    e,\n    i,\n    k,\n    r,\n    rL,\n    t,\n    xdL,\n    ydL,\n    x = this,\n    Ctor = x.constructor,\n    xd = x.d,\n    yd = (y = new Ctor(y)).d;\n  y.s *= x.s;\n\n  // If either is NaN, ±Infinity or ±0...\n  if (!xd || !xd[0] || !yd || !yd[0]) {\n    return new Ctor(!y.s || xd && !xd[0] && !yd || yd && !yd[0] && !xd\n\n    // Return NaN if either is NaN.\n    // Return NaN if x is ±0 and y is ±Infinity, or y is ±0 and x is ±Infinity.\n    ? NaN\n\n    // Return ±Infinity if either is ±Infinity.\n    // Return ±0 if either is ±0.\n    : !xd || !yd ? y.s / 0 : y.s * 0);\n  }\n  e = mathfloor(x.e / LOG_BASE) + mathfloor(y.e / LOG_BASE);\n  xdL = xd.length;\n  ydL = yd.length;\n\n  // Ensure xd points to the longer array.\n  if (xdL < ydL) {\n    r = xd;\n    xd = yd;\n    yd = r;\n    rL = xdL;\n    xdL = ydL;\n    ydL = rL;\n  }\n\n  // Initialise the result array with zeros.\n  r = [];\n  rL = xdL + ydL;\n  for (i = rL; i--;) r.push(0);\n\n  // Multiply!\n  for (i = ydL; --i >= 0;) {\n    carry = 0;\n    for (k = xdL + i; k > i;) {\n      t = r[k] + yd[i] * xd[k - i - 1] + carry;\n      r[k--] = t % BASE | 0;\n      carry = t / BASE | 0;\n    }\n    r[k] = (r[k] + carry) % BASE | 0;\n  }\n\n  // Remove trailing zeros.\n  for (; !r[--rL];) r.pop();\n  if (carry) ++e;else r.shift();\n  y.d = r;\n  y.e = getBase10Exponent(r, e);\n  return external ? finalise(y, Ctor.precision, Ctor.rounding) : y;\n};\n\n/*\r\n * Return a string representing the value of this Decimal in base 2, round to `sd` significant\r\n * digits using rounding mode `rm`.\r\n *\r\n * If the optional `sd` argument is present then return binary exponential notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\nP.toBinary = function (sd, rm) {\n  return toStringBinary(this, 2, sd, rm);\n};\n\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `dp`\r\n * decimal places using rounding mode `rm` or `rounding` if `rm` is omitted.\r\n *\r\n * If `dp` is omitted, return a new Decimal whose value is the value of this Decimal.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\nP.toDecimalPlaces = P.toDP = function (dp, rm) {\n  var x = this,\n    Ctor = x.constructor;\n  x = new Ctor(x);\n  if (dp === void 0) return x;\n  checkInt32(dp, 0, MAX_DIGITS);\n  if (rm === void 0) rm = Ctor.rounding;else checkInt32(rm, 0, 8);\n  return finalise(x, dp + x.e + 1, rm);\n};\n\n/*\r\n * Return a string representing the value of this Decimal in exponential notation rounded to\r\n * `dp` fixed decimal places using rounding mode `rounding`.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\nP.toExponential = function (dp, rm) {\n  var str,\n    x = this,\n    Ctor = x.constructor;\n  if (dp === void 0) {\n    str = finiteToString(x, true);\n  } else {\n    checkInt32(dp, 0, MAX_DIGITS);\n    if (rm === void 0) rm = Ctor.rounding;else checkInt32(rm, 0, 8);\n    x = finalise(new Ctor(x), dp + 1, rm);\n    str = finiteToString(x, true, dp + 1);\n  }\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\n};\n\n/*\r\n * Return a string representing the value of this Decimal in normal (fixed-point) notation to\r\n * `dp` fixed decimal places and rounded using rounding mode `rm` or `rounding` if `rm` is\r\n * omitted.\r\n *\r\n * As with JavaScript numbers, (-0).toFixed(0) is '0', but e.g. (-0.00001).toFixed(0) is '-0'.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n * (-0).toFixed(3) is '0.000'.\r\n * (-0.5).toFixed(0) is '-0'.\r\n *\r\n */\nP.toFixed = function (dp, rm) {\n  var str,\n    y,\n    x = this,\n    Ctor = x.constructor;\n  if (dp === void 0) {\n    str = finiteToString(x);\n  } else {\n    checkInt32(dp, 0, MAX_DIGITS);\n    if (rm === void 0) rm = Ctor.rounding;else checkInt32(rm, 0, 8);\n    y = finalise(new Ctor(x), dp + x.e + 1, rm);\n    str = finiteToString(y, false, dp + y.e + 1);\n  }\n\n  // To determine whether to add the minus sign look at the value before it was rounded,\n  // i.e. look at `x` rather than `y`.\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\n};\n\n/*\r\n * Return an array representing the value of this Decimal as a simple fraction with an integer\r\n * numerator and an integer denominator.\r\n *\r\n * The denominator will be a positive non-zero value less than or equal to the specified maximum\r\n * denominator. If a maximum denominator is not specified, the denominator will be the lowest\r\n * value necessary to represent the number exactly.\r\n *\r\n * [maxD] {number|string|bigint|Decimal} Maximum denominator. Integer >= 1 and < Infinity.\r\n *\r\n */\nP.toFraction = function (maxD) {\n  var d,\n    d0,\n    d1,\n    d2,\n    e,\n    k,\n    n,\n    n0,\n    n1,\n    pr,\n    q,\n    r,\n    x = this,\n    xd = x.d,\n    Ctor = x.constructor;\n  if (!xd) return new Ctor(x);\n  n1 = d0 = new Ctor(1);\n  d1 = n0 = new Ctor(0);\n  d = new Ctor(d1);\n  e = d.e = getPrecision(xd) - x.e - 1;\n  k = e % LOG_BASE;\n  d.d[0] = mathpow(10, k < 0 ? LOG_BASE + k : k);\n  if (maxD == null) {\n    // d is 10**e, the minimum max-denominator needed.\n    maxD = e > 0 ? d : n1;\n  } else {\n    n = new Ctor(maxD);\n    if (!n.isInt() || n.lt(n1)) throw Error(invalidArgument + n);\n    maxD = n.gt(d) ? e > 0 ? d : n1 : n;\n  }\n  external = false;\n  n = new Ctor(digitsToString(xd));\n  pr = Ctor.precision;\n  Ctor.precision = e = xd.length * LOG_BASE * 2;\n  for (;;) {\n    q = divide(n, d, 0, 1, 1);\n    d2 = d0.plus(q.times(d1));\n    if (d2.cmp(maxD) == 1) break;\n    d0 = d1;\n    d1 = d2;\n    d2 = n1;\n    n1 = n0.plus(q.times(d2));\n    n0 = d2;\n    d2 = d;\n    d = n.minus(q.times(d2));\n    n = d2;\n  }\n  d2 = divide(maxD.minus(d0), d1, 0, 1, 1);\n  n0 = n0.plus(d2.times(n1));\n  d0 = d0.plus(d2.times(d1));\n  n0.s = n1.s = x.s;\n\n  // Determine which fraction is closer to x, n0/d0 or n1/d1?\n  r = divide(n1, d1, e, 1).minus(x).abs().cmp(divide(n0, d0, e, 1).minus(x).abs()) < 1 ? [n1, d1] : [n0, d0];\n  Ctor.precision = pr;\n  external = true;\n  return r;\n};\n\n/*\r\n * Return a string representing the value of this Decimal in base 16, round to `sd` significant\r\n * digits using rounding mode `rm`.\r\n *\r\n * If the optional `sd` argument is present then return binary exponential notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\nP.toHexadecimal = P.toHex = function (sd, rm) {\n  return toStringBinary(this, 16, sd, rm);\n};\n\n/*\r\n * Returns a new Decimal whose value is the nearest multiple of `y` in the direction of rounding\r\n * mode `rm`, or `Decimal.rounding` if `rm` is omitted, to the value of this Decimal.\r\n *\r\n * The return value will always have the same sign as this Decimal, unless either this Decimal\r\n * or `y` is NaN, in which case the return value will be also be NaN.\r\n *\r\n * The return value is not affected by the value of `precision`.\r\n *\r\n * y {number|string|bigint|Decimal} The magnitude to round to a multiple of.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * 'toNearest() rounding mode not an integer: {rm}'\r\n * 'toNearest() rounding mode out of range: {rm}'\r\n *\r\n */\nP.toNearest = function (y, rm) {\n  var x = this,\n    Ctor = x.constructor;\n  x = new Ctor(x);\n  if (y == null) {\n    // If x is not finite, return x.\n    if (!x.d) return x;\n    y = new Ctor(1);\n    rm = Ctor.rounding;\n  } else {\n    y = new Ctor(y);\n    if (rm === void 0) {\n      rm = Ctor.rounding;\n    } else {\n      checkInt32(rm, 0, 8);\n    }\n\n    // If x is not finite, return x if y is not NaN, else NaN.\n    if (!x.d) return y.s ? x : y;\n\n    // If y is not finite, return Infinity with the sign of x if y is Infinity, else NaN.\n    if (!y.d) {\n      if (y.s) y.s = x.s;\n      return y;\n    }\n  }\n\n  // If y is not zero, calculate the nearest multiple of y to x.\n  if (y.d[0]) {\n    external = false;\n    x = divide(x, y, 0, rm, 1).times(y);\n    external = true;\n    finalise(x);\n\n    // If y is zero, return zero with the sign of x.\n  } else {\n    y.s = x.s;\n    x = y;\n  }\n  return x;\n};\n\n/*\r\n * Return the value of this Decimal converted to a number primitive.\r\n * Zero keeps its sign.\r\n *\r\n */\nP.toNumber = function () {\n  return +this;\n};\n\n/*\r\n * Return a string representing the value of this Decimal in base 8, round to `sd` significant\r\n * digits using rounding mode `rm`.\r\n *\r\n * If the optional `sd` argument is present then return binary exponential notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\nP.toOctal = function (sd, rm) {\n  return toStringBinary(this, 8, sd, rm);\n};\n\n/*\r\n * Return a new Decimal whose value is the value of this Decimal raised to the power `y`, rounded\r\n * to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * ECMAScript compliant.\r\n *\r\n *   pow(x, NaN)                           = NaN\r\n *   pow(x, ±0)                            = 1\r\n\r\n *   pow(NaN, non-zero)                    = NaN\r\n *   pow(abs(x) > 1, +Infinity)            = +Infinity\r\n *   pow(abs(x) > 1, -Infinity)            = +0\r\n *   pow(abs(x) == 1, ±Infinity)           = NaN\r\n *   pow(abs(x) < 1, +Infinity)            = +0\r\n *   pow(abs(x) < 1, -Infinity)            = +Infinity\r\n *   pow(+Infinity, y > 0)                 = +Infinity\r\n *   pow(+Infinity, y < 0)                 = +0\r\n *   pow(-Infinity, odd integer > 0)       = -Infinity\r\n *   pow(-Infinity, even integer > 0)      = +Infinity\r\n *   pow(-Infinity, odd integer < 0)       = -0\r\n *   pow(-Infinity, even integer < 0)      = +0\r\n *   pow(+0, y > 0)                        = +0\r\n *   pow(+0, y < 0)                        = +Infinity\r\n *   pow(-0, odd integer > 0)              = -0\r\n *   pow(-0, even integer > 0)             = +0\r\n *   pow(-0, odd integer < 0)              = -Infinity\r\n *   pow(-0, even integer < 0)             = +Infinity\r\n *   pow(finite x < 0, finite non-integer) = NaN\r\n *\r\n * For non-integer or very large exponents pow(x, y) is calculated using\r\n *\r\n *   x^y = exp(y*ln(x))\r\n *\r\n * Assuming the first 15 rounding digits are each equally likely to be any digit 0-9, the\r\n * probability of an incorrectly rounded result\r\n * P([49]9{14} | [50]0{14}) = 2 * 0.2 * 10^-14 = 4e-15 = 1/2.5e+14\r\n * i.e. 1 in 250,000,000,000,000\r\n *\r\n * If a result is incorrectly rounded the maximum error will be 1 ulp (unit in last place).\r\n *\r\n * y {number|string|bigint|Decimal} The power to which to raise this Decimal.\r\n *\r\n */\nP.toPower = P.pow = function (y) {\n  var e,\n    k,\n    pr,\n    r,\n    rm,\n    s,\n    x = this,\n    Ctor = x.constructor,\n    yn = +(y = new Ctor(y));\n\n  // Either ±Infinity, NaN or ±0?\n  if (!x.d || !y.d || !x.d[0] || !y.d[0]) return new Ctor(mathpow(+x, yn));\n  x = new Ctor(x);\n  if (x.eq(1)) return x;\n  pr = Ctor.precision;\n  rm = Ctor.rounding;\n  if (y.eq(1)) return finalise(x, pr, rm);\n\n  // y exponent\n  e = mathfloor(y.e / LOG_BASE);\n\n  // If y is a small integer use the 'exponentiation by squaring' algorithm.\n  if (e >= y.d.length - 1 && (k = yn < 0 ? -yn : yn) <= MAX_SAFE_INTEGER) {\n    r = intPow(Ctor, x, k, pr);\n    return y.s < 0 ? new Ctor(1).div(r) : finalise(r, pr, rm);\n  }\n  s = x.s;\n\n  // if x is negative\n  if (s < 0) {\n    // if y is not an integer\n    if (e < y.d.length - 1) return new Ctor(NaN);\n\n    // Result is positive if x is negative and the last digit of integer y is even.\n    if ((y.d[e] & 1) == 0) s = 1;\n\n    // if x.eq(-1)\n    if (x.e == 0 && x.d[0] == 1 && x.d.length == 1) {\n      x.s = s;\n      return x;\n    }\n  }\n\n  // Estimate result exponent.\n  // x^y = 10^e,  where e = y * log10(x)\n  // log10(x) = log10(x_significand) + x_exponent\n  // log10(x_significand) = ln(x_significand) / ln(10)\n  k = mathpow(+x, yn);\n  e = k == 0 || !isFinite(k) ? mathfloor(yn * (Math.log('0.' + digitsToString(x.d)) / Math.LN10 + x.e + 1)) : new Ctor(k + '').e;\n\n  // Exponent estimate may be incorrect e.g. x: 0.999999999999999999, y: 2.29, e: 0, r.e: -1.\n\n  // Overflow/underflow?\n  if (e > Ctor.maxE + 1 || e < Ctor.minE - 1) return new Ctor(e > 0 ? s / 0 : 0);\n  external = false;\n  Ctor.rounding = x.s = 1;\n\n  // Estimate the extra guard digits needed to ensure five correct rounding digits from\n  // naturalLogarithm(x). Example of failure without these extra digits (precision: 10):\n  // new Decimal(2.32456).pow('2087987436534566.46411')\n  // should be 1.162377823e+764914905173815, but is 1.162355823e+764914905173815\n  k = Math.min(12, (e + '').length);\n\n  // r = x^y = exp(y*ln(x))\n  r = naturalExponential(y.times(naturalLogarithm(x, pr + k)), pr);\n\n  // r may be Infinity, e.g. (0.9999999999999999).pow(-1e+40)\n  if (r.d) {\n    // Truncate to the required precision plus five rounding digits.\n    r = finalise(r, pr + 5, 1);\n\n    // If the rounding digits are [49]9999 or [50]0000 increase the precision by 10 and recalculate\n    // the result.\n    if (checkRoundingDigits(r.d, pr, rm)) {\n      e = pr + 10;\n\n      // Truncate to the increased precision plus five rounding digits.\n      r = finalise(naturalExponential(y.times(naturalLogarithm(x, e + k)), e), e + 5, 1);\n\n      // Check for 14 nines from the 2nd rounding digit (the first rounding digit may be 4 or 9).\n      if (+digitsToString(r.d).slice(pr + 1, pr + 15) + 1 == 1e14) {\n        r = finalise(r, pr + 1, 0);\n      }\n    }\n  }\n  r.s = s;\n  external = true;\n  Ctor.rounding = rm;\n  return finalise(r, pr, rm);\n};\n\n/*\r\n * Return a string representing the value of this Decimal rounded to `sd` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * Return exponential notation if `sd` is less than the number of digits necessary to represent\r\n * the integer part of the value in normal notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\nP.toPrecision = function (sd, rm) {\n  var str,\n    x = this,\n    Ctor = x.constructor;\n  if (sd === void 0) {\n    str = finiteToString(x, x.e <= Ctor.toExpNeg || x.e >= Ctor.toExpPos);\n  } else {\n    checkInt32(sd, 1, MAX_DIGITS);\n    if (rm === void 0) rm = Ctor.rounding;else checkInt32(rm, 0, 8);\n    x = finalise(new Ctor(x), sd, rm);\n    str = finiteToString(x, sd <= x.e || x.e <= Ctor.toExpNeg, sd);\n  }\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\n};\n\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `sd`\r\n * significant digits using rounding mode `rm`, or to `precision` and `rounding` respectively if\r\n * omitted.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * 'toSD() digits out of range: {sd}'\r\n * 'toSD() digits not an integer: {sd}'\r\n * 'toSD() rounding mode not an integer: {rm}'\r\n * 'toSD() rounding mode out of range: {rm}'\r\n *\r\n */\nP.toSignificantDigits = P.toSD = function (sd, rm) {\n  var x = this,\n    Ctor = x.constructor;\n  if (sd === void 0) {\n    sd = Ctor.precision;\n    rm = Ctor.rounding;\n  } else {\n    checkInt32(sd, 1, MAX_DIGITS);\n    if (rm === void 0) rm = Ctor.rounding;else checkInt32(rm, 0, 8);\n  }\n  return finalise(new Ctor(x), sd, rm);\n};\n\n/*\r\n * Return a string representing the value of this Decimal.\r\n *\r\n * Return exponential notation if this Decimal has a positive exponent equal to or greater than\r\n * `toExpPos`, or a negative exponent equal to or less than `toExpNeg`.\r\n *\r\n */\nP.toString = function () {\n  var x = this,\n    Ctor = x.constructor,\n    str = finiteToString(x, x.e <= Ctor.toExpNeg || x.e >= Ctor.toExpPos);\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\n};\n\n/*\r\n * Return a new Decimal whose value is the value of this Decimal truncated to a whole number.\r\n *\r\n */\nP.truncated = P.trunc = function () {\n  return finalise(new this.constructor(this), this.e + 1, 1);\n};\n\n/*\r\n * Return a string representing the value of this Decimal.\r\n * Unlike `toString`, negative zero will include the minus sign.\r\n *\r\n */\nP.valueOf = P.toJSON = function () {\n  var x = this,\n    Ctor = x.constructor,\n    str = finiteToString(x, x.e <= Ctor.toExpNeg || x.e >= Ctor.toExpPos);\n  return x.isNeg() ? '-' + str : str;\n};\n\n// Helper functions for Decimal.prototype (P) and/or Decimal methods, and their callers.\n\n/*\r\n *  digitsToString           P.cubeRoot, P.logarithm, P.squareRoot, P.toFraction, P.toPower,\r\n *                           finiteToString, naturalExponential, naturalLogarithm\r\n *  checkInt32               P.toDecimalPlaces, P.toExponential, P.toFixed, P.toNearest,\r\n *                           P.toPrecision, P.toSignificantDigits, toStringBinary, random\r\n *  checkRoundingDigits      P.logarithm, P.toPower, naturalExponential, naturalLogarithm\r\n *  convertBase              toStringBinary, parseOther\r\n *  cos                      P.cos\r\n *  divide                   P.atanh, P.cubeRoot, P.dividedBy, P.dividedToIntegerBy,\r\n *                           P.logarithm, P.modulo, P.squareRoot, P.tan, P.tanh, P.toFraction,\r\n *                           P.toNearest, toStringBinary, naturalExponential, naturalLogarithm,\r\n *                           taylorSeries, atan2, parseOther\r\n *  finalise                 P.absoluteValue, P.atan, P.atanh, P.ceil, P.cos, P.cosh,\r\n *                           P.cubeRoot, P.dividedToIntegerBy, P.floor, P.logarithm, P.minus,\r\n *                           P.modulo, P.negated, P.plus, P.round, P.sin, P.sinh, P.squareRoot,\r\n *                           P.tan, P.times, P.toDecimalPlaces, P.toExponential, P.toFixed,\r\n *                           P.toNearest, P.toPower, P.toPrecision, P.toSignificantDigits,\r\n *                           P.truncated, divide, getLn10, getPi, naturalExponential,\r\n *                           naturalLogarithm, ceil, floor, round, trunc\r\n *  finiteToString           P.toExponential, P.toFixed, P.toPrecision, P.toString, P.valueOf,\r\n *                           toStringBinary\r\n *  getBase10Exponent        P.minus, P.plus, P.times, parseOther\r\n *  getLn10                  P.logarithm, naturalLogarithm\r\n *  getPi                    P.acos, P.asin, P.atan, toLessThanHalfPi, atan2\r\n *  getPrecision             P.precision, P.toFraction\r\n *  getZeroString            digitsToString, finiteToString\r\n *  intPow                   P.toPower, parseOther\r\n *  isOdd                    toLessThanHalfPi\r\n *  maxOrMin                 max, min\r\n *  naturalExponential       P.naturalExponential, P.toPower\r\n *  naturalLogarithm         P.acosh, P.asinh, P.atanh, P.logarithm, P.naturalLogarithm,\r\n *                           P.toPower, naturalExponential\r\n *  nonFiniteToString        finiteToString, toStringBinary\r\n *  parseDecimal             Decimal\r\n *  parseOther               Decimal\r\n *  sin                      P.sin\r\n *  taylorSeries             P.cosh, P.sinh, cos, sin\r\n *  toLessThanHalfPi         P.cos, P.sin\r\n *  toStringBinary           P.toBinary, P.toHexadecimal, P.toOctal\r\n *  truncate                 intPow\r\n *\r\n *  Throws:                  P.logarithm, P.precision, P.toFraction, checkInt32, getLn10, getPi,\r\n *                           naturalLogarithm, config, parseOther, random, Decimal\r\n */\n\nfunction digitsToString(d) {\n  var i,\n    k,\n    ws,\n    indexOfLastWord = d.length - 1,\n    str = '',\n    w = d[0];\n  if (indexOfLastWord > 0) {\n    str += w;\n    for (i = 1; i < indexOfLastWord; i++) {\n      ws = d[i] + '';\n      k = LOG_BASE - ws.length;\n      if (k) str += getZeroString(k);\n      str += ws;\n    }\n    w = d[i];\n    ws = w + '';\n    k = LOG_BASE - ws.length;\n    if (k) str += getZeroString(k);\n  } else if (w === 0) {\n    return '0';\n  }\n\n  // Remove trailing zeros of last w.\n  for (; w % 10 === 0;) w /= 10;\n  return str + w;\n}\nfunction checkInt32(i, min, max) {\n  if (i !== ~~i || i < min || i > max) {\n    throw Error(invalidArgument + i);\n  }\n}\n\n/*\r\n * Check 5 rounding digits if `repeating` is null, 4 otherwise.\r\n * `repeating == null` if caller is `log` or `pow`,\r\n * `repeating != null` if caller is `naturalLogarithm` or `naturalExponential`.\r\n */\nfunction checkRoundingDigits(d, i, rm, repeating) {\n  var di, k, r, rd;\n\n  // Get the length of the first word of the array d.\n  for (k = d[0]; k >= 10; k /= 10) --i;\n\n  // Is the rounding digit in the first word of d?\n  if (--i < 0) {\n    i += LOG_BASE;\n    di = 0;\n  } else {\n    di = Math.ceil((i + 1) / LOG_BASE);\n    i %= LOG_BASE;\n  }\n\n  // i is the index (0 - 6) of the rounding digit.\n  // E.g. if within the word 3487563 the first rounding digit is 5,\n  // then i = 4, k = 1000, rd = 3487563 % 1000 = 563\n  k = mathpow(10, LOG_BASE - i);\n  rd = d[di] % k | 0;\n  if (repeating == null) {\n    if (i < 3) {\n      if (i == 0) rd = rd / 100 | 0;else if (i == 1) rd = rd / 10 | 0;\n      r = rm < 4 && rd == 99999 || rm > 3 && rd == 49999 || rd == 50000 || rd == 0;\n    } else {\n      r = (rm < 4 && rd + 1 == k || rm > 3 && rd + 1 == k / 2) && (d[di + 1] / k / 100 | 0) == mathpow(10, i - 2) - 1 || (rd == k / 2 || rd == 0) && (d[di + 1] / k / 100 | 0) == 0;\n    }\n  } else {\n    if (i < 4) {\n      if (i == 0) rd = rd / 1000 | 0;else if (i == 1) rd = rd / 100 | 0;else if (i == 2) rd = rd / 10 | 0;\n      r = (repeating || rm < 4) && rd == 9999 || !repeating && rm > 3 && rd == 4999;\n    } else {\n      r = ((repeating || rm < 4) && rd + 1 == k || !repeating && rm > 3 && rd + 1 == k / 2) && (d[di + 1] / k / 1000 | 0) == mathpow(10, i - 3) - 1;\n    }\n  }\n  return r;\n}\n\n// Convert string of `baseIn` to an array of numbers of `baseOut`.\n// Eg. convertBase('255', 10, 16) returns [15, 15].\n// Eg. convertBase('ff', 16, 10) returns [2, 5, 5].\nfunction convertBase(str, baseIn, baseOut) {\n  var j,\n    arr = [0],\n    arrL,\n    i = 0,\n    strL = str.length;\n  for (; i < strL;) {\n    for (arrL = arr.length; arrL--;) arr[arrL] *= baseIn;\n    arr[0] += NUMERALS.indexOf(str.charAt(i++));\n    for (j = 0; j < arr.length; j++) {\n      if (arr[j] > baseOut - 1) {\n        if (arr[j + 1] === void 0) arr[j + 1] = 0;\n        arr[j + 1] += arr[j] / baseOut | 0;\n        arr[j] %= baseOut;\n      }\n    }\n  }\n  return arr.reverse();\n}\n\n/*\r\n * cos(x) = 1 - x^2/2! + x^4/4! - ...\r\n * |x| < pi/2\r\n *\r\n */\nfunction cosine(Ctor, x) {\n  var k, len, y;\n  if (x.isZero()) return x;\n\n  // Argument reduction: cos(4x) = 8*(cos^4(x) - cos^2(x)) + 1\n  // i.e. cos(x) = 8*(cos^4(x/4) - cos^2(x/4)) + 1\n\n  // Estimate the optimum number of times to use the argument reduction.\n  len = x.d.length;\n  if (len < 32) {\n    k = Math.ceil(len / 3);\n    y = (1 / tinyPow(4, k)).toString();\n  } else {\n    k = 16;\n    y = '2.3283064365386962890625e-10';\n  }\n  Ctor.precision += k;\n  x = taylorSeries(Ctor, 1, x.times(y), new Ctor(1));\n\n  // Reverse argument reduction\n  for (var i = k; i--;) {\n    var cos2x = x.times(x);\n    x = cos2x.times(cos2x).minus(cos2x).times(8).plus(1);\n  }\n  Ctor.precision -= k;\n  return x;\n}\n\n/*\r\n * Perform division in the specified base.\r\n */\nvar divide = function () {\n  // Assumes non-zero x and k, and hence non-zero result.\n  function multiplyInteger(x, k, base) {\n    var temp,\n      carry = 0,\n      i = x.length;\n    for (x = x.slice(); i--;) {\n      temp = x[i] * k + carry;\n      x[i] = temp % base | 0;\n      carry = temp / base | 0;\n    }\n    if (carry) x.unshift(carry);\n    return x;\n  }\n  function compare(a, b, aL, bL) {\n    var i, r;\n    if (aL != bL) {\n      r = aL > bL ? 1 : -1;\n    } else {\n      for (i = r = 0; i < aL; i++) {\n        if (a[i] != b[i]) {\n          r = a[i] > b[i] ? 1 : -1;\n          break;\n        }\n      }\n    }\n    return r;\n  }\n  function subtract(a, b, aL, base) {\n    var i = 0;\n\n    // Subtract b from a.\n    for (; aL--;) {\n      a[aL] -= i;\n      i = a[aL] < b[aL] ? 1 : 0;\n      a[aL] = i * base + a[aL] - b[aL];\n    }\n\n    // Remove leading zeros.\n    for (; !a[0] && a.length > 1;) a.shift();\n  }\n  return function (x, y, pr, rm, dp, base) {\n    var cmp,\n      e,\n      i,\n      k,\n      logBase,\n      more,\n      prod,\n      prodL,\n      q,\n      qd,\n      rem,\n      remL,\n      rem0,\n      sd,\n      t,\n      xi,\n      xL,\n      yd0,\n      yL,\n      yz,\n      Ctor = x.constructor,\n      sign = x.s == y.s ? 1 : -1,\n      xd = x.d,\n      yd = y.d;\n\n    // Either NaN, Infinity or 0?\n    if (!xd || !xd[0] || !yd || !yd[0]) {\n      return new Ctor(\n      // Return NaN if either NaN, or both Infinity or 0.\n      !x.s || !y.s || (xd ? yd && xd[0] == yd[0] : !yd) ? NaN :\n      // Return ±0 if x is 0 or y is ±Infinity, or return ±Infinity as y is 0.\n      xd && xd[0] == 0 || !yd ? sign * 0 : sign / 0);\n    }\n    if (base) {\n      logBase = 1;\n      e = x.e - y.e;\n    } else {\n      base = BASE;\n      logBase = LOG_BASE;\n      e = mathfloor(x.e / logBase) - mathfloor(y.e / logBase);\n    }\n    yL = yd.length;\n    xL = xd.length;\n    q = new Ctor(sign);\n    qd = q.d = [];\n\n    // Result exponent may be one less than e.\n    // The digit array of a Decimal from toStringBinary may have trailing zeros.\n    for (i = 0; yd[i] == (xd[i] || 0); i++);\n    if (yd[i] > (xd[i] || 0)) e--;\n    if (pr == null) {\n      sd = pr = Ctor.precision;\n      rm = Ctor.rounding;\n    } else if (dp) {\n      sd = pr + (x.e - y.e) + 1;\n    } else {\n      sd = pr;\n    }\n    if (sd < 0) {\n      qd.push(1);\n      more = true;\n    } else {\n      // Convert precision in number of base 10 digits to base 1e7 digits.\n      sd = sd / logBase + 2 | 0;\n      i = 0;\n\n      // divisor < 1e7\n      if (yL == 1) {\n        k = 0;\n        yd = yd[0];\n        sd++;\n\n        // k is the carry.\n        for (; (i < xL || k) && sd--; i++) {\n          t = k * base + (xd[i] || 0);\n          qd[i] = t / yd | 0;\n          k = t % yd | 0;\n        }\n        more = k || i < xL;\n\n        // divisor >= 1e7\n      } else {\n        // Normalise xd and yd so highest order digit of yd is >= base/2\n        k = base / (yd[0] + 1) | 0;\n        if (k > 1) {\n          yd = multiplyInteger(yd, k, base);\n          xd = multiplyInteger(xd, k, base);\n          yL = yd.length;\n          xL = xd.length;\n        }\n        xi = yL;\n        rem = xd.slice(0, yL);\n        remL = rem.length;\n\n        // Add zeros to make remainder as long as divisor.\n        for (; remL < yL;) rem[remL++] = 0;\n        yz = yd.slice();\n        yz.unshift(0);\n        yd0 = yd[0];\n        if (yd[1] >= base / 2) ++yd0;\n        do {\n          k = 0;\n\n          // Compare divisor and remainder.\n          cmp = compare(yd, rem, yL, remL);\n\n          // If divisor < remainder.\n          if (cmp < 0) {\n            // Calculate trial digit, k.\n            rem0 = rem[0];\n            if (yL != remL) rem0 = rem0 * base + (rem[1] || 0);\n\n            // k will be how many times the divisor goes into the current remainder.\n            k = rem0 / yd0 | 0;\n\n            //  Algorithm:\n            //  1. product = divisor * trial digit (k)\n            //  2. if product > remainder: product -= divisor, k--\n            //  3. remainder -= product\n            //  4. if product was < remainder at 2:\n            //    5. compare new remainder and divisor\n            //    6. If remainder > divisor: remainder -= divisor, k++\n\n            if (k > 1) {\n              if (k >= base) k = base - 1;\n\n              // product = divisor * trial digit.\n              prod = multiplyInteger(yd, k, base);\n              prodL = prod.length;\n              remL = rem.length;\n\n              // Compare product and remainder.\n              cmp = compare(prod, rem, prodL, remL);\n\n              // product > remainder.\n              if (cmp == 1) {\n                k--;\n\n                // Subtract divisor from product.\n                subtract(prod, yL < prodL ? yz : yd, prodL, base);\n              }\n            } else {\n              // cmp is -1.\n              // If k is 0, there is no need to compare yd and rem again below, so change cmp to 1\n              // to avoid it. If k is 1 there is a need to compare yd and rem again below.\n              if (k == 0) cmp = k = 1;\n              prod = yd.slice();\n            }\n            prodL = prod.length;\n            if (prodL < remL) prod.unshift(0);\n\n            // Subtract product from remainder.\n            subtract(rem, prod, remL, base);\n\n            // If product was < previous remainder.\n            if (cmp == -1) {\n              remL = rem.length;\n\n              // Compare divisor and new remainder.\n              cmp = compare(yd, rem, yL, remL);\n\n              // If divisor < new remainder, subtract divisor from remainder.\n              if (cmp < 1) {\n                k++;\n\n                // Subtract divisor from remainder.\n                subtract(rem, yL < remL ? yz : yd, remL, base);\n              }\n            }\n            remL = rem.length;\n          } else if (cmp === 0) {\n            k++;\n            rem = [0];\n          } // if cmp === 1, k will be 0\n\n          // Add the next digit, k, to the result array.\n          qd[i++] = k;\n\n          // Update the remainder.\n          if (cmp && rem[0]) {\n            rem[remL++] = xd[xi] || 0;\n          } else {\n            rem = [xd[xi]];\n            remL = 1;\n          }\n        } while ((xi++ < xL || rem[0] !== void 0) && sd--);\n        more = rem[0] !== void 0;\n      }\n\n      // Leading zero?\n      if (!qd[0]) qd.shift();\n    }\n\n    // logBase is 1 when divide is being used for base conversion.\n    if (logBase == 1) {\n      q.e = e;\n      inexact = more;\n    } else {\n      // To calculate q.e, first get the number of digits of qd[0].\n      for (i = 1, k = qd[0]; k >= 10; k /= 10) i++;\n      q.e = i + e * logBase - 1;\n      finalise(q, dp ? pr + q.e + 1 : pr, rm, more);\n    }\n    return q;\n  };\n}();\n\n/*\r\n * Round `x` to `sd` significant digits using rounding mode `rm`.\r\n * Check for over/under-flow.\r\n */\nfunction finalise(x, sd, rm, isTruncated) {\n  var digits,\n    i,\n    j,\n    k,\n    rd,\n    roundUp,\n    w,\n    xd,\n    xdi,\n    Ctor = x.constructor;\n\n  // Don't round if sd is null or undefined.\n  out: if (sd != null) {\n    xd = x.d;\n\n    // Infinity/NaN.\n    if (!xd) return x;\n\n    // rd: the rounding digit, i.e. the digit after the digit that may be rounded up.\n    // w: the word of xd containing rd, a base 1e7 number.\n    // xdi: the index of w within xd.\n    // digits: the number of digits of w.\n    // i: what would be the index of rd within w if all the numbers were 7 digits long (i.e. if\n    // they had leading zeros)\n    // j: if > 0, the actual index of rd within w (if < 0, rd is a leading zero).\n\n    // Get the length of the first word of the digits array xd.\n    for (digits = 1, k = xd[0]; k >= 10; k /= 10) digits++;\n    i = sd - digits;\n\n    // Is the rounding digit in the first word of xd?\n    if (i < 0) {\n      i += LOG_BASE;\n      j = sd;\n      w = xd[xdi = 0];\n\n      // Get the rounding digit at index j of w.\n      rd = w / mathpow(10, digits - j - 1) % 10 | 0;\n    } else {\n      xdi = Math.ceil((i + 1) / LOG_BASE);\n      k = xd.length;\n      if (xdi >= k) {\n        if (isTruncated) {\n          // Needed by `naturalExponential`, `naturalLogarithm` and `squareRoot`.\n          for (; k++ <= xdi;) xd.push(0);\n          w = rd = 0;\n          digits = 1;\n          i %= LOG_BASE;\n          j = i - LOG_BASE + 1;\n        } else {\n          break out;\n        }\n      } else {\n        w = k = xd[xdi];\n\n        // Get the number of digits of w.\n        for (digits = 1; k >= 10; k /= 10) digits++;\n\n        // Get the index of rd within w.\n        i %= LOG_BASE;\n\n        // Get the index of rd within w, adjusted for leading zeros.\n        // The number of leading zeros of w is given by LOG_BASE - digits.\n        j = i - LOG_BASE + digits;\n\n        // Get the rounding digit at index j of w.\n        rd = j < 0 ? 0 : w / mathpow(10, digits - j - 1) % 10 | 0;\n      }\n    }\n\n    // Are there any non-zero digits after the rounding digit?\n    isTruncated = isTruncated || sd < 0 || xd[xdi + 1] !== void 0 || (j < 0 ? w : w % mathpow(10, digits - j - 1));\n\n    // The expression `w % mathpow(10, digits - j - 1)` returns all the digits of w to the right\n    // of the digit at (left-to-right) index j, e.g. if w is 908714 and j is 2, the expression\n    // will give 714.\n\n    roundUp = rm < 4 ? (rd || isTruncated) && (rm == 0 || rm == (x.s < 0 ? 3 : 2)) : rd > 5 || rd == 5 && (rm == 4 || isTruncated || rm == 6 &&\n    // Check whether the digit to the left of the rounding digit is odd.\n    (i > 0 ? j > 0 ? w / mathpow(10, digits - j) : 0 : xd[xdi - 1]) % 10 & 1 || rm == (x.s < 0 ? 8 : 7));\n    if (sd < 1 || !xd[0]) {\n      xd.length = 0;\n      if (roundUp) {\n        // Convert sd to decimal places.\n        sd -= x.e + 1;\n\n        // 1, 0.1, 0.01, 0.001, 0.0001 etc.\n        xd[0] = mathpow(10, (LOG_BASE - sd % LOG_BASE) % LOG_BASE);\n        x.e = -sd || 0;\n      } else {\n        // Zero.\n        xd[0] = x.e = 0;\n      }\n      return x;\n    }\n\n    // Remove excess digits.\n    if (i == 0) {\n      xd.length = xdi;\n      k = 1;\n      xdi--;\n    } else {\n      xd.length = xdi + 1;\n      k = mathpow(10, LOG_BASE - i);\n\n      // E.g. 56700 becomes 56000 if 7 is the rounding digit.\n      // j > 0 means i > number of leading zeros of w.\n      xd[xdi] = j > 0 ? (w / mathpow(10, digits - j) % mathpow(10, j) | 0) * k : 0;\n    }\n    if (roundUp) {\n      for (;;) {\n        // Is the digit to be rounded up in the first word of xd?\n        if (xdi == 0) {\n          // i will be the length of xd[0] before k is added.\n          for (i = 1, j = xd[0]; j >= 10; j /= 10) i++;\n          j = xd[0] += k;\n          for (k = 1; j >= 10; j /= 10) k++;\n\n          // if i != k the length has increased.\n          if (i != k) {\n            x.e++;\n            if (xd[0] == BASE) xd[0] = 1;\n          }\n          break;\n        } else {\n          xd[xdi] += k;\n          if (xd[xdi] != BASE) break;\n          xd[xdi--] = 0;\n          k = 1;\n        }\n      }\n    }\n\n    // Remove trailing zeros.\n    for (i = xd.length; xd[--i] === 0;) xd.pop();\n  }\n  if (external) {\n    // Overflow?\n    if (x.e > Ctor.maxE) {\n      // Infinity.\n      x.d = null;\n      x.e = NaN;\n\n      // Underflow?\n    } else if (x.e < Ctor.minE) {\n      // Zero.\n      x.e = 0;\n      x.d = [0];\n      // Ctor.underflow = true;\n    } // else Ctor.underflow = false;\n  }\n  return x;\n}\nfunction finiteToString(x, isExp, sd) {\n  if (!x.isFinite()) return nonFiniteToString(x);\n  var k,\n    e = x.e,\n    str = digitsToString(x.d),\n    len = str.length;\n  if (isExp) {\n    if (sd && (k = sd - len) > 0) {\n      str = str.charAt(0) + '.' + str.slice(1) + getZeroString(k);\n    } else if (len > 1) {\n      str = str.charAt(0) + '.' + str.slice(1);\n    }\n    str = str + (x.e < 0 ? 'e' : 'e+') + x.e;\n  } else if (e < 0) {\n    str = '0.' + getZeroString(-e - 1) + str;\n    if (sd && (k = sd - len) > 0) str += getZeroString(k);\n  } else if (e >= len) {\n    str += getZeroString(e + 1 - len);\n    if (sd && (k = sd - e - 1) > 0) str = str + '.' + getZeroString(k);\n  } else {\n    if ((k = e + 1) < len) str = str.slice(0, k) + '.' + str.slice(k);\n    if (sd && (k = sd - len) > 0) {\n      if (e + 1 === len) str += '.';\n      str += getZeroString(k);\n    }\n  }\n  return str;\n}\n\n// Calculate the base 10 exponent from the base 1e7 exponent.\nfunction getBase10Exponent(digits, e) {\n  var w = digits[0];\n\n  // Add the number of digits of the first word of the digits array.\n  for (e *= LOG_BASE; w >= 10; w /= 10) e++;\n  return e;\n}\nfunction getLn10(Ctor, sd, pr) {\n  if (sd > LN10_PRECISION) {\n    // Reset global state in case the exception is caught.\n    external = true;\n    if (pr) Ctor.precision = pr;\n    throw Error(precisionLimitExceeded);\n  }\n  return finalise(new Ctor(LN10), sd, 1, true);\n}\nfunction getPi(Ctor, sd, rm) {\n  if (sd > PI_PRECISION) throw Error(precisionLimitExceeded);\n  return finalise(new Ctor(PI), sd, rm, true);\n}\nfunction getPrecision(digits) {\n  var w = digits.length - 1,\n    len = w * LOG_BASE + 1;\n  w = digits[w];\n\n  // If non-zero...\n  if (w) {\n    // Subtract the number of trailing zeros of the last word.\n    for (; w % 10 == 0; w /= 10) len--;\n\n    // Add the number of digits of the first word.\n    for (w = digits[0]; w >= 10; w /= 10) len++;\n  }\n  return len;\n}\nfunction getZeroString(k) {\n  var zs = '';\n  for (; k--;) zs += '0';\n  return zs;\n}\n\n/*\r\n * Return a new Decimal whose value is the value of Decimal `x` to the power `n`, where `n` is an\r\n * integer of type number.\r\n *\r\n * Implements 'exponentiation by squaring'. Called by `pow` and `parseOther`.\r\n *\r\n */\nfunction intPow(Ctor, x, n, pr) {\n  var isTruncated,\n    r = new Ctor(1),\n    // Max n of 9007199254740991 takes 53 loop iterations.\n    // Maximum digits array length; leaves [28, 34] guard digits.\n    k = Math.ceil(pr / LOG_BASE + 4);\n  external = false;\n  for (;;) {\n    if (n % 2) {\n      r = r.times(x);\n      if (truncate(r.d, k)) isTruncated = true;\n    }\n    n = mathfloor(n / 2);\n    if (n === 0) {\n      // To ensure correct rounding when r.d is truncated, increment the last word if it is zero.\n      n = r.d.length - 1;\n      if (isTruncated && r.d[n] === 0) ++r.d[n];\n      break;\n    }\n    x = x.times(x);\n    truncate(x.d, k);\n  }\n  external = true;\n  return r;\n}\nfunction isOdd(n) {\n  return n.d[n.d.length - 1] & 1;\n}\n\n/*\r\n * Handle `max` (`n` is -1) and `min` (`n` is 1).\r\n */\nfunction maxOrMin(Ctor, args, n) {\n  var k,\n    y,\n    x = new Ctor(args[0]),\n    i = 0;\n  for (; ++i < args.length;) {\n    y = new Ctor(args[i]);\n\n    // NaN?\n    if (!y.s) {\n      x = y;\n      break;\n    }\n    k = x.cmp(y);\n    if (k === n || k === 0 && x.s === n) {\n      x = y;\n    }\n  }\n  return x;\n}\n\n/*\r\n * Return a new Decimal whose value is the natural exponential of `x` rounded to `sd` significant\r\n * digits.\r\n *\r\n * Taylor/Maclaurin series.\r\n *\r\n * exp(x) = x^0/0! + x^1/1! + x^2/2! + x^3/3! + ...\r\n *\r\n * Argument reduction:\r\n *   Repeat x = x / 32, k += 5, until |x| < 0.1\r\n *   exp(x) = exp(x / 2^k)^(2^k)\r\n *\r\n * Previously, the argument was initially reduced by\r\n * exp(x) = exp(r) * 10^k  where r = x - k * ln10, k = floor(x / ln10)\r\n * to first put r in the range [0, ln10], before dividing by 32 until |x| < 0.1, but this was\r\n * found to be slower than just dividing repeatedly by 32 as above.\r\n *\r\n * Max integer argument: exp('20723265836946413') = 6.3e+9000000000000000\r\n * Min integer argument: exp('-20723265836946411') = 1.2e-9000000000000000\r\n * (Math object integer min/max: Math.exp(709) = 8.2e+307, Math.exp(-745) = 5e-324)\r\n *\r\n *  exp(Infinity)  = Infinity\r\n *  exp(-Infinity) = 0\r\n *  exp(NaN)       = NaN\r\n *  exp(±0)        = 1\r\n *\r\n *  exp(x) is non-terminating for any finite, non-zero x.\r\n *\r\n *  The result will always be correctly rounded.\r\n *\r\n */\nfunction naturalExponential(x, sd) {\n  var denominator,\n    guard,\n    j,\n    pow,\n    sum,\n    t,\n    wpr,\n    rep = 0,\n    i = 0,\n    k = 0,\n    Ctor = x.constructor,\n    rm = Ctor.rounding,\n    pr = Ctor.precision;\n\n  // 0/NaN/Infinity?\n  if (!x.d || !x.d[0] || x.e > 17) {\n    return new Ctor(x.d ? !x.d[0] ? 1 : x.s < 0 ? 0 : 1 / 0 : x.s ? x.s < 0 ? 0 : x : 0 / 0);\n  }\n  if (sd == null) {\n    external = false;\n    wpr = pr;\n  } else {\n    wpr = sd;\n  }\n  t = new Ctor(0.03125);\n\n  // while abs(x) >= 0.1\n  while (x.e > -2) {\n    // x = x / 2^5\n    x = x.times(t);\n    k += 5;\n  }\n\n  // Use 2 * log10(2^k) + 5 (empirically derived) to estimate the increase in precision\n  // necessary to ensure the first 4 rounding digits are correct.\n  guard = Math.log(mathpow(2, k)) / Math.LN10 * 2 + 5 | 0;\n  wpr += guard;\n  denominator = pow = sum = new Ctor(1);\n  Ctor.precision = wpr;\n  for (;;) {\n    pow = finalise(pow.times(x), wpr, 1);\n    denominator = denominator.times(++i);\n    t = sum.plus(divide(pow, denominator, wpr, 1));\n    if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\n      j = k;\n      while (j--) sum = finalise(sum.times(sum), wpr, 1);\n\n      // Check to see if the first 4 rounding digits are [49]999.\n      // If so, repeat the summation with a higher precision, otherwise\n      // e.g. with precision: 18, rounding: 1\n      // exp(18.404272462595034083567793919843761) = 98372560.1229999999 (should be 98372560.123)\n      // `wpr - guard` is the index of first rounding digit.\n      if (sd == null) {\n        if (rep < 3 && checkRoundingDigits(sum.d, wpr - guard, rm, rep)) {\n          Ctor.precision = wpr += 10;\n          denominator = pow = t = new Ctor(1);\n          i = 0;\n          rep++;\n        } else {\n          return finalise(sum, Ctor.precision = pr, rm, external = true);\n        }\n      } else {\n        Ctor.precision = pr;\n        return sum;\n      }\n    }\n    sum = t;\n  }\n}\n\n/*\r\n * Return a new Decimal whose value is the natural logarithm of `x` rounded to `sd` significant\r\n * digits.\r\n *\r\n *  ln(-n)        = NaN\r\n *  ln(0)         = -Infinity\r\n *  ln(-0)        = -Infinity\r\n *  ln(1)         = 0\r\n *  ln(Infinity)  = Infinity\r\n *  ln(-Infinity) = NaN\r\n *  ln(NaN)       = NaN\r\n *\r\n *  ln(n) (n != 1) is non-terminating.\r\n *\r\n */\nfunction naturalLogarithm(y, sd) {\n  var c,\n    c0,\n    denominator,\n    e,\n    numerator,\n    rep,\n    sum,\n    t,\n    wpr,\n    x1,\n    x2,\n    n = 1,\n    guard = 10,\n    x = y,\n    xd = x.d,\n    Ctor = x.constructor,\n    rm = Ctor.rounding,\n    pr = Ctor.precision;\n\n  // Is x negative or Infinity, NaN, 0 or 1?\n  if (x.s < 0 || !xd || !xd[0] || !x.e && xd[0] == 1 && xd.length == 1) {\n    return new Ctor(xd && !xd[0] ? -1 / 0 : x.s != 1 ? NaN : xd ? 0 : x);\n  }\n  if (sd == null) {\n    external = false;\n    wpr = pr;\n  } else {\n    wpr = sd;\n  }\n  Ctor.precision = wpr += guard;\n  c = digitsToString(xd);\n  c0 = c.charAt(0);\n  if (Math.abs(e = x.e) < 1.5e15) {\n    // Argument reduction.\n    // The series converges faster the closer the argument is to 1, so using\n    // ln(a^b) = b * ln(a),   ln(a) = ln(a^b) / b\n    // multiply the argument by itself until the leading digits of the significand are 7, 8, 9,\n    // 10, 11, 12 or 13, recording the number of multiplications so the sum of the series can\n    // later be divided by this number, then separate out the power of 10 using\n    // ln(a*10^b) = ln(a) + b*ln(10).\n\n    // max n is 21 (gives 0.9, 1.0 or 1.1) (9e15 / 21 = 4.2e14).\n    //while (c0 < 9 && c0 != 1 || c0 == 1 && c.charAt(1) > 1) {\n    // max n is 6 (gives 0.7 - 1.3)\n    while (c0 < 7 && c0 != 1 || c0 == 1 && c.charAt(1) > 3) {\n      x = x.times(y);\n      c = digitsToString(x.d);\n      c0 = c.charAt(0);\n      n++;\n    }\n    e = x.e;\n    if (c0 > 1) {\n      x = new Ctor('0.' + c);\n      e++;\n    } else {\n      x = new Ctor(c0 + '.' + c.slice(1));\n    }\n  } else {\n    // The argument reduction method above may result in overflow if the argument y is a massive\n    // number with exponent >= 1500000000000000 (9e15 / 6 = 1.5e15), so instead recall this\n    // function using ln(x*10^e) = ln(x) + e*ln(10).\n    t = getLn10(Ctor, wpr + 2, pr).times(e + '');\n    x = naturalLogarithm(new Ctor(c0 + '.' + c.slice(1)), wpr - guard).plus(t);\n    Ctor.precision = pr;\n    return sd == null ? finalise(x, pr, rm, external = true) : x;\n  }\n\n  // x1 is x reduced to a value near 1.\n  x1 = x;\n\n  // Taylor series.\n  // ln(y) = ln((1 + x)/(1 - x)) = 2(x + x^3/3 + x^5/5 + x^7/7 + ...)\n  // where x = (y - 1)/(y + 1)    (|x| < 1)\n  sum = numerator = x = divide(x.minus(1), x.plus(1), wpr, 1);\n  x2 = finalise(x.times(x), wpr, 1);\n  denominator = 3;\n  for (;;) {\n    numerator = finalise(numerator.times(x2), wpr, 1);\n    t = sum.plus(divide(numerator, new Ctor(denominator), wpr, 1));\n    if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\n      sum = sum.times(2);\n\n      // Reverse the argument reduction. Check that e is not 0 because, besides preventing an\n      // unnecessary calculation, -0 + 0 = +0 and to ensure correct rounding -0 needs to stay -0.\n      if (e !== 0) sum = sum.plus(getLn10(Ctor, wpr + 2, pr).times(e + ''));\n      sum = divide(sum, new Ctor(n), wpr, 1);\n\n      // Is rm > 3 and the first 4 rounding digits 4999, or rm < 4 (or the summation has\n      // been repeated previously) and the first 4 rounding digits 9999?\n      // If so, restart the summation with a higher precision, otherwise\n      // e.g. with precision: 12, rounding: 1\n      // ln(135520028.6126091714265381533) = 18.7246299999 when it should be 18.72463.\n      // `wpr - guard` is the index of first rounding digit.\n      if (sd == null) {\n        if (checkRoundingDigits(sum.d, wpr - guard, rm, rep)) {\n          Ctor.precision = wpr += guard;\n          t = numerator = x = divide(x1.minus(1), x1.plus(1), wpr, 1);\n          x2 = finalise(x.times(x), wpr, 1);\n          denominator = rep = 1;\n        } else {\n          return finalise(sum, Ctor.precision = pr, rm, external = true);\n        }\n      } else {\n        Ctor.precision = pr;\n        return sum;\n      }\n    }\n    sum = t;\n    denominator += 2;\n  }\n}\n\n// ±Infinity, NaN.\nfunction nonFiniteToString(x) {\n  // Unsigned.\n  return String(x.s * x.s / 0);\n}\n\n/*\r\n * Parse the value of a new Decimal `x` from string `str`.\r\n */\nfunction parseDecimal(x, str) {\n  var e, i, len;\n\n  // TODO BigInt str: no need to check for decimal point, exponential form or leading zeros.\n  // Decimal point?\n  if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\n\n  // Exponential form?\n  if ((i = str.search(/e/i)) > 0) {\n    // Determine exponent.\n    if (e < 0) e = i;\n    e += +str.slice(i + 1);\n    str = str.substring(0, i);\n  } else if (e < 0) {\n    // Integer.\n    e = str.length;\n  }\n\n  // Determine leading zeros.\n  for (i = 0; str.charCodeAt(i) === 48; i++);\n\n  // Determine trailing zeros.\n  for (len = str.length; str.charCodeAt(len - 1) === 48; --len);\n  str = str.slice(i, len);\n  if (str) {\n    len -= i;\n    x.e = e = e - i - 1;\n    x.d = [];\n\n    // Transform base\n\n    // e is the base 10 exponent.\n    // i is where to slice str to get the first word of the digits array.\n    i = (e + 1) % LOG_BASE;\n    if (e < 0) i += LOG_BASE;\n    if (i < len) {\n      if (i) x.d.push(+str.slice(0, i));\n      for (len -= LOG_BASE; i < len;) x.d.push(+str.slice(i, i += LOG_BASE));\n      str = str.slice(i);\n      i = LOG_BASE - str.length;\n    } else {\n      i -= len;\n    }\n    for (; i--;) str += '0';\n    x.d.push(+str);\n    if (external) {\n      // Overflow?\n      if (x.e > x.constructor.maxE) {\n        // Infinity.\n        x.d = null;\n        x.e = NaN;\n\n        // Underflow?\n      } else if (x.e < x.constructor.minE) {\n        // Zero.\n        x.e = 0;\n        x.d = [0];\n        // x.constructor.underflow = true;\n      } // else x.constructor.underflow = false;\n    }\n  } else {\n    // Zero.\n    x.e = 0;\n    x.d = [0];\n  }\n  return x;\n}\n\n/*\r\n * Parse the value of a new Decimal `x` from a string `str`, which is not a decimal value.\r\n */\nfunction parseOther(x, str) {\n  var base, Ctor, divisor, i, isFloat, len, p, xd, xe;\n  if (str.indexOf('_') > -1) {\n    str = str.replace(/(\\d)_(?=\\d)/g, '$1');\n    if (isDecimal.test(str)) return parseDecimal(x, str);\n  } else if (str === 'Infinity' || str === 'NaN') {\n    if (!+str) x.s = NaN;\n    x.e = NaN;\n    x.d = null;\n    return x;\n  }\n  if (isHex.test(str)) {\n    base = 16;\n    str = str.toLowerCase();\n  } else if (isBinary.test(str)) {\n    base = 2;\n  } else if (isOctal.test(str)) {\n    base = 8;\n  } else {\n    throw Error(invalidArgument + str);\n  }\n\n  // Is there a binary exponent part?\n  i = str.search(/p/i);\n  if (i > 0) {\n    p = +str.slice(i + 1);\n    str = str.substring(2, i);\n  } else {\n    str = str.slice(2);\n  }\n\n  // Convert `str` as an integer then divide the result by `base` raised to a power such that the\n  // fraction part will be restored.\n  i = str.indexOf('.');\n  isFloat = i >= 0;\n  Ctor = x.constructor;\n  if (isFloat) {\n    str = str.replace('.', '');\n    len = str.length;\n    i = len - i;\n\n    // log[10](16) = 1.2041... , log[10](88) = 1.9444....\n    divisor = intPow(Ctor, new Ctor(base), i, i * 2);\n  }\n  xd = convertBase(str, base, BASE);\n  xe = xd.length - 1;\n\n  // Remove trailing zeros.\n  for (i = xe; xd[i] === 0; --i) xd.pop();\n  if (i < 0) return new Ctor(x.s * 0);\n  x.e = getBase10Exponent(xd, xe);\n  x.d = xd;\n  external = false;\n\n  // At what precision to perform the division to ensure exact conversion?\n  // maxDecimalIntegerPartDigitCount = ceil(log[10](b) * otherBaseIntegerPartDigitCount)\n  // log[10](2) = 0.30103, log[10](8) = 0.90309, log[10](16) = 1.20412\n  // E.g. ceil(1.2 * 3) = 4, so up to 4 decimal digits are needed to represent 3 hex int digits.\n  // maxDecimalFractionPartDigitCount = {Hex:4|Oct:3|Bin:1} * otherBaseFractionPartDigitCount\n  // Therefore using 4 * the number of digits of str will always be enough.\n  if (isFloat) x = divide(x, divisor, len * 4);\n\n  // Multiply by the binary exponent part if present.\n  if (p) x = x.times(Math.abs(p) < 54 ? mathpow(2, p) : Decimal.pow(2, p));\n  external = true;\n  return x;\n}\n\n/*\r\n * sin(x) = x - x^3/3! + x^5/5! - ...\r\n * |x| < pi/2\r\n *\r\n */\nfunction sine(Ctor, x) {\n  var k,\n    len = x.d.length;\n  if (len < 3) {\n    return x.isZero() ? x : taylorSeries(Ctor, 2, x, x);\n  }\n\n  // Argument reduction: sin(5x) = 16*sin^5(x) - 20*sin^3(x) + 5*sin(x)\n  // i.e. sin(x) = 16*sin^5(x/5) - 20*sin^3(x/5) + 5*sin(x/5)\n  // and  sin(x) = sin(x/5)(5 + sin^2(x/5)(16sin^2(x/5) - 20))\n\n  // Estimate the optimum number of times to use the argument reduction.\n  k = 1.4 * Math.sqrt(len);\n  k = k > 16 ? 16 : k | 0;\n  x = x.times(1 / tinyPow(5, k));\n  x = taylorSeries(Ctor, 2, x, x);\n\n  // Reverse argument reduction\n  var sin2_x,\n    d5 = new Ctor(5),\n    d16 = new Ctor(16),\n    d20 = new Ctor(20);\n  for (; k--;) {\n    sin2_x = x.times(x);\n    x = x.times(d5.plus(sin2_x.times(d16.times(sin2_x).minus(d20))));\n  }\n  return x;\n}\n\n// Calculate Taylor series for `cos`, `cosh`, `sin` and `sinh`.\nfunction taylorSeries(Ctor, n, x, y, isHyperbolic) {\n  var j,\n    t,\n    u,\n    x2,\n    i = 1,\n    pr = Ctor.precision,\n    k = Math.ceil(pr / LOG_BASE);\n  external = false;\n  x2 = x.times(x);\n  u = new Ctor(y);\n  for (;;) {\n    t = divide(u.times(x2), new Ctor(n++ * n++), pr, 1);\n    u = isHyperbolic ? y.plus(t) : y.minus(t);\n    y = divide(t.times(x2), new Ctor(n++ * n++), pr, 1);\n    t = u.plus(y);\n    if (t.d[k] !== void 0) {\n      for (j = k; t.d[j] === u.d[j] && j--;);\n      if (j == -1) break;\n    }\n    j = u;\n    u = y;\n    y = t;\n    t = j;\n    i++;\n  }\n  external = true;\n  t.d.length = k + 1;\n  return t;\n}\n\n// Exponent e must be positive and non-zero.\nfunction tinyPow(b, e) {\n  var n = b;\n  while (--e) n *= b;\n  return n;\n}\n\n// Return the absolute value of `x` reduced to less than or equal to half pi.\nfunction toLessThanHalfPi(Ctor, x) {\n  var t,\n    isNeg = x.s < 0,\n    pi = getPi(Ctor, Ctor.precision, 1),\n    halfPi = pi.times(0.5);\n  x = x.abs();\n  if (x.lte(halfPi)) {\n    quadrant = isNeg ? 4 : 1;\n    return x;\n  }\n  t = x.divToInt(pi);\n  if (t.isZero()) {\n    quadrant = isNeg ? 3 : 2;\n  } else {\n    x = x.minus(t.times(pi));\n\n    // 0 <= x < pi\n    if (x.lte(halfPi)) {\n      quadrant = isOdd(t) ? isNeg ? 2 : 3 : isNeg ? 4 : 1;\n      return x;\n    }\n    quadrant = isOdd(t) ? isNeg ? 1 : 4 : isNeg ? 3 : 2;\n  }\n  return x.minus(pi).abs();\n}\n\n/*\r\n * Return the value of Decimal `x` as a string in base `baseOut`.\r\n *\r\n * If the optional `sd` argument is present include a binary exponent suffix.\r\n */\nfunction toStringBinary(x, baseOut, sd, rm) {\n  var base,\n    e,\n    i,\n    k,\n    len,\n    roundUp,\n    str,\n    xd,\n    y,\n    Ctor = x.constructor,\n    isExp = sd !== void 0;\n  if (isExp) {\n    checkInt32(sd, 1, MAX_DIGITS);\n    if (rm === void 0) rm = Ctor.rounding;else checkInt32(rm, 0, 8);\n  } else {\n    sd = Ctor.precision;\n    rm = Ctor.rounding;\n  }\n  if (!x.isFinite()) {\n    str = nonFiniteToString(x);\n  } else {\n    str = finiteToString(x);\n    i = str.indexOf('.');\n\n    // Use exponential notation according to `toExpPos` and `toExpNeg`? No, but if required:\n    // maxBinaryExponent = floor((decimalExponent + 1) * log[2](10))\n    // minBinaryExponent = floor(decimalExponent * log[2](10))\n    // log[2](10) = 3.321928094887362347870319429489390175864\n\n    if (isExp) {\n      base = 2;\n      if (baseOut == 16) {\n        sd = sd * 4 - 3;\n      } else if (baseOut == 8) {\n        sd = sd * 3 - 2;\n      }\n    } else {\n      base = baseOut;\n    }\n\n    // Convert the number as an integer then divide the result by its base raised to a power such\n    // that the fraction part will be restored.\n\n    // Non-integer.\n    if (i >= 0) {\n      str = str.replace('.', '');\n      y = new Ctor(1);\n      y.e = str.length - i;\n      y.d = convertBase(finiteToString(y), 10, base);\n      y.e = y.d.length;\n    }\n    xd = convertBase(str, 10, base);\n    e = len = xd.length;\n\n    // Remove trailing zeros.\n    for (; xd[--len] == 0;) xd.pop();\n    if (!xd[0]) {\n      str = isExp ? '0p+0' : '0';\n    } else {\n      if (i < 0) {\n        e--;\n      } else {\n        x = new Ctor(x);\n        x.d = xd;\n        x.e = e;\n        x = divide(x, y, sd, rm, 0, base);\n        xd = x.d;\n        e = x.e;\n        roundUp = inexact;\n      }\n\n      // The rounding digit, i.e. the digit after the digit that may be rounded up.\n      i = xd[sd];\n      k = base / 2;\n      roundUp = roundUp || xd[sd + 1] !== void 0;\n      roundUp = rm < 4 ? (i !== void 0 || roundUp) && (rm === 0 || rm === (x.s < 0 ? 3 : 2)) : i > k || i === k && (rm === 4 || roundUp || rm === 6 && xd[sd - 1] & 1 || rm === (x.s < 0 ? 8 : 7));\n      xd.length = sd;\n      if (roundUp) {\n        // Rounding up may mean the previous digit has to be rounded up and so on.\n        for (; ++xd[--sd] > base - 1;) {\n          xd[sd] = 0;\n          if (!sd) {\n            ++e;\n            xd.unshift(1);\n          }\n        }\n      }\n\n      // Determine trailing zeros.\n      for (len = xd.length; !xd[len - 1]; --len);\n\n      // E.g. [4, 11, 15] becomes 4bf.\n      for (i = 0, str = ''; i < len; i++) str += NUMERALS.charAt(xd[i]);\n\n      // Add binary exponent suffix?\n      if (isExp) {\n        if (len > 1) {\n          if (baseOut == 16 || baseOut == 8) {\n            i = baseOut == 16 ? 4 : 3;\n            for (--len; len % i; len++) str += '0';\n            xd = convertBase(str, base, baseOut);\n            for (len = xd.length; !xd[len - 1]; --len);\n\n            // xd[0] will always be be 1\n            for (i = 1, str = '1.'; i < len; i++) str += NUMERALS.charAt(xd[i]);\n          } else {\n            str = str.charAt(0) + '.' + str.slice(1);\n          }\n        }\n        str = str + (e < 0 ? 'p' : 'p+') + e;\n      } else if (e < 0) {\n        for (; ++e;) str = '0' + str;\n        str = '0.' + str;\n      } else {\n        if (++e > len) for (e -= len; e--;) str += '0';else if (e < len) str = str.slice(0, e) + '.' + str.slice(e);\n      }\n    }\n    str = (baseOut == 16 ? '0x' : baseOut == 2 ? '0b' : baseOut == 8 ? '0o' : '') + str;\n  }\n  return x.s < 0 ? '-' + str : str;\n}\n\n// Does not strip trailing zeros.\nfunction truncate(arr, len) {\n  if (arr.length > len) {\n    arr.length = len;\n    return true;\n  }\n}\n\n// Decimal methods\n\n/*\r\n *  abs\r\n *  acos\r\n *  acosh\r\n *  add\r\n *  asin\r\n *  asinh\r\n *  atan\r\n *  atanh\r\n *  atan2\r\n *  cbrt\r\n *  ceil\r\n *  clamp\r\n *  clone\r\n *  config\r\n *  cos\r\n *  cosh\r\n *  div\r\n *  exp\r\n *  floor\r\n *  hypot\r\n *  ln\r\n *  log\r\n *  log2\r\n *  log10\r\n *  max\r\n *  min\r\n *  mod\r\n *  mul\r\n *  pow\r\n *  random\r\n *  round\r\n *  set\r\n *  sign\r\n *  sin\r\n *  sinh\r\n *  sqrt\r\n *  sub\r\n *  sum\r\n *  tan\r\n *  tanh\r\n *  trunc\r\n */\n\n/*\r\n * Return a new Decimal whose value is the absolute value of `x`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\nfunction abs(x) {\n  return new this(x).abs();\n}\n\n/*\r\n * Return a new Decimal whose value is the arccosine in radians of `x`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\nfunction acos(x) {\n  return new this(x).acos();\n}\n\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic cosine of `x`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\nfunction acosh(x) {\n  return new this(x).acosh();\n}\n\n/*\r\n * Return a new Decimal whose value is the sum of `x` and `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\nfunction add(x, y) {\n  return new this(x).plus(y);\n}\n\n/*\r\n * Return a new Decimal whose value is the arcsine in radians of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\nfunction asin(x) {\n  return new this(x).asin();\n}\n\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic sine of `x`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\nfunction asinh(x) {\n  return new this(x).asinh();\n}\n\n/*\r\n * Return a new Decimal whose value is the arctangent in radians of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\nfunction atan(x) {\n  return new this(x).atan();\n}\n\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic tangent of `x`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\nfunction atanh(x) {\n  return new this(x).atanh();\n}\n\n/*\r\n * Return a new Decimal whose value is the arctangent in radians of `y/x` in the range -pi to pi\r\n * (inclusive), rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-pi, pi]\r\n *\r\n * y {number|string|bigint|Decimal} The y-coordinate.\r\n * x {number|string|bigint|Decimal} The x-coordinate.\r\n *\r\n * atan2(±0, -0)               = ±pi\r\n * atan2(±0, +0)               = ±0\r\n * atan2(±0, -x)               = ±pi for x > 0\r\n * atan2(±0, x)                = ±0 for x > 0\r\n * atan2(-y, ±0)               = -pi/2 for y > 0\r\n * atan2(y, ±0)                = pi/2 for y > 0\r\n * atan2(±y, -Infinity)        = ±pi for finite y > 0\r\n * atan2(±y, +Infinity)        = ±0 for finite y > 0\r\n * atan2(±Infinity, x)         = ±pi/2 for finite x\r\n * atan2(±Infinity, -Infinity) = ±3*pi/4\r\n * atan2(±Infinity, +Infinity) = ±pi/4\r\n * atan2(NaN, x) = NaN\r\n * atan2(y, NaN) = NaN\r\n *\r\n */\nfunction atan2(y, x) {\n  y = new this(y);\n  x = new this(x);\n  var r,\n    pr = this.precision,\n    rm = this.rounding,\n    wpr = pr + 4;\n\n  // Either NaN\n  if (!y.s || !x.s) {\n    r = new this(NaN);\n\n    // Both ±Infinity\n  } else if (!y.d && !x.d) {\n    r = getPi(this, wpr, 1).times(x.s > 0 ? 0.25 : 0.75);\n    r.s = y.s;\n\n    // x is ±Infinity or y is ±0\n  } else if (!x.d || y.isZero()) {\n    r = x.s < 0 ? getPi(this, pr, rm) : new this(0);\n    r.s = y.s;\n\n    // y is ±Infinity or x is ±0\n  } else if (!y.d || x.isZero()) {\n    r = getPi(this, wpr, 1).times(0.5);\n    r.s = y.s;\n\n    // Both non-zero and finite\n  } else if (x.s < 0) {\n    this.precision = wpr;\n    this.rounding = 1;\n    r = this.atan(divide(y, x, wpr, 1));\n    x = getPi(this, wpr, 1);\n    this.precision = pr;\n    this.rounding = rm;\n    r = y.s < 0 ? r.minus(x) : r.plus(x);\n  } else {\n    r = this.atan(divide(y, x, wpr, 1));\n  }\n  return r;\n}\n\n/*\r\n * Return a new Decimal whose value is the cube root of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\nfunction cbrt(x) {\n  return new this(x).cbrt();\n}\n\n/*\r\n * Return a new Decimal whose value is `x` rounded to an integer using `ROUND_CEIL`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\nfunction ceil(x) {\n  return finalise(x = new this(x), x.e + 1, 2);\n}\n\n/*\r\n * Return a new Decimal whose value is `x` clamped to the range delineated by `min` and `max`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * min {number|string|bigint|Decimal}\r\n * max {number|string|bigint|Decimal}\r\n *\r\n */\nfunction clamp(x, min, max) {\n  return new this(x).clamp(min, max);\n}\n\n/*\r\n * Configure global settings for a Decimal constructor.\r\n *\r\n * `obj` is an object with one or more of the following properties,\r\n *\r\n *   precision  {number}\r\n *   rounding   {number}\r\n *   toExpNeg   {number}\r\n *   toExpPos   {number}\r\n *   maxE       {number}\r\n *   minE       {number}\r\n *   modulo     {number}\r\n *   crypto     {boolean|number}\r\n *   defaults   {true}\r\n *\r\n * E.g. Decimal.config({ precision: 20, rounding: 4 })\r\n *\r\n */\nfunction config(obj) {\n  if (!obj || typeof obj !== 'object') throw Error(decimalError + 'Object expected');\n  var i,\n    p,\n    v,\n    useDefaults = obj.defaults === true,\n    ps = ['precision', 1, MAX_DIGITS, 'rounding', 0, 8, 'toExpNeg', -EXP_LIMIT, 0, 'toExpPos', 0, EXP_LIMIT, 'maxE', 0, EXP_LIMIT, 'minE', -EXP_LIMIT, 0, 'modulo', 0, 9];\n  for (i = 0; i < ps.length; i += 3) {\n    if (p = ps[i], useDefaults) this[p] = DEFAULTS[p];\n    if ((v = obj[p]) !== void 0) {\n      if (mathfloor(v) === v && v >= ps[i + 1] && v <= ps[i + 2]) this[p] = v;else throw Error(invalidArgument + p + ': ' + v);\n    }\n  }\n  if (p = 'crypto', useDefaults) this[p] = DEFAULTS[p];\n  if ((v = obj[p]) !== void 0) {\n    if (v === true || v === false || v === 0 || v === 1) {\n      if (v) {\n        if (typeof crypto != 'undefined' && crypto && (crypto.getRandomValues || crypto.randomBytes)) {\n          this[p] = true;\n        } else {\n          throw Error(cryptoUnavailable);\n        }\n      } else {\n        this[p] = false;\n      }\n    } else {\n      throw Error(invalidArgument + p + ': ' + v);\n    }\n  }\n  return this;\n}\n\n/*\r\n * Return a new Decimal whose value is the cosine of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\nfunction cos(x) {\n  return new this(x).cos();\n}\n\n/*\r\n * Return a new Decimal whose value is the hyperbolic cosine of `x`, rounded to precision\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\nfunction cosh(x) {\n  return new this(x).cosh();\n}\n\n/*\r\n * Create and return a Decimal constructor with the same configuration properties as this Decimal\r\n * constructor.\r\n *\r\n */\nfunction clone(obj) {\n  var i, p, ps;\n\n  /*\r\n   * The Decimal constructor and exported function.\r\n   * Return a new Decimal instance.\r\n   *\r\n   * v {number|string|bigint|Decimal} A numeric value.\r\n   *\r\n   */\n  function Decimal(v) {\n    var e,\n      i,\n      t,\n      x = this;\n\n    // Decimal called without new.\n    if (!(x instanceof Decimal)) return new Decimal(v);\n\n    // Retain a reference to this Decimal constructor, and shadow Decimal.prototype.constructor\n    // which points to Object.\n    x.constructor = Decimal;\n    if (isDecimalInstance(v)) {\n      x.s = v.s;\n      if (external) {\n        if (!v.d || v.e > Decimal.maxE) {\n          // Infinity.\n          x.e = NaN;\n          x.d = null;\n        } else if (v.e < Decimal.minE) {\n          // Zero.\n          x.e = 0;\n          x.d = [0];\n        } else {\n          x.e = v.e;\n          x.d = v.d.slice();\n        }\n      } else {\n        x.e = v.e;\n        x.d = v.d ? v.d.slice() : v.d;\n      }\n      return;\n    }\n    t = typeof v;\n    if (t === 'number') {\n      if (v === 0) {\n        x.s = 1 / v < 0 ? -1 : 1;\n        x.e = 0;\n        x.d = [0];\n        return;\n      }\n      if (v < 0) {\n        v = -v;\n        x.s = -1;\n      } else {\n        x.s = 1;\n      }\n\n      // Fast path for small integers.\n      if (v === ~~v && v < 1e7) {\n        for (e = 0, i = v; i >= 10; i /= 10) e++;\n        if (external) {\n          if (e > Decimal.maxE) {\n            x.e = NaN;\n            x.d = null;\n          } else if (e < Decimal.minE) {\n            x.e = 0;\n            x.d = [0];\n          } else {\n            x.e = e;\n            x.d = [v];\n          }\n        } else {\n          x.e = e;\n          x.d = [v];\n        }\n        return;\n      }\n\n      // Infinity or NaN?\n      if (v * 0 !== 0) {\n        if (!v) x.s = NaN;\n        x.e = NaN;\n        x.d = null;\n        return;\n      }\n      return parseDecimal(x, v.toString());\n    }\n    if (t === 'string') {\n      if ((i = v.charCodeAt(0)) === 45) {\n        // minus sign\n        v = v.slice(1);\n        x.s = -1;\n      } else {\n        if (i === 43) v = v.slice(1); // plus sign\n        x.s = 1;\n      }\n      return isDecimal.test(v) ? parseDecimal(x, v) : parseOther(x, v);\n    }\n    if (t === 'bigint') {\n      if (v < 0) {\n        v = -v;\n        x.s = -1;\n      } else {\n        x.s = 1;\n      }\n      return parseDecimal(x, v.toString());\n    }\n    throw Error(invalidArgument + v);\n  }\n  Decimal.prototype = P;\n  Decimal.ROUND_UP = 0;\n  Decimal.ROUND_DOWN = 1;\n  Decimal.ROUND_CEIL = 2;\n  Decimal.ROUND_FLOOR = 3;\n  Decimal.ROUND_HALF_UP = 4;\n  Decimal.ROUND_HALF_DOWN = 5;\n  Decimal.ROUND_HALF_EVEN = 6;\n  Decimal.ROUND_HALF_CEIL = 7;\n  Decimal.ROUND_HALF_FLOOR = 8;\n  Decimal.EUCLID = 9;\n  Decimal.config = Decimal.set = config;\n  Decimal.clone = clone;\n  Decimal.isDecimal = isDecimalInstance;\n  Decimal.abs = abs;\n  Decimal.acos = acos;\n  Decimal.acosh = acosh; // ES6\n  Decimal.add = add;\n  Decimal.asin = asin;\n  Decimal.asinh = asinh; // ES6\n  Decimal.atan = atan;\n  Decimal.atanh = atanh; // ES6\n  Decimal.atan2 = atan2;\n  Decimal.cbrt = cbrt; // ES6\n  Decimal.ceil = ceil;\n  Decimal.clamp = clamp;\n  Decimal.cos = cos;\n  Decimal.cosh = cosh; // ES6\n  Decimal.div = div;\n  Decimal.exp = exp;\n  Decimal.floor = floor;\n  Decimal.hypot = hypot; // ES6\n  Decimal.ln = ln;\n  Decimal.log = log;\n  Decimal.log10 = log10; // ES6\n  Decimal.log2 = log2; // ES6\n  Decimal.max = max;\n  Decimal.min = min;\n  Decimal.mod = mod;\n  Decimal.mul = mul;\n  Decimal.pow = pow;\n  Decimal.random = random;\n  Decimal.round = round;\n  Decimal.sign = sign; // ES6\n  Decimal.sin = sin;\n  Decimal.sinh = sinh; // ES6\n  Decimal.sqrt = sqrt;\n  Decimal.sub = sub;\n  Decimal.sum = sum;\n  Decimal.tan = tan;\n  Decimal.tanh = tanh; // ES6\n  Decimal.trunc = trunc; // ES6\n\n  if (obj === void 0) obj = {};\n  if (obj) {\n    if (obj.defaults !== true) {\n      ps = ['precision', 'rounding', 'toExpNeg', 'toExpPos', 'maxE', 'minE', 'modulo', 'crypto'];\n      for (i = 0; i < ps.length;) if (!obj.hasOwnProperty(p = ps[i++])) obj[p] = this[p];\n    }\n  }\n  Decimal.config(obj);\n  return Decimal;\n}\n\n/*\r\n * Return a new Decimal whose value is `x` divided by `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\nfunction div(x, y) {\n  return new this(x).div(y);\n}\n\n/*\r\n * Return a new Decimal whose value is the natural exponential of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} The power to which to raise the base of the natural log.\r\n *\r\n */\nfunction exp(x) {\n  return new this(x).exp();\n}\n\n/*\r\n * Return a new Decimal whose value is `x` round to an integer using `ROUND_FLOOR`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\nfunction floor(x) {\n  return finalise(x = new this(x), x.e + 1, 3);\n}\n\n/*\r\n * Return a new Decimal whose value is the square root of the sum of the squares of the arguments,\r\n * rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * hypot(a, b, ...) = sqrt(a^2 + b^2 + ...)\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\nfunction hypot() {\n  var i,\n    n,\n    t = new this(0);\n  external = false;\n  for (i = 0; i < arguments.length;) {\n    n = new this(arguments[i++]);\n    if (!n.d) {\n      if (n.s) {\n        external = true;\n        return new this(1 / 0);\n      }\n      t = n;\n    } else if (t.d) {\n      t = t.plus(n.times(n));\n    }\n  }\n  external = true;\n  return t.sqrt();\n}\n\n/*\r\n * Return true if object is a Decimal instance (where Decimal is any Decimal constructor),\r\n * otherwise return false.\r\n *\r\n */\nfunction isDecimalInstance(obj) {\n  return obj instanceof Decimal || obj && obj.toStringTag === tag || false;\n}\n\n/*\r\n * Return a new Decimal whose value is the natural logarithm of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\nfunction ln(x) {\n  return new this(x).ln();\n}\n\n/*\r\n * Return a new Decimal whose value is the log of `x` to the base `y`, or to base 10 if no base\r\n * is specified, rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * log[y](x)\r\n *\r\n * x {number|string|bigint|Decimal} The argument of the logarithm.\r\n * y {number|string|bigint|Decimal} The base of the logarithm.\r\n *\r\n */\nfunction log(x, y) {\n  return new this(x).log(y);\n}\n\n/*\r\n * Return a new Decimal whose value is the base 2 logarithm of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\nfunction log2(x) {\n  return new this(x).log(2);\n}\n\n/*\r\n * Return a new Decimal whose value is the base 10 logarithm of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\nfunction log10(x) {\n  return new this(x).log(10);\n}\n\n/*\r\n * Return a new Decimal whose value is the maximum of the arguments.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\nfunction max() {\n  return maxOrMin(this, arguments, -1);\n}\n\n/*\r\n * Return a new Decimal whose value is the minimum of the arguments.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\nfunction min() {\n  return maxOrMin(this, arguments, 1);\n}\n\n/*\r\n * Return a new Decimal whose value is `x` modulo `y`, rounded to `precision` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\nfunction mod(x, y) {\n  return new this(x).mod(y);\n}\n\n/*\r\n * Return a new Decimal whose value is `x` multiplied by `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\nfunction mul(x, y) {\n  return new this(x).mul(y);\n}\n\n/*\r\n * Return a new Decimal whose value is `x` raised to the power `y`, rounded to precision\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} The base.\r\n * y {number|string|bigint|Decimal} The exponent.\r\n *\r\n */\nfunction pow(x, y) {\n  return new this(x).pow(y);\n}\n\n/*\r\n * Returns a new Decimal with a random value equal to or greater than 0 and less than 1, and with\r\n * `sd`, or `Decimal.precision` if `sd` is omitted, significant digits (or less if trailing zeros\r\n * are produced).\r\n *\r\n * [sd] {number} Significant digits. Integer, 0 to MAX_DIGITS inclusive.\r\n *\r\n */\nfunction random(sd) {\n  var d,\n    e,\n    k,\n    n,\n    i = 0,\n    r = new this(1),\n    rd = [];\n  if (sd === void 0) sd = this.precision;else checkInt32(sd, 1, MAX_DIGITS);\n  k = Math.ceil(sd / LOG_BASE);\n  if (!this.crypto) {\n    for (; i < k;) rd[i++] = Math.random() * 1e7 | 0;\n\n    // Browsers supporting crypto.getRandomValues.\n  } else if (crypto.getRandomValues) {\n    d = crypto.getRandomValues(new Uint32Array(k));\n    for (; i < k;) {\n      n = d[i];\n\n      // 0 <= n < 4294967296\n      // Probability n >= 4.29e9, is 4967296 / 4294967296 = 0.00116 (1 in 865).\n      if (n >= 4.29e9) {\n        d[i] = crypto.getRandomValues(new Uint32Array(1))[0];\n      } else {\n        // 0 <= n <= 4289999999\n        // 0 <= (n % 1e7) <= 9999999\n        rd[i++] = n % 1e7;\n      }\n    }\n\n    // Node.js supporting crypto.randomBytes.\n  } else if (crypto.randomBytes) {\n    // buffer\n    d = crypto.randomBytes(k *= 4);\n    for (; i < k;) {\n      // 0 <= n < 2147483648\n      n = d[i] + (d[i + 1] << 8) + (d[i + 2] << 16) + ((d[i + 3] & 0x7f) << 24);\n\n      // Probability n >= 2.14e9, is 7483648 / 2147483648 = 0.0035 (1 in 286).\n      if (n >= 2.14e9) {\n        crypto.randomBytes(4).copy(d, i);\n      } else {\n        // 0 <= n <= 2139999999\n        // 0 <= (n % 1e7) <= 9999999\n        rd.push(n % 1e7);\n        i += 4;\n      }\n    }\n    i = k / 4;\n  } else {\n    throw Error(cryptoUnavailable);\n  }\n  k = rd[--i];\n  sd %= LOG_BASE;\n\n  // Convert trailing digits to zeros according to sd.\n  if (k && sd) {\n    n = mathpow(10, LOG_BASE - sd);\n    rd[i] = (k / n | 0) * n;\n  }\n\n  // Remove trailing words which are zero.\n  for (; rd[i] === 0; i--) rd.pop();\n\n  // Zero?\n  if (i < 0) {\n    e = 0;\n    rd = [0];\n  } else {\n    e = -1;\n\n    // Remove leading words which are zero and adjust exponent accordingly.\n    for (; rd[0] === 0; e -= LOG_BASE) rd.shift();\n\n    // Count the digits of the first word of rd to determine leading zeros.\n    for (k = 1, n = rd[0]; n >= 10; n /= 10) k++;\n\n    // Adjust the exponent for leading zeros of the first word of rd.\n    if (k < LOG_BASE) e -= LOG_BASE - k;\n  }\n  r.e = e;\n  r.d = rd;\n  return r;\n}\n\n/*\r\n * Return a new Decimal whose value is `x` rounded to an integer using rounding mode `rounding`.\r\n *\r\n * To emulate `Math.round`, set rounding to 7 (ROUND_HALF_CEIL).\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\nfunction round(x) {\n  return finalise(x = new this(x), x.e + 1, this.rounding);\n}\n\n/*\r\n * Return\r\n *   1    if x > 0,\r\n *  -1    if x < 0,\r\n *   0    if x is 0,\r\n *  -0    if x is -0,\r\n *   NaN  otherwise\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\nfunction sign(x) {\n  x = new this(x);\n  return x.d ? x.d[0] ? x.s : 0 * x.s : x.s || NaN;\n}\n\n/*\r\n * Return a new Decimal whose value is the sine of `x`, rounded to `precision` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\nfunction sin(x) {\n  return new this(x).sin();\n}\n\n/*\r\n * Return a new Decimal whose value is the hyperbolic sine of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\nfunction sinh(x) {\n  return new this(x).sinh();\n}\n\n/*\r\n * Return a new Decimal whose value is the square root of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\nfunction sqrt(x) {\n  return new this(x).sqrt();\n}\n\n/*\r\n * Return a new Decimal whose value is `x` minus `y`, rounded to `precision` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\nfunction sub(x, y) {\n  return new this(x).sub(y);\n}\n\n/*\r\n * Return a new Decimal whose value is the sum of the arguments, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * Only the result is rounded, not the intermediate calculations.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\nfunction sum() {\n  var i = 0,\n    args = arguments,\n    x = new this(args[i]);\n  external = false;\n  for (; x.s && ++i < args.length;) x = x.plus(args[i]);\n  external = true;\n  return finalise(x, this.precision, this.rounding);\n}\n\n/*\r\n * Return a new Decimal whose value is the tangent of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\nfunction tan(x) {\n  return new this(x).tan();\n}\n\n/*\r\n * Return a new Decimal whose value is the hyperbolic tangent of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\nfunction tanh(x) {\n  return new this(x).tanh();\n}\n\n/*\r\n * Return a new Decimal whose value is `x` truncated to an integer.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\nfunction trunc(x) {\n  return finalise(x = new this(x), x.e + 1, 1);\n}\nP[Symbol.for('nodejs.util.inspect.custom')] = P.toString;\nP[Symbol.toStringTag] = 'Decimal';\n\n// Create and configure initial Decimal constructor.\nexport var Decimal = P.constructor = clone(DEFAULTS);\n\n// Create the internal constants from their string values.\nLN10 = new Decimal(LN10);\nPI = new Decimal(PI);\nexport default Decimal;", "map": {"version": 3, "names": ["EXP_LIMIT", "MAX_DIGITS", "NUMERALS", "LN10", "PI", "DEFAULTS", "precision", "rounding", "modulo", "toExpNeg", "toExpPos", "minE", "maxE", "crypto", "inexact", "quadrant", "external", "decimalError", "invalidArgument", "precisionLimitExceeded", "cryptoUnavailable", "tag", "mathfloor", "Math", "floor", "mathpow", "pow", "isBinary", "isHex", "isOctal", "isDecimal", "BASE", "LOG_BASE", "MAX_SAFE_INTEGER", "LN10_PRECISION", "length", "PI_PRECISION", "P", "toStringTag", "absoluteValue", "abs", "x", "constructor", "s", "finalise", "ceil", "e", "clampedTo", "clamp", "min", "max", "k", "Ctor", "NaN", "gt", "Error", "cmp", "comparedTo", "y", "i", "j", "xdL", "ydL", "xd", "d", "yd", "xs", "ys", "cosine", "cos", "pr", "rm", "sd", "toLessThanHalfPi", "neg", "cubeRoot", "cbrt", "m", "n", "r", "rep", "t", "t3", "t3plusx", "isFinite", "isZero", "digitsToString", "toExponential", "slice", "indexOf", "toString", "times", "plus", "divide", "eq", "char<PERSON>t", "decimalPlaces", "dp", "w", "dividedBy", "div", "dividedToIntegerBy", "divToInt", "equals", "greaterThan", "greaterThanOrEqualTo", "gte", "hyperbolicCosine", "cosh", "len", "one", "tinyPow", "taylorSeries", "cosh2_x", "d8", "minus", "hyperbolicSine", "sinh", "sqrt", "sinh2_x", "d5", "d16", "d20", "hyperbolicTangent", "tanh", "inverseCosine", "acos", "isNeg", "getPi", "atan", "inverseHyperbolicCosine", "acosh", "lte", "ln", "inverseHyperbolicSine", "asinh", "inverseHyperbolicTangent", "atanh", "wpr", "xsd", "inverseSine", "asin", "halfPi", "inverseTangent", "px", "x2", "isInteger", "isInt", "isNaN", "isNegative", "isPositive", "isPos", "lessThan", "lt", "lessThanOrEqualTo", "logarithm", "log", "base", "isBase10", "denominator", "inf", "num", "arg", "guard", "naturalLogarithm", "getLn10", "checkRoundingDigits", "sub", "xe", "xLTy", "reverse", "push", "pop", "shift", "getBase10Exponent", "mod", "q", "naturalExponential", "exp", "negated", "add", "carry", "unshift", "z", "getPrecision", "round", "sine", "sin", "squareRoot", "tangent", "tan", "mul", "rL", "toBinary", "toStringBinary", "toDecimalPlaces", "toDP", "checkInt32", "str", "finiteToString", "toFixed", "toFraction", "maxD", "d0", "d1", "d2", "n0", "n1", "toHexadecimal", "toHex", "toNearest", "toNumber", "toOctal", "<PERSON><PERSON><PERSON><PERSON>", "yn", "intPow", "toPrecision", "toSignificantDigits", "toSD", "truncated", "trunc", "valueOf", "toJSON", "ws", "indexOfLastWord", "getZeroString", "repeating", "di", "rd", "convertBase", "baseIn", "baseOut", "arr", "arrL", "strL", "cos2x", "multiplyInteger", "temp", "compare", "a", "b", "aL", "bL", "subtract", "logBase", "more", "prod", "prodL", "qd", "rem", "remL", "rem0", "xi", "xL", "yd0", "yL", "yz", "sign", "isTruncated", "digits", "roundUp", "xdi", "out", "isExp", "nonFiniteToString", "zs", "truncate", "isOdd", "maxOrMin", "args", "sum", "c", "c0", "numerator", "x1", "String", "parseDecimal", "replace", "search", "substring", "charCodeAt", "parseOther", "divisor", "isFloat", "p", "test", "toLowerCase", "Decimal", "sin2_x", "isHyperbolic", "u", "pi", "atan2", "config", "obj", "v", "useDefaults", "defaults", "ps", "getRandomValues", "randomBytes", "clone", "isDecimalInstance", "prototype", "ROUND_UP", "ROUND_DOWN", "ROUND_CEIL", "ROUND_FLOOR", "ROUND_HALF_UP", "ROUND_HALF_DOWN", "ROUND_HALF_EVEN", "ROUND_HALF_CEIL", "ROUND_HALF_FLOOR", "EUCLID", "set", "hypot", "log10", "log2", "random", "hasOwnProperty", "arguments", "Uint32Array", "copy", "Symbol", "for"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/decimal.js/decimal.mjs"], "sourcesContent": ["/*!\r\n *  decimal.js v10.5.0\r\n *  An arbitrary-precision Decimal type for JavaScript.\r\n *  https://github.com/MikeMcl/decimal.js\r\n *  Copyright (c) 2025 <PERSON> <<EMAIL>>\r\n *  MIT Licence\r\n */\r\n\r\n\r\n// -----------------------------------  EDITABLE DEFAULTS  ------------------------------------ //\r\n\r\n\r\n  // The maximum exponent magnitude.\r\n  // The limit on the value of `toExpNeg`, `toExpPos`, `minE` and `maxE`.\r\nvar EXP_LIMIT = 9e15,                      // 0 to 9e15\r\n\r\n  // The limit on the value of `precision`, and on the value of the first argument to\r\n  // `toDecimalPlaces`, `toExponential`, `toFixed`, `toPrecision` and `toSignificantDigits`.\r\n  MAX_DIGITS = 1e9,                        // 0 to 1e9\r\n\r\n  // Base conversion alphabet.\r\n  NUMERALS = '0123456789abcdef',\r\n\r\n  // The natural logarithm of 10 (1025 digits).\r\n  LN10 = '2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058',\r\n\r\n  // Pi (1025 digits).\r\n  PI = '3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789',\r\n\r\n\r\n  // The initial configuration properties of the Decimal constructor.\r\n  DEFAULTS = {\r\n\r\n    // These values must be integers within the stated ranges (inclusive).\r\n    // Most of these values can be changed at run-time using the `Decimal.config` method.\r\n\r\n    // The maximum number of significant digits of the result of a calculation or base conversion.\r\n    // E.g. `Decimal.config({ precision: 20 });`\r\n    precision: 20,                         // 1 to MAX_DIGITS\r\n\r\n    // The rounding mode used when rounding to `precision`.\r\n    //\r\n    // ROUND_UP         0 Away from zero.\r\n    // ROUND_DOWN       1 Towards zero.\r\n    // ROUND_CEIL       2 Towards +Infinity.\r\n    // ROUND_FLOOR      3 Towards -Infinity.\r\n    // ROUND_HALF_UP    4 Towards nearest neighbour. If equidistant, up.\r\n    // ROUND_HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\r\n    // ROUND_HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\r\n    // ROUND_HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\r\n    // ROUND_HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\r\n    //\r\n    // E.g.\r\n    // `Decimal.rounding = 4;`\r\n    // `Decimal.rounding = Decimal.ROUND_HALF_UP;`\r\n    rounding: 4,                           // 0 to 8\r\n\r\n    // The modulo mode used when calculating the modulus: a mod n.\r\n    // The quotient (q = a / n) is calculated according to the corresponding rounding mode.\r\n    // The remainder (r) is calculated as: r = a - n * q.\r\n    //\r\n    // UP         0 The remainder is positive if the dividend is negative, else is negative.\r\n    // DOWN       1 The remainder has the same sign as the dividend (JavaScript %).\r\n    // FLOOR      3 The remainder has the same sign as the divisor (Python %).\r\n    // HALF_EVEN  6 The IEEE 754 remainder function.\r\n    // EUCLID     9 Euclidian division. q = sign(n) * floor(a / abs(n)). Always positive.\r\n    //\r\n    // Truncated division (1), floored division (3), the IEEE 754 remainder (6), and Euclidian\r\n    // division (9) are commonly used for the modulus operation. The other rounding modes can also\r\n    // be used, but they may not give useful results.\r\n    modulo: 1,                             // 0 to 9\r\n\r\n    // The exponent value at and beneath which `toString` returns exponential notation.\r\n    // JavaScript numbers: -7\r\n    toExpNeg: -7,                          // 0 to -EXP_LIMIT\r\n\r\n    // The exponent value at and above which `toString` returns exponential notation.\r\n    // JavaScript numbers: 21\r\n    toExpPos:  21,                         // 0 to EXP_LIMIT\r\n\r\n    // The minimum exponent value, beneath which underflow to zero occurs.\r\n    // JavaScript numbers: -324  (5e-324)\r\n    minE: -EXP_LIMIT,                      // -1 to -EXP_LIMIT\r\n\r\n    // The maximum exponent value, above which overflow to Infinity occurs.\r\n    // JavaScript numbers: 308  (1.7976931348623157e+308)\r\n    maxE: EXP_LIMIT,                       // 1 to EXP_LIMIT\r\n\r\n    // Whether to use cryptographically-secure random number generation, if available.\r\n    crypto: false                          // true/false\r\n  },\r\n\r\n\r\n// ----------------------------------- END OF EDITABLE DEFAULTS ------------------------------- //\r\n\r\n\r\n  inexact, quadrant,\r\n  external = true,\r\n\r\n  decimalError = '[DecimalError] ',\r\n  invalidArgument = decimalError + 'Invalid argument: ',\r\n  precisionLimitExceeded = decimalError + 'Precision limit exceeded',\r\n  cryptoUnavailable = decimalError + 'crypto unavailable',\r\n  tag = '[object Decimal]',\r\n\r\n  mathfloor = Math.floor,\r\n  mathpow = Math.pow,\r\n\r\n  isBinary = /^0b([01]+(\\.[01]*)?|\\.[01]+)(p[+-]?\\d+)?$/i,\r\n  isHex = /^0x([0-9a-f]+(\\.[0-9a-f]*)?|\\.[0-9a-f]+)(p[+-]?\\d+)?$/i,\r\n  isOctal = /^0o([0-7]+(\\.[0-7]*)?|\\.[0-7]+)(p[+-]?\\d+)?$/i,\r\n  isDecimal = /^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i,\r\n\r\n  BASE = 1e7,\r\n  LOG_BASE = 7,\r\n  MAX_SAFE_INTEGER = 9007199254740991,\r\n\r\n  LN10_PRECISION = LN10.length - 1,\r\n  PI_PRECISION = PI.length - 1,\r\n\r\n  // Decimal.prototype object\r\n  P = { toStringTag: tag };\r\n\r\n\r\n// Decimal prototype methods\r\n\r\n\r\n/*\r\n *  absoluteValue             abs\r\n *  ceil\r\n *  clampedTo                 clamp\r\n *  comparedTo                cmp\r\n *  cosine                    cos\r\n *  cubeRoot                  cbrt\r\n *  decimalPlaces             dp\r\n *  dividedBy                 div\r\n *  dividedToIntegerBy        divToInt\r\n *  equals                    eq\r\n *  floor\r\n *  greaterThan               gt\r\n *  greaterThanOrEqualTo      gte\r\n *  hyperbolicCosine          cosh\r\n *  hyperbolicSine            sinh\r\n *  hyperbolicTangent         tanh\r\n *  inverseCosine             acos\r\n *  inverseHyperbolicCosine   acosh\r\n *  inverseHyperbolicSine     asinh\r\n *  inverseHyperbolicTangent  atanh\r\n *  inverseSine               asin\r\n *  inverseTangent            atan\r\n *  isFinite\r\n *  isInteger                 isInt\r\n *  isNaN\r\n *  isNegative                isNeg\r\n *  isPositive                isPos\r\n *  isZero\r\n *  lessThan                  lt\r\n *  lessThanOrEqualTo         lte\r\n *  logarithm                 log\r\n *  [maximum]                 [max]\r\n *  [minimum]                 [min]\r\n *  minus                     sub\r\n *  modulo                    mod\r\n *  naturalExponential        exp\r\n *  naturalLogarithm          ln\r\n *  negated                   neg\r\n *  plus                      add\r\n *  precision                 sd\r\n *  round\r\n *  sine                      sin\r\n *  squareRoot                sqrt\r\n *  tangent                   tan\r\n *  times                     mul\r\n *  toBinary\r\n *  toDecimalPlaces           toDP\r\n *  toExponential\r\n *  toFixed\r\n *  toFraction\r\n *  toHexadecimal             toHex\r\n *  toNearest\r\n *  toNumber\r\n *  toOctal\r\n *  toPower                   pow\r\n *  toPrecision\r\n *  toSignificantDigits       toSD\r\n *  toString\r\n *  truncated                 trunc\r\n *  valueOf                   toJSON\r\n */\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the absolute value of this Decimal.\r\n *\r\n */\r\nP.absoluteValue = P.abs = function () {\r\n  var x = new this.constructor(this);\r\n  if (x.s < 0) x.s = 1;\r\n  return finalise(x);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number in the\r\n * direction of positive Infinity.\r\n *\r\n */\r\nP.ceil = function () {\r\n  return finalise(new this.constructor(this), this.e + 1, 2);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal clamped to the range\r\n * delineated by `min` and `max`.\r\n *\r\n * min {number|string|bigint|Decimal}\r\n * max {number|string|bigint|Decimal}\r\n *\r\n */\r\nP.clampedTo = P.clamp = function (min, max) {\r\n  var k,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n  min = new Ctor(min);\r\n  max = new Ctor(max);\r\n  if (!min.s || !max.s) return new Ctor(NaN);\r\n  if (min.gt(max)) throw Error(invalidArgument + max);\r\n  k = x.cmp(min);\r\n  return k < 0 ? min : x.cmp(max) > 0 ? max : new Ctor(x);\r\n};\r\n\r\n\r\n/*\r\n * Return\r\n *   1    if the value of this Decimal is greater than the value of `y`,\r\n *  -1    if the value of this Decimal is less than the value of `y`,\r\n *   0    if they have the same value,\r\n *   NaN  if the value of either Decimal is NaN.\r\n *\r\n */\r\nP.comparedTo = P.cmp = function (y) {\r\n  var i, j, xdL, ydL,\r\n    x = this,\r\n    xd = x.d,\r\n    yd = (y = new x.constructor(y)).d,\r\n    xs = x.s,\r\n    ys = y.s;\r\n\r\n  // Either NaN or ±Infinity?\r\n  if (!xd || !yd) {\r\n    return !xs || !ys ? NaN : xs !== ys ? xs : xd === yd ? 0 : !xd ^ xs < 0 ? 1 : -1;\r\n  }\r\n\r\n  // Either zero?\r\n  if (!xd[0] || !yd[0]) return xd[0] ? xs : yd[0] ? -ys : 0;\r\n\r\n  // Signs differ?\r\n  if (xs !== ys) return xs;\r\n\r\n  // Compare exponents.\r\n  if (x.e !== y.e) return x.e > y.e ^ xs < 0 ? 1 : -1;\r\n\r\n  xdL = xd.length;\r\n  ydL = yd.length;\r\n\r\n  // Compare digit by digit.\r\n  for (i = 0, j = xdL < ydL ? xdL : ydL; i < j; ++i) {\r\n    if (xd[i] !== yd[i]) return xd[i] > yd[i] ^ xs < 0 ? 1 : -1;\r\n  }\r\n\r\n  // Compare lengths.\r\n  return xdL === ydL ? 0 : xdL > ydL ^ xs < 0 ? 1 : -1;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the cosine of the value in radians of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-1, 1]\r\n *\r\n * cos(0)         = 1\r\n * cos(-0)        = 1\r\n * cos(Infinity)  = NaN\r\n * cos(-Infinity) = NaN\r\n * cos(NaN)       = NaN\r\n *\r\n */\r\nP.cosine = P.cos = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.d) return new Ctor(NaN);\r\n\r\n  // cos(0) = cos(-0) = 1\r\n  if (!x.d[0]) return new Ctor(1);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + LOG_BASE;\r\n  Ctor.rounding = 1;\r\n\r\n  x = cosine(Ctor, toLessThanHalfPi(Ctor, x));\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(quadrant == 2 || quadrant == 3 ? x.neg() : x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n *\r\n * Return a new Decimal whose value is the cube root of the value of this Decimal, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n *  cbrt(0)  =  0\r\n *  cbrt(-0) = -0\r\n *  cbrt(1)  =  1\r\n *  cbrt(-1) = -1\r\n *  cbrt(N)  =  N\r\n *  cbrt(-I) = -I\r\n *  cbrt(I)  =  I\r\n *\r\n * Math.cbrt(x) = (x < 0 ? -Math.pow(-x, 1/3) : Math.pow(x, 1/3))\r\n *\r\n */\r\nP.cubeRoot = P.cbrt = function () {\r\n  var e, m, n, r, rep, s, sd, t, t3, t3plusx,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite() || x.isZero()) return new Ctor(x);\r\n  external = false;\r\n\r\n  // Initial estimate.\r\n  s = x.s * mathpow(x.s * x, 1 / 3);\r\n\r\n   // Math.cbrt underflow/overflow?\r\n   // Pass x to Math.pow as integer, then adjust the exponent of the result.\r\n  if (!s || Math.abs(s) == 1 / 0) {\r\n    n = digitsToString(x.d);\r\n    e = x.e;\r\n\r\n    // Adjust n exponent so it is a multiple of 3 away from x exponent.\r\n    if (s = (e - n.length + 1) % 3) n += (s == 1 || s == -2 ? '0' : '00');\r\n    s = mathpow(n, 1 / 3);\r\n\r\n    // Rarely, e may be one less than the result exponent value.\r\n    e = mathfloor((e + 1) / 3) - (e % 3 == (e < 0 ? -1 : 2));\r\n\r\n    if (s == 1 / 0) {\r\n      n = '5e' + e;\r\n    } else {\r\n      n = s.toExponential();\r\n      n = n.slice(0, n.indexOf('e') + 1) + e;\r\n    }\r\n\r\n    r = new Ctor(n);\r\n    r.s = x.s;\r\n  } else {\r\n    r = new Ctor(s.toString());\r\n  }\r\n\r\n  sd = (e = Ctor.precision) + 3;\r\n\r\n  // Halley's method.\r\n  // TODO? Compare Newton's method.\r\n  for (;;) {\r\n    t = r;\r\n    t3 = t.times(t).times(t);\r\n    t3plusx = t3.plus(x);\r\n    r = divide(t3plusx.plus(x).times(t), t3plusx.plus(t3), sd + 2, 1);\r\n\r\n    // TODO? Replace with for-loop and checkRoundingDigits.\r\n    if (digitsToString(t.d).slice(0, sd) === (n = digitsToString(r.d)).slice(0, sd)) {\r\n      n = n.slice(sd - 3, sd + 1);\r\n\r\n      // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or 4999\r\n      // , i.e. approaching a rounding boundary, continue the iteration.\r\n      if (n == '9999' || !rep && n == '4999') {\r\n\r\n        // On the first iteration only, check to see if rounding up gives the exact result as the\r\n        // nines may infinitely repeat.\r\n        if (!rep) {\r\n          finalise(t, e + 1, 0);\r\n\r\n          if (t.times(t).times(t).eq(x)) {\r\n            r = t;\r\n            break;\r\n          }\r\n        }\r\n\r\n        sd += 4;\r\n        rep = 1;\r\n      } else {\r\n\r\n        // If the rounding digits are null, 0{0,4} or 50{0,3}, check for an exact result.\r\n        // If not, then there are further digits and m will be truthy.\r\n        if (!+n || !+n.slice(1) && n.charAt(0) == '5') {\r\n\r\n          // Truncate to the first rounding digit.\r\n          finalise(r, e + 1, 1);\r\n          m = !r.times(r).times(r).eq(x);\r\n        }\r\n\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  external = true;\r\n\r\n  return finalise(r, e, Ctor.rounding, m);\r\n};\r\n\r\n\r\n/*\r\n * Return the number of decimal places of the value of this Decimal.\r\n *\r\n */\r\nP.decimalPlaces = P.dp = function () {\r\n  var w,\r\n    d = this.d,\r\n    n = NaN;\r\n\r\n  if (d) {\r\n    w = d.length - 1;\r\n    n = (w - mathfloor(this.e / LOG_BASE)) * LOG_BASE;\r\n\r\n    // Subtract the number of trailing zeros of the last word.\r\n    w = d[w];\r\n    if (w) for (; w % 10 == 0; w /= 10) n--;\r\n    if (n < 0) n = 0;\r\n  }\r\n\r\n  return n;\r\n};\r\n\r\n\r\n/*\r\n *  n / 0 = I\r\n *  n / N = N\r\n *  n / I = 0\r\n *  0 / n = 0\r\n *  0 / 0 = N\r\n *  0 / N = N\r\n *  0 / I = 0\r\n *  N / n = N\r\n *  N / 0 = N\r\n *  N / N = N\r\n *  N / I = N\r\n *  I / n = I\r\n *  I / 0 = I\r\n *  I / N = N\r\n *  I / I = N\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal divided by `y`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.dividedBy = P.div = function (y) {\r\n  return divide(this, new this.constructor(y));\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the integer part of dividing the value of this Decimal\r\n * by the value of `y`, rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.dividedToIntegerBy = P.divToInt = function (y) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n  return finalise(divide(x, new Ctor(y), 0, 1, 1), Ctor.precision, Ctor.rounding);\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is equal to the value of `y`, otherwise return false.\r\n *\r\n */\r\nP.equals = P.eq = function (y) {\r\n  return this.cmp(y) === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number in the\r\n * direction of negative Infinity.\r\n *\r\n */\r\nP.floor = function () {\r\n  return finalise(new this.constructor(this), this.e + 1, 3);\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is greater than the value of `y`, otherwise return\r\n * false.\r\n *\r\n */\r\nP.greaterThan = P.gt = function (y) {\r\n  return this.cmp(y) > 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is greater than or equal to the value of `y`,\r\n * otherwise return false.\r\n *\r\n */\r\nP.greaterThanOrEqualTo = P.gte = function (y) {\r\n  var k = this.cmp(y);\r\n  return k == 1 || k === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic cosine of the value in radians of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [1, Infinity]\r\n *\r\n * cosh(x) = 1 + x^2/2! + x^4/4! + x^6/6! + ...\r\n *\r\n * cosh(0)         = 1\r\n * cosh(-0)        = 1\r\n * cosh(Infinity)  = Infinity\r\n * cosh(-Infinity) = Infinity\r\n * cosh(NaN)       = NaN\r\n *\r\n *  x        time taken (ms)   result\r\n * 1000      9                 9.8503555700852349694e+433\r\n * 10000     25                4.4034091128314607936e+4342\r\n * 100000    171               1.4033316802130615897e+43429\r\n * 1000000   3817              1.5166076984010437725e+434294\r\n * 10000000  abandoned after 2 minute wait\r\n *\r\n * TODO? Compare performance of cosh(x) = 0.5 * (exp(x) + exp(-x))\r\n *\r\n */\r\nP.hyperbolicCosine = P.cosh = function () {\r\n  var k, n, pr, rm, len,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    one = new Ctor(1);\r\n\r\n  if (!x.isFinite()) return new Ctor(x.s ? 1 / 0 : NaN);\r\n  if (x.isZero()) return one;\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + 4;\r\n  Ctor.rounding = 1;\r\n  len = x.d.length;\r\n\r\n  // Argument reduction: cos(4x) = 1 - 8cos^2(x) + 8cos^4(x) + 1\r\n  // i.e. cos(x) = 1 - cos^2(x/4)(8 - 8cos^2(x/4))\r\n\r\n  // Estimate the optimum number of times to use the argument reduction.\r\n  // TODO? Estimation reused from cosine() and may not be optimal here.\r\n  if (len < 32) {\r\n    k = Math.ceil(len / 3);\r\n    n = (1 / tinyPow(4, k)).toString();\r\n  } else {\r\n    k = 16;\r\n    n = '2.3283064365386962890625e-10';\r\n  }\r\n\r\n  x = taylorSeries(Ctor, 1, x.times(n), new Ctor(1), true);\r\n\r\n  // Reverse argument reduction\r\n  var cosh2_x,\r\n    i = k,\r\n    d8 = new Ctor(8);\r\n  for (; i--;) {\r\n    cosh2_x = x.times(x);\r\n    x = one.minus(cosh2_x.times(d8.minus(cosh2_x.times(d8))));\r\n  }\r\n\r\n  return finalise(x, Ctor.precision = pr, Ctor.rounding = rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic sine of the value in radians of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * sinh(x) = x + x^3/3! + x^5/5! + x^7/7! + ...\r\n *\r\n * sinh(0)         = 0\r\n * sinh(-0)        = -0\r\n * sinh(Infinity)  = Infinity\r\n * sinh(-Infinity) = -Infinity\r\n * sinh(NaN)       = NaN\r\n *\r\n * x        time taken (ms)\r\n * 10       2 ms\r\n * 100      5 ms\r\n * 1000     14 ms\r\n * 10000    82 ms\r\n * 100000   886 ms            1.4033316802130615897e+43429\r\n * 200000   2613 ms\r\n * 300000   5407 ms\r\n * 400000   8824 ms\r\n * 500000   13026 ms          8.7080643612718084129e+217146\r\n * 1000000  48543 ms\r\n *\r\n * TODO? Compare performance of sinh(x) = 0.5 * (exp(x) - exp(-x))\r\n *\r\n */\r\nP.hyperbolicSine = P.sinh = function () {\r\n  var k, pr, rm, len,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite() || x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + 4;\r\n  Ctor.rounding = 1;\r\n  len = x.d.length;\r\n\r\n  if (len < 3) {\r\n    x = taylorSeries(Ctor, 2, x, x, true);\r\n  } else {\r\n\r\n    // Alternative argument reduction: sinh(3x) = sinh(x)(3 + 4sinh^2(x))\r\n    // i.e. sinh(x) = sinh(x/3)(3 + 4sinh^2(x/3))\r\n    // 3 multiplications and 1 addition\r\n\r\n    // Argument reduction: sinh(5x) = sinh(x)(5 + sinh^2(x)(20 + 16sinh^2(x)))\r\n    // i.e. sinh(x) = sinh(x/5)(5 + sinh^2(x/5)(20 + 16sinh^2(x/5)))\r\n    // 4 multiplications and 2 additions\r\n\r\n    // Estimate the optimum number of times to use the argument reduction.\r\n    k = 1.4 * Math.sqrt(len);\r\n    k = k > 16 ? 16 : k | 0;\r\n\r\n    x = x.times(1 / tinyPow(5, k));\r\n    x = taylorSeries(Ctor, 2, x, x, true);\r\n\r\n    // Reverse argument reduction\r\n    var sinh2_x,\r\n      d5 = new Ctor(5),\r\n      d16 = new Ctor(16),\r\n      d20 = new Ctor(20);\r\n    for (; k--;) {\r\n      sinh2_x = x.times(x);\r\n      x = x.times(d5.plus(sinh2_x.times(d16.times(sinh2_x).plus(d20))));\r\n    }\r\n  }\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic tangent of the value in radians of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-1, 1]\r\n *\r\n * tanh(x) = sinh(x) / cosh(x)\r\n *\r\n * tanh(0)         = 0\r\n * tanh(-0)        = -0\r\n * tanh(Infinity)  = 1\r\n * tanh(-Infinity) = -1\r\n * tanh(NaN)       = NaN\r\n *\r\n */\r\nP.hyperbolicTangent = P.tanh = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(x.s);\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + 7;\r\n  Ctor.rounding = 1;\r\n\r\n  return divide(x.sinh(), x.cosh(), Ctor.precision = pr, Ctor.rounding = rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arccosine (inverse cosine) in radians of the value of\r\n * this Decimal.\r\n *\r\n * Domain: [-1, 1]\r\n * Range: [0, pi]\r\n *\r\n * acos(x) = pi/2 - asin(x)\r\n *\r\n * acos(0)       = pi/2\r\n * acos(-0)      = pi/2\r\n * acos(1)       = 0\r\n * acos(-1)      = pi\r\n * acos(1/2)     = pi/3\r\n * acos(-1/2)    = 2*pi/3\r\n * acos(|x| > 1) = NaN\r\n * acos(NaN)     = NaN\r\n *\r\n */\r\nP.inverseCosine = P.acos = function () {\r\n  var x = this,\r\n    Ctor = x.constructor,\r\n    k = x.abs().cmp(1),\r\n    pr = Ctor.precision,\r\n    rm = Ctor.rounding;\r\n\r\n  if (k !== -1) {\r\n    return k === 0\r\n      // |x| is 1\r\n      ? x.isNeg() ? getPi(Ctor, pr, rm) : new Ctor(0)\r\n      // |x| > 1 or x is NaN\r\n      : new Ctor(NaN);\r\n  }\r\n\r\n  if (x.isZero()) return getPi(Ctor, pr + 4, rm).times(0.5);\r\n\r\n  // TODO? Special case acos(0.5) = pi/3 and acos(-0.5) = 2*pi/3\r\n\r\n  Ctor.precision = pr + 6;\r\n  Ctor.rounding = 1;\r\n\r\n  // See https://github.com/MikeMcl/decimal.js/pull/217\r\n  x = new Ctor(1).minus(x).div(x.plus(1)).sqrt().atan();\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.times(2);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic cosine in radians of the\r\n * value of this Decimal.\r\n *\r\n * Domain: [1, Infinity]\r\n * Range: [0, Infinity]\r\n *\r\n * acosh(x) = ln(x + sqrt(x^2 - 1))\r\n *\r\n * acosh(x < 1)     = NaN\r\n * acosh(NaN)       = NaN\r\n * acosh(Infinity)  = Infinity\r\n * acosh(-Infinity) = NaN\r\n * acosh(0)         = NaN\r\n * acosh(-0)        = NaN\r\n * acosh(1)         = 0\r\n * acosh(-1)        = NaN\r\n *\r\n */\r\nP.inverseHyperbolicCosine = P.acosh = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (x.lte(1)) return new Ctor(x.eq(1) ? 0 : NaN);\r\n  if (!x.isFinite()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(Math.abs(x.e), x.sd()) + 4;\r\n  Ctor.rounding = 1;\r\n  external = false;\r\n\r\n  x = x.times(x).minus(1).sqrt().plus(x);\r\n\r\n  external = true;\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.ln();\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic sine in radians of the value\r\n * of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * asinh(x) = ln(x + sqrt(x^2 + 1))\r\n *\r\n * asinh(NaN)       = NaN\r\n * asinh(Infinity)  = Infinity\r\n * asinh(-Infinity) = -Infinity\r\n * asinh(0)         = 0\r\n * asinh(-0)        = -0\r\n *\r\n */\r\nP.inverseHyperbolicSine = P.asinh = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite() || x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + 2 * Math.max(Math.abs(x.e), x.sd()) + 6;\r\n  Ctor.rounding = 1;\r\n  external = false;\r\n\r\n  x = x.times(x).plus(1).sqrt().plus(x);\r\n\r\n  external = true;\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.ln();\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic tangent in radians of the\r\n * value of this Decimal.\r\n *\r\n * Domain: [-1, 1]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * atanh(x) = 0.5 * ln((1 + x) / (1 - x))\r\n *\r\n * atanh(|x| > 1)   = NaN\r\n * atanh(NaN)       = NaN\r\n * atanh(Infinity)  = NaN\r\n * atanh(-Infinity) = NaN\r\n * atanh(0)         = 0\r\n * atanh(-0)        = -0\r\n * atanh(1)         = Infinity\r\n * atanh(-1)        = -Infinity\r\n *\r\n */\r\nP.inverseHyperbolicTangent = P.atanh = function () {\r\n  var pr, rm, wpr, xsd,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(NaN);\r\n  if (x.e >= 0) return new Ctor(x.abs().eq(1) ? x.s / 0 : x.isZero() ? x : NaN);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  xsd = x.sd();\r\n\r\n  if (Math.max(xsd, pr) < 2 * -x.e - 1) return finalise(new Ctor(x), pr, rm, true);\r\n\r\n  Ctor.precision = wpr = xsd - x.e;\r\n\r\n  x = divide(x.plus(1), new Ctor(1).minus(x), wpr + pr, 1);\r\n\r\n  Ctor.precision = pr + 4;\r\n  Ctor.rounding = 1;\r\n\r\n  x = x.ln();\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.times(0.5);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arcsine (inverse sine) in radians of the value of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-pi/2, pi/2]\r\n *\r\n * asin(x) = 2*atan(x/(1 + sqrt(1 - x^2)))\r\n *\r\n * asin(0)       = 0\r\n * asin(-0)      = -0\r\n * asin(1/2)     = pi/6\r\n * asin(-1/2)    = -pi/6\r\n * asin(1)       = pi/2\r\n * asin(-1)      = -pi/2\r\n * asin(|x| > 1) = NaN\r\n * asin(NaN)     = NaN\r\n *\r\n * TODO? Compare performance of Taylor series.\r\n *\r\n */\r\nP.inverseSine = P.asin = function () {\r\n  var halfPi, k,\r\n    pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  k = x.abs().cmp(1);\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  if (k !== -1) {\r\n\r\n    // |x| is 1\r\n    if (k === 0) {\r\n      halfPi = getPi(Ctor, pr + 4, rm).times(0.5);\r\n      halfPi.s = x.s;\r\n      return halfPi;\r\n    }\r\n\r\n    // |x| > 1 or x is NaN\r\n    return new Ctor(NaN);\r\n  }\r\n\r\n  // TODO? Special case asin(1/2) = pi/6 and asin(-1/2) = -pi/6\r\n\r\n  Ctor.precision = pr + 6;\r\n  Ctor.rounding = 1;\r\n\r\n  x = x.div(new Ctor(1).minus(x.times(x)).sqrt().plus(1)).atan();\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.times(2);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arctangent (inverse tangent) in radians of the value\r\n * of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-pi/2, pi/2]\r\n *\r\n * atan(x) = x - x^3/3 + x^5/5 - x^7/7 + ...\r\n *\r\n * atan(0)         = 0\r\n * atan(-0)        = -0\r\n * atan(1)         = pi/4\r\n * atan(-1)        = -pi/4\r\n * atan(Infinity)  = pi/2\r\n * atan(-Infinity) = -pi/2\r\n * atan(NaN)       = NaN\r\n *\r\n */\r\nP.inverseTangent = P.atan = function () {\r\n  var i, j, k, n, px, t, r, wpr, x2,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    pr = Ctor.precision,\r\n    rm = Ctor.rounding;\r\n\r\n  if (!x.isFinite()) {\r\n    if (!x.s) return new Ctor(NaN);\r\n    if (pr + 4 <= PI_PRECISION) {\r\n      r = getPi(Ctor, pr + 4, rm).times(0.5);\r\n      r.s = x.s;\r\n      return r;\r\n    }\r\n  } else if (x.isZero()) {\r\n    return new Ctor(x);\r\n  } else if (x.abs().eq(1) && pr + 4 <= PI_PRECISION) {\r\n    r = getPi(Ctor, pr + 4, rm).times(0.25);\r\n    r.s = x.s;\r\n    return r;\r\n  }\r\n\r\n  Ctor.precision = wpr = pr + 10;\r\n  Ctor.rounding = 1;\r\n\r\n  // TODO? if (x >= 1 && pr <= PI_PRECISION) atan(x) = halfPi * x.s - atan(1 / x);\r\n\r\n  // Argument reduction\r\n  // Ensure |x| < 0.42\r\n  // atan(x) = 2 * atan(x / (1 + sqrt(1 + x^2)))\r\n\r\n  k = Math.min(28, wpr / LOG_BASE + 2 | 0);\r\n\r\n  for (i = k; i; --i) x = x.div(x.times(x).plus(1).sqrt().plus(1));\r\n\r\n  external = false;\r\n\r\n  j = Math.ceil(wpr / LOG_BASE);\r\n  n = 1;\r\n  x2 = x.times(x);\r\n  r = new Ctor(x);\r\n  px = x;\r\n\r\n  // atan(x) = x - x^3/3 + x^5/5 - x^7/7 + ...\r\n  for (; i !== -1;) {\r\n    px = px.times(x2);\r\n    t = r.minus(px.div(n += 2));\r\n\r\n    px = px.times(x2);\r\n    r = t.plus(px.div(n += 2));\r\n\r\n    if (r.d[j] !== void 0) for (i = j; r.d[i] === t.d[i] && i--;);\r\n  }\r\n\r\n  if (k) r = r.times(2 << (k - 1));\r\n\r\n  external = true;\r\n\r\n  return finalise(r, Ctor.precision = pr, Ctor.rounding = rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is a finite number, otherwise return false.\r\n *\r\n */\r\nP.isFinite = function () {\r\n  return !!this.d;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is an integer, otherwise return false.\r\n *\r\n */\r\nP.isInteger = P.isInt = function () {\r\n  return !!this.d && mathfloor(this.e / LOG_BASE) > this.d.length - 2;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is NaN, otherwise return false.\r\n *\r\n */\r\nP.isNaN = function () {\r\n  return !this.s;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is negative, otherwise return false.\r\n *\r\n */\r\nP.isNegative = P.isNeg = function () {\r\n  return this.s < 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is positive, otherwise return false.\r\n *\r\n */\r\nP.isPositive = P.isPos = function () {\r\n  return this.s > 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is 0 or -0, otherwise return false.\r\n *\r\n */\r\nP.isZero = function () {\r\n  return !!this.d && this.d[0] === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is less than `y`, otherwise return false.\r\n *\r\n */\r\nP.lessThan = P.lt = function (y) {\r\n  return this.cmp(y) < 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is less than or equal to `y`, otherwise return false.\r\n *\r\n */\r\nP.lessThanOrEqualTo = P.lte = function (y) {\r\n  return this.cmp(y) < 1;\r\n};\r\n\r\n\r\n/*\r\n * Return the logarithm of the value of this Decimal to the specified base, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * If no base is specified, return log[10](arg).\r\n *\r\n * log[base](arg) = ln(arg) / ln(base)\r\n *\r\n * The result will always be correctly rounded if the base of the log is 10, and 'almost always'\r\n * otherwise:\r\n *\r\n * Depending on the rounding mode, the result may be incorrectly rounded if the first fifteen\r\n * rounding digits are [49]99999999999999 or [50]00000000000000. In that case, the maximum error\r\n * between the result and the correctly rounded result will be one ulp (unit in the last place).\r\n *\r\n * log[-b](a)       = NaN\r\n * log[0](a)        = NaN\r\n * log[1](a)        = NaN\r\n * log[NaN](a)      = NaN\r\n * log[Infinity](a) = NaN\r\n * log[b](0)        = -Infinity\r\n * log[b](-0)       = -Infinity\r\n * log[b](-a)       = NaN\r\n * log[b](1)        = 0\r\n * log[b](Infinity) = Infinity\r\n * log[b](NaN)      = NaN\r\n *\r\n * [base] {number|string|bigint|Decimal} The base of the logarithm.\r\n *\r\n */\r\nP.logarithm = P.log = function (base) {\r\n  var isBase10, d, denominator, k, inf, num, sd, r,\r\n    arg = this,\r\n    Ctor = arg.constructor,\r\n    pr = Ctor.precision,\r\n    rm = Ctor.rounding,\r\n    guard = 5;\r\n\r\n  // Default base is 10.\r\n  if (base == null) {\r\n    base = new Ctor(10);\r\n    isBase10 = true;\r\n  } else {\r\n    base = new Ctor(base);\r\n    d = base.d;\r\n\r\n    // Return NaN if base is negative, or non-finite, or is 0 or 1.\r\n    if (base.s < 0 || !d || !d[0] || base.eq(1)) return new Ctor(NaN);\r\n\r\n    isBase10 = base.eq(10);\r\n  }\r\n\r\n  d = arg.d;\r\n\r\n  // Is arg negative, non-finite, 0 or 1?\r\n  if (arg.s < 0 || !d || !d[0] || arg.eq(1)) {\r\n    return new Ctor(d && !d[0] ? -1 / 0 : arg.s != 1 ? NaN : d ? 0 : 1 / 0);\r\n  }\r\n\r\n  // The result will have a non-terminating decimal expansion if base is 10 and arg is not an\r\n  // integer power of 10.\r\n  if (isBase10) {\r\n    if (d.length > 1) {\r\n      inf = true;\r\n    } else {\r\n      for (k = d[0]; k % 10 === 0;) k /= 10;\r\n      inf = k !== 1;\r\n    }\r\n  }\r\n\r\n  external = false;\r\n  sd = pr + guard;\r\n  num = naturalLogarithm(arg, sd);\r\n  denominator = isBase10 ? getLn10(Ctor, sd + 10) : naturalLogarithm(base, sd);\r\n\r\n  // The result will have 5 rounding digits.\r\n  r = divide(num, denominator, sd, 1);\r\n\r\n  // If at a rounding boundary, i.e. the result's rounding digits are [49]9999 or [50]0000,\r\n  // calculate 10 further digits.\r\n  //\r\n  // If the result is known to have an infinite decimal expansion, repeat this until it is clear\r\n  // that the result is above or below the boundary. Otherwise, if after calculating the 10\r\n  // further digits, the last 14 are nines, round up and assume the result is exact.\r\n  // Also assume the result is exact if the last 14 are zero.\r\n  //\r\n  // Example of a result that will be incorrectly rounded:\r\n  // log[1048576](4503599627370502) = 2.60000000000000009610279511444746...\r\n  // The above result correctly rounded using ROUND_CEIL to 1 decimal place should be 2.7, but it\r\n  // will be given as 2.6 as there are 15 zeros immediately after the requested decimal place, so\r\n  // the exact result would be assumed to be 2.6, which rounded using ROUND_CEIL to 1 decimal\r\n  // place is still 2.6.\r\n  if (checkRoundingDigits(r.d, k = pr, rm)) {\r\n\r\n    do {\r\n      sd += 10;\r\n      num = naturalLogarithm(arg, sd);\r\n      denominator = isBase10 ? getLn10(Ctor, sd + 10) : naturalLogarithm(base, sd);\r\n      r = divide(num, denominator, sd, 1);\r\n\r\n      if (!inf) {\r\n\r\n        // Check for 14 nines from the 2nd rounding digit, as the first may be 4.\r\n        if (+digitsToString(r.d).slice(k + 1, k + 15) + 1 == 1e14) {\r\n          r = finalise(r, pr + 1, 0);\r\n        }\r\n\r\n        break;\r\n      }\r\n    } while (checkRoundingDigits(r.d, k += 10, rm));\r\n  }\r\n\r\n  external = true;\r\n\r\n  return finalise(r, pr, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the maximum of the arguments and the value of this Decimal.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\nP.max = function () {\r\n  Array.prototype.push.call(arguments, this);\r\n  return maxOrMin(this.constructor, arguments, -1);\r\n};\r\n */\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the minimum of the arguments and the value of this Decimal.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\nP.min = function () {\r\n  Array.prototype.push.call(arguments, this);\r\n  return maxOrMin(this.constructor, arguments, 1);\r\n};\r\n */\r\n\r\n\r\n/*\r\n *  n - 0 = n\r\n *  n - N = N\r\n *  n - I = -I\r\n *  0 - n = -n\r\n *  0 - 0 = 0\r\n *  0 - N = N\r\n *  0 - I = -I\r\n *  N - n = N\r\n *  N - 0 = N\r\n *  N - N = N\r\n *  N - I = N\r\n *  I - n = I\r\n *  I - 0 = I\r\n *  I - N = N\r\n *  I - I = N\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal minus `y`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.minus = P.sub = function (y) {\r\n  var d, e, i, j, k, len, pr, rm, xd, xe, xLTy, yd,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  y = new Ctor(y);\r\n\r\n  // If either is not finite...\r\n  if (!x.d || !y.d) {\r\n\r\n    // Return NaN if either is NaN.\r\n    if (!x.s || !y.s) y = new Ctor(NaN);\r\n\r\n    // Return y negated if x is finite and y is ±Infinity.\r\n    else if (x.d) y.s = -y.s;\r\n\r\n    // Return x if y is finite and x is ±Infinity.\r\n    // Return x if both are ±Infinity with different signs.\r\n    // Return NaN if both are ±Infinity with the same sign.\r\n    else y = new Ctor(y.d || x.s !== y.s ? x : NaN);\r\n\r\n    return y;\r\n  }\r\n\r\n  // If signs differ...\r\n  if (x.s != y.s) {\r\n    y.s = -y.s;\r\n    return x.plus(y);\r\n  }\r\n\r\n  xd = x.d;\r\n  yd = y.d;\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  // If either is zero...\r\n  if (!xd[0] || !yd[0]) {\r\n\r\n    // Return y negated if x is zero and y is non-zero.\r\n    if (yd[0]) y.s = -y.s;\r\n\r\n    // Return x if y is zero and x is non-zero.\r\n    else if (xd[0]) y = new Ctor(x);\r\n\r\n    // Return zero if both are zero.\r\n    // From IEEE 754 (2008) 6.3: 0 - 0 = -0 - -0 = -0 when rounding to -Infinity.\r\n    else return new Ctor(rm === 3 ? -0 : 0);\r\n\r\n    return external ? finalise(y, pr, rm) : y;\r\n  }\r\n\r\n  // x and y are finite, non-zero numbers with the same sign.\r\n\r\n  // Calculate base 1e7 exponents.\r\n  e = mathfloor(y.e / LOG_BASE);\r\n  xe = mathfloor(x.e / LOG_BASE);\r\n\r\n  xd = xd.slice();\r\n  k = xe - e;\r\n\r\n  // If base 1e7 exponents differ...\r\n  if (k) {\r\n    xLTy = k < 0;\r\n\r\n    if (xLTy) {\r\n      d = xd;\r\n      k = -k;\r\n      len = yd.length;\r\n    } else {\r\n      d = yd;\r\n      e = xe;\r\n      len = xd.length;\r\n    }\r\n\r\n    // Numbers with massively different exponents would result in a very high number of\r\n    // zeros needing to be prepended, but this can be avoided while still ensuring correct\r\n    // rounding by limiting the number of zeros to `Math.ceil(pr / LOG_BASE) + 2`.\r\n    i = Math.max(Math.ceil(pr / LOG_BASE), len) + 2;\r\n\r\n    if (k > i) {\r\n      k = i;\r\n      d.length = 1;\r\n    }\r\n\r\n    // Prepend zeros to equalise exponents.\r\n    d.reverse();\r\n    for (i = k; i--;) d.push(0);\r\n    d.reverse();\r\n\r\n  // Base 1e7 exponents equal.\r\n  } else {\r\n\r\n    // Check digits to determine which is the bigger number.\r\n\r\n    i = xd.length;\r\n    len = yd.length;\r\n    xLTy = i < len;\r\n    if (xLTy) len = i;\r\n\r\n    for (i = 0; i < len; i++) {\r\n      if (xd[i] != yd[i]) {\r\n        xLTy = xd[i] < yd[i];\r\n        break;\r\n      }\r\n    }\r\n\r\n    k = 0;\r\n  }\r\n\r\n  if (xLTy) {\r\n    d = xd;\r\n    xd = yd;\r\n    yd = d;\r\n    y.s = -y.s;\r\n  }\r\n\r\n  len = xd.length;\r\n\r\n  // Append zeros to `xd` if shorter.\r\n  // Don't add zeros to `yd` if shorter as subtraction only needs to start at `yd` length.\r\n  for (i = yd.length - len; i > 0; --i) xd[len++] = 0;\r\n\r\n  // Subtract yd from xd.\r\n  for (i = yd.length; i > k;) {\r\n\r\n    if (xd[--i] < yd[i]) {\r\n      for (j = i; j && xd[--j] === 0;) xd[j] = BASE - 1;\r\n      --xd[j];\r\n      xd[i] += BASE;\r\n    }\r\n\r\n    xd[i] -= yd[i];\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (; xd[--len] === 0;) xd.pop();\r\n\r\n  // Remove leading zeros and adjust exponent accordingly.\r\n  for (; xd[0] === 0; xd.shift()) --e;\r\n\r\n  // Zero?\r\n  if (!xd[0]) return new Ctor(rm === 3 ? -0 : 0);\r\n\r\n  y.d = xd;\r\n  y.e = getBase10Exponent(xd, e);\r\n\r\n  return external ? finalise(y, pr, rm) : y;\r\n};\r\n\r\n\r\n/*\r\n *   n % 0 =  N\r\n *   n % N =  N\r\n *   n % I =  n\r\n *   0 % n =  0\r\n *  -0 % n = -0\r\n *   0 % 0 =  N\r\n *   0 % N =  N\r\n *   0 % I =  0\r\n *   N % n =  N\r\n *   N % 0 =  N\r\n *   N % N =  N\r\n *   N % I =  N\r\n *   I % n =  N\r\n *   I % 0 =  N\r\n *   I % N =  N\r\n *   I % I =  N\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal modulo `y`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * The result depends on the modulo mode.\r\n *\r\n */\r\nP.modulo = P.mod = function (y) {\r\n  var q,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  y = new Ctor(y);\r\n\r\n  // Return NaN if x is ±Infinity or NaN, or y is NaN or ±0.\r\n  if (!x.d || !y.s || y.d && !y.d[0]) return new Ctor(NaN);\r\n\r\n  // Return x if y is ±Infinity or x is ±0.\r\n  if (!y.d || x.d && !x.d[0]) {\r\n    return finalise(new Ctor(x), Ctor.precision, Ctor.rounding);\r\n  }\r\n\r\n  // Prevent rounding of intermediate calculations.\r\n  external = false;\r\n\r\n  if (Ctor.modulo == 9) {\r\n\r\n    // Euclidian division: q = sign(y) * floor(x / abs(y))\r\n    // result = x - q * y    where  0 <= result < abs(y)\r\n    q = divide(x, y.abs(), 0, 3, 1);\r\n    q.s *= y.s;\r\n  } else {\r\n    q = divide(x, y, 0, Ctor.modulo, 1);\r\n  }\r\n\r\n  q = q.times(y);\r\n\r\n  external = true;\r\n\r\n  return x.minus(q);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural exponential of the value of this Decimal,\r\n * i.e. the base e raised to the power the value of this Decimal, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.naturalExponential = P.exp = function () {\r\n  return naturalExponential(this);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural logarithm of the value of this Decimal,\r\n * rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.naturalLogarithm = P.ln = function () {\r\n  return naturalLogarithm(this);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal negated, i.e. as if multiplied by\r\n * -1.\r\n *\r\n */\r\nP.negated = P.neg = function () {\r\n  var x = new this.constructor(this);\r\n  x.s = -x.s;\r\n  return finalise(x);\r\n};\r\n\r\n\r\n/*\r\n *  n + 0 = n\r\n *  n + N = N\r\n *  n + I = I\r\n *  0 + n = n\r\n *  0 + 0 = 0\r\n *  0 + N = N\r\n *  0 + I = I\r\n *  N + n = N\r\n *  N + 0 = N\r\n *  N + N = N\r\n *  N + I = N\r\n *  I + n = I\r\n *  I + 0 = I\r\n *  I + N = N\r\n *  I + I = I\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal plus `y`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.plus = P.add = function (y) {\r\n  var carry, d, e, i, k, len, pr, rm, xd, yd,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  y = new Ctor(y);\r\n\r\n  // If either is not finite...\r\n  if (!x.d || !y.d) {\r\n\r\n    // Return NaN if either is NaN.\r\n    if (!x.s || !y.s) y = new Ctor(NaN);\r\n\r\n    // Return x if y is finite and x is ±Infinity.\r\n    // Return x if both are ±Infinity with the same sign.\r\n    // Return NaN if both are ±Infinity with different signs.\r\n    // Return y if x is finite and y is ±Infinity.\r\n    else if (!x.d) y = new Ctor(y.d || x.s === y.s ? x : NaN);\r\n\r\n    return y;\r\n  }\r\n\r\n   // If signs differ...\r\n  if (x.s != y.s) {\r\n    y.s = -y.s;\r\n    return x.minus(y);\r\n  }\r\n\r\n  xd = x.d;\r\n  yd = y.d;\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  // If either is zero...\r\n  if (!xd[0] || !yd[0]) {\r\n\r\n    // Return x if y is zero.\r\n    // Return y if y is non-zero.\r\n    if (!yd[0]) y = new Ctor(x);\r\n\r\n    return external ? finalise(y, pr, rm) : y;\r\n  }\r\n\r\n  // x and y are finite, non-zero numbers with the same sign.\r\n\r\n  // Calculate base 1e7 exponents.\r\n  k = mathfloor(x.e / LOG_BASE);\r\n  e = mathfloor(y.e / LOG_BASE);\r\n\r\n  xd = xd.slice();\r\n  i = k - e;\r\n\r\n  // If base 1e7 exponents differ...\r\n  if (i) {\r\n\r\n    if (i < 0) {\r\n      d = xd;\r\n      i = -i;\r\n      len = yd.length;\r\n    } else {\r\n      d = yd;\r\n      e = k;\r\n      len = xd.length;\r\n    }\r\n\r\n    // Limit number of zeros prepended to max(ceil(pr / LOG_BASE), len) + 1.\r\n    k = Math.ceil(pr / LOG_BASE);\r\n    len = k > len ? k + 1 : len + 1;\r\n\r\n    if (i > len) {\r\n      i = len;\r\n      d.length = 1;\r\n    }\r\n\r\n    // Prepend zeros to equalise exponents. Note: Faster to use reverse then do unshifts.\r\n    d.reverse();\r\n    for (; i--;) d.push(0);\r\n    d.reverse();\r\n  }\r\n\r\n  len = xd.length;\r\n  i = yd.length;\r\n\r\n  // If yd is longer than xd, swap xd and yd so xd points to the longer array.\r\n  if (len - i < 0) {\r\n    i = len;\r\n    d = yd;\r\n    yd = xd;\r\n    xd = d;\r\n  }\r\n\r\n  // Only start adding at yd.length - 1 as the further digits of xd can be left as they are.\r\n  for (carry = 0; i;) {\r\n    carry = (xd[--i] = xd[i] + yd[i] + carry) / BASE | 0;\r\n    xd[i] %= BASE;\r\n  }\r\n\r\n  if (carry) {\r\n    xd.unshift(carry);\r\n    ++e;\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n  for (len = xd.length; xd[--len] == 0;) xd.pop();\r\n\r\n  y.d = xd;\r\n  y.e = getBase10Exponent(xd, e);\r\n\r\n  return external ? finalise(y, pr, rm) : y;\r\n};\r\n\r\n\r\n/*\r\n * Return the number of significant digits of the value of this Decimal.\r\n *\r\n * [z] {boolean|number} Whether to count integer-part trailing zeros: true, false, 1 or 0.\r\n *\r\n */\r\nP.precision = P.sd = function (z) {\r\n  var k,\r\n    x = this;\r\n\r\n  if (z !== void 0 && z !== !!z && z !== 1 && z !== 0) throw Error(invalidArgument + z);\r\n\r\n  if (x.d) {\r\n    k = getPrecision(x.d);\r\n    if (z && x.e + 1 > k) k = x.e + 1;\r\n  } else {\r\n    k = NaN;\r\n  }\r\n\r\n  return k;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number using\r\n * rounding mode `rounding`.\r\n *\r\n */\r\nP.round = function () {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  return finalise(new Ctor(x), x.e + 1, Ctor.rounding);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sine of the value in radians of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-1, 1]\r\n *\r\n * sin(x) = x - x^3/3! + x^5/5! - ...\r\n *\r\n * sin(0)         = 0\r\n * sin(-0)        = -0\r\n * sin(Infinity)  = NaN\r\n * sin(-Infinity) = NaN\r\n * sin(NaN)       = NaN\r\n *\r\n */\r\nP.sine = P.sin = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(NaN);\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + LOG_BASE;\r\n  Ctor.rounding = 1;\r\n\r\n  x = sine(Ctor, toLessThanHalfPi(Ctor, x));\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(quadrant > 2 ? x.neg() : x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the square root of this Decimal, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n *  sqrt(-n) =  N\r\n *  sqrt(N)  =  N\r\n *  sqrt(-I) =  N\r\n *  sqrt(I)  =  I\r\n *  sqrt(0)  =  0\r\n *  sqrt(-0) = -0\r\n *\r\n */\r\nP.squareRoot = P.sqrt = function () {\r\n  var m, n, sd, r, rep, t,\r\n    x = this,\r\n    d = x.d,\r\n    e = x.e,\r\n    s = x.s,\r\n    Ctor = x.constructor;\r\n\r\n  // Negative/NaN/Infinity/zero?\r\n  if (s !== 1 || !d || !d[0]) {\r\n    return new Ctor(!s || s < 0 && (!d || d[0]) ? NaN : d ? x : 1 / 0);\r\n  }\r\n\r\n  external = false;\r\n\r\n  // Initial estimate.\r\n  s = Math.sqrt(+x);\r\n\r\n  // Math.sqrt underflow/overflow?\r\n  // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\r\n  if (s == 0 || s == 1 / 0) {\r\n    n = digitsToString(d);\r\n\r\n    if ((n.length + e) % 2 == 0) n += '0';\r\n    s = Math.sqrt(n);\r\n    e = mathfloor((e + 1) / 2) - (e < 0 || e % 2);\r\n\r\n    if (s == 1 / 0) {\r\n      n = '5e' + e;\r\n    } else {\r\n      n = s.toExponential();\r\n      n = n.slice(0, n.indexOf('e') + 1) + e;\r\n    }\r\n\r\n    r = new Ctor(n);\r\n  } else {\r\n    r = new Ctor(s.toString());\r\n  }\r\n\r\n  sd = (e = Ctor.precision) + 3;\r\n\r\n  // Newton-Raphson iteration.\r\n  for (;;) {\r\n    t = r;\r\n    r = t.plus(divide(x, t, sd + 2, 1)).times(0.5);\r\n\r\n    // TODO? Replace with for-loop and checkRoundingDigits.\r\n    if (digitsToString(t.d).slice(0, sd) === (n = digitsToString(r.d)).slice(0, sd)) {\r\n      n = n.slice(sd - 3, sd + 1);\r\n\r\n      // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or\r\n      // 4999, i.e. approaching a rounding boundary, continue the iteration.\r\n      if (n == '9999' || !rep && n == '4999') {\r\n\r\n        // On the first iteration only, check to see if rounding up gives the exact result as the\r\n        // nines may infinitely repeat.\r\n        if (!rep) {\r\n          finalise(t, e + 1, 0);\r\n\r\n          if (t.times(t).eq(x)) {\r\n            r = t;\r\n            break;\r\n          }\r\n        }\r\n\r\n        sd += 4;\r\n        rep = 1;\r\n      } else {\r\n\r\n        // If the rounding digits are null, 0{0,4} or 50{0,3}, check for an exact result.\r\n        // If not, then there are further digits and m will be truthy.\r\n        if (!+n || !+n.slice(1) && n.charAt(0) == '5') {\r\n\r\n          // Truncate to the first rounding digit.\r\n          finalise(r, e + 1, 1);\r\n          m = !r.times(r).eq(x);\r\n        }\r\n\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  external = true;\r\n\r\n  return finalise(r, e, Ctor.rounding, m);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the tangent of the value in radians of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * tan(0)         = 0\r\n * tan(-0)        = -0\r\n * tan(Infinity)  = NaN\r\n * tan(-Infinity) = NaN\r\n * tan(NaN)       = NaN\r\n *\r\n */\r\nP.tangent = P.tan = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(NaN);\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + 10;\r\n  Ctor.rounding = 1;\r\n\r\n  x = x.sin();\r\n  x.s = 1;\r\n  x = divide(x, new Ctor(1).minus(x.times(x)).sqrt(), pr + 10, 0);\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(quadrant == 2 || quadrant == 4 ? x.neg() : x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n *  n * 0 = 0\r\n *  n * N = N\r\n *  n * I = I\r\n *  0 * n = 0\r\n *  0 * 0 = 0\r\n *  0 * N = N\r\n *  0 * I = N\r\n *  N * n = N\r\n *  N * 0 = N\r\n *  N * N = N\r\n *  N * I = N\r\n *  I * n = I\r\n *  I * 0 = N\r\n *  I * N = N\r\n *  I * I = I\r\n *\r\n * Return a new Decimal whose value is this Decimal times `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.times = P.mul = function (y) {\r\n  var carry, e, i, k, r, rL, t, xdL, ydL,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    xd = x.d,\r\n    yd = (y = new Ctor(y)).d;\r\n\r\n  y.s *= x.s;\r\n\r\n   // If either is NaN, ±Infinity or ±0...\r\n  if (!xd || !xd[0] || !yd || !yd[0]) {\r\n\r\n    return new Ctor(!y.s || xd && !xd[0] && !yd || yd && !yd[0] && !xd\r\n\r\n      // Return NaN if either is NaN.\r\n      // Return NaN if x is ±0 and y is ±Infinity, or y is ±0 and x is ±Infinity.\r\n      ? NaN\r\n\r\n      // Return ±Infinity if either is ±Infinity.\r\n      // Return ±0 if either is ±0.\r\n      : !xd || !yd ? y.s / 0 : y.s * 0);\r\n  }\r\n\r\n  e = mathfloor(x.e / LOG_BASE) + mathfloor(y.e / LOG_BASE);\r\n  xdL = xd.length;\r\n  ydL = yd.length;\r\n\r\n  // Ensure xd points to the longer array.\r\n  if (xdL < ydL) {\r\n    r = xd;\r\n    xd = yd;\r\n    yd = r;\r\n    rL = xdL;\r\n    xdL = ydL;\r\n    ydL = rL;\r\n  }\r\n\r\n  // Initialise the result array with zeros.\r\n  r = [];\r\n  rL = xdL + ydL;\r\n  for (i = rL; i--;) r.push(0);\r\n\r\n  // Multiply!\r\n  for (i = ydL; --i >= 0;) {\r\n    carry = 0;\r\n    for (k = xdL + i; k > i;) {\r\n      t = r[k] + yd[i] * xd[k - i - 1] + carry;\r\n      r[k--] = t % BASE | 0;\r\n      carry = t / BASE | 0;\r\n    }\r\n\r\n    r[k] = (r[k] + carry) % BASE | 0;\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (; !r[--rL];) r.pop();\r\n\r\n  if (carry) ++e;\r\n  else r.shift();\r\n\r\n  y.d = r;\r\n  y.e = getBase10Exponent(r, e);\r\n\r\n  return external ? finalise(y, Ctor.precision, Ctor.rounding) : y;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in base 2, round to `sd` significant\r\n * digits using rounding mode `rm`.\r\n *\r\n * If the optional `sd` argument is present then return binary exponential notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toBinary = function (sd, rm) {\r\n  return toStringBinary(this, 2, sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `dp`\r\n * decimal places using rounding mode `rm` or `rounding` if `rm` is omitted.\r\n *\r\n * If `dp` is omitted, return a new Decimal whose value is the value of this Decimal.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toDecimalPlaces = P.toDP = function (dp, rm) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  x = new Ctor(x);\r\n  if (dp === void 0) return x;\r\n\r\n  checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n  if (rm === void 0) rm = Ctor.rounding;\r\n  else checkInt32(rm, 0, 8);\r\n\r\n  return finalise(x, dp + x.e + 1, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in exponential notation rounded to\r\n * `dp` fixed decimal places using rounding mode `rounding`.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toExponential = function (dp, rm) {\r\n  var str,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (dp === void 0) {\r\n    str = finiteToString(x, true);\r\n  } else {\r\n    checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    x = finalise(new Ctor(x), dp + 1, rm);\r\n    str = finiteToString(x, true, dp + 1);\r\n  }\r\n\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in normal (fixed-point) notation to\r\n * `dp` fixed decimal places and rounded using rounding mode `rm` or `rounding` if `rm` is\r\n * omitted.\r\n *\r\n * As with JavaScript numbers, (-0).toFixed(0) is '0', but e.g. (-0.00001).toFixed(0) is '-0'.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n * (-0).toFixed(3) is '0.000'.\r\n * (-0.5).toFixed(0) is '-0'.\r\n *\r\n */\r\nP.toFixed = function (dp, rm) {\r\n  var str, y,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (dp === void 0) {\r\n    str = finiteToString(x);\r\n  } else {\r\n    checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    y = finalise(new Ctor(x), dp + x.e + 1, rm);\r\n    str = finiteToString(y, false, dp + y.e + 1);\r\n  }\r\n\r\n  // To determine whether to add the minus sign look at the value before it was rounded,\r\n  // i.e. look at `x` rather than `y`.\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return an array representing the value of this Decimal as a simple fraction with an integer\r\n * numerator and an integer denominator.\r\n *\r\n * The denominator will be a positive non-zero value less than or equal to the specified maximum\r\n * denominator. If a maximum denominator is not specified, the denominator will be the lowest\r\n * value necessary to represent the number exactly.\r\n *\r\n * [maxD] {number|string|bigint|Decimal} Maximum denominator. Integer >= 1 and < Infinity.\r\n *\r\n */\r\nP.toFraction = function (maxD) {\r\n  var d, d0, d1, d2, e, k, n, n0, n1, pr, q, r,\r\n    x = this,\r\n    xd = x.d,\r\n    Ctor = x.constructor;\r\n\r\n  if (!xd) return new Ctor(x);\r\n\r\n  n1 = d0 = new Ctor(1);\r\n  d1 = n0 = new Ctor(0);\r\n\r\n  d = new Ctor(d1);\r\n  e = d.e = getPrecision(xd) - x.e - 1;\r\n  k = e % LOG_BASE;\r\n  d.d[0] = mathpow(10, k < 0 ? LOG_BASE + k : k);\r\n\r\n  if (maxD == null) {\r\n\r\n    // d is 10**e, the minimum max-denominator needed.\r\n    maxD = e > 0 ? d : n1;\r\n  } else {\r\n    n = new Ctor(maxD);\r\n    if (!n.isInt() || n.lt(n1)) throw Error(invalidArgument + n);\r\n    maxD = n.gt(d) ? (e > 0 ? d : n1) : n;\r\n  }\r\n\r\n  external = false;\r\n  n = new Ctor(digitsToString(xd));\r\n  pr = Ctor.precision;\r\n  Ctor.precision = e = xd.length * LOG_BASE * 2;\r\n\r\n  for (;;)  {\r\n    q = divide(n, d, 0, 1, 1);\r\n    d2 = d0.plus(q.times(d1));\r\n    if (d2.cmp(maxD) == 1) break;\r\n    d0 = d1;\r\n    d1 = d2;\r\n    d2 = n1;\r\n    n1 = n0.plus(q.times(d2));\r\n    n0 = d2;\r\n    d2 = d;\r\n    d = n.minus(q.times(d2));\r\n    n = d2;\r\n  }\r\n\r\n  d2 = divide(maxD.minus(d0), d1, 0, 1, 1);\r\n  n0 = n0.plus(d2.times(n1));\r\n  d0 = d0.plus(d2.times(d1));\r\n  n0.s = n1.s = x.s;\r\n\r\n  // Determine which fraction is closer to x, n0/d0 or n1/d1?\r\n  r = divide(n1, d1, e, 1).minus(x).abs().cmp(divide(n0, d0, e, 1).minus(x).abs()) < 1\r\n      ? [n1, d1] : [n0, d0];\r\n\r\n  Ctor.precision = pr;\r\n  external = true;\r\n\r\n  return r;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in base 16, round to `sd` significant\r\n * digits using rounding mode `rm`.\r\n *\r\n * If the optional `sd` argument is present then return binary exponential notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toHexadecimal = P.toHex = function (sd, rm) {\r\n  return toStringBinary(this, 16, sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Returns a new Decimal whose value is the nearest multiple of `y` in the direction of rounding\r\n * mode `rm`, or `Decimal.rounding` if `rm` is omitted, to the value of this Decimal.\r\n *\r\n * The return value will always have the same sign as this Decimal, unless either this Decimal\r\n * or `y` is NaN, in which case the return value will be also be NaN.\r\n *\r\n * The return value is not affected by the value of `precision`.\r\n *\r\n * y {number|string|bigint|Decimal} The magnitude to round to a multiple of.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * 'toNearest() rounding mode not an integer: {rm}'\r\n * 'toNearest() rounding mode out of range: {rm}'\r\n *\r\n */\r\nP.toNearest = function (y, rm) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  x = new Ctor(x);\r\n\r\n  if (y == null) {\r\n\r\n    // If x is not finite, return x.\r\n    if (!x.d) return x;\r\n\r\n    y = new Ctor(1);\r\n    rm = Ctor.rounding;\r\n  } else {\r\n    y = new Ctor(y);\r\n    if (rm === void 0) {\r\n      rm = Ctor.rounding;\r\n    } else {\r\n      checkInt32(rm, 0, 8);\r\n    }\r\n\r\n    // If x is not finite, return x if y is not NaN, else NaN.\r\n    if (!x.d) return y.s ? x : y;\r\n\r\n    // If y is not finite, return Infinity with the sign of x if y is Infinity, else NaN.\r\n    if (!y.d) {\r\n      if (y.s) y.s = x.s;\r\n      return y;\r\n    }\r\n  }\r\n\r\n  // If y is not zero, calculate the nearest multiple of y to x.\r\n  if (y.d[0]) {\r\n    external = false;\r\n    x = divide(x, y, 0, rm, 1).times(y);\r\n    external = true;\r\n    finalise(x);\r\n\r\n  // If y is zero, return zero with the sign of x.\r\n  } else {\r\n    y.s = x.s;\r\n    x = y;\r\n  }\r\n\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return the value of this Decimal converted to a number primitive.\r\n * Zero keeps its sign.\r\n *\r\n */\r\nP.toNumber = function () {\r\n  return +this;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in base 8, round to `sd` significant\r\n * digits using rounding mode `rm`.\r\n *\r\n * If the optional `sd` argument is present then return binary exponential notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toOctal = function (sd, rm) {\r\n  return toStringBinary(this, 8, sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal raised to the power `y`, rounded\r\n * to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * ECMAScript compliant.\r\n *\r\n *   pow(x, NaN)                           = NaN\r\n *   pow(x, ±0)                            = 1\r\n\r\n *   pow(NaN, non-zero)                    = NaN\r\n *   pow(abs(x) > 1, +Infinity)            = +Infinity\r\n *   pow(abs(x) > 1, -Infinity)            = +0\r\n *   pow(abs(x) == 1, ±Infinity)           = NaN\r\n *   pow(abs(x) < 1, +Infinity)            = +0\r\n *   pow(abs(x) < 1, -Infinity)            = +Infinity\r\n *   pow(+Infinity, y > 0)                 = +Infinity\r\n *   pow(+Infinity, y < 0)                 = +0\r\n *   pow(-Infinity, odd integer > 0)       = -Infinity\r\n *   pow(-Infinity, even integer > 0)      = +Infinity\r\n *   pow(-Infinity, odd integer < 0)       = -0\r\n *   pow(-Infinity, even integer < 0)      = +0\r\n *   pow(+0, y > 0)                        = +0\r\n *   pow(+0, y < 0)                        = +Infinity\r\n *   pow(-0, odd integer > 0)              = -0\r\n *   pow(-0, even integer > 0)             = +0\r\n *   pow(-0, odd integer < 0)              = -Infinity\r\n *   pow(-0, even integer < 0)             = +Infinity\r\n *   pow(finite x < 0, finite non-integer) = NaN\r\n *\r\n * For non-integer or very large exponents pow(x, y) is calculated using\r\n *\r\n *   x^y = exp(y*ln(x))\r\n *\r\n * Assuming the first 15 rounding digits are each equally likely to be any digit 0-9, the\r\n * probability of an incorrectly rounded result\r\n * P([49]9{14} | [50]0{14}) = 2 * 0.2 * 10^-14 = 4e-15 = 1/2.5e+14\r\n * i.e. 1 in 250,000,000,000,000\r\n *\r\n * If a result is incorrectly rounded the maximum error will be 1 ulp (unit in last place).\r\n *\r\n * y {number|string|bigint|Decimal} The power to which to raise this Decimal.\r\n *\r\n */\r\nP.toPower = P.pow = function (y) {\r\n  var e, k, pr, r, rm, s,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    yn = +(y = new Ctor(y));\r\n\r\n  // Either ±Infinity, NaN or ±0?\r\n  if (!x.d || !y.d || !x.d[0] || !y.d[0]) return new Ctor(mathpow(+x, yn));\r\n\r\n  x = new Ctor(x);\r\n\r\n  if (x.eq(1)) return x;\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  if (y.eq(1)) return finalise(x, pr, rm);\r\n\r\n  // y exponent\r\n  e = mathfloor(y.e / LOG_BASE);\r\n\r\n  // If y is a small integer use the 'exponentiation by squaring' algorithm.\r\n  if (e >= y.d.length - 1 && (k = yn < 0 ? -yn : yn) <= MAX_SAFE_INTEGER) {\r\n    r = intPow(Ctor, x, k, pr);\r\n    return y.s < 0 ? new Ctor(1).div(r) : finalise(r, pr, rm);\r\n  }\r\n\r\n  s = x.s;\r\n\r\n  // if x is negative\r\n  if (s < 0) {\r\n\r\n    // if y is not an integer\r\n    if (e < y.d.length - 1) return new Ctor(NaN);\r\n\r\n    // Result is positive if x is negative and the last digit of integer y is even.\r\n    if ((y.d[e] & 1) == 0) s = 1;\r\n\r\n    // if x.eq(-1)\r\n    if (x.e == 0 && x.d[0] == 1 && x.d.length == 1) {\r\n      x.s = s;\r\n      return x;\r\n    }\r\n  }\r\n\r\n  // Estimate result exponent.\r\n  // x^y = 10^e,  where e = y * log10(x)\r\n  // log10(x) = log10(x_significand) + x_exponent\r\n  // log10(x_significand) = ln(x_significand) / ln(10)\r\n  k = mathpow(+x, yn);\r\n  e = k == 0 || !isFinite(k)\r\n    ? mathfloor(yn * (Math.log('0.' + digitsToString(x.d)) / Math.LN10 + x.e + 1))\r\n    : new Ctor(k + '').e;\r\n\r\n  // Exponent estimate may be incorrect e.g. x: 0.999999999999999999, y: 2.29, e: 0, r.e: -1.\r\n\r\n  // Overflow/underflow?\r\n  if (e > Ctor.maxE + 1 || e < Ctor.minE - 1) return new Ctor(e > 0 ? s / 0 : 0);\r\n\r\n  external = false;\r\n  Ctor.rounding = x.s = 1;\r\n\r\n  // Estimate the extra guard digits needed to ensure five correct rounding digits from\r\n  // naturalLogarithm(x). Example of failure without these extra digits (precision: 10):\r\n  // new Decimal(2.32456).pow('2087987436534566.46411')\r\n  // should be 1.162377823e+764914905173815, but is 1.162355823e+764914905173815\r\n  k = Math.min(12, (e + '').length);\r\n\r\n  // r = x^y = exp(y*ln(x))\r\n  r = naturalExponential(y.times(naturalLogarithm(x, pr + k)), pr);\r\n\r\n  // r may be Infinity, e.g. (0.9999999999999999).pow(-1e+40)\r\n  if (r.d) {\r\n\r\n    // Truncate to the required precision plus five rounding digits.\r\n    r = finalise(r, pr + 5, 1);\r\n\r\n    // If the rounding digits are [49]9999 or [50]0000 increase the precision by 10 and recalculate\r\n    // the result.\r\n    if (checkRoundingDigits(r.d, pr, rm)) {\r\n      e = pr + 10;\r\n\r\n      // Truncate to the increased precision plus five rounding digits.\r\n      r = finalise(naturalExponential(y.times(naturalLogarithm(x, e + k)), e), e + 5, 1);\r\n\r\n      // Check for 14 nines from the 2nd rounding digit (the first rounding digit may be 4 or 9).\r\n      if (+digitsToString(r.d).slice(pr + 1, pr + 15) + 1 == 1e14) {\r\n        r = finalise(r, pr + 1, 0);\r\n      }\r\n    }\r\n  }\r\n\r\n  r.s = s;\r\n  external = true;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(r, pr, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal rounded to `sd` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * Return exponential notation if `sd` is less than the number of digits necessary to represent\r\n * the integer part of the value in normal notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toPrecision = function (sd, rm) {\r\n  var str,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (sd === void 0) {\r\n    str = finiteToString(x, x.e <= Ctor.toExpNeg || x.e >= Ctor.toExpPos);\r\n  } else {\r\n    checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    x = finalise(new Ctor(x), sd, rm);\r\n    str = finiteToString(x, sd <= x.e || x.e <= Ctor.toExpNeg, sd);\r\n  }\r\n\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `sd`\r\n * significant digits using rounding mode `rm`, or to `precision` and `rounding` respectively if\r\n * omitted.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * 'toSD() digits out of range: {sd}'\r\n * 'toSD() digits not an integer: {sd}'\r\n * 'toSD() rounding mode not an integer: {rm}'\r\n * 'toSD() rounding mode out of range: {rm}'\r\n *\r\n */\r\nP.toSignificantDigits = P.toSD = function (sd, rm) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (sd === void 0) {\r\n    sd = Ctor.precision;\r\n    rm = Ctor.rounding;\r\n  } else {\r\n    checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n  }\r\n\r\n  return finalise(new Ctor(x), sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal.\r\n *\r\n * Return exponential notation if this Decimal has a positive exponent equal to or greater than\r\n * `toExpPos`, or a negative exponent equal to or less than `toExpNeg`.\r\n *\r\n */\r\nP.toString = function () {\r\n  var x = this,\r\n    Ctor = x.constructor,\r\n    str = finiteToString(x, x.e <= Ctor.toExpNeg || x.e >= Ctor.toExpPos);\r\n\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal truncated to a whole number.\r\n *\r\n */\r\nP.truncated = P.trunc = function () {\r\n  return finalise(new this.constructor(this), this.e + 1, 1);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal.\r\n * Unlike `toString`, negative zero will include the minus sign.\r\n *\r\n */\r\nP.valueOf = P.toJSON = function () {\r\n  var x = this,\r\n    Ctor = x.constructor,\r\n    str = finiteToString(x, x.e <= Ctor.toExpNeg || x.e >= Ctor.toExpPos);\r\n\r\n  return x.isNeg() ? '-' + str : str;\r\n};\r\n\r\n\r\n// Helper functions for Decimal.prototype (P) and/or Decimal methods, and their callers.\r\n\r\n\r\n/*\r\n *  digitsToString           P.cubeRoot, P.logarithm, P.squareRoot, P.toFraction, P.toPower,\r\n *                           finiteToString, naturalExponential, naturalLogarithm\r\n *  checkInt32               P.toDecimalPlaces, P.toExponential, P.toFixed, P.toNearest,\r\n *                           P.toPrecision, P.toSignificantDigits, toStringBinary, random\r\n *  checkRoundingDigits      P.logarithm, P.toPower, naturalExponential, naturalLogarithm\r\n *  convertBase              toStringBinary, parseOther\r\n *  cos                      P.cos\r\n *  divide                   P.atanh, P.cubeRoot, P.dividedBy, P.dividedToIntegerBy,\r\n *                           P.logarithm, P.modulo, P.squareRoot, P.tan, P.tanh, P.toFraction,\r\n *                           P.toNearest, toStringBinary, naturalExponential, naturalLogarithm,\r\n *                           taylorSeries, atan2, parseOther\r\n *  finalise                 P.absoluteValue, P.atan, P.atanh, P.ceil, P.cos, P.cosh,\r\n *                           P.cubeRoot, P.dividedToIntegerBy, P.floor, P.logarithm, P.minus,\r\n *                           P.modulo, P.negated, P.plus, P.round, P.sin, P.sinh, P.squareRoot,\r\n *                           P.tan, P.times, P.toDecimalPlaces, P.toExponential, P.toFixed,\r\n *                           P.toNearest, P.toPower, P.toPrecision, P.toSignificantDigits,\r\n *                           P.truncated, divide, getLn10, getPi, naturalExponential,\r\n *                           naturalLogarithm, ceil, floor, round, trunc\r\n *  finiteToString           P.toExponential, P.toFixed, P.toPrecision, P.toString, P.valueOf,\r\n *                           toStringBinary\r\n *  getBase10Exponent        P.minus, P.plus, P.times, parseOther\r\n *  getLn10                  P.logarithm, naturalLogarithm\r\n *  getPi                    P.acos, P.asin, P.atan, toLessThanHalfPi, atan2\r\n *  getPrecision             P.precision, P.toFraction\r\n *  getZeroString            digitsToString, finiteToString\r\n *  intPow                   P.toPower, parseOther\r\n *  isOdd                    toLessThanHalfPi\r\n *  maxOrMin                 max, min\r\n *  naturalExponential       P.naturalExponential, P.toPower\r\n *  naturalLogarithm         P.acosh, P.asinh, P.atanh, P.logarithm, P.naturalLogarithm,\r\n *                           P.toPower, naturalExponential\r\n *  nonFiniteToString        finiteToString, toStringBinary\r\n *  parseDecimal             Decimal\r\n *  parseOther               Decimal\r\n *  sin                      P.sin\r\n *  taylorSeries             P.cosh, P.sinh, cos, sin\r\n *  toLessThanHalfPi         P.cos, P.sin\r\n *  toStringBinary           P.toBinary, P.toHexadecimal, P.toOctal\r\n *  truncate                 intPow\r\n *\r\n *  Throws:                  P.logarithm, P.precision, P.toFraction, checkInt32, getLn10, getPi,\r\n *                           naturalLogarithm, config, parseOther, random, Decimal\r\n */\r\n\r\n\r\nfunction digitsToString(d) {\r\n  var i, k, ws,\r\n    indexOfLastWord = d.length - 1,\r\n    str = '',\r\n    w = d[0];\r\n\r\n  if (indexOfLastWord > 0) {\r\n    str += w;\r\n    for (i = 1; i < indexOfLastWord; i++) {\r\n      ws = d[i] + '';\r\n      k = LOG_BASE - ws.length;\r\n      if (k) str += getZeroString(k);\r\n      str += ws;\r\n    }\r\n\r\n    w = d[i];\r\n    ws = w + '';\r\n    k = LOG_BASE - ws.length;\r\n    if (k) str += getZeroString(k);\r\n  } else if (w === 0) {\r\n    return '0';\r\n  }\r\n\r\n  // Remove trailing zeros of last w.\r\n  for (; w % 10 === 0;) w /= 10;\r\n\r\n  return str + w;\r\n}\r\n\r\n\r\nfunction checkInt32(i, min, max) {\r\n  if (i !== ~~i || i < min || i > max) {\r\n    throw Error(invalidArgument + i);\r\n  }\r\n}\r\n\r\n\r\n/*\r\n * Check 5 rounding digits if `repeating` is null, 4 otherwise.\r\n * `repeating == null` if caller is `log` or `pow`,\r\n * `repeating != null` if caller is `naturalLogarithm` or `naturalExponential`.\r\n */\r\nfunction checkRoundingDigits(d, i, rm, repeating) {\r\n  var di, k, r, rd;\r\n\r\n  // Get the length of the first word of the array d.\r\n  for (k = d[0]; k >= 10; k /= 10) --i;\r\n\r\n  // Is the rounding digit in the first word of d?\r\n  if (--i < 0) {\r\n    i += LOG_BASE;\r\n    di = 0;\r\n  } else {\r\n    di = Math.ceil((i + 1) / LOG_BASE);\r\n    i %= LOG_BASE;\r\n  }\r\n\r\n  // i is the index (0 - 6) of the rounding digit.\r\n  // E.g. if within the word 3487563 the first rounding digit is 5,\r\n  // then i = 4, k = 1000, rd = 3487563 % 1000 = 563\r\n  k = mathpow(10, LOG_BASE - i);\r\n  rd = d[di] % k | 0;\r\n\r\n  if (repeating == null) {\r\n    if (i < 3) {\r\n      if (i == 0) rd = rd / 100 | 0;\r\n      else if (i == 1) rd = rd / 10 | 0;\r\n      r = rm < 4 && rd == 99999 || rm > 3 && rd == 49999 || rd == 50000 || rd == 0;\r\n    } else {\r\n      r = (rm < 4 && rd + 1 == k || rm > 3 && rd + 1 == k / 2) &&\r\n        (d[di + 1] / k / 100 | 0) == mathpow(10, i - 2) - 1 ||\r\n          (rd == k / 2 || rd == 0) && (d[di + 1] / k / 100 | 0) == 0;\r\n    }\r\n  } else {\r\n    if (i < 4) {\r\n      if (i == 0) rd = rd / 1000 | 0;\r\n      else if (i == 1) rd = rd / 100 | 0;\r\n      else if (i == 2) rd = rd / 10 | 0;\r\n      r = (repeating || rm < 4) && rd == 9999 || !repeating && rm > 3 && rd == 4999;\r\n    } else {\r\n      r = ((repeating || rm < 4) && rd + 1 == k ||\r\n      (!repeating && rm > 3) && rd + 1 == k / 2) &&\r\n        (d[di + 1] / k / 1000 | 0) == mathpow(10, i - 3) - 1;\r\n    }\r\n  }\r\n\r\n  return r;\r\n}\r\n\r\n\r\n// Convert string of `baseIn` to an array of numbers of `baseOut`.\r\n// Eg. convertBase('255', 10, 16) returns [15, 15].\r\n// Eg. convertBase('ff', 16, 10) returns [2, 5, 5].\r\nfunction convertBase(str, baseIn, baseOut) {\r\n  var j,\r\n    arr = [0],\r\n    arrL,\r\n    i = 0,\r\n    strL = str.length;\r\n\r\n  for (; i < strL;) {\r\n    for (arrL = arr.length; arrL--;) arr[arrL] *= baseIn;\r\n    arr[0] += NUMERALS.indexOf(str.charAt(i++));\r\n    for (j = 0; j < arr.length; j++) {\r\n      if (arr[j] > baseOut - 1) {\r\n        if (arr[j + 1] === void 0) arr[j + 1] = 0;\r\n        arr[j + 1] += arr[j] / baseOut | 0;\r\n        arr[j] %= baseOut;\r\n      }\r\n    }\r\n  }\r\n\r\n  return arr.reverse();\r\n}\r\n\r\n\r\n/*\r\n * cos(x) = 1 - x^2/2! + x^4/4! - ...\r\n * |x| < pi/2\r\n *\r\n */\r\nfunction cosine(Ctor, x) {\r\n  var k, len, y;\r\n\r\n  if (x.isZero()) return x;\r\n\r\n  // Argument reduction: cos(4x) = 8*(cos^4(x) - cos^2(x)) + 1\r\n  // i.e. cos(x) = 8*(cos^4(x/4) - cos^2(x/4)) + 1\r\n\r\n  // Estimate the optimum number of times to use the argument reduction.\r\n  len = x.d.length;\r\n  if (len < 32) {\r\n    k = Math.ceil(len / 3);\r\n    y = (1 / tinyPow(4, k)).toString();\r\n  } else {\r\n    k = 16;\r\n    y = '2.3283064365386962890625e-10';\r\n  }\r\n\r\n  Ctor.precision += k;\r\n\r\n  x = taylorSeries(Ctor, 1, x.times(y), new Ctor(1));\r\n\r\n  // Reverse argument reduction\r\n  for (var i = k; i--;) {\r\n    var cos2x = x.times(x);\r\n    x = cos2x.times(cos2x).minus(cos2x).times(8).plus(1);\r\n  }\r\n\r\n  Ctor.precision -= k;\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Perform division in the specified base.\r\n */\r\nvar divide = (function () {\r\n\r\n  // Assumes non-zero x and k, and hence non-zero result.\r\n  function multiplyInteger(x, k, base) {\r\n    var temp,\r\n      carry = 0,\r\n      i = x.length;\r\n\r\n    for (x = x.slice(); i--;) {\r\n      temp = x[i] * k + carry;\r\n      x[i] = temp % base | 0;\r\n      carry = temp / base | 0;\r\n    }\r\n\r\n    if (carry) x.unshift(carry);\r\n\r\n    return x;\r\n  }\r\n\r\n  function compare(a, b, aL, bL) {\r\n    var i, r;\r\n\r\n    if (aL != bL) {\r\n      r = aL > bL ? 1 : -1;\r\n    } else {\r\n      for (i = r = 0; i < aL; i++) {\r\n        if (a[i] != b[i]) {\r\n          r = a[i] > b[i] ? 1 : -1;\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    return r;\r\n  }\r\n\r\n  function subtract(a, b, aL, base) {\r\n    var i = 0;\r\n\r\n    // Subtract b from a.\r\n    for (; aL--;) {\r\n      a[aL] -= i;\r\n      i = a[aL] < b[aL] ? 1 : 0;\r\n      a[aL] = i * base + a[aL] - b[aL];\r\n    }\r\n\r\n    // Remove leading zeros.\r\n    for (; !a[0] && a.length > 1;) a.shift();\r\n  }\r\n\r\n  return function (x, y, pr, rm, dp, base) {\r\n    var cmp, e, i, k, logBase, more, prod, prodL, q, qd, rem, remL, rem0, sd, t, xi, xL, yd0,\r\n      yL, yz,\r\n      Ctor = x.constructor,\r\n      sign = x.s == y.s ? 1 : -1,\r\n      xd = x.d,\r\n      yd = y.d;\r\n\r\n    // Either NaN, Infinity or 0?\r\n    if (!xd || !xd[0] || !yd || !yd[0]) {\r\n\r\n      return new Ctor(// Return NaN if either NaN, or both Infinity or 0.\r\n        !x.s || !y.s || (xd ? yd && xd[0] == yd[0] : !yd) ? NaN :\r\n\r\n        // Return ±0 if x is 0 or y is ±Infinity, or return ±Infinity as y is 0.\r\n        xd && xd[0] == 0 || !yd ? sign * 0 : sign / 0);\r\n    }\r\n\r\n    if (base) {\r\n      logBase = 1;\r\n      e = x.e - y.e;\r\n    } else {\r\n      base = BASE;\r\n      logBase = LOG_BASE;\r\n      e = mathfloor(x.e / logBase) - mathfloor(y.e / logBase);\r\n    }\r\n\r\n    yL = yd.length;\r\n    xL = xd.length;\r\n    q = new Ctor(sign);\r\n    qd = q.d = [];\r\n\r\n    // Result exponent may be one less than e.\r\n    // The digit array of a Decimal from toStringBinary may have trailing zeros.\r\n    for (i = 0; yd[i] == (xd[i] || 0); i++);\r\n\r\n    if (yd[i] > (xd[i] || 0)) e--;\r\n\r\n    if (pr == null) {\r\n      sd = pr = Ctor.precision;\r\n      rm = Ctor.rounding;\r\n    } else if (dp) {\r\n      sd = pr + (x.e - y.e) + 1;\r\n    } else {\r\n      sd = pr;\r\n    }\r\n\r\n    if (sd < 0) {\r\n      qd.push(1);\r\n      more = true;\r\n    } else {\r\n\r\n      // Convert precision in number of base 10 digits to base 1e7 digits.\r\n      sd = sd / logBase + 2 | 0;\r\n      i = 0;\r\n\r\n      // divisor < 1e7\r\n      if (yL == 1) {\r\n        k = 0;\r\n        yd = yd[0];\r\n        sd++;\r\n\r\n        // k is the carry.\r\n        for (; (i < xL || k) && sd--; i++) {\r\n          t = k * base + (xd[i] || 0);\r\n          qd[i] = t / yd | 0;\r\n          k = t % yd | 0;\r\n        }\r\n\r\n        more = k || i < xL;\r\n\r\n      // divisor >= 1e7\r\n      } else {\r\n\r\n        // Normalise xd and yd so highest order digit of yd is >= base/2\r\n        k = base / (yd[0] + 1) | 0;\r\n\r\n        if (k > 1) {\r\n          yd = multiplyInteger(yd, k, base);\r\n          xd = multiplyInteger(xd, k, base);\r\n          yL = yd.length;\r\n          xL = xd.length;\r\n        }\r\n\r\n        xi = yL;\r\n        rem = xd.slice(0, yL);\r\n        remL = rem.length;\r\n\r\n        // Add zeros to make remainder as long as divisor.\r\n        for (; remL < yL;) rem[remL++] = 0;\r\n\r\n        yz = yd.slice();\r\n        yz.unshift(0);\r\n        yd0 = yd[0];\r\n\r\n        if (yd[1] >= base / 2) ++yd0;\r\n\r\n        do {\r\n          k = 0;\r\n\r\n          // Compare divisor and remainder.\r\n          cmp = compare(yd, rem, yL, remL);\r\n\r\n          // If divisor < remainder.\r\n          if (cmp < 0) {\r\n\r\n            // Calculate trial digit, k.\r\n            rem0 = rem[0];\r\n            if (yL != remL) rem0 = rem0 * base + (rem[1] || 0);\r\n\r\n            // k will be how many times the divisor goes into the current remainder.\r\n            k = rem0 / yd0 | 0;\r\n\r\n            //  Algorithm:\r\n            //  1. product = divisor * trial digit (k)\r\n            //  2. if product > remainder: product -= divisor, k--\r\n            //  3. remainder -= product\r\n            //  4. if product was < remainder at 2:\r\n            //    5. compare new remainder and divisor\r\n            //    6. If remainder > divisor: remainder -= divisor, k++\r\n\r\n            if (k > 1) {\r\n              if (k >= base) k = base - 1;\r\n\r\n              // product = divisor * trial digit.\r\n              prod = multiplyInteger(yd, k, base);\r\n              prodL = prod.length;\r\n              remL = rem.length;\r\n\r\n              // Compare product and remainder.\r\n              cmp = compare(prod, rem, prodL, remL);\r\n\r\n              // product > remainder.\r\n              if (cmp == 1) {\r\n                k--;\r\n\r\n                // Subtract divisor from product.\r\n                subtract(prod, yL < prodL ? yz : yd, prodL, base);\r\n              }\r\n            } else {\r\n\r\n              // cmp is -1.\r\n              // If k is 0, there is no need to compare yd and rem again below, so change cmp to 1\r\n              // to avoid it. If k is 1 there is a need to compare yd and rem again below.\r\n              if (k == 0) cmp = k = 1;\r\n              prod = yd.slice();\r\n            }\r\n\r\n            prodL = prod.length;\r\n            if (prodL < remL) prod.unshift(0);\r\n\r\n            // Subtract product from remainder.\r\n            subtract(rem, prod, remL, base);\r\n\r\n            // If product was < previous remainder.\r\n            if (cmp == -1) {\r\n              remL = rem.length;\r\n\r\n              // Compare divisor and new remainder.\r\n              cmp = compare(yd, rem, yL, remL);\r\n\r\n              // If divisor < new remainder, subtract divisor from remainder.\r\n              if (cmp < 1) {\r\n                k++;\r\n\r\n                // Subtract divisor from remainder.\r\n                subtract(rem, yL < remL ? yz : yd, remL, base);\r\n              }\r\n            }\r\n\r\n            remL = rem.length;\r\n          } else if (cmp === 0) {\r\n            k++;\r\n            rem = [0];\r\n          }    // if cmp === 1, k will be 0\r\n\r\n          // Add the next digit, k, to the result array.\r\n          qd[i++] = k;\r\n\r\n          // Update the remainder.\r\n          if (cmp && rem[0]) {\r\n            rem[remL++] = xd[xi] || 0;\r\n          } else {\r\n            rem = [xd[xi]];\r\n            remL = 1;\r\n          }\r\n\r\n        } while ((xi++ < xL || rem[0] !== void 0) && sd--);\r\n\r\n        more = rem[0] !== void 0;\r\n      }\r\n\r\n      // Leading zero?\r\n      if (!qd[0]) qd.shift();\r\n    }\r\n\r\n    // logBase is 1 when divide is being used for base conversion.\r\n    if (logBase == 1) {\r\n      q.e = e;\r\n      inexact = more;\r\n    } else {\r\n\r\n      // To calculate q.e, first get the number of digits of qd[0].\r\n      for (i = 1, k = qd[0]; k >= 10; k /= 10) i++;\r\n      q.e = i + e * logBase - 1;\r\n\r\n      finalise(q, dp ? pr + q.e + 1 : pr, rm, more);\r\n    }\r\n\r\n    return q;\r\n  };\r\n})();\r\n\r\n\r\n/*\r\n * Round `x` to `sd` significant digits using rounding mode `rm`.\r\n * Check for over/under-flow.\r\n */\r\n function finalise(x, sd, rm, isTruncated) {\r\n  var digits, i, j, k, rd, roundUp, w, xd, xdi,\r\n    Ctor = x.constructor;\r\n\r\n  // Don't round if sd is null or undefined.\r\n  out: if (sd != null) {\r\n    xd = x.d;\r\n\r\n    // Infinity/NaN.\r\n    if (!xd) return x;\r\n\r\n    // rd: the rounding digit, i.e. the digit after the digit that may be rounded up.\r\n    // w: the word of xd containing rd, a base 1e7 number.\r\n    // xdi: the index of w within xd.\r\n    // digits: the number of digits of w.\r\n    // i: what would be the index of rd within w if all the numbers were 7 digits long (i.e. if\r\n    // they had leading zeros)\r\n    // j: if > 0, the actual index of rd within w (if < 0, rd is a leading zero).\r\n\r\n    // Get the length of the first word of the digits array xd.\r\n    for (digits = 1, k = xd[0]; k >= 10; k /= 10) digits++;\r\n    i = sd - digits;\r\n\r\n    // Is the rounding digit in the first word of xd?\r\n    if (i < 0) {\r\n      i += LOG_BASE;\r\n      j = sd;\r\n      w = xd[xdi = 0];\r\n\r\n      // Get the rounding digit at index j of w.\r\n      rd = w / mathpow(10, digits - j - 1) % 10 | 0;\r\n    } else {\r\n      xdi = Math.ceil((i + 1) / LOG_BASE);\r\n      k = xd.length;\r\n      if (xdi >= k) {\r\n        if (isTruncated) {\r\n\r\n          // Needed by `naturalExponential`, `naturalLogarithm` and `squareRoot`.\r\n          for (; k++ <= xdi;) xd.push(0);\r\n          w = rd = 0;\r\n          digits = 1;\r\n          i %= LOG_BASE;\r\n          j = i - LOG_BASE + 1;\r\n        } else {\r\n          break out;\r\n        }\r\n      } else {\r\n        w = k = xd[xdi];\r\n\r\n        // Get the number of digits of w.\r\n        for (digits = 1; k >= 10; k /= 10) digits++;\r\n\r\n        // Get the index of rd within w.\r\n        i %= LOG_BASE;\r\n\r\n        // Get the index of rd within w, adjusted for leading zeros.\r\n        // The number of leading zeros of w is given by LOG_BASE - digits.\r\n        j = i - LOG_BASE + digits;\r\n\r\n        // Get the rounding digit at index j of w.\r\n        rd = j < 0 ? 0 : w / mathpow(10, digits - j - 1) % 10 | 0;\r\n      }\r\n    }\r\n\r\n    // Are there any non-zero digits after the rounding digit?\r\n    isTruncated = isTruncated || sd < 0 ||\r\n      xd[xdi + 1] !== void 0 || (j < 0 ? w : w % mathpow(10, digits - j - 1));\r\n\r\n    // The expression `w % mathpow(10, digits - j - 1)` returns all the digits of w to the right\r\n    // of the digit at (left-to-right) index j, e.g. if w is 908714 and j is 2, the expression\r\n    // will give 714.\r\n\r\n    roundUp = rm < 4\r\n      ? (rd || isTruncated) && (rm == 0 || rm == (x.s < 0 ? 3 : 2))\r\n      : rd > 5 || rd == 5 && (rm == 4 || isTruncated || rm == 6 &&\r\n\r\n        // Check whether the digit to the left of the rounding digit is odd.\r\n        ((i > 0 ? j > 0 ? w / mathpow(10, digits - j) : 0 : xd[xdi - 1]) % 10) & 1 ||\r\n          rm == (x.s < 0 ? 8 : 7));\r\n\r\n    if (sd < 1 || !xd[0]) {\r\n      xd.length = 0;\r\n      if (roundUp) {\r\n\r\n        // Convert sd to decimal places.\r\n        sd -= x.e + 1;\r\n\r\n        // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n        xd[0] = mathpow(10, (LOG_BASE - sd % LOG_BASE) % LOG_BASE);\r\n        x.e = -sd || 0;\r\n      } else {\r\n\r\n        // Zero.\r\n        xd[0] = x.e = 0;\r\n      }\r\n\r\n      return x;\r\n    }\r\n\r\n    // Remove excess digits.\r\n    if (i == 0) {\r\n      xd.length = xdi;\r\n      k = 1;\r\n      xdi--;\r\n    } else {\r\n      xd.length = xdi + 1;\r\n      k = mathpow(10, LOG_BASE - i);\r\n\r\n      // E.g. 56700 becomes 56000 if 7 is the rounding digit.\r\n      // j > 0 means i > number of leading zeros of w.\r\n      xd[xdi] = j > 0 ? (w / mathpow(10, digits - j) % mathpow(10, j) | 0) * k : 0;\r\n    }\r\n\r\n    if (roundUp) {\r\n      for (;;) {\r\n\r\n        // Is the digit to be rounded up in the first word of xd?\r\n        if (xdi == 0) {\r\n\r\n          // i will be the length of xd[0] before k is added.\r\n          for (i = 1, j = xd[0]; j >= 10; j /= 10) i++;\r\n          j = xd[0] += k;\r\n          for (k = 1; j >= 10; j /= 10) k++;\r\n\r\n          // if i != k the length has increased.\r\n          if (i != k) {\r\n            x.e++;\r\n            if (xd[0] == BASE) xd[0] = 1;\r\n          }\r\n\r\n          break;\r\n        } else {\r\n          xd[xdi] += k;\r\n          if (xd[xdi] != BASE) break;\r\n          xd[xdi--] = 0;\r\n          k = 1;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    for (i = xd.length; xd[--i] === 0;) xd.pop();\r\n  }\r\n\r\n  if (external) {\r\n\r\n    // Overflow?\r\n    if (x.e > Ctor.maxE) {\r\n\r\n      // Infinity.\r\n      x.d = null;\r\n      x.e = NaN;\r\n\r\n    // Underflow?\r\n    } else if (x.e < Ctor.minE) {\r\n\r\n      // Zero.\r\n      x.e = 0;\r\n      x.d = [0];\r\n      // Ctor.underflow = true;\r\n    } // else Ctor.underflow = false;\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\nfunction finiteToString(x, isExp, sd) {\r\n  if (!x.isFinite()) return nonFiniteToString(x);\r\n  var k,\r\n    e = x.e,\r\n    str = digitsToString(x.d),\r\n    len = str.length;\r\n\r\n  if (isExp) {\r\n    if (sd && (k = sd - len) > 0) {\r\n      str = str.charAt(0) + '.' + str.slice(1) + getZeroString(k);\r\n    } else if (len > 1) {\r\n      str = str.charAt(0) + '.' + str.slice(1);\r\n    }\r\n\r\n    str = str + (x.e < 0 ? 'e' : 'e+') + x.e;\r\n  } else if (e < 0) {\r\n    str = '0.' + getZeroString(-e - 1) + str;\r\n    if (sd && (k = sd - len) > 0) str += getZeroString(k);\r\n  } else if (e >= len) {\r\n    str += getZeroString(e + 1 - len);\r\n    if (sd && (k = sd - e - 1) > 0) str = str + '.' + getZeroString(k);\r\n  } else {\r\n    if ((k = e + 1) < len) str = str.slice(0, k) + '.' + str.slice(k);\r\n    if (sd && (k = sd - len) > 0) {\r\n      if (e + 1 === len) str += '.';\r\n      str += getZeroString(k);\r\n    }\r\n  }\r\n\r\n  return str;\r\n}\r\n\r\n\r\n// Calculate the base 10 exponent from the base 1e7 exponent.\r\nfunction getBase10Exponent(digits, e) {\r\n  var w = digits[0];\r\n\r\n  // Add the number of digits of the first word of the digits array.\r\n  for ( e *= LOG_BASE; w >= 10; w /= 10) e++;\r\n  return e;\r\n}\r\n\r\n\r\nfunction getLn10(Ctor, sd, pr) {\r\n  if (sd > LN10_PRECISION) {\r\n\r\n    // Reset global state in case the exception is caught.\r\n    external = true;\r\n    if (pr) Ctor.precision = pr;\r\n    throw Error(precisionLimitExceeded);\r\n  }\r\n  return finalise(new Ctor(LN10), sd, 1, true);\r\n}\r\n\r\n\r\nfunction getPi(Ctor, sd, rm) {\r\n  if (sd > PI_PRECISION) throw Error(precisionLimitExceeded);\r\n  return finalise(new Ctor(PI), sd, rm, true);\r\n}\r\n\r\n\r\nfunction getPrecision(digits) {\r\n  var w = digits.length - 1,\r\n    len = w * LOG_BASE + 1;\r\n\r\n  w = digits[w];\r\n\r\n  // If non-zero...\r\n  if (w) {\r\n\r\n    // Subtract the number of trailing zeros of the last word.\r\n    for (; w % 10 == 0; w /= 10) len--;\r\n\r\n    // Add the number of digits of the first word.\r\n    for (w = digits[0]; w >= 10; w /= 10) len++;\r\n  }\r\n\r\n  return len;\r\n}\r\n\r\n\r\nfunction getZeroString(k) {\r\n  var zs = '';\r\n  for (; k--;) zs += '0';\r\n  return zs;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of Decimal `x` to the power `n`, where `n` is an\r\n * integer of type number.\r\n *\r\n * Implements 'exponentiation by squaring'. Called by `pow` and `parseOther`.\r\n *\r\n */\r\nfunction intPow(Ctor, x, n, pr) {\r\n  var isTruncated,\r\n    r = new Ctor(1),\r\n\r\n    // Max n of 9007199254740991 takes 53 loop iterations.\r\n    // Maximum digits array length; leaves [28, 34] guard digits.\r\n    k = Math.ceil(pr / LOG_BASE + 4);\r\n\r\n  external = false;\r\n\r\n  for (;;) {\r\n    if (n % 2) {\r\n      r = r.times(x);\r\n      if (truncate(r.d, k)) isTruncated = true;\r\n    }\r\n\r\n    n = mathfloor(n / 2);\r\n    if (n === 0) {\r\n\r\n      // To ensure correct rounding when r.d is truncated, increment the last word if it is zero.\r\n      n = r.d.length - 1;\r\n      if (isTruncated && r.d[n] === 0) ++r.d[n];\r\n      break;\r\n    }\r\n\r\n    x = x.times(x);\r\n    truncate(x.d, k);\r\n  }\r\n\r\n  external = true;\r\n\r\n  return r;\r\n}\r\n\r\n\r\nfunction isOdd(n) {\r\n  return n.d[n.d.length - 1] & 1;\r\n}\r\n\r\n\r\n/*\r\n * Handle `max` (`n` is -1) and `min` (`n` is 1).\r\n */\r\nfunction maxOrMin(Ctor, args, n) {\r\n  var k, y,\r\n    x = new Ctor(args[0]),\r\n    i = 0;\r\n\r\n  for (; ++i < args.length;) {\r\n    y = new Ctor(args[i]);\r\n\r\n    // NaN?\r\n    if (!y.s) {\r\n      x = y;\r\n      break;\r\n    }\r\n\r\n    k = x.cmp(y);\r\n\r\n    if (k === n || k === 0 && x.s === n) {\r\n      x = y;\r\n    }\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural exponential of `x` rounded to `sd` significant\r\n * digits.\r\n *\r\n * Taylor/Maclaurin series.\r\n *\r\n * exp(x) = x^0/0! + x^1/1! + x^2/2! + x^3/3! + ...\r\n *\r\n * Argument reduction:\r\n *   Repeat x = x / 32, k += 5, until |x| < 0.1\r\n *   exp(x) = exp(x / 2^k)^(2^k)\r\n *\r\n * Previously, the argument was initially reduced by\r\n * exp(x) = exp(r) * 10^k  where r = x - k * ln10, k = floor(x / ln10)\r\n * to first put r in the range [0, ln10], before dividing by 32 until |x| < 0.1, but this was\r\n * found to be slower than just dividing repeatedly by 32 as above.\r\n *\r\n * Max integer argument: exp('20723265836946413') = 6.3e+9000000000000000\r\n * Min integer argument: exp('-20723265836946411') = 1.2e-9000000000000000\r\n * (Math object integer min/max: Math.exp(709) = 8.2e+307, Math.exp(-745) = 5e-324)\r\n *\r\n *  exp(Infinity)  = Infinity\r\n *  exp(-Infinity) = 0\r\n *  exp(NaN)       = NaN\r\n *  exp(±0)        = 1\r\n *\r\n *  exp(x) is non-terminating for any finite, non-zero x.\r\n *\r\n *  The result will always be correctly rounded.\r\n *\r\n */\r\nfunction naturalExponential(x, sd) {\r\n  var denominator, guard, j, pow, sum, t, wpr,\r\n    rep = 0,\r\n    i = 0,\r\n    k = 0,\r\n    Ctor = x.constructor,\r\n    rm = Ctor.rounding,\r\n    pr = Ctor.precision;\r\n\r\n  // 0/NaN/Infinity?\r\n  if (!x.d || !x.d[0] || x.e > 17) {\r\n\r\n    return new Ctor(x.d\r\n      ? !x.d[0] ? 1 : x.s < 0 ? 0 : 1 / 0\r\n      : x.s ? x.s < 0 ? 0 : x : 0 / 0);\r\n  }\r\n\r\n  if (sd == null) {\r\n    external = false;\r\n    wpr = pr;\r\n  } else {\r\n    wpr = sd;\r\n  }\r\n\r\n  t = new Ctor(0.03125);\r\n\r\n  // while abs(x) >= 0.1\r\n  while (x.e > -2) {\r\n\r\n    // x = x / 2^5\r\n    x = x.times(t);\r\n    k += 5;\r\n  }\r\n\r\n  // Use 2 * log10(2^k) + 5 (empirically derived) to estimate the increase in precision\r\n  // necessary to ensure the first 4 rounding digits are correct.\r\n  guard = Math.log(mathpow(2, k)) / Math.LN10 * 2 + 5 | 0;\r\n  wpr += guard;\r\n  denominator = pow = sum = new Ctor(1);\r\n  Ctor.precision = wpr;\r\n\r\n  for (;;) {\r\n    pow = finalise(pow.times(x), wpr, 1);\r\n    denominator = denominator.times(++i);\r\n    t = sum.plus(divide(pow, denominator, wpr, 1));\r\n\r\n    if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\r\n      j = k;\r\n      while (j--) sum = finalise(sum.times(sum), wpr, 1);\r\n\r\n      // Check to see if the first 4 rounding digits are [49]999.\r\n      // If so, repeat the summation with a higher precision, otherwise\r\n      // e.g. with precision: 18, rounding: 1\r\n      // exp(18.404272462595034083567793919843761) = 98372560.1229999999 (should be 98372560.123)\r\n      // `wpr - guard` is the index of first rounding digit.\r\n      if (sd == null) {\r\n\r\n        if (rep < 3 && checkRoundingDigits(sum.d, wpr - guard, rm, rep)) {\r\n          Ctor.precision = wpr += 10;\r\n          denominator = pow = t = new Ctor(1);\r\n          i = 0;\r\n          rep++;\r\n        } else {\r\n          return finalise(sum, Ctor.precision = pr, rm, external = true);\r\n        }\r\n      } else {\r\n        Ctor.precision = pr;\r\n        return sum;\r\n      }\r\n    }\r\n\r\n    sum = t;\r\n  }\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural logarithm of `x` rounded to `sd` significant\r\n * digits.\r\n *\r\n *  ln(-n)        = NaN\r\n *  ln(0)         = -Infinity\r\n *  ln(-0)        = -Infinity\r\n *  ln(1)         = 0\r\n *  ln(Infinity)  = Infinity\r\n *  ln(-Infinity) = NaN\r\n *  ln(NaN)       = NaN\r\n *\r\n *  ln(n) (n != 1) is non-terminating.\r\n *\r\n */\r\nfunction naturalLogarithm(y, sd) {\r\n  var c, c0, denominator, e, numerator, rep, sum, t, wpr, x1, x2,\r\n    n = 1,\r\n    guard = 10,\r\n    x = y,\r\n    xd = x.d,\r\n    Ctor = x.constructor,\r\n    rm = Ctor.rounding,\r\n    pr = Ctor.precision;\r\n\r\n  // Is x negative or Infinity, NaN, 0 or 1?\r\n  if (x.s < 0 || !xd || !xd[0] || !x.e && xd[0] == 1 && xd.length == 1) {\r\n    return new Ctor(xd && !xd[0] ? -1 / 0 : x.s != 1 ? NaN : xd ? 0 : x);\r\n  }\r\n\r\n  if (sd == null) {\r\n    external = false;\r\n    wpr = pr;\r\n  } else {\r\n    wpr = sd;\r\n  }\r\n\r\n  Ctor.precision = wpr += guard;\r\n  c = digitsToString(xd);\r\n  c0 = c.charAt(0);\r\n\r\n  if (Math.abs(e = x.e) < 1.5e15) {\r\n\r\n    // Argument reduction.\r\n    // The series converges faster the closer the argument is to 1, so using\r\n    // ln(a^b) = b * ln(a),   ln(a) = ln(a^b) / b\r\n    // multiply the argument by itself until the leading digits of the significand are 7, 8, 9,\r\n    // 10, 11, 12 or 13, recording the number of multiplications so the sum of the series can\r\n    // later be divided by this number, then separate out the power of 10 using\r\n    // ln(a*10^b) = ln(a) + b*ln(10).\r\n\r\n    // max n is 21 (gives 0.9, 1.0 or 1.1) (9e15 / 21 = 4.2e14).\r\n    //while (c0 < 9 && c0 != 1 || c0 == 1 && c.charAt(1) > 1) {\r\n    // max n is 6 (gives 0.7 - 1.3)\r\n    while (c0 < 7 && c0 != 1 || c0 == 1 && c.charAt(1) > 3) {\r\n      x = x.times(y);\r\n      c = digitsToString(x.d);\r\n      c0 = c.charAt(0);\r\n      n++;\r\n    }\r\n\r\n    e = x.e;\r\n\r\n    if (c0 > 1) {\r\n      x = new Ctor('0.' + c);\r\n      e++;\r\n    } else {\r\n      x = new Ctor(c0 + '.' + c.slice(1));\r\n    }\r\n  } else {\r\n\r\n    // The argument reduction method above may result in overflow if the argument y is a massive\r\n    // number with exponent >= 1500000000000000 (9e15 / 6 = 1.5e15), so instead recall this\r\n    // function using ln(x*10^e) = ln(x) + e*ln(10).\r\n    t = getLn10(Ctor, wpr + 2, pr).times(e + '');\r\n    x = naturalLogarithm(new Ctor(c0 + '.' + c.slice(1)), wpr - guard).plus(t);\r\n    Ctor.precision = pr;\r\n\r\n    return sd == null ? finalise(x, pr, rm, external = true) : x;\r\n  }\r\n\r\n  // x1 is x reduced to a value near 1.\r\n  x1 = x;\r\n\r\n  // Taylor series.\r\n  // ln(y) = ln((1 + x)/(1 - x)) = 2(x + x^3/3 + x^5/5 + x^7/7 + ...)\r\n  // where x = (y - 1)/(y + 1)    (|x| < 1)\r\n  sum = numerator = x = divide(x.minus(1), x.plus(1), wpr, 1);\r\n  x2 = finalise(x.times(x), wpr, 1);\r\n  denominator = 3;\r\n\r\n  for (;;) {\r\n    numerator = finalise(numerator.times(x2), wpr, 1);\r\n    t = sum.plus(divide(numerator, new Ctor(denominator), wpr, 1));\r\n\r\n    if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\r\n      sum = sum.times(2);\r\n\r\n      // Reverse the argument reduction. Check that e is not 0 because, besides preventing an\r\n      // unnecessary calculation, -0 + 0 = +0 and to ensure correct rounding -0 needs to stay -0.\r\n      if (e !== 0) sum = sum.plus(getLn10(Ctor, wpr + 2, pr).times(e + ''));\r\n      sum = divide(sum, new Ctor(n), wpr, 1);\r\n\r\n      // Is rm > 3 and the first 4 rounding digits 4999, or rm < 4 (or the summation has\r\n      // been repeated previously) and the first 4 rounding digits 9999?\r\n      // If so, restart the summation with a higher precision, otherwise\r\n      // e.g. with precision: 12, rounding: 1\r\n      // ln(135520028.6126091714265381533) = 18.7246299999 when it should be 18.72463.\r\n      // `wpr - guard` is the index of first rounding digit.\r\n      if (sd == null) {\r\n        if (checkRoundingDigits(sum.d, wpr - guard, rm, rep)) {\r\n          Ctor.precision = wpr += guard;\r\n          t = numerator = x = divide(x1.minus(1), x1.plus(1), wpr, 1);\r\n          x2 = finalise(x.times(x), wpr, 1);\r\n          denominator = rep = 1;\r\n        } else {\r\n          return finalise(sum, Ctor.precision = pr, rm, external = true);\r\n        }\r\n      } else {\r\n        Ctor.precision = pr;\r\n        return sum;\r\n      }\r\n    }\r\n\r\n    sum = t;\r\n    denominator += 2;\r\n  }\r\n}\r\n\r\n\r\n// ±Infinity, NaN.\r\nfunction nonFiniteToString(x) {\r\n  // Unsigned.\r\n  return String(x.s * x.s / 0);\r\n}\r\n\r\n\r\n/*\r\n * Parse the value of a new Decimal `x` from string `str`.\r\n */\r\nfunction parseDecimal(x, str) {\r\n  var e, i, len;\r\n\r\n  // TODO BigInt str: no need to check for decimal point, exponential form or leading zeros.\r\n  // Decimal point?\r\n  if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\r\n\r\n  // Exponential form?\r\n  if ((i = str.search(/e/i)) > 0) {\r\n\r\n    // Determine exponent.\r\n    if (e < 0) e = i;\r\n    e += +str.slice(i + 1);\r\n    str = str.substring(0, i);\r\n  } else if (e < 0) {\r\n\r\n    // Integer.\r\n    e = str.length;\r\n  }\r\n\r\n  // Determine leading zeros.\r\n  for (i = 0; str.charCodeAt(i) === 48; i++);\r\n\r\n  // Determine trailing zeros.\r\n  for (len = str.length; str.charCodeAt(len - 1) === 48; --len);\r\n  str = str.slice(i, len);\r\n\r\n  if (str) {\r\n    len -= i;\r\n    x.e = e = e - i - 1;\r\n    x.d = [];\r\n\r\n    // Transform base\r\n\r\n    // e is the base 10 exponent.\r\n    // i is where to slice str to get the first word of the digits array.\r\n    i = (e + 1) % LOG_BASE;\r\n    if (e < 0) i += LOG_BASE;\r\n\r\n    if (i < len) {\r\n      if (i) x.d.push(+str.slice(0, i));\r\n      for (len -= LOG_BASE; i < len;) x.d.push(+str.slice(i, i += LOG_BASE));\r\n      str = str.slice(i);\r\n      i = LOG_BASE - str.length;\r\n    } else {\r\n      i -= len;\r\n    }\r\n\r\n    for (; i--;) str += '0';\r\n    x.d.push(+str);\r\n\r\n    if (external) {\r\n\r\n      // Overflow?\r\n      if (x.e > x.constructor.maxE) {\r\n\r\n        // Infinity.\r\n        x.d = null;\r\n        x.e = NaN;\r\n\r\n      // Underflow?\r\n      } else if (x.e < x.constructor.minE) {\r\n\r\n        // Zero.\r\n        x.e = 0;\r\n        x.d = [0];\r\n        // x.constructor.underflow = true;\r\n      } // else x.constructor.underflow = false;\r\n    }\r\n  } else {\r\n\r\n    // Zero.\r\n    x.e = 0;\r\n    x.d = [0];\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Parse the value of a new Decimal `x` from a string `str`, which is not a decimal value.\r\n */\r\nfunction parseOther(x, str) {\r\n  var base, Ctor, divisor, i, isFloat, len, p, xd, xe;\r\n\r\n  if (str.indexOf('_') > -1) {\r\n    str = str.replace(/(\\d)_(?=\\d)/g, '$1');\r\n    if (isDecimal.test(str)) return parseDecimal(x, str);\r\n  } else if (str === 'Infinity' || str === 'NaN') {\r\n    if (!+str) x.s = NaN;\r\n    x.e = NaN;\r\n    x.d = null;\r\n    return x;\r\n  }\r\n\r\n  if (isHex.test(str))  {\r\n    base = 16;\r\n    str = str.toLowerCase();\r\n  } else if (isBinary.test(str))  {\r\n    base = 2;\r\n  } else if (isOctal.test(str))  {\r\n    base = 8;\r\n  } else {\r\n    throw Error(invalidArgument + str);\r\n  }\r\n\r\n  // Is there a binary exponent part?\r\n  i = str.search(/p/i);\r\n\r\n  if (i > 0) {\r\n    p = +str.slice(i + 1);\r\n    str = str.substring(2, i);\r\n  } else {\r\n    str = str.slice(2);\r\n  }\r\n\r\n  // Convert `str` as an integer then divide the result by `base` raised to a power such that the\r\n  // fraction part will be restored.\r\n  i = str.indexOf('.');\r\n  isFloat = i >= 0;\r\n  Ctor = x.constructor;\r\n\r\n  if (isFloat) {\r\n    str = str.replace('.', '');\r\n    len = str.length;\r\n    i = len - i;\r\n\r\n    // log[10](16) = 1.2041... , log[10](88) = 1.9444....\r\n    divisor = intPow(Ctor, new Ctor(base), i, i * 2);\r\n  }\r\n\r\n  xd = convertBase(str, base, BASE);\r\n  xe = xd.length - 1;\r\n\r\n  // Remove trailing zeros.\r\n  for (i = xe; xd[i] === 0; --i) xd.pop();\r\n  if (i < 0) return new Ctor(x.s * 0);\r\n  x.e = getBase10Exponent(xd, xe);\r\n  x.d = xd;\r\n  external = false;\r\n\r\n  // At what precision to perform the division to ensure exact conversion?\r\n  // maxDecimalIntegerPartDigitCount = ceil(log[10](b) * otherBaseIntegerPartDigitCount)\r\n  // log[10](2) = 0.30103, log[10](8) = 0.90309, log[10](16) = 1.20412\r\n  // E.g. ceil(1.2 * 3) = 4, so up to 4 decimal digits are needed to represent 3 hex int digits.\r\n  // maxDecimalFractionPartDigitCount = {Hex:4|Oct:3|Bin:1} * otherBaseFractionPartDigitCount\r\n  // Therefore using 4 * the number of digits of str will always be enough.\r\n  if (isFloat) x = divide(x, divisor, len * 4);\r\n\r\n  // Multiply by the binary exponent part if present.\r\n  if (p) x = x.times(Math.abs(p) < 54 ? mathpow(2, p) : Decimal.pow(2, p));\r\n  external = true;\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * sin(x) = x - x^3/3! + x^5/5! - ...\r\n * |x| < pi/2\r\n *\r\n */\r\nfunction sine(Ctor, x) {\r\n  var k,\r\n    len = x.d.length;\r\n\r\n  if (len < 3) {\r\n    return x.isZero() ? x : taylorSeries(Ctor, 2, x, x);\r\n  }\r\n\r\n  // Argument reduction: sin(5x) = 16*sin^5(x) - 20*sin^3(x) + 5*sin(x)\r\n  // i.e. sin(x) = 16*sin^5(x/5) - 20*sin^3(x/5) + 5*sin(x/5)\r\n  // and  sin(x) = sin(x/5)(5 + sin^2(x/5)(16sin^2(x/5) - 20))\r\n\r\n  // Estimate the optimum number of times to use the argument reduction.\r\n  k = 1.4 * Math.sqrt(len);\r\n  k = k > 16 ? 16 : k | 0;\r\n\r\n  x = x.times(1 / tinyPow(5, k));\r\n  x = taylorSeries(Ctor, 2, x, x);\r\n\r\n  // Reverse argument reduction\r\n  var sin2_x,\r\n    d5 = new Ctor(5),\r\n    d16 = new Ctor(16),\r\n    d20 = new Ctor(20);\r\n  for (; k--;) {\r\n    sin2_x = x.times(x);\r\n    x = x.times(d5.plus(sin2_x.times(d16.times(sin2_x).minus(d20))));\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n// Calculate Taylor series for `cos`, `cosh`, `sin` and `sinh`.\r\nfunction taylorSeries(Ctor, n, x, y, isHyperbolic) {\r\n  var j, t, u, x2,\r\n    i = 1,\r\n    pr = Ctor.precision,\r\n    k = Math.ceil(pr / LOG_BASE);\r\n\r\n  external = false;\r\n  x2 = x.times(x);\r\n  u = new Ctor(y);\r\n\r\n  for (;;) {\r\n    t = divide(u.times(x2), new Ctor(n++ * n++), pr, 1);\r\n    u = isHyperbolic ? y.plus(t) : y.minus(t);\r\n    y = divide(t.times(x2), new Ctor(n++ * n++), pr, 1);\r\n    t = u.plus(y);\r\n\r\n    if (t.d[k] !== void 0) {\r\n      for (j = k; t.d[j] === u.d[j] && j--;);\r\n      if (j == -1) break;\r\n    }\r\n\r\n    j = u;\r\n    u = y;\r\n    y = t;\r\n    t = j;\r\n    i++;\r\n  }\r\n\r\n  external = true;\r\n  t.d.length = k + 1;\r\n\r\n  return t;\r\n}\r\n\r\n\r\n// Exponent e must be positive and non-zero.\r\nfunction tinyPow(b, e) {\r\n  var n = b;\r\n  while (--e) n *= b;\r\n  return n;\r\n}\r\n\r\n\r\n// Return the absolute value of `x` reduced to less than or equal to half pi.\r\nfunction toLessThanHalfPi(Ctor, x) {\r\n  var t,\r\n    isNeg = x.s < 0,\r\n    pi = getPi(Ctor, Ctor.precision, 1),\r\n    halfPi = pi.times(0.5);\r\n\r\n  x = x.abs();\r\n\r\n  if (x.lte(halfPi)) {\r\n    quadrant = isNeg ? 4 : 1;\r\n    return x;\r\n  }\r\n\r\n  t = x.divToInt(pi);\r\n\r\n  if (t.isZero()) {\r\n    quadrant = isNeg ? 3 : 2;\r\n  } else {\r\n    x = x.minus(t.times(pi));\r\n\r\n    // 0 <= x < pi\r\n    if (x.lte(halfPi)) {\r\n      quadrant = isOdd(t) ? (isNeg ? 2 : 3) : (isNeg ? 4 : 1);\r\n      return x;\r\n    }\r\n\r\n    quadrant = isOdd(t) ? (isNeg ? 1 : 4) : (isNeg ? 3 : 2);\r\n  }\r\n\r\n  return x.minus(pi).abs();\r\n}\r\n\r\n\r\n/*\r\n * Return the value of Decimal `x` as a string in base `baseOut`.\r\n *\r\n * If the optional `sd` argument is present include a binary exponent suffix.\r\n */\r\nfunction toStringBinary(x, baseOut, sd, rm) {\r\n  var base, e, i, k, len, roundUp, str, xd, y,\r\n    Ctor = x.constructor,\r\n    isExp = sd !== void 0;\r\n\r\n  if (isExp) {\r\n    checkInt32(sd, 1, MAX_DIGITS);\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n  } else {\r\n    sd = Ctor.precision;\r\n    rm = Ctor.rounding;\r\n  }\r\n\r\n  if (!x.isFinite()) {\r\n    str = nonFiniteToString(x);\r\n  } else {\r\n    str = finiteToString(x);\r\n    i = str.indexOf('.');\r\n\r\n    // Use exponential notation according to `toExpPos` and `toExpNeg`? No, but if required:\r\n    // maxBinaryExponent = floor((decimalExponent + 1) * log[2](10))\r\n    // minBinaryExponent = floor(decimalExponent * log[2](10))\r\n    // log[2](10) = 3.321928094887362347870319429489390175864\r\n\r\n    if (isExp) {\r\n      base = 2;\r\n      if (baseOut == 16) {\r\n        sd = sd * 4 - 3;\r\n      } else if (baseOut == 8) {\r\n        sd = sd * 3 - 2;\r\n      }\r\n    } else {\r\n      base = baseOut;\r\n    }\r\n\r\n    // Convert the number as an integer then divide the result by its base raised to a power such\r\n    // that the fraction part will be restored.\r\n\r\n    // Non-integer.\r\n    if (i >= 0) {\r\n      str = str.replace('.', '');\r\n      y = new Ctor(1);\r\n      y.e = str.length - i;\r\n      y.d = convertBase(finiteToString(y), 10, base);\r\n      y.e = y.d.length;\r\n    }\r\n\r\n    xd = convertBase(str, 10, base);\r\n    e = len = xd.length;\r\n\r\n    // Remove trailing zeros.\r\n    for (; xd[--len] == 0;) xd.pop();\r\n\r\n    if (!xd[0]) {\r\n      str = isExp ? '0p+0' : '0';\r\n    } else {\r\n      if (i < 0) {\r\n        e--;\r\n      } else {\r\n        x = new Ctor(x);\r\n        x.d = xd;\r\n        x.e = e;\r\n        x = divide(x, y, sd, rm, 0, base);\r\n        xd = x.d;\r\n        e = x.e;\r\n        roundUp = inexact;\r\n      }\r\n\r\n      // The rounding digit, i.e. the digit after the digit that may be rounded up.\r\n      i = xd[sd];\r\n      k = base / 2;\r\n      roundUp = roundUp || xd[sd + 1] !== void 0;\r\n\r\n      roundUp = rm < 4\r\n        ? (i !== void 0 || roundUp) && (rm === 0 || rm === (x.s < 0 ? 3 : 2))\r\n        : i > k || i === k && (rm === 4 || roundUp || rm === 6 && xd[sd - 1] & 1 ||\r\n          rm === (x.s < 0 ? 8 : 7));\r\n\r\n      xd.length = sd;\r\n\r\n      if (roundUp) {\r\n\r\n        // Rounding up may mean the previous digit has to be rounded up and so on.\r\n        for (; ++xd[--sd] > base - 1;) {\r\n          xd[sd] = 0;\r\n          if (!sd) {\r\n            ++e;\r\n            xd.unshift(1);\r\n          }\r\n        }\r\n      }\r\n\r\n      // Determine trailing zeros.\r\n      for (len = xd.length; !xd[len - 1]; --len);\r\n\r\n      // E.g. [4, 11, 15] becomes 4bf.\r\n      for (i = 0, str = ''; i < len; i++) str += NUMERALS.charAt(xd[i]);\r\n\r\n      // Add binary exponent suffix?\r\n      if (isExp) {\r\n        if (len > 1) {\r\n          if (baseOut == 16 || baseOut == 8) {\r\n            i = baseOut == 16 ? 4 : 3;\r\n            for (--len; len % i; len++) str += '0';\r\n            xd = convertBase(str, base, baseOut);\r\n            for (len = xd.length; !xd[len - 1]; --len);\r\n\r\n            // xd[0] will always be be 1\r\n            for (i = 1, str = '1.'; i < len; i++) str += NUMERALS.charAt(xd[i]);\r\n          } else {\r\n            str = str.charAt(0) + '.' + str.slice(1);\r\n          }\r\n        }\r\n\r\n        str =  str + (e < 0 ? 'p' : 'p+') + e;\r\n      } else if (e < 0) {\r\n        for (; ++e;) str = '0' + str;\r\n        str = '0.' + str;\r\n      } else {\r\n        if (++e > len) for (e -= len; e-- ;) str += '0';\r\n        else if (e < len) str = str.slice(0, e) + '.' + str.slice(e);\r\n      }\r\n    }\r\n\r\n    str = (baseOut == 16 ? '0x' : baseOut == 2 ? '0b' : baseOut == 8 ? '0o' : '') + str;\r\n  }\r\n\r\n  return x.s < 0 ? '-' + str : str;\r\n}\r\n\r\n\r\n// Does not strip trailing zeros.\r\nfunction truncate(arr, len) {\r\n  if (arr.length > len) {\r\n    arr.length = len;\r\n    return true;\r\n  }\r\n}\r\n\r\n\r\n// Decimal methods\r\n\r\n\r\n/*\r\n *  abs\r\n *  acos\r\n *  acosh\r\n *  add\r\n *  asin\r\n *  asinh\r\n *  atan\r\n *  atanh\r\n *  atan2\r\n *  cbrt\r\n *  ceil\r\n *  clamp\r\n *  clone\r\n *  config\r\n *  cos\r\n *  cosh\r\n *  div\r\n *  exp\r\n *  floor\r\n *  hypot\r\n *  ln\r\n *  log\r\n *  log2\r\n *  log10\r\n *  max\r\n *  min\r\n *  mod\r\n *  mul\r\n *  pow\r\n *  random\r\n *  round\r\n *  set\r\n *  sign\r\n *  sin\r\n *  sinh\r\n *  sqrt\r\n *  sub\r\n *  sum\r\n *  tan\r\n *  tanh\r\n *  trunc\r\n */\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the absolute value of `x`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction abs(x) {\r\n  return new this(x).abs();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arccosine in radians of `x`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction acos(x) {\r\n  return new this(x).acos();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic cosine of `x`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction acosh(x) {\r\n  return new this(x).acosh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sum of `x` and `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction add(x, y) {\r\n  return new this(x).plus(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arcsine in radians of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction asin(x) {\r\n  return new this(x).asin();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic sine of `x`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction asinh(x) {\r\n  return new this(x).asinh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arctangent in radians of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction atan(x) {\r\n  return new this(x).atan();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic tangent of `x`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction atanh(x) {\r\n  return new this(x).atanh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arctangent in radians of `y/x` in the range -pi to pi\r\n * (inclusive), rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-pi, pi]\r\n *\r\n * y {number|string|bigint|Decimal} The y-coordinate.\r\n * x {number|string|bigint|Decimal} The x-coordinate.\r\n *\r\n * atan2(±0, -0)               = ±pi\r\n * atan2(±0, +0)               = ±0\r\n * atan2(±0, -x)               = ±pi for x > 0\r\n * atan2(±0, x)                = ±0 for x > 0\r\n * atan2(-y, ±0)               = -pi/2 for y > 0\r\n * atan2(y, ±0)                = pi/2 for y > 0\r\n * atan2(±y, -Infinity)        = ±pi for finite y > 0\r\n * atan2(±y, +Infinity)        = ±0 for finite y > 0\r\n * atan2(±Infinity, x)         = ±pi/2 for finite x\r\n * atan2(±Infinity, -Infinity) = ±3*pi/4\r\n * atan2(±Infinity, +Infinity) = ±pi/4\r\n * atan2(NaN, x) = NaN\r\n * atan2(y, NaN) = NaN\r\n *\r\n */\r\nfunction atan2(y, x) {\r\n  y = new this(y);\r\n  x = new this(x);\r\n  var r,\r\n    pr = this.precision,\r\n    rm = this.rounding,\r\n    wpr = pr + 4;\r\n\r\n  // Either NaN\r\n  if (!y.s || !x.s) {\r\n    r = new this(NaN);\r\n\r\n  // Both ±Infinity\r\n  } else if (!y.d && !x.d) {\r\n    r = getPi(this, wpr, 1).times(x.s > 0 ? 0.25 : 0.75);\r\n    r.s = y.s;\r\n\r\n  // x is ±Infinity or y is ±0\r\n  } else if (!x.d || y.isZero()) {\r\n    r = x.s < 0 ? getPi(this, pr, rm) : new this(0);\r\n    r.s = y.s;\r\n\r\n  // y is ±Infinity or x is ±0\r\n  } else if (!y.d || x.isZero()) {\r\n    r = getPi(this, wpr, 1).times(0.5);\r\n    r.s = y.s;\r\n\r\n  // Both non-zero and finite\r\n  } else if (x.s < 0) {\r\n    this.precision = wpr;\r\n    this.rounding = 1;\r\n    r = this.atan(divide(y, x, wpr, 1));\r\n    x = getPi(this, wpr, 1);\r\n    this.precision = pr;\r\n    this.rounding = rm;\r\n    r = y.s < 0 ? r.minus(x) : r.plus(x);\r\n  } else {\r\n    r = this.atan(divide(y, x, wpr, 1));\r\n  }\r\n\r\n  return r;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the cube root of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction cbrt(x) {\r\n  return new this(x).cbrt();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` rounded to an integer using `ROUND_CEIL`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction ceil(x) {\r\n  return finalise(x = new this(x), x.e + 1, 2);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` clamped to the range delineated by `min` and `max`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * min {number|string|bigint|Decimal}\r\n * max {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction clamp(x, min, max) {\r\n  return new this(x).clamp(min, max);\r\n}\r\n\r\n\r\n/*\r\n * Configure global settings for a Decimal constructor.\r\n *\r\n * `obj` is an object with one or more of the following properties,\r\n *\r\n *   precision  {number}\r\n *   rounding   {number}\r\n *   toExpNeg   {number}\r\n *   toExpPos   {number}\r\n *   maxE       {number}\r\n *   minE       {number}\r\n *   modulo     {number}\r\n *   crypto     {boolean|number}\r\n *   defaults   {true}\r\n *\r\n * E.g. Decimal.config({ precision: 20, rounding: 4 })\r\n *\r\n */\r\nfunction config(obj) {\r\n  if (!obj || typeof obj !== 'object') throw Error(decimalError + 'Object expected');\r\n  var i, p, v,\r\n    useDefaults = obj.defaults === true,\r\n    ps = [\r\n      'precision', 1, MAX_DIGITS,\r\n      'rounding', 0, 8,\r\n      'toExpNeg', -EXP_LIMIT, 0,\r\n      'toExpPos', 0, EXP_LIMIT,\r\n      'maxE', 0, EXP_LIMIT,\r\n      'minE', -EXP_LIMIT, 0,\r\n      'modulo', 0, 9\r\n    ];\r\n\r\n  for (i = 0; i < ps.length; i += 3) {\r\n    if (p = ps[i], useDefaults) this[p] = DEFAULTS[p];\r\n    if ((v = obj[p]) !== void 0) {\r\n      if (mathfloor(v) === v && v >= ps[i + 1] && v <= ps[i + 2]) this[p] = v;\r\n      else throw Error(invalidArgument + p + ': ' + v);\r\n    }\r\n  }\r\n\r\n  if (p = 'crypto', useDefaults) this[p] = DEFAULTS[p];\r\n  if ((v = obj[p]) !== void 0) {\r\n    if (v === true || v === false || v === 0 || v === 1) {\r\n      if (v) {\r\n        if (typeof crypto != 'undefined' && crypto &&\r\n          (crypto.getRandomValues || crypto.randomBytes)) {\r\n          this[p] = true;\r\n        } else {\r\n          throw Error(cryptoUnavailable);\r\n        }\r\n      } else {\r\n        this[p] = false;\r\n      }\r\n    } else {\r\n      throw Error(invalidArgument + p + ': ' + v);\r\n    }\r\n  }\r\n\r\n  return this;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the cosine of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction cos(x) {\r\n  return new this(x).cos();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic cosine of `x`, rounded to precision\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction cosh(x) {\r\n  return new this(x).cosh();\r\n}\r\n\r\n\r\n/*\r\n * Create and return a Decimal constructor with the same configuration properties as this Decimal\r\n * constructor.\r\n *\r\n */\r\nfunction clone(obj) {\r\n  var i, p, ps;\r\n\r\n  /*\r\n   * The Decimal constructor and exported function.\r\n   * Return a new Decimal instance.\r\n   *\r\n   * v {number|string|bigint|Decimal} A numeric value.\r\n   *\r\n   */\r\n  function Decimal(v) {\r\n    var e, i, t,\r\n      x = this;\r\n\r\n    // Decimal called without new.\r\n    if (!(x instanceof Decimal)) return new Decimal(v);\r\n\r\n    // Retain a reference to this Decimal constructor, and shadow Decimal.prototype.constructor\r\n    // which points to Object.\r\n    x.constructor = Decimal;\r\n\r\n    if (isDecimalInstance(v)) {\r\n      x.s = v.s;\r\n\r\n      if (external) {\r\n        if (!v.d || v.e > Decimal.maxE) {\r\n\r\n          // Infinity.\r\n          x.e = NaN;\r\n          x.d = null;\r\n        } else if (v.e < Decimal.minE) {\r\n\r\n          // Zero.\r\n          x.e = 0;\r\n          x.d = [0];\r\n        } else {\r\n          x.e = v.e;\r\n          x.d = v.d.slice();\r\n        }\r\n      } else {\r\n        x.e = v.e;\r\n        x.d = v.d ? v.d.slice() : v.d;\r\n      }\r\n\r\n      return;\r\n    }\r\n\r\n    t = typeof v;\r\n\r\n    if (t === 'number') {\r\n      if (v === 0) {\r\n        x.s = 1 / v < 0 ? -1 : 1;\r\n        x.e = 0;\r\n        x.d = [0];\r\n        return;\r\n      }\r\n\r\n      if (v < 0) {\r\n        v = -v;\r\n        x.s = -1;\r\n      } else {\r\n        x.s = 1;\r\n      }\r\n\r\n      // Fast path for small integers.\r\n      if (v === ~~v && v < 1e7) {\r\n        for (e = 0, i = v; i >= 10; i /= 10) e++;\r\n\r\n        if (external) {\r\n          if (e > Decimal.maxE) {\r\n            x.e = NaN;\r\n            x.d = null;\r\n          } else if (e < Decimal.minE) {\r\n            x.e = 0;\r\n            x.d = [0];\r\n          } else {\r\n            x.e = e;\r\n            x.d = [v];\r\n          }\r\n        } else {\r\n          x.e = e;\r\n          x.d = [v];\r\n        }\r\n\r\n        return;\r\n      }\r\n\r\n      // Infinity or NaN?\r\n      if (v * 0 !== 0) {\r\n        if (!v) x.s = NaN;\r\n        x.e = NaN;\r\n        x.d = null;\r\n        return;\r\n      }\r\n\r\n      return parseDecimal(x, v.toString());\r\n    }\r\n\r\n    if (t === 'string') {\r\n      if ((i = v.charCodeAt(0)) === 45) {  // minus sign\r\n        v = v.slice(1);\r\n        x.s = -1;\r\n      } else {\r\n        if (i === 43) v = v.slice(1);  // plus sign\r\n        x.s = 1;\r\n      }\r\n\r\n      return isDecimal.test(v) ? parseDecimal(x, v) : parseOther(x, v);\r\n    }\r\n\r\n    if (t === 'bigint') {\r\n      if (v < 0) {\r\n        v = -v;\r\n        x.s = -1;\r\n      } else {\r\n        x.s = 1;\r\n      }\r\n\r\n      return parseDecimal(x, v.toString());\r\n    }\r\n\r\n    throw Error(invalidArgument + v);\r\n  }\r\n\r\n  Decimal.prototype = P;\r\n\r\n  Decimal.ROUND_UP = 0;\r\n  Decimal.ROUND_DOWN = 1;\r\n  Decimal.ROUND_CEIL = 2;\r\n  Decimal.ROUND_FLOOR = 3;\r\n  Decimal.ROUND_HALF_UP = 4;\r\n  Decimal.ROUND_HALF_DOWN = 5;\r\n  Decimal.ROUND_HALF_EVEN = 6;\r\n  Decimal.ROUND_HALF_CEIL = 7;\r\n  Decimal.ROUND_HALF_FLOOR = 8;\r\n  Decimal.EUCLID = 9;\r\n\r\n  Decimal.config = Decimal.set = config;\r\n  Decimal.clone = clone;\r\n  Decimal.isDecimal = isDecimalInstance;\r\n\r\n  Decimal.abs = abs;\r\n  Decimal.acos = acos;\r\n  Decimal.acosh = acosh;        // ES6\r\n  Decimal.add = add;\r\n  Decimal.asin = asin;\r\n  Decimal.asinh = asinh;        // ES6\r\n  Decimal.atan = atan;\r\n  Decimal.atanh = atanh;        // ES6\r\n  Decimal.atan2 = atan2;\r\n  Decimal.cbrt = cbrt;          // ES6\r\n  Decimal.ceil = ceil;\r\n  Decimal.clamp = clamp;\r\n  Decimal.cos = cos;\r\n  Decimal.cosh = cosh;          // ES6\r\n  Decimal.div = div;\r\n  Decimal.exp = exp;\r\n  Decimal.floor = floor;\r\n  Decimal.hypot = hypot;        // ES6\r\n  Decimal.ln = ln;\r\n  Decimal.log = log;\r\n  Decimal.log10 = log10;        // ES6\r\n  Decimal.log2 = log2;          // ES6\r\n  Decimal.max = max;\r\n  Decimal.min = min;\r\n  Decimal.mod = mod;\r\n  Decimal.mul = mul;\r\n  Decimal.pow = pow;\r\n  Decimal.random = random;\r\n  Decimal.round = round;\r\n  Decimal.sign = sign;          // ES6\r\n  Decimal.sin = sin;\r\n  Decimal.sinh = sinh;          // ES6\r\n  Decimal.sqrt = sqrt;\r\n  Decimal.sub = sub;\r\n  Decimal.sum = sum;\r\n  Decimal.tan = tan;\r\n  Decimal.tanh = tanh;          // ES6\r\n  Decimal.trunc = trunc;        // ES6\r\n\r\n  if (obj === void 0) obj = {};\r\n  if (obj) {\r\n    if (obj.defaults !== true) {\r\n      ps = ['precision', 'rounding', 'toExpNeg', 'toExpPos', 'maxE', 'minE', 'modulo', 'crypto'];\r\n      for (i = 0; i < ps.length;) if (!obj.hasOwnProperty(p = ps[i++])) obj[p] = this[p];\r\n    }\r\n  }\r\n\r\n  Decimal.config(obj);\r\n\r\n  return Decimal;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` divided by `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction div(x, y) {\r\n  return new this(x).div(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural exponential of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} The power to which to raise the base of the natural log.\r\n *\r\n */\r\nfunction exp(x) {\r\n  return new this(x).exp();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` round to an integer using `ROUND_FLOOR`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction floor(x) {\r\n  return finalise(x = new this(x), x.e + 1, 3);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the square root of the sum of the squares of the arguments,\r\n * rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * hypot(a, b, ...) = sqrt(a^2 + b^2 + ...)\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction hypot() {\r\n  var i, n,\r\n    t = new this(0);\r\n\r\n  external = false;\r\n\r\n  for (i = 0; i < arguments.length;) {\r\n    n = new this(arguments[i++]);\r\n    if (!n.d) {\r\n      if (n.s) {\r\n        external = true;\r\n        return new this(1 / 0);\r\n      }\r\n      t = n;\r\n    } else if (t.d) {\r\n      t = t.plus(n.times(n));\r\n    }\r\n  }\r\n\r\n  external = true;\r\n\r\n  return t.sqrt();\r\n}\r\n\r\n\r\n/*\r\n * Return true if object is a Decimal instance (where Decimal is any Decimal constructor),\r\n * otherwise return false.\r\n *\r\n */\r\nfunction isDecimalInstance(obj) {\r\n  return obj instanceof Decimal || obj && obj.toStringTag === tag || false;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural logarithm of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction ln(x) {\r\n  return new this(x).ln();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the log of `x` to the base `y`, or to base 10 if no base\r\n * is specified, rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * log[y](x)\r\n *\r\n * x {number|string|bigint|Decimal} The argument of the logarithm.\r\n * y {number|string|bigint|Decimal} The base of the logarithm.\r\n *\r\n */\r\nfunction log(x, y) {\r\n  return new this(x).log(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the base 2 logarithm of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction log2(x) {\r\n  return new this(x).log(2);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the base 10 logarithm of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction log10(x) {\r\n  return new this(x).log(10);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the maximum of the arguments.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction max() {\r\n  return maxOrMin(this, arguments, -1);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the minimum of the arguments.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction min() {\r\n  return maxOrMin(this, arguments, 1);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` modulo `y`, rounded to `precision` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction mod(x, y) {\r\n  return new this(x).mod(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` multiplied by `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction mul(x, y) {\r\n  return new this(x).mul(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` raised to the power `y`, rounded to precision\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} The base.\r\n * y {number|string|bigint|Decimal} The exponent.\r\n *\r\n */\r\nfunction pow(x, y) {\r\n  return new this(x).pow(y);\r\n}\r\n\r\n\r\n/*\r\n * Returns a new Decimal with a random value equal to or greater than 0 and less than 1, and with\r\n * `sd`, or `Decimal.precision` if `sd` is omitted, significant digits (or less if trailing zeros\r\n * are produced).\r\n *\r\n * [sd] {number} Significant digits. Integer, 0 to MAX_DIGITS inclusive.\r\n *\r\n */\r\nfunction random(sd) {\r\n  var d, e, k, n,\r\n    i = 0,\r\n    r = new this(1),\r\n    rd = [];\r\n\r\n  if (sd === void 0) sd = this.precision;\r\n  else checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n  k = Math.ceil(sd / LOG_BASE);\r\n\r\n  if (!this.crypto) {\r\n    for (; i < k;) rd[i++] = Math.random() * 1e7 | 0;\r\n\r\n  // Browsers supporting crypto.getRandomValues.\r\n  } else if (crypto.getRandomValues) {\r\n    d = crypto.getRandomValues(new Uint32Array(k));\r\n\r\n    for (; i < k;) {\r\n      n = d[i];\r\n\r\n      // 0 <= n < 4294967296\r\n      // Probability n >= 4.29e9, is 4967296 / 4294967296 = 0.00116 (1 in 865).\r\n      if (n >= 4.29e9) {\r\n        d[i] = crypto.getRandomValues(new Uint32Array(1))[0];\r\n      } else {\r\n\r\n        // 0 <= n <= 4289999999\r\n        // 0 <= (n % 1e7) <= 9999999\r\n        rd[i++] = n % 1e7;\r\n      }\r\n    }\r\n\r\n  // Node.js supporting crypto.randomBytes.\r\n  } else if (crypto.randomBytes) {\r\n\r\n    // buffer\r\n    d = crypto.randomBytes(k *= 4);\r\n\r\n    for (; i < k;) {\r\n\r\n      // 0 <= n < 2147483648\r\n      n = d[i] + (d[i + 1] << 8) + (d[i + 2] << 16) + ((d[i + 3] & 0x7f) << 24);\r\n\r\n      // Probability n >= 2.14e9, is 7483648 / 2147483648 = 0.0035 (1 in 286).\r\n      if (n >= 2.14e9) {\r\n        crypto.randomBytes(4).copy(d, i);\r\n      } else {\r\n\r\n        // 0 <= n <= 2139999999\r\n        // 0 <= (n % 1e7) <= 9999999\r\n        rd.push(n % 1e7);\r\n        i += 4;\r\n      }\r\n    }\r\n\r\n    i = k / 4;\r\n  } else {\r\n    throw Error(cryptoUnavailable);\r\n  }\r\n\r\n  k = rd[--i];\r\n  sd %= LOG_BASE;\r\n\r\n  // Convert trailing digits to zeros according to sd.\r\n  if (k && sd) {\r\n    n = mathpow(10, LOG_BASE - sd);\r\n    rd[i] = (k / n | 0) * n;\r\n  }\r\n\r\n  // Remove trailing words which are zero.\r\n  for (; rd[i] === 0; i--) rd.pop();\r\n\r\n  // Zero?\r\n  if (i < 0) {\r\n    e = 0;\r\n    rd = [0];\r\n  } else {\r\n    e = -1;\r\n\r\n    // Remove leading words which are zero and adjust exponent accordingly.\r\n    for (; rd[0] === 0; e -= LOG_BASE) rd.shift();\r\n\r\n    // Count the digits of the first word of rd to determine leading zeros.\r\n    for (k = 1, n = rd[0]; n >= 10; n /= 10) k++;\r\n\r\n    // Adjust the exponent for leading zeros of the first word of rd.\r\n    if (k < LOG_BASE) e -= LOG_BASE - k;\r\n  }\r\n\r\n  r.e = e;\r\n  r.d = rd;\r\n\r\n  return r;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` rounded to an integer using rounding mode `rounding`.\r\n *\r\n * To emulate `Math.round`, set rounding to 7 (ROUND_HALF_CEIL).\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction round(x) {\r\n  return finalise(x = new this(x), x.e + 1, this.rounding);\r\n}\r\n\r\n\r\n/*\r\n * Return\r\n *   1    if x > 0,\r\n *  -1    if x < 0,\r\n *   0    if x is 0,\r\n *  -0    if x is -0,\r\n *   NaN  otherwise\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sign(x) {\r\n  x = new this(x);\r\n  return x.d ? (x.d[0] ? x.s : 0 * x.s) : x.s || NaN;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sine of `x`, rounded to `precision` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction sin(x) {\r\n  return new this(x).sin();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic sine of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction sinh(x) {\r\n  return new this(x).sinh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the square root of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sqrt(x) {\r\n  return new this(x).sqrt();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` minus `y`, rounded to `precision` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sub(x, y) {\r\n  return new this(x).sub(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sum of the arguments, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * Only the result is rounded, not the intermediate calculations.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sum() {\r\n  var i = 0,\r\n    args = arguments,\r\n    x = new this(args[i]);\r\n\r\n  external = false;\r\n  for (; x.s && ++i < args.length;) x = x.plus(args[i]);\r\n  external = true;\r\n\r\n  return finalise(x, this.precision, this.rounding);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the tangent of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction tan(x) {\r\n  return new this(x).tan();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic tangent of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction tanh(x) {\r\n  return new this(x).tanh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` truncated to an integer.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction trunc(x) {\r\n  return finalise(x = new this(x), x.e + 1, 1);\r\n}\r\n\r\n\r\nP[Symbol.for('nodejs.util.inspect.custom')] = P.toString;\r\nP[Symbol.toStringTag] = 'Decimal';\r\n\r\n// Create and configure initial Decimal constructor.\r\nexport var Decimal = P.constructor = clone(DEFAULTS);\r\n\r\n// Create the internal constants from their string values.\r\nLN10 = new Decimal(LN10);\r\nPI = new Decimal(PI);\r\n\r\nexport default Decimal;\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;;AAGE;AACA;AACF,IAAIA,SAAS,GAAG,IAAI;EAAuB;;EAEzC;EACA;EACAC,UAAU,GAAG,GAAG;EAAyB;;EAEzC;EACAC,QAAQ,GAAG,kBAAkB;EAE7B;EACAC,IAAI,GAAG,ogCAAogC;EAE3gC;EACAC,EAAE,GAAG,ogCAAogC;EAGzgC;EACAC,QAAQ,GAAG;IAET;IACA;;IAEA;IACA;IACAC,SAAS,EAAE,EAAE;IAA0B;;IAEvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,QAAQ,EAAE,CAAC;IAA4B;;IAEvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,MAAM,EAAE,CAAC;IAA8B;;IAEvC;IACA;IACAC,QAAQ,EAAE,CAAC,CAAC;IAA2B;;IAEvC;IACA;IACAC,QAAQ,EAAG,EAAE;IAA0B;;IAEvC;IACA;IACAC,IAAI,EAAE,CAACX,SAAS;IAAuB;;IAEvC;IACA;IACAY,IAAI,EAAEZ,SAAS;IAAwB;;IAEvC;IACAa,MAAM,EAAE,KAAK,CAA0B;EACzC,CAAC;EAGH;;EAGEC,OAAO;EAAEC,QAAQ;EACjBC,QAAQ,GAAG,IAAI;EAEfC,YAAY,GAAG,iBAAiB;EAChCC,eAAe,GAAGD,YAAY,GAAG,oBAAoB;EACrDE,sBAAsB,GAAGF,YAAY,GAAG,0BAA0B;EAClEG,iBAAiB,GAAGH,YAAY,GAAG,oBAAoB;EACvDI,GAAG,GAAG,kBAAkB;EAExBC,SAAS,GAAGC,IAAI,CAACC,KAAK;EACtBC,OAAO,GAAGF,IAAI,CAACG,GAAG;EAElBC,QAAQ,GAAG,4CAA4C;EACvDC,KAAK,GAAG,wDAAwD;EAChEC,OAAO,GAAG,+CAA+C;EACzDC,SAAS,GAAG,oCAAoC;EAEhDC,IAAI,GAAG,GAAG;EACVC,QAAQ,GAAG,CAAC;EACZC,gBAAgB,GAAG,gBAAgB;EAEnCC,cAAc,GAAG/B,IAAI,CAACgC,MAAM,GAAG,CAAC;EAChCC,YAAY,GAAGhC,EAAE,CAAC+B,MAAM,GAAG,CAAC;EAE5B;EACAE,CAAC,GAAG;IAAEC,WAAW,EAAEjB;EAAI,CAAC;;AAG1B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACAgB,CAAC,CAACE,aAAa,GAAGF,CAAC,CAACG,GAAG,GAAG,YAAY;EACpC,IAAIC,CAAC,GAAG,IAAI,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC;EAClC,IAAID,CAAC,CAACE,CAAC,GAAG,CAAC,EAAEF,CAAC,CAACE,CAAC,GAAG,CAAC;EACpB,OAAOC,QAAQ,CAACH,CAAC,CAAC;AACpB,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACAJ,CAAC,CAACQ,IAAI,GAAG,YAAY;EACnB,OAAOD,QAAQ,CAAC,IAAI,IAAI,CAACF,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,CAACI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC5D,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAT,CAAC,CAACU,SAAS,GAAGV,CAAC,CAACW,KAAK,GAAG,UAAUC,GAAG,EAAEC,GAAG,EAAE;EAC1C,IAAIC,CAAC;IACHV,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EACtBO,GAAG,GAAG,IAAIG,IAAI,CAACH,GAAG,CAAC;EACnBC,GAAG,GAAG,IAAIE,IAAI,CAACF,GAAG,CAAC;EACnB,IAAI,CAACD,GAAG,CAACN,CAAC,IAAI,CAACO,GAAG,CAACP,CAAC,EAAE,OAAO,IAAIS,IAAI,CAACC,GAAG,CAAC;EAC1C,IAAIJ,GAAG,CAACK,EAAE,CAACJ,GAAG,CAAC,EAAE,MAAMK,KAAK,CAACrC,eAAe,GAAGgC,GAAG,CAAC;EACnDC,CAAC,GAAGV,CAAC,CAACe,GAAG,CAACP,GAAG,CAAC;EACd,OAAOE,CAAC,GAAG,CAAC,GAAGF,GAAG,GAAGR,CAAC,CAACe,GAAG,CAACN,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG,GAAG,IAAIE,IAAI,CAACX,CAAC,CAAC;AACzD,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAJ,CAAC,CAACoB,UAAU,GAAGpB,CAAC,CAACmB,GAAG,GAAG,UAAUE,CAAC,EAAE;EAClC,IAAIC,CAAC;IAAEC,CAAC;IAAEC,GAAG;IAAEC,GAAG;IAChBrB,CAAC,GAAG,IAAI;IACRsB,EAAE,GAAGtB,CAAC,CAACuB,CAAC;IACRC,EAAE,GAAG,CAACP,CAAC,GAAG,IAAIjB,CAAC,CAACC,WAAW,CAACgB,CAAC,CAAC,EAAEM,CAAC;IACjCE,EAAE,GAAGzB,CAAC,CAACE,CAAC;IACRwB,EAAE,GAAGT,CAAC,CAACf,CAAC;;EAEV;EACA,IAAI,CAACoB,EAAE,IAAI,CAACE,EAAE,EAAE;IACd,OAAO,CAACC,EAAE,IAAI,CAACC,EAAE,GAAGd,GAAG,GAAGa,EAAE,KAAKC,EAAE,GAAGD,EAAE,GAAGH,EAAE,KAAKE,EAAE,GAAG,CAAC,GAAG,CAACF,EAAE,GAAGG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAClF;;EAEA;EACA,IAAI,CAACH,EAAE,CAAC,CAAC,CAAC,IAAI,CAACE,EAAE,CAAC,CAAC,CAAC,EAAE,OAAOF,EAAE,CAAC,CAAC,CAAC,GAAGG,EAAE,GAAGD,EAAE,CAAC,CAAC,CAAC,GAAG,CAACE,EAAE,GAAG,CAAC;;EAEzD;EACA,IAAID,EAAE,KAAKC,EAAE,EAAE,OAAOD,EAAE;;EAExB;EACA,IAAIzB,CAAC,CAACK,CAAC,KAAKY,CAAC,CAACZ,CAAC,EAAE,OAAOL,CAAC,CAACK,CAAC,GAAGY,CAAC,CAACZ,CAAC,GAAGoB,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAEnDL,GAAG,GAAGE,EAAE,CAAC5B,MAAM;EACf2B,GAAG,GAAGG,EAAE,CAAC9B,MAAM;;EAEf;EACA,KAAKwB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,GAAG,GAAGC,GAAG,GAAGD,GAAG,GAAGC,GAAG,EAAEH,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IACjD,IAAII,EAAE,CAACJ,CAAC,CAAC,KAAKM,EAAE,CAACN,CAAC,CAAC,EAAE,OAAOI,EAAE,CAACJ,CAAC,CAAC,GAAGM,EAAE,CAACN,CAAC,CAAC,GAAGO,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC7D;;EAEA;EACA,OAAOL,GAAG,KAAKC,GAAG,GAAG,CAAC,GAAGD,GAAG,GAAGC,GAAG,GAAGI,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACtD,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA7B,CAAC,CAAC+B,MAAM,GAAG/B,CAAC,CAACgC,GAAG,GAAG,YAAY;EAC7B,IAAIC,EAAE;IAAEC,EAAE;IACR9B,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,IAAI,CAACD,CAAC,CAACuB,CAAC,EAAE,OAAO,IAAIZ,IAAI,CAACC,GAAG,CAAC;;EAE9B;EACA,IAAI,CAACZ,CAAC,CAACuB,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAIZ,IAAI,CAAC,CAAC,CAAC;EAE/BkB,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;EACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EAClB6C,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,GAAG/C,IAAI,CAAC2B,GAAG,CAACT,CAAC,CAACK,CAAC,EAAEL,CAAC,CAAC+B,EAAE,CAAC,CAAC,CAAC,GAAGxC,QAAQ;EACtDoB,IAAI,CAAC7C,QAAQ,GAAG,CAAC;EAEjBkC,CAAC,GAAG2B,MAAM,CAAChB,IAAI,EAAEqB,gBAAgB,CAACrB,IAAI,EAAEX,CAAC,CAAC,CAAC;EAE3CW,IAAI,CAAC9C,SAAS,GAAGgE,EAAE;EACnBlB,IAAI,CAAC7C,QAAQ,GAAGgE,EAAE;EAElB,OAAO3B,QAAQ,CAAC7B,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,CAAC,GAAG0B,CAAC,CAACiC,GAAG,CAAC,CAAC,GAAGjC,CAAC,EAAE6B,EAAE,EAAEC,EAAE,EAAE,IAAI,CAAC;AAC7E,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,CAAC,CAACsC,QAAQ,GAAGtC,CAAC,CAACuC,IAAI,GAAG,YAAY;EAChC,IAAI9B,CAAC;IAAE+B,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEC,GAAG;IAAErC,CAAC;IAAE6B,EAAE;IAAES,CAAC;IAAEC,EAAE;IAAEC,OAAO;IACxC1C,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,IAAI,CAACD,CAAC,CAAC2C,QAAQ,CAAC,CAAC,IAAI3C,CAAC,CAAC4C,MAAM,CAAC,CAAC,EAAE,OAAO,IAAIjC,IAAI,CAACX,CAAC,CAAC;EACnDzB,QAAQ,GAAG,KAAK;;EAEhB;EACA2B,CAAC,GAAGF,CAAC,CAACE,CAAC,GAAGlB,OAAO,CAACgB,CAAC,CAACE,CAAC,GAAGF,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;;EAEhC;EACA;EACD,IAAI,CAACE,CAAC,IAAIpB,IAAI,CAACiB,GAAG,CAACG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;IAC9BmC,CAAC,GAAGQ,cAAc,CAAC7C,CAAC,CAACuB,CAAC,CAAC;IACvBlB,CAAC,GAAGL,CAAC,CAACK,CAAC;;IAEP;IACA,IAAIH,CAAC,GAAG,CAACG,CAAC,GAAGgC,CAAC,CAAC3C,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE2C,CAAC,IAAKnC,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,IAAK;IACrEA,CAAC,GAAGlB,OAAO,CAACqD,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;;IAErB;IACAhC,CAAC,GAAGxB,SAAS,CAAC,CAACwB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,KAAKA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAExD,IAAIH,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;MACdmC,CAAC,GAAG,IAAI,GAAGhC,CAAC;IACd,CAAC,MAAM;MACLgC,CAAC,GAAGnC,CAAC,CAAC4C,aAAa,CAAC,CAAC;MACrBT,CAAC,GAAGA,CAAC,CAACU,KAAK,CAAC,CAAC,EAAEV,CAAC,CAACW,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG3C,CAAC;IACxC;IAEAiC,CAAC,GAAG,IAAI3B,IAAI,CAAC0B,CAAC,CAAC;IACfC,CAAC,CAACpC,CAAC,GAAGF,CAAC,CAACE,CAAC;EACX,CAAC,MAAM;IACLoC,CAAC,GAAG,IAAI3B,IAAI,CAACT,CAAC,CAAC+C,QAAQ,CAAC,CAAC,CAAC;EAC5B;EAEAlB,EAAE,GAAG,CAAC1B,CAAC,GAAGM,IAAI,CAAC9C,SAAS,IAAI,CAAC;;EAE7B;EACA;EACA,SAAS;IACP2E,CAAC,GAAGF,CAAC;IACLG,EAAE,GAAGD,CAAC,CAACU,KAAK,CAACV,CAAC,CAAC,CAACU,KAAK,CAACV,CAAC,CAAC;IACxBE,OAAO,GAAGD,EAAE,CAACU,IAAI,CAACnD,CAAC,CAAC;IACpBsC,CAAC,GAAGc,MAAM,CAACV,OAAO,CAACS,IAAI,CAACnD,CAAC,CAAC,CAACkD,KAAK,CAACV,CAAC,CAAC,EAAEE,OAAO,CAACS,IAAI,CAACV,EAAE,CAAC,EAAEV,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;;IAEjE;IACA,IAAIc,cAAc,CAACL,CAAC,CAACjB,CAAC,CAAC,CAACwB,KAAK,CAAC,CAAC,EAAEhB,EAAE,CAAC,KAAK,CAACM,CAAC,GAAGQ,cAAc,CAACP,CAAC,CAACf,CAAC,CAAC,EAAEwB,KAAK,CAAC,CAAC,EAAEhB,EAAE,CAAC,EAAE;MAC/EM,CAAC,GAAGA,CAAC,CAACU,KAAK,CAAChB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,CAAC;;MAE3B;MACA;MACA,IAAIM,CAAC,IAAI,MAAM,IAAI,CAACE,GAAG,IAAIF,CAAC,IAAI,MAAM,EAAE;QAEtC;QACA;QACA,IAAI,CAACE,GAAG,EAAE;UACRpC,QAAQ,CAACqC,CAAC,EAAEnC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;UAErB,IAAImC,CAAC,CAACU,KAAK,CAACV,CAAC,CAAC,CAACU,KAAK,CAACV,CAAC,CAAC,CAACa,EAAE,CAACrD,CAAC,CAAC,EAAE;YAC7BsC,CAAC,GAAGE,CAAC;YACL;UACF;QACF;QAEAT,EAAE,IAAI,CAAC;QACPQ,GAAG,GAAG,CAAC;MACT,CAAC,MAAM;QAEL;QACA;QACA,IAAI,CAAC,CAACF,CAAC,IAAI,CAAC,CAACA,CAAC,CAACU,KAAK,CAAC,CAAC,CAAC,IAAIV,CAAC,CAACiB,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;UAE7C;UACAnD,QAAQ,CAACmC,CAAC,EAAEjC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;UACrB+B,CAAC,GAAG,CAACE,CAAC,CAACY,KAAK,CAACZ,CAAC,CAAC,CAACY,KAAK,CAACZ,CAAC,CAAC,CAACe,EAAE,CAACrD,CAAC,CAAC;QAChC;QAEA;MACF;IACF;EACF;EAEAzB,QAAQ,GAAG,IAAI;EAEf,OAAO4B,QAAQ,CAACmC,CAAC,EAAEjC,CAAC,EAAEM,IAAI,CAAC7C,QAAQ,EAAEsE,CAAC,CAAC;AACzC,CAAC;;AAGD;AACA;AACA;AACA;AACAxC,CAAC,CAAC2D,aAAa,GAAG3D,CAAC,CAAC4D,EAAE,GAAG,YAAY;EACnC,IAAIC,CAAC;IACHlC,CAAC,GAAG,IAAI,CAACA,CAAC;IACVc,CAAC,GAAGzB,GAAG;EAET,IAAIW,CAAC,EAAE;IACLkC,CAAC,GAAGlC,CAAC,CAAC7B,MAAM,GAAG,CAAC;IAChB2C,CAAC,GAAG,CAACoB,CAAC,GAAG5E,SAAS,CAAC,IAAI,CAACwB,CAAC,GAAGd,QAAQ,CAAC,IAAIA,QAAQ;;IAEjD;IACAkE,CAAC,GAAGlC,CAAC,CAACkC,CAAC,CAAC;IACR,IAAIA,CAAC,EAAE,OAAOA,CAAC,GAAG,EAAE,IAAI,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEpB,CAAC,EAAE;IACvC,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC;EAClB;EAEA,OAAOA,CAAC;AACV,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAzC,CAAC,CAAC8D,SAAS,GAAG9D,CAAC,CAAC+D,GAAG,GAAG,UAAU1C,CAAC,EAAE;EACjC,OAAOmC,MAAM,CAAC,IAAI,EAAE,IAAI,IAAI,CAACnD,WAAW,CAACgB,CAAC,CAAC,CAAC;AAC9C,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACArB,CAAC,CAACgE,kBAAkB,GAAGhE,CAAC,CAACiE,QAAQ,GAAG,UAAU5C,CAAC,EAAE;EAC/C,IAAIjB,CAAC,GAAG,IAAI;IACVW,IAAI,GAAGX,CAAC,CAACC,WAAW;EACtB,OAAOE,QAAQ,CAACiD,MAAM,CAACpD,CAAC,EAAE,IAAIW,IAAI,CAACM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEN,IAAI,CAAC9C,SAAS,EAAE8C,IAAI,CAAC7C,QAAQ,CAAC;AACjF,CAAC;;AAGD;AACA;AACA;AACA;AACA8B,CAAC,CAACkE,MAAM,GAAGlE,CAAC,CAACyD,EAAE,GAAG,UAAUpC,CAAC,EAAE;EAC7B,OAAO,IAAI,CAACF,GAAG,CAACE,CAAC,CAAC,KAAK,CAAC;AAC1B,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACArB,CAAC,CAACb,KAAK,GAAG,YAAY;EACpB,OAAOoB,QAAQ,CAAC,IAAI,IAAI,CAACF,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,CAACI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC5D,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACAT,CAAC,CAACmE,WAAW,GAAGnE,CAAC,CAACiB,EAAE,GAAG,UAAUI,CAAC,EAAE;EAClC,OAAO,IAAI,CAACF,GAAG,CAACE,CAAC,CAAC,GAAG,CAAC;AACxB,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACArB,CAAC,CAACoE,oBAAoB,GAAGpE,CAAC,CAACqE,GAAG,GAAG,UAAUhD,CAAC,EAAE;EAC5C,IAAIP,CAAC,GAAG,IAAI,CAACK,GAAG,CAACE,CAAC,CAAC;EACnB,OAAOP,CAAC,IAAI,CAAC,IAAIA,CAAC,KAAK,CAAC;AAC1B,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAd,CAAC,CAACsE,gBAAgB,GAAGtE,CAAC,CAACuE,IAAI,GAAG,YAAY;EACxC,IAAIzD,CAAC;IAAE2B,CAAC;IAAER,EAAE;IAAEC,EAAE;IAAEsC,GAAG;IACnBpE,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;IACpBoE,GAAG,GAAG,IAAI1D,IAAI,CAAC,CAAC,CAAC;EAEnB,IAAI,CAACX,CAAC,CAAC2C,QAAQ,CAAC,CAAC,EAAE,OAAO,IAAIhC,IAAI,CAACX,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGU,GAAG,CAAC;EACrD,IAAIZ,CAAC,CAAC4C,MAAM,CAAC,CAAC,EAAE,OAAOyB,GAAG;EAE1BxC,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;EACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EAClB6C,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,GAAG/C,IAAI,CAAC2B,GAAG,CAACT,CAAC,CAACK,CAAC,EAAEL,CAAC,CAAC+B,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;EAC/CpB,IAAI,CAAC7C,QAAQ,GAAG,CAAC;EACjBsG,GAAG,GAAGpE,CAAC,CAACuB,CAAC,CAAC7B,MAAM;;EAEhB;EACA;;EAEA;EACA;EACA,IAAI0E,GAAG,GAAG,EAAE,EAAE;IACZ1D,CAAC,GAAG5B,IAAI,CAACsB,IAAI,CAACgE,GAAG,GAAG,CAAC,CAAC;IACtB/B,CAAC,GAAG,CAAC,CAAC,GAAGiC,OAAO,CAAC,CAAC,EAAE5D,CAAC,CAAC,EAAEuC,QAAQ,CAAC,CAAC;EACpC,CAAC,MAAM;IACLvC,CAAC,GAAG,EAAE;IACN2B,CAAC,GAAG,8BAA8B;EACpC;EAEArC,CAAC,GAAGuE,YAAY,CAAC5D,IAAI,EAAE,CAAC,EAAEX,CAAC,CAACkD,KAAK,CAACb,CAAC,CAAC,EAAE,IAAI1B,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;;EAExD;EACA,IAAI6D,OAAO;IACTtD,CAAC,GAAGR,CAAC;IACL+D,EAAE,GAAG,IAAI9D,IAAI,CAAC,CAAC,CAAC;EAClB,OAAOO,CAAC,EAAE,GAAG;IACXsD,OAAO,GAAGxE,CAAC,CAACkD,KAAK,CAAClD,CAAC,CAAC;IACpBA,CAAC,GAAGqE,GAAG,CAACK,KAAK,CAACF,OAAO,CAACtB,KAAK,CAACuB,EAAE,CAACC,KAAK,CAACF,OAAO,CAACtB,KAAK,CAACuB,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3D;EAEA,OAAOtE,QAAQ,CAACH,CAAC,EAAEW,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,EAAElB,IAAI,CAAC7C,QAAQ,GAAGgE,EAAE,EAAE,IAAI,CAAC;AACnE,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,CAAC,CAAC+E,cAAc,GAAG/E,CAAC,CAACgF,IAAI,GAAG,YAAY;EACtC,IAAIlE,CAAC;IAAEmB,EAAE;IAAEC,EAAE;IAAEsC,GAAG;IAChBpE,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,IAAI,CAACD,CAAC,CAAC2C,QAAQ,CAAC,CAAC,IAAI3C,CAAC,CAAC4C,MAAM,CAAC,CAAC,EAAE,OAAO,IAAIjC,IAAI,CAACX,CAAC,CAAC;EAEnD6B,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;EACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EAClB6C,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,GAAG/C,IAAI,CAAC2B,GAAG,CAACT,CAAC,CAACK,CAAC,EAAEL,CAAC,CAAC+B,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;EAC/CpB,IAAI,CAAC7C,QAAQ,GAAG,CAAC;EACjBsG,GAAG,GAAGpE,CAAC,CAACuB,CAAC,CAAC7B,MAAM;EAEhB,IAAI0E,GAAG,GAAG,CAAC,EAAE;IACXpE,CAAC,GAAGuE,YAAY,CAAC5D,IAAI,EAAE,CAAC,EAAEX,CAAC,EAAEA,CAAC,EAAE,IAAI,CAAC;EACvC,CAAC,MAAM;IAEL;IACA;IACA;;IAEA;IACA;IACA;;IAEA;IACAU,CAAC,GAAG,GAAG,GAAG5B,IAAI,CAAC+F,IAAI,CAACT,GAAG,CAAC;IACxB1D,CAAC,GAAGA,CAAC,GAAG,EAAE,GAAG,EAAE,GAAGA,CAAC,GAAG,CAAC;IAEvBV,CAAC,GAAGA,CAAC,CAACkD,KAAK,CAAC,CAAC,GAAGoB,OAAO,CAAC,CAAC,EAAE5D,CAAC,CAAC,CAAC;IAC9BV,CAAC,GAAGuE,YAAY,CAAC5D,IAAI,EAAE,CAAC,EAAEX,CAAC,EAAEA,CAAC,EAAE,IAAI,CAAC;;IAErC;IACA,IAAI8E,OAAO;MACTC,EAAE,GAAG,IAAIpE,IAAI,CAAC,CAAC,CAAC;MAChBqE,GAAG,GAAG,IAAIrE,IAAI,CAAC,EAAE,CAAC;MAClBsE,GAAG,GAAG,IAAItE,IAAI,CAAC,EAAE,CAAC;IACpB,OAAOD,CAAC,EAAE,GAAG;MACXoE,OAAO,GAAG9E,CAAC,CAACkD,KAAK,CAAClD,CAAC,CAAC;MACpBA,CAAC,GAAGA,CAAC,CAACkD,KAAK,CAAC6B,EAAE,CAAC5B,IAAI,CAAC2B,OAAO,CAAC5B,KAAK,CAAC8B,GAAG,CAAC9B,KAAK,CAAC4B,OAAO,CAAC,CAAC3B,IAAI,CAAC8B,GAAG,CAAC,CAAC,CAAC,CAAC;IACnE;EACF;EAEAtE,IAAI,CAAC9C,SAAS,GAAGgE,EAAE;EACnBlB,IAAI,CAAC7C,QAAQ,GAAGgE,EAAE;EAElB,OAAO3B,QAAQ,CAACH,CAAC,EAAE6B,EAAE,EAAEC,EAAE,EAAE,IAAI,CAAC;AAClC,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,CAAC,CAACsF,iBAAiB,GAAGtF,CAAC,CAACuF,IAAI,GAAG,YAAY;EACzC,IAAItD,EAAE;IAAEC,EAAE;IACR9B,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,IAAI,CAACD,CAAC,CAAC2C,QAAQ,CAAC,CAAC,EAAE,OAAO,IAAIhC,IAAI,CAACX,CAAC,CAACE,CAAC,CAAC;EACvC,IAAIF,CAAC,CAAC4C,MAAM,CAAC,CAAC,EAAE,OAAO,IAAIjC,IAAI,CAACX,CAAC,CAAC;EAElC6B,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;EACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EAClB6C,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,GAAG,CAAC;EACvBlB,IAAI,CAAC7C,QAAQ,GAAG,CAAC;EAEjB,OAAOsF,MAAM,CAACpD,CAAC,CAAC4E,IAAI,CAAC,CAAC,EAAE5E,CAAC,CAACmE,IAAI,CAAC,CAAC,EAAExD,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,EAAElB,IAAI,CAAC7C,QAAQ,GAAGgE,EAAE,CAAC;AAC5E,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,CAAC,CAACwF,aAAa,GAAGxF,CAAC,CAACyF,IAAI,GAAG,YAAY;EACrC,IAAIrF,CAAC,GAAG,IAAI;IACVW,IAAI,GAAGX,CAAC,CAACC,WAAW;IACpBS,CAAC,GAAGV,CAAC,CAACD,GAAG,CAAC,CAAC,CAACgB,GAAG,CAAC,CAAC,CAAC;IAClBc,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;IACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EAEpB,IAAI4C,CAAC,KAAK,CAAC,CAAC,EAAE;IACZ,OAAOA,CAAC,KAAK;IACX;IAAA,EACEV,CAAC,CAACsF,KAAK,CAAC,CAAC,GAAGC,KAAK,CAAC5E,IAAI,EAAEkB,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAInB,IAAI,CAAC,CAAC;IAC9C;IAAA,EACE,IAAIA,IAAI,CAACC,GAAG,CAAC;EACnB;EAEA,IAAIZ,CAAC,CAAC4C,MAAM,CAAC,CAAC,EAAE,OAAO2C,KAAK,CAAC5E,IAAI,EAAEkB,EAAE,GAAG,CAAC,EAAEC,EAAE,CAAC,CAACoB,KAAK,CAAC,GAAG,CAAC;;EAEzD;;EAEAvC,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,GAAG,CAAC;EACvBlB,IAAI,CAAC7C,QAAQ,GAAG,CAAC;;EAEjB;EACAkC,CAAC,GAAG,IAAIW,IAAI,CAAC,CAAC,CAAC,CAAC+D,KAAK,CAAC1E,CAAC,CAAC,CAAC2D,GAAG,CAAC3D,CAAC,CAACmD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0B,IAAI,CAAC,CAAC,CAACW,IAAI,CAAC,CAAC;EAErD7E,IAAI,CAAC9C,SAAS,GAAGgE,EAAE;EACnBlB,IAAI,CAAC7C,QAAQ,GAAGgE,EAAE;EAElB,OAAO9B,CAAC,CAACkD,KAAK,CAAC,CAAC,CAAC;AACnB,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAtD,CAAC,CAAC6F,uBAAuB,GAAG7F,CAAC,CAAC8F,KAAK,GAAG,YAAY;EAChD,IAAI7D,EAAE;IAAEC,EAAE;IACR9B,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,IAAID,CAAC,CAAC2F,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,IAAIhF,IAAI,CAACX,CAAC,CAACqD,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGzC,GAAG,CAAC;EAChD,IAAI,CAACZ,CAAC,CAAC2C,QAAQ,CAAC,CAAC,EAAE,OAAO,IAAIhC,IAAI,CAACX,CAAC,CAAC;EAErC6B,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;EACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EAClB6C,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,GAAG/C,IAAI,CAAC2B,GAAG,CAAC3B,IAAI,CAACiB,GAAG,CAACC,CAAC,CAACK,CAAC,CAAC,EAAEL,CAAC,CAAC+B,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;EACzDpB,IAAI,CAAC7C,QAAQ,GAAG,CAAC;EACjBS,QAAQ,GAAG,KAAK;EAEhByB,CAAC,GAAGA,CAAC,CAACkD,KAAK,CAAClD,CAAC,CAAC,CAAC0E,KAAK,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC1B,IAAI,CAACnD,CAAC,CAAC;EAEtCzB,QAAQ,GAAG,IAAI;EACfoC,IAAI,CAAC9C,SAAS,GAAGgE,EAAE;EACnBlB,IAAI,CAAC7C,QAAQ,GAAGgE,EAAE;EAElB,OAAO9B,CAAC,CAAC4F,EAAE,CAAC,CAAC;AACf,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAhG,CAAC,CAACiG,qBAAqB,GAAGjG,CAAC,CAACkG,KAAK,GAAG,YAAY;EAC9C,IAAIjE,EAAE;IAAEC,EAAE;IACR9B,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,IAAI,CAACD,CAAC,CAAC2C,QAAQ,CAAC,CAAC,IAAI3C,CAAC,CAAC4C,MAAM,CAAC,CAAC,EAAE,OAAO,IAAIjC,IAAI,CAACX,CAAC,CAAC;EAEnD6B,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;EACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EAClB6C,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,GAAG,CAAC,GAAG/C,IAAI,CAAC2B,GAAG,CAAC3B,IAAI,CAACiB,GAAG,CAACC,CAAC,CAACK,CAAC,CAAC,EAAEL,CAAC,CAAC+B,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;EAC7DpB,IAAI,CAAC7C,QAAQ,GAAG,CAAC;EACjBS,QAAQ,GAAG,KAAK;EAEhByB,CAAC,GAAGA,CAAC,CAACkD,KAAK,CAAClD,CAAC,CAAC,CAACmD,IAAI,CAAC,CAAC,CAAC,CAAC0B,IAAI,CAAC,CAAC,CAAC1B,IAAI,CAACnD,CAAC,CAAC;EAErCzB,QAAQ,GAAG,IAAI;EACfoC,IAAI,CAAC9C,SAAS,GAAGgE,EAAE;EACnBlB,IAAI,CAAC7C,QAAQ,GAAGgE,EAAE;EAElB,OAAO9B,CAAC,CAAC4F,EAAE,CAAC,CAAC;AACf,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAhG,CAAC,CAACmG,wBAAwB,GAAGnG,CAAC,CAACoG,KAAK,GAAG,YAAY;EACjD,IAAInE,EAAE;IAAEC,EAAE;IAAEmE,GAAG;IAAEC,GAAG;IAClBlG,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,IAAI,CAACD,CAAC,CAAC2C,QAAQ,CAAC,CAAC,EAAE,OAAO,IAAIhC,IAAI,CAACC,GAAG,CAAC;EACvC,IAAIZ,CAAC,CAACK,CAAC,IAAI,CAAC,EAAE,OAAO,IAAIM,IAAI,CAACX,CAAC,CAACD,GAAG,CAAC,CAAC,CAACsD,EAAE,CAAC,CAAC,CAAC,GAAGrD,CAAC,CAACE,CAAC,GAAG,CAAC,GAAGF,CAAC,CAAC4C,MAAM,CAAC,CAAC,GAAG5C,CAAC,GAAGY,GAAG,CAAC;EAE7EiB,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;EACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EAClBoI,GAAG,GAAGlG,CAAC,CAAC+B,EAAE,CAAC,CAAC;EAEZ,IAAIjD,IAAI,CAAC2B,GAAG,CAACyF,GAAG,EAAErE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC7B,CAAC,CAACK,CAAC,GAAG,CAAC,EAAE,OAAOF,QAAQ,CAAC,IAAIQ,IAAI,CAACX,CAAC,CAAC,EAAE6B,EAAE,EAAEC,EAAE,EAAE,IAAI,CAAC;EAEhFnB,IAAI,CAAC9C,SAAS,GAAGoI,GAAG,GAAGC,GAAG,GAAGlG,CAAC,CAACK,CAAC;EAEhCL,CAAC,GAAGoD,MAAM,CAACpD,CAAC,CAACmD,IAAI,CAAC,CAAC,CAAC,EAAE,IAAIxC,IAAI,CAAC,CAAC,CAAC,CAAC+D,KAAK,CAAC1E,CAAC,CAAC,EAAEiG,GAAG,GAAGpE,EAAE,EAAE,CAAC,CAAC;EAExDlB,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,GAAG,CAAC;EACvBlB,IAAI,CAAC7C,QAAQ,GAAG,CAAC;EAEjBkC,CAAC,GAAGA,CAAC,CAAC4F,EAAE,CAAC,CAAC;EAEVjF,IAAI,CAAC9C,SAAS,GAAGgE,EAAE;EACnBlB,IAAI,CAAC7C,QAAQ,GAAGgE,EAAE;EAElB,OAAO9B,CAAC,CAACkD,KAAK,CAAC,GAAG,CAAC;AACrB,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAtD,CAAC,CAACuG,WAAW,GAAGvG,CAAC,CAACwG,IAAI,GAAG,YAAY;EACnC,IAAIC,MAAM;IAAE3F,CAAC;IACXmB,EAAE;IAAEC,EAAE;IACN9B,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,IAAID,CAAC,CAAC4C,MAAM,CAAC,CAAC,EAAE,OAAO,IAAIjC,IAAI,CAACX,CAAC,CAAC;EAElCU,CAAC,GAAGV,CAAC,CAACD,GAAG,CAAC,CAAC,CAACgB,GAAG,CAAC,CAAC,CAAC;EAClBc,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;EACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EAElB,IAAI4C,CAAC,KAAK,CAAC,CAAC,EAAE;IAEZ;IACA,IAAIA,CAAC,KAAK,CAAC,EAAE;MACX2F,MAAM,GAAGd,KAAK,CAAC5E,IAAI,EAAEkB,EAAE,GAAG,CAAC,EAAEC,EAAE,CAAC,CAACoB,KAAK,CAAC,GAAG,CAAC;MAC3CmD,MAAM,CAACnG,CAAC,GAAGF,CAAC,CAACE,CAAC;MACd,OAAOmG,MAAM;IACf;;IAEA;IACA,OAAO,IAAI1F,IAAI,CAACC,GAAG,CAAC;EACtB;;EAEA;;EAEAD,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,GAAG,CAAC;EACvBlB,IAAI,CAAC7C,QAAQ,GAAG,CAAC;EAEjBkC,CAAC,GAAGA,CAAC,CAAC2D,GAAG,CAAC,IAAIhD,IAAI,CAAC,CAAC,CAAC,CAAC+D,KAAK,CAAC1E,CAAC,CAACkD,KAAK,CAAClD,CAAC,CAAC,CAAC,CAAC6E,IAAI,CAAC,CAAC,CAAC1B,IAAI,CAAC,CAAC,CAAC,CAAC,CAACqC,IAAI,CAAC,CAAC;EAE9D7E,IAAI,CAAC9C,SAAS,GAAGgE,EAAE;EACnBlB,IAAI,CAAC7C,QAAQ,GAAGgE,EAAE;EAElB,OAAO9B,CAAC,CAACkD,KAAK,CAAC,CAAC,CAAC;AACnB,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAtD,CAAC,CAAC0G,cAAc,GAAG1G,CAAC,CAAC4F,IAAI,GAAG,YAAY;EACtC,IAAItE,CAAC;IAAEC,CAAC;IAAET,CAAC;IAAE2B,CAAC;IAAEkE,EAAE;IAAE/D,CAAC;IAAEF,CAAC;IAAE2D,GAAG;IAAEO,EAAE;IAC/BxG,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;IACpB4B,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;IACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EAEpB,IAAI,CAACkC,CAAC,CAAC2C,QAAQ,CAAC,CAAC,EAAE;IACjB,IAAI,CAAC3C,CAAC,CAACE,CAAC,EAAE,OAAO,IAAIS,IAAI,CAACC,GAAG,CAAC;IAC9B,IAAIiB,EAAE,GAAG,CAAC,IAAIlC,YAAY,EAAE;MAC1B2C,CAAC,GAAGiD,KAAK,CAAC5E,IAAI,EAAEkB,EAAE,GAAG,CAAC,EAAEC,EAAE,CAAC,CAACoB,KAAK,CAAC,GAAG,CAAC;MACtCZ,CAAC,CAACpC,CAAC,GAAGF,CAAC,CAACE,CAAC;MACT,OAAOoC,CAAC;IACV;EACF,CAAC,MAAM,IAAItC,CAAC,CAAC4C,MAAM,CAAC,CAAC,EAAE;IACrB,OAAO,IAAIjC,IAAI,CAACX,CAAC,CAAC;EACpB,CAAC,MAAM,IAAIA,CAAC,CAACD,GAAG,CAAC,CAAC,CAACsD,EAAE,CAAC,CAAC,CAAC,IAAIxB,EAAE,GAAG,CAAC,IAAIlC,YAAY,EAAE;IAClD2C,CAAC,GAAGiD,KAAK,CAAC5E,IAAI,EAAEkB,EAAE,GAAG,CAAC,EAAEC,EAAE,CAAC,CAACoB,KAAK,CAAC,IAAI,CAAC;IACvCZ,CAAC,CAACpC,CAAC,GAAGF,CAAC,CAACE,CAAC;IACT,OAAOoC,CAAC;EACV;EAEA3B,IAAI,CAAC9C,SAAS,GAAGoI,GAAG,GAAGpE,EAAE,GAAG,EAAE;EAC9BlB,IAAI,CAAC7C,QAAQ,GAAG,CAAC;;EAEjB;;EAEA;EACA;EACA;;EAEA4C,CAAC,GAAG5B,IAAI,CAAC0B,GAAG,CAAC,EAAE,EAAEyF,GAAG,GAAG1G,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;EAExC,KAAK2B,CAAC,GAAGR,CAAC,EAAEQ,CAAC,EAAE,EAAEA,CAAC,EAAElB,CAAC,GAAGA,CAAC,CAAC2D,GAAG,CAAC3D,CAAC,CAACkD,KAAK,CAAClD,CAAC,CAAC,CAACmD,IAAI,CAAC,CAAC,CAAC,CAAC0B,IAAI,CAAC,CAAC,CAAC1B,IAAI,CAAC,CAAC,CAAC,CAAC;EAEhE5E,QAAQ,GAAG,KAAK;EAEhB4C,CAAC,GAAGrC,IAAI,CAACsB,IAAI,CAAC6F,GAAG,GAAG1G,QAAQ,CAAC;EAC7B8C,CAAC,GAAG,CAAC;EACLmE,EAAE,GAAGxG,CAAC,CAACkD,KAAK,CAAClD,CAAC,CAAC;EACfsC,CAAC,GAAG,IAAI3B,IAAI,CAACX,CAAC,CAAC;EACfuG,EAAE,GAAGvG,CAAC;;EAEN;EACA,OAAOkB,CAAC,KAAK,CAAC,CAAC,GAAG;IAChBqF,EAAE,GAAGA,EAAE,CAACrD,KAAK,CAACsD,EAAE,CAAC;IACjBhE,CAAC,GAAGF,CAAC,CAACoC,KAAK,CAAC6B,EAAE,CAAC5C,GAAG,CAACtB,CAAC,IAAI,CAAC,CAAC,CAAC;IAE3BkE,EAAE,GAAGA,EAAE,CAACrD,KAAK,CAACsD,EAAE,CAAC;IACjBlE,CAAC,GAAGE,CAAC,CAACW,IAAI,CAACoD,EAAE,CAAC5C,GAAG,CAACtB,CAAC,IAAI,CAAC,CAAC,CAAC;IAE1B,IAAIC,CAAC,CAACf,CAAC,CAACJ,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE,KAAKD,CAAC,GAAGC,CAAC,EAAEmB,CAAC,CAACf,CAAC,CAACL,CAAC,CAAC,KAAKsB,CAAC,CAACjB,CAAC,CAACL,CAAC,CAAC,IAAIA,CAAC,EAAE,EAAE;EAC/D;EAEA,IAAIR,CAAC,EAAE4B,CAAC,GAAGA,CAAC,CAACY,KAAK,CAAC,CAAC,IAAKxC,CAAC,GAAG,CAAE,CAAC;EAEhCnC,QAAQ,GAAG,IAAI;EAEf,OAAO4B,QAAQ,CAACmC,CAAC,EAAE3B,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,EAAElB,IAAI,CAAC7C,QAAQ,GAAGgE,EAAE,EAAE,IAAI,CAAC;AACnE,CAAC;;AAGD;AACA;AACA;AACA;AACAlC,CAAC,CAAC+C,QAAQ,GAAG,YAAY;EACvB,OAAO,CAAC,CAAC,IAAI,CAACpB,CAAC;AACjB,CAAC;;AAGD;AACA;AACA;AACA;AACA3B,CAAC,CAAC6G,SAAS,GAAG7G,CAAC,CAAC8G,KAAK,GAAG,YAAY;EAClC,OAAO,CAAC,CAAC,IAAI,CAACnF,CAAC,IAAI1C,SAAS,CAAC,IAAI,CAACwB,CAAC,GAAGd,QAAQ,CAAC,GAAG,IAAI,CAACgC,CAAC,CAAC7B,MAAM,GAAG,CAAC;AACrE,CAAC;;AAGD;AACA;AACA;AACA;AACAE,CAAC,CAAC+G,KAAK,GAAG,YAAY;EACpB,OAAO,CAAC,IAAI,CAACzG,CAAC;AAChB,CAAC;;AAGD;AACA;AACA;AACA;AACAN,CAAC,CAACgH,UAAU,GAAGhH,CAAC,CAAC0F,KAAK,GAAG,YAAY;EACnC,OAAO,IAAI,CAACpF,CAAC,GAAG,CAAC;AACnB,CAAC;;AAGD;AACA;AACA;AACA;AACAN,CAAC,CAACiH,UAAU,GAAGjH,CAAC,CAACkH,KAAK,GAAG,YAAY;EACnC,OAAO,IAAI,CAAC5G,CAAC,GAAG,CAAC;AACnB,CAAC;;AAGD;AACA;AACA;AACA;AACAN,CAAC,CAACgD,MAAM,GAAG,YAAY;EACrB,OAAO,CAAC,CAAC,IAAI,CAACrB,CAAC,IAAI,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AACpC,CAAC;;AAGD;AACA;AACA;AACA;AACA3B,CAAC,CAACmH,QAAQ,GAAGnH,CAAC,CAACoH,EAAE,GAAG,UAAU/F,CAAC,EAAE;EAC/B,OAAO,IAAI,CAACF,GAAG,CAACE,CAAC,CAAC,GAAG,CAAC;AACxB,CAAC;;AAGD;AACA;AACA;AACA;AACArB,CAAC,CAACqH,iBAAiB,GAAGrH,CAAC,CAAC+F,GAAG,GAAG,UAAU1E,CAAC,EAAE;EACzC,OAAO,IAAI,CAACF,GAAG,CAACE,CAAC,CAAC,GAAG,CAAC;AACxB,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACArB,CAAC,CAACsH,SAAS,GAAGtH,CAAC,CAACuH,GAAG,GAAG,UAAUC,IAAI,EAAE;EACpC,IAAIC,QAAQ;IAAE9F,CAAC;IAAE+F,WAAW;IAAE5G,CAAC;IAAE6G,GAAG;IAAEC,GAAG;IAAEzF,EAAE;IAAEO,CAAC;IAC9CmF,GAAG,GAAG,IAAI;IACV9G,IAAI,GAAG8G,GAAG,CAACxH,WAAW;IACtB4B,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;IACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;IAClB4J,KAAK,GAAG,CAAC;;EAEX;EACA,IAAIN,IAAI,IAAI,IAAI,EAAE;IAChBA,IAAI,GAAG,IAAIzG,IAAI,CAAC,EAAE,CAAC;IACnB0G,QAAQ,GAAG,IAAI;EACjB,CAAC,MAAM;IACLD,IAAI,GAAG,IAAIzG,IAAI,CAACyG,IAAI,CAAC;IACrB7F,CAAC,GAAG6F,IAAI,CAAC7F,CAAC;;IAEV;IACA,IAAI6F,IAAI,CAAClH,CAAC,GAAG,CAAC,IAAI,CAACqB,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI6F,IAAI,CAAC/D,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI1C,IAAI,CAACC,GAAG,CAAC;IAEjEyG,QAAQ,GAAGD,IAAI,CAAC/D,EAAE,CAAC,EAAE,CAAC;EACxB;EAEA9B,CAAC,GAAGkG,GAAG,CAAClG,CAAC;;EAET;EACA,IAAIkG,GAAG,CAACvH,CAAC,GAAG,CAAC,IAAI,CAACqB,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAIkG,GAAG,CAACpE,EAAE,CAAC,CAAC,CAAC,EAAE;IACzC,OAAO,IAAI1C,IAAI,CAACY,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGkG,GAAG,CAACvH,CAAC,IAAI,CAAC,GAAGU,GAAG,GAAGW,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACzE;;EAEA;EACA;EACA,IAAI8F,QAAQ,EAAE;IACZ,IAAI9F,CAAC,CAAC7B,MAAM,GAAG,CAAC,EAAE;MAChB6H,GAAG,GAAG,IAAI;IACZ,CAAC,MAAM;MACL,KAAK7G,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC,EAAEb,CAAC,GAAG,EAAE,KAAK,CAAC,GAAGA,CAAC,IAAI,EAAE;MACrC6G,GAAG,GAAG7G,CAAC,KAAK,CAAC;IACf;EACF;EAEAnC,QAAQ,GAAG,KAAK;EAChBwD,EAAE,GAAGF,EAAE,GAAG6F,KAAK;EACfF,GAAG,GAAGG,gBAAgB,CAACF,GAAG,EAAE1F,EAAE,CAAC;EAC/BuF,WAAW,GAAGD,QAAQ,GAAGO,OAAO,CAACjH,IAAI,EAAEoB,EAAE,GAAG,EAAE,CAAC,GAAG4F,gBAAgB,CAACP,IAAI,EAAErF,EAAE,CAAC;;EAE5E;EACAO,CAAC,GAAGc,MAAM,CAACoE,GAAG,EAAEF,WAAW,EAAEvF,EAAE,EAAE,CAAC,CAAC;;EAEnC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI8F,mBAAmB,CAACvF,CAAC,CAACf,CAAC,EAAEb,CAAC,GAAGmB,EAAE,EAAEC,EAAE,CAAC,EAAE;IAExC,GAAG;MACDC,EAAE,IAAI,EAAE;MACRyF,GAAG,GAAGG,gBAAgB,CAACF,GAAG,EAAE1F,EAAE,CAAC;MAC/BuF,WAAW,GAAGD,QAAQ,GAAGO,OAAO,CAACjH,IAAI,EAAEoB,EAAE,GAAG,EAAE,CAAC,GAAG4F,gBAAgB,CAACP,IAAI,EAAErF,EAAE,CAAC;MAC5EO,CAAC,GAAGc,MAAM,CAACoE,GAAG,EAAEF,WAAW,EAAEvF,EAAE,EAAE,CAAC,CAAC;MAEnC,IAAI,CAACwF,GAAG,EAAE;QAER;QACA,IAAI,CAAC1E,cAAc,CAACP,CAAC,CAACf,CAAC,CAAC,CAACwB,KAAK,CAACrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE;UACzD4B,CAAC,GAAGnC,QAAQ,CAACmC,CAAC,EAAET,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;QAC5B;QAEA;MACF;IACF,CAAC,QAAQgG,mBAAmB,CAACvF,CAAC,CAACf,CAAC,EAAEb,CAAC,IAAI,EAAE,EAAEoB,EAAE,CAAC;EAChD;EAEAvD,QAAQ,GAAG,IAAI;EAEf,OAAO4B,QAAQ,CAACmC,CAAC,EAAET,EAAE,EAAEC,EAAE,CAAC;AAC5B,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,CAAC,CAAC8E,KAAK,GAAG9E,CAAC,CAACkI,GAAG,GAAG,UAAU7G,CAAC,EAAE;EAC7B,IAAIM,CAAC;IAAElB,CAAC;IAAEa,CAAC;IAAEC,CAAC;IAAET,CAAC;IAAE0D,GAAG;IAAEvC,EAAE;IAAEC,EAAE;IAAER,EAAE;IAAEyG,EAAE;IAAEC,IAAI;IAAExG,EAAE;IAC9CxB,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtBgB,CAAC,GAAG,IAAIN,IAAI,CAACM,CAAC,CAAC;;EAEf;EACA,IAAI,CAACjB,CAAC,CAACuB,CAAC,IAAI,CAACN,CAAC,CAACM,CAAC,EAAE;IAEhB;IACA,IAAI,CAACvB,CAAC,CAACE,CAAC,IAAI,CAACe,CAAC,CAACf,CAAC,EAAEe,CAAC,GAAG,IAAIN,IAAI,CAACC,GAAG,CAAC;;IAEnC;IAAA,KACK,IAAIZ,CAAC,CAACuB,CAAC,EAAEN,CAAC,CAACf,CAAC,GAAG,CAACe,CAAC,CAACf,CAAC;;IAExB;IACA;IACA;IAAA,KACKe,CAAC,GAAG,IAAIN,IAAI,CAACM,CAAC,CAACM,CAAC,IAAIvB,CAAC,CAACE,CAAC,KAAKe,CAAC,CAACf,CAAC,GAAGF,CAAC,GAAGY,GAAG,CAAC;IAE/C,OAAOK,CAAC;EACV;;EAEA;EACA,IAAIjB,CAAC,CAACE,CAAC,IAAIe,CAAC,CAACf,CAAC,EAAE;IACde,CAAC,CAACf,CAAC,GAAG,CAACe,CAAC,CAACf,CAAC;IACV,OAAOF,CAAC,CAACmD,IAAI,CAAClC,CAAC,CAAC;EAClB;EAEAK,EAAE,GAAGtB,CAAC,CAACuB,CAAC;EACRC,EAAE,GAAGP,CAAC,CAACM,CAAC;EACRM,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;EACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;;EAElB;EACA,IAAI,CAACwD,EAAE,CAAC,CAAC,CAAC,IAAI,CAACE,EAAE,CAAC,CAAC,CAAC,EAAE;IAEpB;IACA,IAAIA,EAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACf,CAAC,GAAG,CAACe,CAAC,CAACf,CAAC;;IAErB;IAAA,KACK,IAAIoB,EAAE,CAAC,CAAC,CAAC,EAAEL,CAAC,GAAG,IAAIN,IAAI,CAACX,CAAC,CAAC;;IAE/B;IACA;IAAA,KACK,OAAO,IAAIW,IAAI,CAACmB,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAEvC,OAAOvD,QAAQ,GAAG4B,QAAQ,CAACc,CAAC,EAAEY,EAAE,EAAEC,EAAE,CAAC,GAAGb,CAAC;EAC3C;;EAEA;;EAEA;EACAZ,CAAC,GAAGxB,SAAS,CAACoC,CAAC,CAACZ,CAAC,GAAGd,QAAQ,CAAC;EAC7BwI,EAAE,GAAGlJ,SAAS,CAACmB,CAAC,CAACK,CAAC,GAAGd,QAAQ,CAAC;EAE9B+B,EAAE,GAAGA,EAAE,CAACyB,KAAK,CAAC,CAAC;EACfrC,CAAC,GAAGqH,EAAE,GAAG1H,CAAC;;EAEV;EACA,IAAIK,CAAC,EAAE;IACLsH,IAAI,GAAGtH,CAAC,GAAG,CAAC;IAEZ,IAAIsH,IAAI,EAAE;MACRzG,CAAC,GAAGD,EAAE;MACNZ,CAAC,GAAG,CAACA,CAAC;MACN0D,GAAG,GAAG5C,EAAE,CAAC9B,MAAM;IACjB,CAAC,MAAM;MACL6B,CAAC,GAAGC,EAAE;MACNnB,CAAC,GAAG0H,EAAE;MACN3D,GAAG,GAAG9C,EAAE,CAAC5B,MAAM;IACjB;;IAEA;IACA;IACA;IACAwB,CAAC,GAAGpC,IAAI,CAAC2B,GAAG,CAAC3B,IAAI,CAACsB,IAAI,CAACyB,EAAE,GAAGtC,QAAQ,CAAC,EAAE6E,GAAG,CAAC,GAAG,CAAC;IAE/C,IAAI1D,CAAC,GAAGQ,CAAC,EAAE;MACTR,CAAC,GAAGQ,CAAC;MACLK,CAAC,CAAC7B,MAAM,GAAG,CAAC;IACd;;IAEA;IACA6B,CAAC,CAAC0G,OAAO,CAAC,CAAC;IACX,KAAK/G,CAAC,GAAGR,CAAC,EAAEQ,CAAC,EAAE,GAAGK,CAAC,CAAC2G,IAAI,CAAC,CAAC,CAAC;IAC3B3G,CAAC,CAAC0G,OAAO,CAAC,CAAC;;IAEb;EACA,CAAC,MAAM;IAEL;;IAEA/G,CAAC,GAAGI,EAAE,CAAC5B,MAAM;IACb0E,GAAG,GAAG5C,EAAE,CAAC9B,MAAM;IACfsI,IAAI,GAAG9G,CAAC,GAAGkD,GAAG;IACd,IAAI4D,IAAI,EAAE5D,GAAG,GAAGlD,CAAC;IAEjB,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkD,GAAG,EAAElD,CAAC,EAAE,EAAE;MACxB,IAAII,EAAE,CAACJ,CAAC,CAAC,IAAIM,EAAE,CAACN,CAAC,CAAC,EAAE;QAClB8G,IAAI,GAAG1G,EAAE,CAACJ,CAAC,CAAC,GAAGM,EAAE,CAACN,CAAC,CAAC;QACpB;MACF;IACF;IAEAR,CAAC,GAAG,CAAC;EACP;EAEA,IAAIsH,IAAI,EAAE;IACRzG,CAAC,GAAGD,EAAE;IACNA,EAAE,GAAGE,EAAE;IACPA,EAAE,GAAGD,CAAC;IACNN,CAAC,CAACf,CAAC,GAAG,CAACe,CAAC,CAACf,CAAC;EACZ;EAEAkE,GAAG,GAAG9C,EAAE,CAAC5B,MAAM;;EAEf;EACA;EACA,KAAKwB,CAAC,GAAGM,EAAE,CAAC9B,MAAM,GAAG0E,GAAG,EAAElD,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAEI,EAAE,CAAC8C,GAAG,EAAE,CAAC,GAAG,CAAC;;EAEnD;EACA,KAAKlD,CAAC,GAAGM,EAAE,CAAC9B,MAAM,EAAEwB,CAAC,GAAGR,CAAC,GAAG;IAE1B,IAAIY,EAAE,CAAC,EAAEJ,CAAC,CAAC,GAAGM,EAAE,CAACN,CAAC,CAAC,EAAE;MACnB,KAAKC,CAAC,GAAGD,CAAC,EAAEC,CAAC,IAAIG,EAAE,CAAC,EAAEH,CAAC,CAAC,KAAK,CAAC,GAAGG,EAAE,CAACH,CAAC,CAAC,GAAG7B,IAAI,GAAG,CAAC;MACjD,EAAEgC,EAAE,CAACH,CAAC,CAAC;MACPG,EAAE,CAACJ,CAAC,CAAC,IAAI5B,IAAI;IACf;IAEAgC,EAAE,CAACJ,CAAC,CAAC,IAAIM,EAAE,CAACN,CAAC,CAAC;EAChB;;EAEA;EACA,OAAOI,EAAE,CAAC,EAAE8C,GAAG,CAAC,KAAK,CAAC,GAAG9C,EAAE,CAAC6G,GAAG,CAAC,CAAC;;EAEjC;EACA,OAAO7G,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAEA,EAAE,CAAC8G,KAAK,CAAC,CAAC,EAAE,EAAE/H,CAAC;;EAEnC;EACA,IAAI,CAACiB,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,IAAIX,IAAI,CAACmB,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAE9Cb,CAAC,CAACM,CAAC,GAAGD,EAAE;EACRL,CAAC,CAACZ,CAAC,GAAGgI,iBAAiB,CAAC/G,EAAE,EAAEjB,CAAC,CAAC;EAE9B,OAAO9B,QAAQ,GAAG4B,QAAQ,CAACc,CAAC,EAAEY,EAAE,EAAEC,EAAE,CAAC,GAAGb,CAAC;AAC3C,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACArB,CAAC,CAAC7B,MAAM,GAAG6B,CAAC,CAAC0I,GAAG,GAAG,UAAUrH,CAAC,EAAE;EAC9B,IAAIsH,CAAC;IACHvI,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtBgB,CAAC,GAAG,IAAIN,IAAI,CAACM,CAAC,CAAC;;EAEf;EACA,IAAI,CAACjB,CAAC,CAACuB,CAAC,IAAI,CAACN,CAAC,CAACf,CAAC,IAAIe,CAAC,CAACM,CAAC,IAAI,CAACN,CAAC,CAACM,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAIZ,IAAI,CAACC,GAAG,CAAC;;EAExD;EACA,IAAI,CAACK,CAAC,CAACM,CAAC,IAAIvB,CAAC,CAACuB,CAAC,IAAI,CAACvB,CAAC,CAACuB,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1B,OAAOpB,QAAQ,CAAC,IAAIQ,IAAI,CAACX,CAAC,CAAC,EAAEW,IAAI,CAAC9C,SAAS,EAAE8C,IAAI,CAAC7C,QAAQ,CAAC;EAC7D;;EAEA;EACAS,QAAQ,GAAG,KAAK;EAEhB,IAAIoC,IAAI,CAAC5C,MAAM,IAAI,CAAC,EAAE;IAEpB;IACA;IACAwK,CAAC,GAAGnF,MAAM,CAACpD,CAAC,EAAEiB,CAAC,CAAClB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/BwI,CAAC,CAACrI,CAAC,IAAIe,CAAC,CAACf,CAAC;EACZ,CAAC,MAAM;IACLqI,CAAC,GAAGnF,MAAM,CAACpD,CAAC,EAAEiB,CAAC,EAAE,CAAC,EAAEN,IAAI,CAAC5C,MAAM,EAAE,CAAC,CAAC;EACrC;EAEAwK,CAAC,GAAGA,CAAC,CAACrF,KAAK,CAACjC,CAAC,CAAC;EAEd1C,QAAQ,GAAG,IAAI;EAEf,OAAOyB,CAAC,CAAC0E,KAAK,CAAC6D,CAAC,CAAC;AACnB,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA3I,CAAC,CAAC4I,kBAAkB,GAAG5I,CAAC,CAAC6I,GAAG,GAAG,YAAY;EACzC,OAAOD,kBAAkB,CAAC,IAAI,CAAC;AACjC,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA5I,CAAC,CAAC+H,gBAAgB,GAAG/H,CAAC,CAACgG,EAAE,GAAG,YAAY;EACtC,OAAO+B,gBAAgB,CAAC,IAAI,CAAC;AAC/B,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA/H,CAAC,CAAC8I,OAAO,GAAG9I,CAAC,CAACqC,GAAG,GAAG,YAAY;EAC9B,IAAIjC,CAAC,GAAG,IAAI,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC;EAClCD,CAAC,CAACE,CAAC,GAAG,CAACF,CAAC,CAACE,CAAC;EACV,OAAOC,QAAQ,CAACH,CAAC,CAAC;AACpB,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAJ,CAAC,CAACuD,IAAI,GAAGvD,CAAC,CAAC+I,GAAG,GAAG,UAAU1H,CAAC,EAAE;EAC5B,IAAI2H,KAAK;IAAErH,CAAC;IAAElB,CAAC;IAAEa,CAAC;IAAER,CAAC;IAAE0D,GAAG;IAAEvC,EAAE;IAAEC,EAAE;IAAER,EAAE;IAAEE,EAAE;IACxCxB,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtBgB,CAAC,GAAG,IAAIN,IAAI,CAACM,CAAC,CAAC;;EAEf;EACA,IAAI,CAACjB,CAAC,CAACuB,CAAC,IAAI,CAACN,CAAC,CAACM,CAAC,EAAE;IAEhB;IACA,IAAI,CAACvB,CAAC,CAACE,CAAC,IAAI,CAACe,CAAC,CAACf,CAAC,EAAEe,CAAC,GAAG,IAAIN,IAAI,CAACC,GAAG,CAAC;;IAEnC;IACA;IACA;IACA;IAAA,KACK,IAAI,CAACZ,CAAC,CAACuB,CAAC,EAAEN,CAAC,GAAG,IAAIN,IAAI,CAACM,CAAC,CAACM,CAAC,IAAIvB,CAAC,CAACE,CAAC,KAAKe,CAAC,CAACf,CAAC,GAAGF,CAAC,GAAGY,GAAG,CAAC;IAEzD,OAAOK,CAAC;EACV;;EAEC;EACD,IAAIjB,CAAC,CAACE,CAAC,IAAIe,CAAC,CAACf,CAAC,EAAE;IACde,CAAC,CAACf,CAAC,GAAG,CAACe,CAAC,CAACf,CAAC;IACV,OAAOF,CAAC,CAAC0E,KAAK,CAACzD,CAAC,CAAC;EACnB;EAEAK,EAAE,GAAGtB,CAAC,CAACuB,CAAC;EACRC,EAAE,GAAGP,CAAC,CAACM,CAAC;EACRM,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;EACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;;EAElB;EACA,IAAI,CAACwD,EAAE,CAAC,CAAC,CAAC,IAAI,CAACE,EAAE,CAAC,CAAC,CAAC,EAAE;IAEpB;IACA;IACA,IAAI,CAACA,EAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,GAAG,IAAIN,IAAI,CAACX,CAAC,CAAC;IAE3B,OAAOzB,QAAQ,GAAG4B,QAAQ,CAACc,CAAC,EAAEY,EAAE,EAAEC,EAAE,CAAC,GAAGb,CAAC;EAC3C;;EAEA;;EAEA;EACAP,CAAC,GAAG7B,SAAS,CAACmB,CAAC,CAACK,CAAC,GAAGd,QAAQ,CAAC;EAC7Bc,CAAC,GAAGxB,SAAS,CAACoC,CAAC,CAACZ,CAAC,GAAGd,QAAQ,CAAC;EAE7B+B,EAAE,GAAGA,EAAE,CAACyB,KAAK,CAAC,CAAC;EACf7B,CAAC,GAAGR,CAAC,GAAGL,CAAC;;EAET;EACA,IAAIa,CAAC,EAAE;IAEL,IAAIA,CAAC,GAAG,CAAC,EAAE;MACTK,CAAC,GAAGD,EAAE;MACNJ,CAAC,GAAG,CAACA,CAAC;MACNkD,GAAG,GAAG5C,EAAE,CAAC9B,MAAM;IACjB,CAAC,MAAM;MACL6B,CAAC,GAAGC,EAAE;MACNnB,CAAC,GAAGK,CAAC;MACL0D,GAAG,GAAG9C,EAAE,CAAC5B,MAAM;IACjB;;IAEA;IACAgB,CAAC,GAAG5B,IAAI,CAACsB,IAAI,CAACyB,EAAE,GAAGtC,QAAQ,CAAC;IAC5B6E,GAAG,GAAG1D,CAAC,GAAG0D,GAAG,GAAG1D,CAAC,GAAG,CAAC,GAAG0D,GAAG,GAAG,CAAC;IAE/B,IAAIlD,CAAC,GAAGkD,GAAG,EAAE;MACXlD,CAAC,GAAGkD,GAAG;MACP7C,CAAC,CAAC7B,MAAM,GAAG,CAAC;IACd;;IAEA;IACA6B,CAAC,CAAC0G,OAAO,CAAC,CAAC;IACX,OAAO/G,CAAC,EAAE,GAAGK,CAAC,CAAC2G,IAAI,CAAC,CAAC,CAAC;IACtB3G,CAAC,CAAC0G,OAAO,CAAC,CAAC;EACb;EAEA7D,GAAG,GAAG9C,EAAE,CAAC5B,MAAM;EACfwB,CAAC,GAAGM,EAAE,CAAC9B,MAAM;;EAEb;EACA,IAAI0E,GAAG,GAAGlD,CAAC,GAAG,CAAC,EAAE;IACfA,CAAC,GAAGkD,GAAG;IACP7C,CAAC,GAAGC,EAAE;IACNA,EAAE,GAAGF,EAAE;IACPA,EAAE,GAAGC,CAAC;EACR;;EAEA;EACA,KAAKqH,KAAK,GAAG,CAAC,EAAE1H,CAAC,GAAG;IAClB0H,KAAK,GAAG,CAACtH,EAAE,CAAC,EAAEJ,CAAC,CAAC,GAAGI,EAAE,CAACJ,CAAC,CAAC,GAAGM,EAAE,CAACN,CAAC,CAAC,GAAG0H,KAAK,IAAItJ,IAAI,GAAG,CAAC;IACpDgC,EAAE,CAACJ,CAAC,CAAC,IAAI5B,IAAI;EACf;EAEA,IAAIsJ,KAAK,EAAE;IACTtH,EAAE,CAACuH,OAAO,CAACD,KAAK,CAAC;IACjB,EAAEvI,CAAC;EACL;;EAEA;EACA;EACA,KAAK+D,GAAG,GAAG9C,EAAE,CAAC5B,MAAM,EAAE4B,EAAE,CAAC,EAAE8C,GAAG,CAAC,IAAI,CAAC,GAAG9C,EAAE,CAAC6G,GAAG,CAAC,CAAC;EAE/ClH,CAAC,CAACM,CAAC,GAAGD,EAAE;EACRL,CAAC,CAACZ,CAAC,GAAGgI,iBAAiB,CAAC/G,EAAE,EAAEjB,CAAC,CAAC;EAE9B,OAAO9B,QAAQ,GAAG4B,QAAQ,CAACc,CAAC,EAAEY,EAAE,EAAEC,EAAE,CAAC,GAAGb,CAAC;AAC3C,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACArB,CAAC,CAAC/B,SAAS,GAAG+B,CAAC,CAACmC,EAAE,GAAG,UAAU+G,CAAC,EAAE;EAChC,IAAIpI,CAAC;IACHV,CAAC,GAAG,IAAI;EAEV,IAAI8I,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,CAACA,CAAC,IAAIA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,EAAE,MAAMhI,KAAK,CAACrC,eAAe,GAAGqK,CAAC,CAAC;EAErF,IAAI9I,CAAC,CAACuB,CAAC,EAAE;IACPb,CAAC,GAAGqI,YAAY,CAAC/I,CAAC,CAACuB,CAAC,CAAC;IACrB,IAAIuH,CAAC,IAAI9I,CAAC,CAACK,CAAC,GAAG,CAAC,GAAGK,CAAC,EAAEA,CAAC,GAAGV,CAAC,CAACK,CAAC,GAAG,CAAC;EACnC,CAAC,MAAM;IACLK,CAAC,GAAGE,GAAG;EACT;EAEA,OAAOF,CAAC;AACV,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACAd,CAAC,CAACoJ,KAAK,GAAG,YAAY;EACpB,IAAIhJ,CAAC,GAAG,IAAI;IACVW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,OAAOE,QAAQ,CAAC,IAAIQ,IAAI,CAACX,CAAC,CAAC,EAAEA,CAAC,CAACK,CAAC,GAAG,CAAC,EAAEM,IAAI,CAAC7C,QAAQ,CAAC;AACtD,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA8B,CAAC,CAACqJ,IAAI,GAAGrJ,CAAC,CAACsJ,GAAG,GAAG,YAAY;EAC3B,IAAIrH,EAAE;IAAEC,EAAE;IACR9B,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,IAAI,CAACD,CAAC,CAAC2C,QAAQ,CAAC,CAAC,EAAE,OAAO,IAAIhC,IAAI,CAACC,GAAG,CAAC;EACvC,IAAIZ,CAAC,CAAC4C,MAAM,CAAC,CAAC,EAAE,OAAO,IAAIjC,IAAI,CAACX,CAAC,CAAC;EAElC6B,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;EACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EAClB6C,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,GAAG/C,IAAI,CAAC2B,GAAG,CAACT,CAAC,CAACK,CAAC,EAAEL,CAAC,CAAC+B,EAAE,CAAC,CAAC,CAAC,GAAGxC,QAAQ;EACtDoB,IAAI,CAAC7C,QAAQ,GAAG,CAAC;EAEjBkC,CAAC,GAAGiJ,IAAI,CAACtI,IAAI,EAAEqB,gBAAgB,CAACrB,IAAI,EAAEX,CAAC,CAAC,CAAC;EAEzCW,IAAI,CAAC9C,SAAS,GAAGgE,EAAE;EACnBlB,IAAI,CAAC7C,QAAQ,GAAGgE,EAAE;EAElB,OAAO3B,QAAQ,CAAC7B,QAAQ,GAAG,CAAC,GAAG0B,CAAC,CAACiC,GAAG,CAAC,CAAC,GAAGjC,CAAC,EAAE6B,EAAE,EAAEC,EAAE,EAAE,IAAI,CAAC;AAC3D,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,CAAC,CAACuJ,UAAU,GAAGvJ,CAAC,CAACiF,IAAI,GAAG,YAAY;EAClC,IAAIzC,CAAC;IAAEC,CAAC;IAAEN,EAAE;IAAEO,CAAC;IAAEC,GAAG;IAAEC,CAAC;IACrBxC,CAAC,GAAG,IAAI;IACRuB,CAAC,GAAGvB,CAAC,CAACuB,CAAC;IACPlB,CAAC,GAAGL,CAAC,CAACK,CAAC;IACPH,CAAC,GAAGF,CAAC,CAACE,CAAC;IACPS,IAAI,GAAGX,CAAC,CAACC,WAAW;;EAEtB;EACA,IAAIC,CAAC,KAAK,CAAC,IAAI,CAACqB,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1B,OAAO,IAAIZ,IAAI,CAAC,CAACT,CAAC,IAAIA,CAAC,GAAG,CAAC,KAAK,CAACqB,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGX,GAAG,GAAGW,CAAC,GAAGvB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACpE;EAEAzB,QAAQ,GAAG,KAAK;;EAEhB;EACA2B,CAAC,GAAGpB,IAAI,CAAC+F,IAAI,CAAC,CAAC7E,CAAC,CAAC;;EAEjB;EACA;EACA,IAAIE,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;IACxBmC,CAAC,GAAGQ,cAAc,CAACtB,CAAC,CAAC;IAErB,IAAI,CAACc,CAAC,CAAC3C,MAAM,GAAGW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAEgC,CAAC,IAAI,GAAG;IACrCnC,CAAC,GAAGpB,IAAI,CAAC+F,IAAI,CAACxC,CAAC,CAAC;IAChBhC,CAAC,GAAGxB,SAAS,CAAC,CAACwB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;IAE7C,IAAIH,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;MACdmC,CAAC,GAAG,IAAI,GAAGhC,CAAC;IACd,CAAC,MAAM;MACLgC,CAAC,GAAGnC,CAAC,CAAC4C,aAAa,CAAC,CAAC;MACrBT,CAAC,GAAGA,CAAC,CAACU,KAAK,CAAC,CAAC,EAAEV,CAAC,CAACW,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG3C,CAAC;IACxC;IAEAiC,CAAC,GAAG,IAAI3B,IAAI,CAAC0B,CAAC,CAAC;EACjB,CAAC,MAAM;IACLC,CAAC,GAAG,IAAI3B,IAAI,CAACT,CAAC,CAAC+C,QAAQ,CAAC,CAAC,CAAC;EAC5B;EAEAlB,EAAE,GAAG,CAAC1B,CAAC,GAAGM,IAAI,CAAC9C,SAAS,IAAI,CAAC;;EAE7B;EACA,SAAS;IACP2E,CAAC,GAAGF,CAAC;IACLA,CAAC,GAAGE,CAAC,CAACW,IAAI,CAACC,MAAM,CAACpD,CAAC,EAAEwC,CAAC,EAAET,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAACmB,KAAK,CAAC,GAAG,CAAC;;IAE9C;IACA,IAAIL,cAAc,CAACL,CAAC,CAACjB,CAAC,CAAC,CAACwB,KAAK,CAAC,CAAC,EAAEhB,EAAE,CAAC,KAAK,CAACM,CAAC,GAAGQ,cAAc,CAACP,CAAC,CAACf,CAAC,CAAC,EAAEwB,KAAK,CAAC,CAAC,EAAEhB,EAAE,CAAC,EAAE;MAC/EM,CAAC,GAAGA,CAAC,CAACU,KAAK,CAAChB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,CAAC;;MAE3B;MACA;MACA,IAAIM,CAAC,IAAI,MAAM,IAAI,CAACE,GAAG,IAAIF,CAAC,IAAI,MAAM,EAAE;QAEtC;QACA;QACA,IAAI,CAACE,GAAG,EAAE;UACRpC,QAAQ,CAACqC,CAAC,EAAEnC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;UAErB,IAAImC,CAAC,CAACU,KAAK,CAACV,CAAC,CAAC,CAACa,EAAE,CAACrD,CAAC,CAAC,EAAE;YACpBsC,CAAC,GAAGE,CAAC;YACL;UACF;QACF;QAEAT,EAAE,IAAI,CAAC;QACPQ,GAAG,GAAG,CAAC;MACT,CAAC,MAAM;QAEL;QACA;QACA,IAAI,CAAC,CAACF,CAAC,IAAI,CAAC,CAACA,CAAC,CAACU,KAAK,CAAC,CAAC,CAAC,IAAIV,CAAC,CAACiB,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;UAE7C;UACAnD,QAAQ,CAACmC,CAAC,EAAEjC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;UACrB+B,CAAC,GAAG,CAACE,CAAC,CAACY,KAAK,CAACZ,CAAC,CAAC,CAACe,EAAE,CAACrD,CAAC,CAAC;QACvB;QAEA;MACF;IACF;EACF;EAEAzB,QAAQ,GAAG,IAAI;EAEf,OAAO4B,QAAQ,CAACmC,CAAC,EAAEjC,CAAC,EAAEM,IAAI,CAAC7C,QAAQ,EAAEsE,CAAC,CAAC;AACzC,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAxC,CAAC,CAACwJ,OAAO,GAAGxJ,CAAC,CAACyJ,GAAG,GAAG,YAAY;EAC9B,IAAIxH,EAAE;IAAEC,EAAE;IACR9B,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,IAAI,CAACD,CAAC,CAAC2C,QAAQ,CAAC,CAAC,EAAE,OAAO,IAAIhC,IAAI,CAACC,GAAG,CAAC;EACvC,IAAIZ,CAAC,CAAC4C,MAAM,CAAC,CAAC,EAAE,OAAO,IAAIjC,IAAI,CAACX,CAAC,CAAC;EAElC6B,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;EACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EAClB6C,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,GAAG,EAAE;EACxBlB,IAAI,CAAC7C,QAAQ,GAAG,CAAC;EAEjBkC,CAAC,GAAGA,CAAC,CAACkJ,GAAG,CAAC,CAAC;EACXlJ,CAAC,CAACE,CAAC,GAAG,CAAC;EACPF,CAAC,GAAGoD,MAAM,CAACpD,CAAC,EAAE,IAAIW,IAAI,CAAC,CAAC,CAAC,CAAC+D,KAAK,CAAC1E,CAAC,CAACkD,KAAK,CAAClD,CAAC,CAAC,CAAC,CAAC6E,IAAI,CAAC,CAAC,EAAEhD,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;EAE/DlB,IAAI,CAAC9C,SAAS,GAAGgE,EAAE;EACnBlB,IAAI,CAAC7C,QAAQ,GAAGgE,EAAE;EAElB,OAAO3B,QAAQ,CAAC7B,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,CAAC,GAAG0B,CAAC,CAACiC,GAAG,CAAC,CAAC,GAAGjC,CAAC,EAAE6B,EAAE,EAAEC,EAAE,EAAE,IAAI,CAAC;AAC7E,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,CAAC,CAACsD,KAAK,GAAGtD,CAAC,CAAC0J,GAAG,GAAG,UAAUrI,CAAC,EAAE;EAC7B,IAAI2H,KAAK;IAAEvI,CAAC;IAAEa,CAAC;IAAER,CAAC;IAAE4B,CAAC;IAAEiH,EAAE;IAAE/G,CAAC;IAAEpB,GAAG;IAAEC,GAAG;IACpCrB,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;IACpBqB,EAAE,GAAGtB,CAAC,CAACuB,CAAC;IACRC,EAAE,GAAG,CAACP,CAAC,GAAG,IAAIN,IAAI,CAACM,CAAC,CAAC,EAAEM,CAAC;EAE1BN,CAAC,CAACf,CAAC,IAAIF,CAAC,CAACE,CAAC;;EAET;EACD,IAAI,CAACoB,EAAE,IAAI,CAACA,EAAE,CAAC,CAAC,CAAC,IAAI,CAACE,EAAE,IAAI,CAACA,EAAE,CAAC,CAAC,CAAC,EAAE;IAElC,OAAO,IAAIb,IAAI,CAAC,CAACM,CAAC,CAACf,CAAC,IAAIoB,EAAE,IAAI,CAACA,EAAE,CAAC,CAAC,CAAC,IAAI,CAACE,EAAE,IAAIA,EAAE,IAAI,CAACA,EAAE,CAAC,CAAC,CAAC,IAAI,CAACF;;IAE9D;IACA;IAAA,EACEV;;IAEF;IACA;IAAA,EACE,CAACU,EAAE,IAAI,CAACE,EAAE,GAAGP,CAAC,CAACf,CAAC,GAAG,CAAC,GAAGe,CAAC,CAACf,CAAC,GAAG,CAAC,CAAC;EACrC;EAEAG,CAAC,GAAGxB,SAAS,CAACmB,CAAC,CAACK,CAAC,GAAGd,QAAQ,CAAC,GAAGV,SAAS,CAACoC,CAAC,CAACZ,CAAC,GAAGd,QAAQ,CAAC;EACzD6B,GAAG,GAAGE,EAAE,CAAC5B,MAAM;EACf2B,GAAG,GAAGG,EAAE,CAAC9B,MAAM;;EAEf;EACA,IAAI0B,GAAG,GAAGC,GAAG,EAAE;IACbiB,CAAC,GAAGhB,EAAE;IACNA,EAAE,GAAGE,EAAE;IACPA,EAAE,GAAGc,CAAC;IACNiH,EAAE,GAAGnI,GAAG;IACRA,GAAG,GAAGC,GAAG;IACTA,GAAG,GAAGkI,EAAE;EACV;;EAEA;EACAjH,CAAC,GAAG,EAAE;EACNiH,EAAE,GAAGnI,GAAG,GAAGC,GAAG;EACd,KAAKH,CAAC,GAAGqI,EAAE,EAAErI,CAAC,EAAE,GAAGoB,CAAC,CAAC4F,IAAI,CAAC,CAAC,CAAC;;EAE5B;EACA,KAAKhH,CAAC,GAAGG,GAAG,EAAE,EAAEH,CAAC,IAAI,CAAC,GAAG;IACvB0H,KAAK,GAAG,CAAC;IACT,KAAKlI,CAAC,GAAGU,GAAG,GAAGF,CAAC,EAAER,CAAC,GAAGQ,CAAC,GAAG;MACxBsB,CAAC,GAAGF,CAAC,CAAC5B,CAAC,CAAC,GAAGc,EAAE,CAACN,CAAC,CAAC,GAAGI,EAAE,CAACZ,CAAC,GAAGQ,CAAC,GAAG,CAAC,CAAC,GAAG0H,KAAK;MACxCtG,CAAC,CAAC5B,CAAC,EAAE,CAAC,GAAG8B,CAAC,GAAGlD,IAAI,GAAG,CAAC;MACrBsJ,KAAK,GAAGpG,CAAC,GAAGlD,IAAI,GAAG,CAAC;IACtB;IAEAgD,CAAC,CAAC5B,CAAC,CAAC,GAAG,CAAC4B,CAAC,CAAC5B,CAAC,CAAC,GAAGkI,KAAK,IAAItJ,IAAI,GAAG,CAAC;EAClC;;EAEA;EACA,OAAO,CAACgD,CAAC,CAAC,EAAEiH,EAAE,CAAC,GAAGjH,CAAC,CAAC6F,GAAG,CAAC,CAAC;EAEzB,IAAIS,KAAK,EAAE,EAAEvI,CAAC,CAAC,KACViC,CAAC,CAAC8F,KAAK,CAAC,CAAC;EAEdnH,CAAC,CAACM,CAAC,GAAGe,CAAC;EACPrB,CAAC,CAACZ,CAAC,GAAGgI,iBAAiB,CAAC/F,CAAC,EAAEjC,CAAC,CAAC;EAE7B,OAAO9B,QAAQ,GAAG4B,QAAQ,CAACc,CAAC,EAAEN,IAAI,CAAC9C,SAAS,EAAE8C,IAAI,CAAC7C,QAAQ,CAAC,GAAGmD,CAAC;AAClE,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACArB,CAAC,CAAC4J,QAAQ,GAAG,UAAUzH,EAAE,EAAED,EAAE,EAAE;EAC7B,OAAO2H,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE1H,EAAE,EAAED,EAAE,CAAC;AACxC,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,CAAC,CAAC8J,eAAe,GAAG9J,CAAC,CAAC+J,IAAI,GAAG,UAAUnG,EAAE,EAAE1B,EAAE,EAAE;EAC7C,IAAI9B,CAAC,GAAG,IAAI;IACVW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtBD,CAAC,GAAG,IAAIW,IAAI,CAACX,CAAC,CAAC;EACf,IAAIwD,EAAE,KAAK,KAAK,CAAC,EAAE,OAAOxD,CAAC;EAE3B4J,UAAU,CAACpG,EAAE,EAAE,CAAC,EAAEhG,UAAU,CAAC;EAE7B,IAAIsE,EAAE,KAAK,KAAK,CAAC,EAAEA,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ,CAAC,KACjC8L,UAAU,CAAC9H,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;EAEzB,OAAO3B,QAAQ,CAACH,CAAC,EAAEwD,EAAE,GAAGxD,CAAC,CAACK,CAAC,GAAG,CAAC,EAAEyB,EAAE,CAAC;AACtC,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,CAAC,CAACkD,aAAa,GAAG,UAAUU,EAAE,EAAE1B,EAAE,EAAE;EAClC,IAAI+H,GAAG;IACL7J,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,IAAIuD,EAAE,KAAK,KAAK,CAAC,EAAE;IACjBqG,GAAG,GAAGC,cAAc,CAAC9J,CAAC,EAAE,IAAI,CAAC;EAC/B,CAAC,MAAM;IACL4J,UAAU,CAACpG,EAAE,EAAE,CAAC,EAAEhG,UAAU,CAAC;IAE7B,IAAIsE,EAAE,KAAK,KAAK,CAAC,EAAEA,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ,CAAC,KACjC8L,UAAU,CAAC9H,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;IAEzB9B,CAAC,GAAGG,QAAQ,CAAC,IAAIQ,IAAI,CAACX,CAAC,CAAC,EAAEwD,EAAE,GAAG,CAAC,EAAE1B,EAAE,CAAC;IACrC+H,GAAG,GAAGC,cAAc,CAAC9J,CAAC,EAAE,IAAI,EAAEwD,EAAE,GAAG,CAAC,CAAC;EACvC;EAEA,OAAOxD,CAAC,CAACsF,KAAK,CAAC,CAAC,IAAI,CAACtF,CAAC,CAAC4C,MAAM,CAAC,CAAC,GAAG,GAAG,GAAGiH,GAAG,GAAGA,GAAG;AACnD,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAjK,CAAC,CAACmK,OAAO,GAAG,UAAUvG,EAAE,EAAE1B,EAAE,EAAE;EAC5B,IAAI+H,GAAG;IAAE5I,CAAC;IACRjB,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,IAAIuD,EAAE,KAAK,KAAK,CAAC,EAAE;IACjBqG,GAAG,GAAGC,cAAc,CAAC9J,CAAC,CAAC;EACzB,CAAC,MAAM;IACL4J,UAAU,CAACpG,EAAE,EAAE,CAAC,EAAEhG,UAAU,CAAC;IAE7B,IAAIsE,EAAE,KAAK,KAAK,CAAC,EAAEA,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ,CAAC,KACjC8L,UAAU,CAAC9H,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;IAEzBb,CAAC,GAAGd,QAAQ,CAAC,IAAIQ,IAAI,CAACX,CAAC,CAAC,EAAEwD,EAAE,GAAGxD,CAAC,CAACK,CAAC,GAAG,CAAC,EAAEyB,EAAE,CAAC;IAC3C+H,GAAG,GAAGC,cAAc,CAAC7I,CAAC,EAAE,KAAK,EAAEuC,EAAE,GAAGvC,CAAC,CAACZ,CAAC,GAAG,CAAC,CAAC;EAC9C;;EAEA;EACA;EACA,OAAOL,CAAC,CAACsF,KAAK,CAAC,CAAC,IAAI,CAACtF,CAAC,CAAC4C,MAAM,CAAC,CAAC,GAAG,GAAG,GAAGiH,GAAG,GAAGA,GAAG;AACnD,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAjK,CAAC,CAACoK,UAAU,GAAG,UAAUC,IAAI,EAAE;EAC7B,IAAI1I,CAAC;IAAE2I,EAAE;IAAEC,EAAE;IAAEC,EAAE;IAAE/J,CAAC;IAAEK,CAAC;IAAE2B,CAAC;IAAEgI,EAAE;IAAEC,EAAE;IAAEzI,EAAE;IAAE0G,CAAC;IAAEjG,CAAC;IAC1CtC,CAAC,GAAG,IAAI;IACRsB,EAAE,GAAGtB,CAAC,CAACuB,CAAC;IACRZ,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,IAAI,CAACqB,EAAE,EAAE,OAAO,IAAIX,IAAI,CAACX,CAAC,CAAC;EAE3BsK,EAAE,GAAGJ,EAAE,GAAG,IAAIvJ,IAAI,CAAC,CAAC,CAAC;EACrBwJ,EAAE,GAAGE,EAAE,GAAG,IAAI1J,IAAI,CAAC,CAAC,CAAC;EAErBY,CAAC,GAAG,IAAIZ,IAAI,CAACwJ,EAAE,CAAC;EAChB9J,CAAC,GAAGkB,CAAC,CAAClB,CAAC,GAAG0I,YAAY,CAACzH,EAAE,CAAC,GAAGtB,CAAC,CAACK,CAAC,GAAG,CAAC;EACpCK,CAAC,GAAGL,CAAC,GAAGd,QAAQ;EAChBgC,CAAC,CAACA,CAAC,CAAC,CAAC,CAAC,GAAGvC,OAAO,CAAC,EAAE,EAAE0B,CAAC,GAAG,CAAC,GAAGnB,QAAQ,GAAGmB,CAAC,GAAGA,CAAC,CAAC;EAE9C,IAAIuJ,IAAI,IAAI,IAAI,EAAE;IAEhB;IACAA,IAAI,GAAG5J,CAAC,GAAG,CAAC,GAAGkB,CAAC,GAAG+I,EAAE;EACvB,CAAC,MAAM;IACLjI,CAAC,GAAG,IAAI1B,IAAI,CAACsJ,IAAI,CAAC;IAClB,IAAI,CAAC5H,CAAC,CAACqE,KAAK,CAAC,CAAC,IAAIrE,CAAC,CAAC2E,EAAE,CAACsD,EAAE,CAAC,EAAE,MAAMxJ,KAAK,CAACrC,eAAe,GAAG4D,CAAC,CAAC;IAC5D4H,IAAI,GAAG5H,CAAC,CAACxB,EAAE,CAACU,CAAC,CAAC,GAAIlB,CAAC,GAAG,CAAC,GAAGkB,CAAC,GAAG+I,EAAE,GAAIjI,CAAC;EACvC;EAEA9D,QAAQ,GAAG,KAAK;EAChB8D,CAAC,GAAG,IAAI1B,IAAI,CAACkC,cAAc,CAACvB,EAAE,CAAC,CAAC;EAChCO,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;EACnB8C,IAAI,CAAC9C,SAAS,GAAGwC,CAAC,GAAGiB,EAAE,CAAC5B,MAAM,GAAGH,QAAQ,GAAG,CAAC;EAE7C,SAAU;IACRgJ,CAAC,GAAGnF,MAAM,CAACf,CAAC,EAAEd,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzB6I,EAAE,GAAGF,EAAE,CAAC/G,IAAI,CAACoF,CAAC,CAACrF,KAAK,CAACiH,EAAE,CAAC,CAAC;IACzB,IAAIC,EAAE,CAACrJ,GAAG,CAACkJ,IAAI,CAAC,IAAI,CAAC,EAAE;IACvBC,EAAE,GAAGC,EAAE;IACPA,EAAE,GAAGC,EAAE;IACPA,EAAE,GAAGE,EAAE;IACPA,EAAE,GAAGD,EAAE,CAAClH,IAAI,CAACoF,CAAC,CAACrF,KAAK,CAACkH,EAAE,CAAC,CAAC;IACzBC,EAAE,GAAGD,EAAE;IACPA,EAAE,GAAG7I,CAAC;IACNA,CAAC,GAAGc,CAAC,CAACqC,KAAK,CAAC6D,CAAC,CAACrF,KAAK,CAACkH,EAAE,CAAC,CAAC;IACxB/H,CAAC,GAAG+H,EAAE;EACR;EAEAA,EAAE,GAAGhH,MAAM,CAAC6G,IAAI,CAACvF,KAAK,CAACwF,EAAE,CAAC,EAAEC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACxCE,EAAE,GAAGA,EAAE,CAAClH,IAAI,CAACiH,EAAE,CAAClH,KAAK,CAACoH,EAAE,CAAC,CAAC;EAC1BJ,EAAE,GAAGA,EAAE,CAAC/G,IAAI,CAACiH,EAAE,CAAClH,KAAK,CAACiH,EAAE,CAAC,CAAC;EAC1BE,EAAE,CAACnK,CAAC,GAAGoK,EAAE,CAACpK,CAAC,GAAGF,CAAC,CAACE,CAAC;;EAEjB;EACAoC,CAAC,GAAGc,MAAM,CAACkH,EAAE,EAAEH,EAAE,EAAE9J,CAAC,EAAE,CAAC,CAAC,CAACqE,KAAK,CAAC1E,CAAC,CAAC,CAACD,GAAG,CAAC,CAAC,CAACgB,GAAG,CAACqC,MAAM,CAACiH,EAAE,EAAEH,EAAE,EAAE7J,CAAC,EAAE,CAAC,CAAC,CAACqE,KAAK,CAAC1E,CAAC,CAAC,CAACD,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAC9E,CAACuK,EAAE,EAAEH,EAAE,CAAC,GAAG,CAACE,EAAE,EAAEH,EAAE,CAAC;EAEzBvJ,IAAI,CAAC9C,SAAS,GAAGgE,EAAE;EACnBtD,QAAQ,GAAG,IAAI;EAEf,OAAO+D,CAAC;AACV,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA1C,CAAC,CAAC2K,aAAa,GAAG3K,CAAC,CAAC4K,KAAK,GAAG,UAAUzI,EAAE,EAAED,EAAE,EAAE;EAC5C,OAAO2H,cAAc,CAAC,IAAI,EAAE,EAAE,EAAE1H,EAAE,EAAED,EAAE,CAAC;AACzC,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,CAAC,CAAC6K,SAAS,GAAG,UAAUxJ,CAAC,EAAEa,EAAE,EAAE;EAC7B,IAAI9B,CAAC,GAAG,IAAI;IACVW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtBD,CAAC,GAAG,IAAIW,IAAI,CAACX,CAAC,CAAC;EAEf,IAAIiB,CAAC,IAAI,IAAI,EAAE;IAEb;IACA,IAAI,CAACjB,CAAC,CAACuB,CAAC,EAAE,OAAOvB,CAAC;IAElBiB,CAAC,GAAG,IAAIN,IAAI,CAAC,CAAC,CAAC;IACfmB,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EACpB,CAAC,MAAM;IACLmD,CAAC,GAAG,IAAIN,IAAI,CAACM,CAAC,CAAC;IACf,IAAIa,EAAE,KAAK,KAAK,CAAC,EAAE;MACjBA,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;IACpB,CAAC,MAAM;MACL8L,UAAU,CAAC9H,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB;;IAEA;IACA,IAAI,CAAC9B,CAAC,CAACuB,CAAC,EAAE,OAAON,CAAC,CAACf,CAAC,GAAGF,CAAC,GAAGiB,CAAC;;IAE5B;IACA,IAAI,CAACA,CAAC,CAACM,CAAC,EAAE;MACR,IAAIN,CAAC,CAACf,CAAC,EAAEe,CAAC,CAACf,CAAC,GAAGF,CAAC,CAACE,CAAC;MAClB,OAAOe,CAAC;IACV;EACF;;EAEA;EACA,IAAIA,CAAC,CAACM,CAAC,CAAC,CAAC,CAAC,EAAE;IACVhD,QAAQ,GAAG,KAAK;IAChByB,CAAC,GAAGoD,MAAM,CAACpD,CAAC,EAAEiB,CAAC,EAAE,CAAC,EAAEa,EAAE,EAAE,CAAC,CAAC,CAACoB,KAAK,CAACjC,CAAC,CAAC;IACnC1C,QAAQ,GAAG,IAAI;IACf4B,QAAQ,CAACH,CAAC,CAAC;;IAEb;EACA,CAAC,MAAM;IACLiB,CAAC,CAACf,CAAC,GAAGF,CAAC,CAACE,CAAC;IACTF,CAAC,GAAGiB,CAAC;EACP;EAEA,OAAOjB,CAAC;AACV,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACAJ,CAAC,CAAC8K,QAAQ,GAAG,YAAY;EACvB,OAAO,CAAC,IAAI;AACd,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA9K,CAAC,CAAC+K,OAAO,GAAG,UAAU5I,EAAE,EAAED,EAAE,EAAE;EAC5B,OAAO2H,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE1H,EAAE,EAAED,EAAE,CAAC;AACxC,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,CAAC,CAACgL,OAAO,GAAGhL,CAAC,CAACX,GAAG,GAAG,UAAUgC,CAAC,EAAE;EAC/B,IAAIZ,CAAC;IAAEK,CAAC;IAAEmB,EAAE;IAAES,CAAC;IAAER,EAAE;IAAE5B,CAAC;IACpBF,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;IACpB4K,EAAE,GAAG,EAAE5J,CAAC,GAAG,IAAIN,IAAI,CAACM,CAAC,CAAC,CAAC;;EAEzB;EACA,IAAI,CAACjB,CAAC,CAACuB,CAAC,IAAI,CAACN,CAAC,CAACM,CAAC,IAAI,CAACvB,CAAC,CAACuB,CAAC,CAAC,CAAC,CAAC,IAAI,CAACN,CAAC,CAACM,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAIZ,IAAI,CAAC3B,OAAO,CAAC,CAACgB,CAAC,EAAE6K,EAAE,CAAC,CAAC;EAExE7K,CAAC,GAAG,IAAIW,IAAI,CAACX,CAAC,CAAC;EAEf,IAAIA,CAAC,CAACqD,EAAE,CAAC,CAAC,CAAC,EAAE,OAAOrD,CAAC;EAErB6B,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;EACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EAElB,IAAImD,CAAC,CAACoC,EAAE,CAAC,CAAC,CAAC,EAAE,OAAOlD,QAAQ,CAACH,CAAC,EAAE6B,EAAE,EAAEC,EAAE,CAAC;;EAEvC;EACAzB,CAAC,GAAGxB,SAAS,CAACoC,CAAC,CAACZ,CAAC,GAAGd,QAAQ,CAAC;;EAE7B;EACA,IAAIc,CAAC,IAAIY,CAAC,CAACM,CAAC,CAAC7B,MAAM,GAAG,CAAC,IAAI,CAACgB,CAAC,GAAGmK,EAAE,GAAG,CAAC,GAAG,CAACA,EAAE,GAAGA,EAAE,KAAKrL,gBAAgB,EAAE;IACtE8C,CAAC,GAAGwI,MAAM,CAACnK,IAAI,EAAEX,CAAC,EAAEU,CAAC,EAAEmB,EAAE,CAAC;IAC1B,OAAOZ,CAAC,CAACf,CAAC,GAAG,CAAC,GAAG,IAAIS,IAAI,CAAC,CAAC,CAAC,CAACgD,GAAG,CAACrB,CAAC,CAAC,GAAGnC,QAAQ,CAACmC,CAAC,EAAET,EAAE,EAAEC,EAAE,CAAC;EAC3D;EAEA5B,CAAC,GAAGF,CAAC,CAACE,CAAC;;EAEP;EACA,IAAIA,CAAC,GAAG,CAAC,EAAE;IAET;IACA,IAAIG,CAAC,GAAGY,CAAC,CAACM,CAAC,CAAC7B,MAAM,GAAG,CAAC,EAAE,OAAO,IAAIiB,IAAI,CAACC,GAAG,CAAC;;IAE5C;IACA,IAAI,CAACK,CAAC,CAACM,CAAC,CAAClB,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAEH,CAAC,GAAG,CAAC;;IAE5B;IACA,IAAIF,CAAC,CAACK,CAAC,IAAI,CAAC,IAAIL,CAAC,CAACuB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAIvB,CAAC,CAACuB,CAAC,CAAC7B,MAAM,IAAI,CAAC,EAAE;MAC9CM,CAAC,CAACE,CAAC,GAAGA,CAAC;MACP,OAAOF,CAAC;IACV;EACF;;EAEA;EACA;EACA;EACA;EACAU,CAAC,GAAG1B,OAAO,CAAC,CAACgB,CAAC,EAAE6K,EAAE,CAAC;EACnBxK,CAAC,GAAGK,CAAC,IAAI,CAAC,IAAI,CAACiC,QAAQ,CAACjC,CAAC,CAAC,GACtB7B,SAAS,CAACgM,EAAE,IAAI/L,IAAI,CAACqI,GAAG,CAAC,IAAI,GAAGtE,cAAc,CAAC7C,CAAC,CAACuB,CAAC,CAAC,CAAC,GAAGzC,IAAI,CAACpB,IAAI,GAAGsC,CAAC,CAACK,CAAC,GAAG,CAAC,CAAC,CAAC,GAC5E,IAAIM,IAAI,CAACD,CAAC,GAAG,EAAE,CAAC,CAACL,CAAC;;EAEtB;;EAEA;EACA,IAAIA,CAAC,GAAGM,IAAI,CAACxC,IAAI,GAAG,CAAC,IAAIkC,CAAC,GAAGM,IAAI,CAACzC,IAAI,GAAG,CAAC,EAAE,OAAO,IAAIyC,IAAI,CAACN,CAAC,GAAG,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAE9E3B,QAAQ,GAAG,KAAK;EAChBoC,IAAI,CAAC7C,QAAQ,GAAGkC,CAAC,CAACE,CAAC,GAAG,CAAC;;EAEvB;EACA;EACA;EACA;EACAQ,CAAC,GAAG5B,IAAI,CAAC0B,GAAG,CAAC,EAAE,EAAE,CAACH,CAAC,GAAG,EAAE,EAAEX,MAAM,CAAC;;EAEjC;EACA4C,CAAC,GAAGkG,kBAAkB,CAACvH,CAAC,CAACiC,KAAK,CAACyE,gBAAgB,CAAC3H,CAAC,EAAE6B,EAAE,GAAGnB,CAAC,CAAC,CAAC,EAAEmB,EAAE,CAAC;;EAEhE;EACA,IAAIS,CAAC,CAACf,CAAC,EAAE;IAEP;IACAe,CAAC,GAAGnC,QAAQ,CAACmC,CAAC,EAAET,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;;IAE1B;IACA;IACA,IAAIgG,mBAAmB,CAACvF,CAAC,CAACf,CAAC,EAAEM,EAAE,EAAEC,EAAE,CAAC,EAAE;MACpCzB,CAAC,GAAGwB,EAAE,GAAG,EAAE;;MAEX;MACAS,CAAC,GAAGnC,QAAQ,CAACqI,kBAAkB,CAACvH,CAAC,CAACiC,KAAK,CAACyE,gBAAgB,CAAC3H,CAAC,EAAEK,CAAC,GAAGK,CAAC,CAAC,CAAC,EAAEL,CAAC,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;;MAElF;MACA,IAAI,CAACwC,cAAc,CAACP,CAAC,CAACf,CAAC,CAAC,CAACwB,KAAK,CAAClB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE;QAC3DS,CAAC,GAAGnC,QAAQ,CAACmC,CAAC,EAAET,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;MAC5B;IACF;EACF;EAEAS,CAAC,CAACpC,CAAC,GAAGA,CAAC;EACP3B,QAAQ,GAAG,IAAI;EACfoC,IAAI,CAAC7C,QAAQ,GAAGgE,EAAE;EAElB,OAAO3B,QAAQ,CAACmC,CAAC,EAAET,EAAE,EAAEC,EAAE,CAAC;AAC5B,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,CAAC,CAACmL,WAAW,GAAG,UAAUhJ,EAAE,EAAED,EAAE,EAAE;EAChC,IAAI+H,GAAG;IACL7J,CAAC,GAAG,IAAI;IACRW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,IAAI8B,EAAE,KAAK,KAAK,CAAC,EAAE;IACjB8H,GAAG,GAAGC,cAAc,CAAC9J,CAAC,EAAEA,CAAC,CAACK,CAAC,IAAIM,IAAI,CAAC3C,QAAQ,IAAIgC,CAAC,CAACK,CAAC,IAAIM,IAAI,CAAC1C,QAAQ,CAAC;EACvE,CAAC,MAAM;IACL2L,UAAU,CAAC7H,EAAE,EAAE,CAAC,EAAEvE,UAAU,CAAC;IAE7B,IAAIsE,EAAE,KAAK,KAAK,CAAC,EAAEA,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ,CAAC,KACjC8L,UAAU,CAAC9H,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;IAEzB9B,CAAC,GAAGG,QAAQ,CAAC,IAAIQ,IAAI,CAACX,CAAC,CAAC,EAAE+B,EAAE,EAAED,EAAE,CAAC;IACjC+H,GAAG,GAAGC,cAAc,CAAC9J,CAAC,EAAE+B,EAAE,IAAI/B,CAAC,CAACK,CAAC,IAAIL,CAAC,CAACK,CAAC,IAAIM,IAAI,CAAC3C,QAAQ,EAAE+D,EAAE,CAAC;EAChE;EAEA,OAAO/B,CAAC,CAACsF,KAAK,CAAC,CAAC,IAAI,CAACtF,CAAC,CAAC4C,MAAM,CAAC,CAAC,GAAG,GAAG,GAAGiH,GAAG,GAAGA,GAAG;AACnD,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAjK,CAAC,CAACoL,mBAAmB,GAAGpL,CAAC,CAACqL,IAAI,GAAG,UAAUlJ,EAAE,EAAED,EAAE,EAAE;EACjD,IAAI9B,CAAC,GAAG,IAAI;IACVW,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEtB,IAAI8B,EAAE,KAAK,KAAK,CAAC,EAAE;IACjBA,EAAE,GAAGpB,IAAI,CAAC9C,SAAS;IACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EACpB,CAAC,MAAM;IACL8L,UAAU,CAAC7H,EAAE,EAAE,CAAC,EAAEvE,UAAU,CAAC;IAE7B,IAAIsE,EAAE,KAAK,KAAK,CAAC,EAAEA,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ,CAAC,KACjC8L,UAAU,CAAC9H,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3B;EAEA,OAAO3B,QAAQ,CAAC,IAAIQ,IAAI,CAACX,CAAC,CAAC,EAAE+B,EAAE,EAAED,EAAE,CAAC;AACtC,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,CAAC,CAACqD,QAAQ,GAAG,YAAY;EACvB,IAAIjD,CAAC,GAAG,IAAI;IACVW,IAAI,GAAGX,CAAC,CAACC,WAAW;IACpB4J,GAAG,GAAGC,cAAc,CAAC9J,CAAC,EAAEA,CAAC,CAACK,CAAC,IAAIM,IAAI,CAAC3C,QAAQ,IAAIgC,CAAC,CAACK,CAAC,IAAIM,IAAI,CAAC1C,QAAQ,CAAC;EAEvE,OAAO+B,CAAC,CAACsF,KAAK,CAAC,CAAC,IAAI,CAACtF,CAAC,CAAC4C,MAAM,CAAC,CAAC,GAAG,GAAG,GAAGiH,GAAG,GAAGA,GAAG;AACnD,CAAC;;AAGD;AACA;AACA;AACA;AACAjK,CAAC,CAACsL,SAAS,GAAGtL,CAAC,CAACuL,KAAK,GAAG,YAAY;EAClC,OAAOhL,QAAQ,CAAC,IAAI,IAAI,CAACF,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,CAACI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC5D,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACAT,CAAC,CAACwL,OAAO,GAAGxL,CAAC,CAACyL,MAAM,GAAG,YAAY;EACjC,IAAIrL,CAAC,GAAG,IAAI;IACVW,IAAI,GAAGX,CAAC,CAACC,WAAW;IACpB4J,GAAG,GAAGC,cAAc,CAAC9J,CAAC,EAAEA,CAAC,CAACK,CAAC,IAAIM,IAAI,CAAC3C,QAAQ,IAAIgC,CAAC,CAACK,CAAC,IAAIM,IAAI,CAAC1C,QAAQ,CAAC;EAEvE,OAAO+B,CAAC,CAACsF,KAAK,CAAC,CAAC,GAAG,GAAG,GAAGuE,GAAG,GAAGA,GAAG;AACpC,CAAC;;AAGD;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAAShH,cAAcA,CAACtB,CAAC,EAAE;EACzB,IAAIL,CAAC;IAAER,CAAC;IAAE4K,EAAE;IACVC,eAAe,GAAGhK,CAAC,CAAC7B,MAAM,GAAG,CAAC;IAC9BmK,GAAG,GAAG,EAAE;IACRpG,CAAC,GAAGlC,CAAC,CAAC,CAAC,CAAC;EAEV,IAAIgK,eAAe,GAAG,CAAC,EAAE;IACvB1B,GAAG,IAAIpG,CAAC;IACR,KAAKvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqK,eAAe,EAAErK,CAAC,EAAE,EAAE;MACpCoK,EAAE,GAAG/J,CAAC,CAACL,CAAC,CAAC,GAAG,EAAE;MACdR,CAAC,GAAGnB,QAAQ,GAAG+L,EAAE,CAAC5L,MAAM;MACxB,IAAIgB,CAAC,EAAEmJ,GAAG,IAAI2B,aAAa,CAAC9K,CAAC,CAAC;MAC9BmJ,GAAG,IAAIyB,EAAE;IACX;IAEA7H,CAAC,GAAGlC,CAAC,CAACL,CAAC,CAAC;IACRoK,EAAE,GAAG7H,CAAC,GAAG,EAAE;IACX/C,CAAC,GAAGnB,QAAQ,GAAG+L,EAAE,CAAC5L,MAAM;IACxB,IAAIgB,CAAC,EAAEmJ,GAAG,IAAI2B,aAAa,CAAC9K,CAAC,CAAC;EAChC,CAAC,MAAM,IAAI+C,CAAC,KAAK,CAAC,EAAE;IAClB,OAAO,GAAG;EACZ;;EAEA;EACA,OAAOA,CAAC,GAAG,EAAE,KAAK,CAAC,GAAGA,CAAC,IAAI,EAAE;EAE7B,OAAOoG,GAAG,GAAGpG,CAAC;AAChB;AAGA,SAASmG,UAAUA,CAAC1I,CAAC,EAAEV,GAAG,EAAEC,GAAG,EAAE;EAC/B,IAAIS,CAAC,KAAK,CAAC,CAACA,CAAC,IAAIA,CAAC,GAAGV,GAAG,IAAIU,CAAC,GAAGT,GAAG,EAAE;IACnC,MAAMK,KAAK,CAACrC,eAAe,GAAGyC,CAAC,CAAC;EAClC;AACF;;AAGA;AACA;AACA;AACA;AACA;AACA,SAAS2G,mBAAmBA,CAACtG,CAAC,EAAEL,CAAC,EAAEY,EAAE,EAAE2J,SAAS,EAAE;EAChD,IAAIC,EAAE,EAAEhL,CAAC,EAAE4B,CAAC,EAAEqJ,EAAE;;EAEhB;EACA,KAAKjL,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC,EAAEb,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAE,EAAEQ,CAAC;;EAEpC;EACA,IAAI,EAAEA,CAAC,GAAG,CAAC,EAAE;IACXA,CAAC,IAAI3B,QAAQ;IACbmM,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACLA,EAAE,GAAG5M,IAAI,CAACsB,IAAI,CAAC,CAACc,CAAC,GAAG,CAAC,IAAI3B,QAAQ,CAAC;IAClC2B,CAAC,IAAI3B,QAAQ;EACf;;EAEA;EACA;EACA;EACAmB,CAAC,GAAG1B,OAAO,CAAC,EAAE,EAAEO,QAAQ,GAAG2B,CAAC,CAAC;EAC7ByK,EAAE,GAAGpK,CAAC,CAACmK,EAAE,CAAC,GAAGhL,CAAC,GAAG,CAAC;EAElB,IAAI+K,SAAS,IAAI,IAAI,EAAE;IACrB,IAAIvK,CAAC,GAAG,CAAC,EAAE;MACT,IAAIA,CAAC,IAAI,CAAC,EAAEyK,EAAE,GAAGA,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,KACzB,IAAIzK,CAAC,IAAI,CAAC,EAAEyK,EAAE,GAAGA,EAAE,GAAG,EAAE,GAAG,CAAC;MACjCrJ,CAAC,GAAGR,EAAE,GAAG,CAAC,IAAI6J,EAAE,IAAI,KAAK,IAAI7J,EAAE,GAAG,CAAC,IAAI6J,EAAE,IAAI,KAAK,IAAIA,EAAE,IAAI,KAAK,IAAIA,EAAE,IAAI,CAAC;IAC9E,CAAC,MAAM;MACLrJ,CAAC,GAAG,CAACR,EAAE,GAAG,CAAC,IAAI6J,EAAE,GAAG,CAAC,IAAIjL,CAAC,IAAIoB,EAAE,GAAG,CAAC,IAAI6J,EAAE,GAAG,CAAC,IAAIjL,CAAC,GAAG,CAAC,KACrD,CAACa,CAAC,CAACmK,EAAE,GAAG,CAAC,CAAC,GAAGhL,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK1B,OAAO,CAAC,EAAE,EAAEkC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IACjD,CAACyK,EAAE,IAAIjL,CAAC,GAAG,CAAC,IAAIiL,EAAE,IAAI,CAAC,KAAK,CAACpK,CAAC,CAACmK,EAAE,GAAG,CAAC,CAAC,GAAGhL,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC;IAChE;EACF,CAAC,MAAM;IACL,IAAIQ,CAAC,GAAG,CAAC,EAAE;MACT,IAAIA,CAAC,IAAI,CAAC,EAAEyK,EAAE,GAAGA,EAAE,GAAG,IAAI,GAAG,CAAC,CAAC,KAC1B,IAAIzK,CAAC,IAAI,CAAC,EAAEyK,EAAE,GAAGA,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,KAC9B,IAAIzK,CAAC,IAAI,CAAC,EAAEyK,EAAE,GAAGA,EAAE,GAAG,EAAE,GAAG,CAAC;MACjCrJ,CAAC,GAAG,CAACmJ,SAAS,IAAI3J,EAAE,GAAG,CAAC,KAAK6J,EAAE,IAAI,IAAI,IAAI,CAACF,SAAS,IAAI3J,EAAE,GAAG,CAAC,IAAI6J,EAAE,IAAI,IAAI;IAC/E,CAAC,MAAM;MACLrJ,CAAC,GAAG,CAAC,CAACmJ,SAAS,IAAI3J,EAAE,GAAG,CAAC,KAAK6J,EAAE,GAAG,CAAC,IAAIjL,CAAC,IACxC,CAAC+K,SAAS,IAAI3J,EAAE,GAAG,CAAC,IAAK6J,EAAE,GAAG,CAAC,IAAIjL,CAAC,GAAG,CAAC,KACvC,CAACa,CAAC,CAACmK,EAAE,GAAG,CAAC,CAAC,GAAGhL,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK1B,OAAO,CAAC,EAAE,EAAEkC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACxD;EACF;EAEA,OAAOoB,CAAC;AACV;;AAGA;AACA;AACA;AACA,SAASsJ,WAAWA,CAAC/B,GAAG,EAAEgC,MAAM,EAAEC,OAAO,EAAE;EACzC,IAAI3K,CAAC;IACH4K,GAAG,GAAG,CAAC,CAAC,CAAC;IACTC,IAAI;IACJ9K,CAAC,GAAG,CAAC;IACL+K,IAAI,GAAGpC,GAAG,CAACnK,MAAM;EAEnB,OAAOwB,CAAC,GAAG+K,IAAI,GAAG;IAChB,KAAKD,IAAI,GAAGD,GAAG,CAACrM,MAAM,EAAEsM,IAAI,EAAE,GAAGD,GAAG,CAACC,IAAI,CAAC,IAAIH,MAAM;IACpDE,GAAG,CAAC,CAAC,CAAC,IAAItO,QAAQ,CAACuF,OAAO,CAAC6G,GAAG,CAACvG,MAAM,CAACpC,CAAC,EAAE,CAAC,CAAC;IAC3C,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4K,GAAG,CAACrM,MAAM,EAAEyB,CAAC,EAAE,EAAE;MAC/B,IAAI4K,GAAG,CAAC5K,CAAC,CAAC,GAAG2K,OAAO,GAAG,CAAC,EAAE;QACxB,IAAIC,GAAG,CAAC5K,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE4K,GAAG,CAAC5K,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;QACzC4K,GAAG,CAAC5K,CAAC,GAAG,CAAC,CAAC,IAAI4K,GAAG,CAAC5K,CAAC,CAAC,GAAG2K,OAAO,GAAG,CAAC;QAClCC,GAAG,CAAC5K,CAAC,CAAC,IAAI2K,OAAO;MACnB;IACF;EACF;EAEA,OAAOC,GAAG,CAAC9D,OAAO,CAAC,CAAC;AACtB;;AAGA;AACA;AACA;AACA;AACA;AACA,SAAStG,MAAMA,CAAChB,IAAI,EAAEX,CAAC,EAAE;EACvB,IAAIU,CAAC,EAAE0D,GAAG,EAAEnD,CAAC;EAEb,IAAIjB,CAAC,CAAC4C,MAAM,CAAC,CAAC,EAAE,OAAO5C,CAAC;;EAExB;EACA;;EAEA;EACAoE,GAAG,GAAGpE,CAAC,CAACuB,CAAC,CAAC7B,MAAM;EAChB,IAAI0E,GAAG,GAAG,EAAE,EAAE;IACZ1D,CAAC,GAAG5B,IAAI,CAACsB,IAAI,CAACgE,GAAG,GAAG,CAAC,CAAC;IACtBnD,CAAC,GAAG,CAAC,CAAC,GAAGqD,OAAO,CAAC,CAAC,EAAE5D,CAAC,CAAC,EAAEuC,QAAQ,CAAC,CAAC;EACpC,CAAC,MAAM;IACLvC,CAAC,GAAG,EAAE;IACNO,CAAC,GAAG,8BAA8B;EACpC;EAEAN,IAAI,CAAC9C,SAAS,IAAI6C,CAAC;EAEnBV,CAAC,GAAGuE,YAAY,CAAC5D,IAAI,EAAE,CAAC,EAAEX,CAAC,CAACkD,KAAK,CAACjC,CAAC,CAAC,EAAE,IAAIN,IAAI,CAAC,CAAC,CAAC,CAAC;;EAElD;EACA,KAAK,IAAIO,CAAC,GAAGR,CAAC,EAAEQ,CAAC,EAAE,GAAG;IACpB,IAAIgL,KAAK,GAAGlM,CAAC,CAACkD,KAAK,CAAClD,CAAC,CAAC;IACtBA,CAAC,GAAGkM,KAAK,CAAChJ,KAAK,CAACgJ,KAAK,CAAC,CAACxH,KAAK,CAACwH,KAAK,CAAC,CAAChJ,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;EACtD;EAEAxC,IAAI,CAAC9C,SAAS,IAAI6C,CAAC;EAEnB,OAAOV,CAAC;AACV;;AAGA;AACA;AACA;AACA,IAAIoD,MAAM,GAAI,YAAY;EAExB;EACA,SAAS+I,eAAeA,CAACnM,CAAC,EAAEU,CAAC,EAAE0G,IAAI,EAAE;IACnC,IAAIgF,IAAI;MACNxD,KAAK,GAAG,CAAC;MACT1H,CAAC,GAAGlB,CAAC,CAACN,MAAM;IAEd,KAAKM,CAAC,GAAGA,CAAC,CAAC+C,KAAK,CAAC,CAAC,EAAE7B,CAAC,EAAE,GAAG;MACxBkL,IAAI,GAAGpM,CAAC,CAACkB,CAAC,CAAC,GAAGR,CAAC,GAAGkI,KAAK;MACvB5I,CAAC,CAACkB,CAAC,CAAC,GAAGkL,IAAI,GAAGhF,IAAI,GAAG,CAAC;MACtBwB,KAAK,GAAGwD,IAAI,GAAGhF,IAAI,GAAG,CAAC;IACzB;IAEA,IAAIwB,KAAK,EAAE5I,CAAC,CAAC6I,OAAO,CAACD,KAAK,CAAC;IAE3B,OAAO5I,CAAC;EACV;EAEA,SAASqM,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAE;IAC7B,IAAIvL,CAAC,EAAEoB,CAAC;IAER,IAAIkK,EAAE,IAAIC,EAAE,EAAE;MACZnK,CAAC,GAAGkK,EAAE,GAAGC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC,MAAM;MACL,KAAKvL,CAAC,GAAGoB,CAAC,GAAG,CAAC,EAAEpB,CAAC,GAAGsL,EAAE,EAAEtL,CAAC,EAAE,EAAE;QAC3B,IAAIoL,CAAC,CAACpL,CAAC,CAAC,IAAIqL,CAAC,CAACrL,CAAC,CAAC,EAAE;UAChBoB,CAAC,GAAGgK,CAAC,CAACpL,CAAC,CAAC,GAAGqL,CAAC,CAACrL,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;UACxB;QACF;MACF;IACF;IAEA,OAAOoB,CAAC;EACV;EAEA,SAASoK,QAAQA,CAACJ,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEpF,IAAI,EAAE;IAChC,IAAIlG,CAAC,GAAG,CAAC;;IAET;IACA,OAAOsL,EAAE,EAAE,GAAG;MACZF,CAAC,CAACE,EAAE,CAAC,IAAItL,CAAC;MACVA,CAAC,GAAGoL,CAAC,CAACE,EAAE,CAAC,GAAGD,CAAC,CAACC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;MACzBF,CAAC,CAACE,EAAE,CAAC,GAAGtL,CAAC,GAAGkG,IAAI,GAAGkF,CAAC,CAACE,EAAE,CAAC,GAAGD,CAAC,CAACC,EAAE,CAAC;IAClC;;IAEA;IACA,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,IAAIA,CAAC,CAAC5M,MAAM,GAAG,CAAC,GAAG4M,CAAC,CAAClE,KAAK,CAAC,CAAC;EAC1C;EAEA,OAAO,UAAUpI,CAAC,EAAEiB,CAAC,EAAEY,EAAE,EAAEC,EAAE,EAAE0B,EAAE,EAAE4D,IAAI,EAAE;IACvC,IAAIrG,GAAG;MAAEV,CAAC;MAAEa,CAAC;MAAER,CAAC;MAAEiM,OAAO;MAAEC,IAAI;MAAEC,IAAI;MAAEC,KAAK;MAAEvE,CAAC;MAAEwE,EAAE;MAAEC,GAAG;MAAEC,IAAI;MAAEC,IAAI;MAAEnL,EAAE;MAAES,CAAC;MAAE2K,EAAE;MAAEC,EAAE;MAAEC,GAAG;MACtFC,EAAE;MAAEC,EAAE;MACN5M,IAAI,GAAGX,CAAC,CAACC,WAAW;MACpBuN,IAAI,GAAGxN,CAAC,CAACE,CAAC,IAAIe,CAAC,CAACf,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAC1BoB,EAAE,GAAGtB,CAAC,CAACuB,CAAC;MACRC,EAAE,GAAGP,CAAC,CAACM,CAAC;;IAEV;IACA,IAAI,CAACD,EAAE,IAAI,CAACA,EAAE,CAAC,CAAC,CAAC,IAAI,CAACE,EAAE,IAAI,CAACA,EAAE,CAAC,CAAC,CAAC,EAAE;MAElC,OAAO,IAAIb,IAAI;MAAC;MACd,CAACX,CAAC,CAACE,CAAC,IAAI,CAACe,CAAC,CAACf,CAAC,KAAKoB,EAAE,GAAGE,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC,IAAIE,EAAE,CAAC,CAAC,CAAC,GAAG,CAACA,EAAE,CAAC,GAAGZ,GAAG;MAEvD;MACAU,EAAE,IAAIA,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACE,EAAE,GAAGgM,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;IAClD;IAEA,IAAIpG,IAAI,EAAE;MACRuF,OAAO,GAAG,CAAC;MACXtM,CAAC,GAAGL,CAAC,CAACK,CAAC,GAAGY,CAAC,CAACZ,CAAC;IACf,CAAC,MAAM;MACL+G,IAAI,GAAG9H,IAAI;MACXqN,OAAO,GAAGpN,QAAQ;MAClBc,CAAC,GAAGxB,SAAS,CAACmB,CAAC,CAACK,CAAC,GAAGsM,OAAO,CAAC,GAAG9N,SAAS,CAACoC,CAAC,CAACZ,CAAC,GAAGsM,OAAO,CAAC;IACzD;IAEAW,EAAE,GAAG9L,EAAE,CAAC9B,MAAM;IACd0N,EAAE,GAAG9L,EAAE,CAAC5B,MAAM;IACd6I,CAAC,GAAG,IAAI5H,IAAI,CAAC6M,IAAI,CAAC;IAClBT,EAAE,GAAGxE,CAAC,CAAChH,CAAC,GAAG,EAAE;;IAEb;IACA;IACA,KAAKL,CAAC,GAAG,CAAC,EAAEM,EAAE,CAACN,CAAC,CAAC,KAAKI,EAAE,CAACJ,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAC;IAEvC,IAAIM,EAAE,CAACN,CAAC,CAAC,IAAII,EAAE,CAACJ,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEb,CAAC,EAAE;IAE7B,IAAIwB,EAAE,IAAI,IAAI,EAAE;MACdE,EAAE,GAAGF,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;MACxBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;IACpB,CAAC,MAAM,IAAI0F,EAAE,EAAE;MACbzB,EAAE,GAAGF,EAAE,IAAI7B,CAAC,CAACK,CAAC,GAAGY,CAAC,CAACZ,CAAC,CAAC,GAAG,CAAC;IAC3B,CAAC,MAAM;MACL0B,EAAE,GAAGF,EAAE;IACT;IAEA,IAAIE,EAAE,GAAG,CAAC,EAAE;MACVgL,EAAE,CAAC7E,IAAI,CAAC,CAAC,CAAC;MACV0E,IAAI,GAAG,IAAI;IACb,CAAC,MAAM;MAEL;MACA7K,EAAE,GAAGA,EAAE,GAAG4K,OAAO,GAAG,CAAC,GAAG,CAAC;MACzBzL,CAAC,GAAG,CAAC;;MAEL;MACA,IAAIoM,EAAE,IAAI,CAAC,EAAE;QACX5M,CAAC,GAAG,CAAC;QACLc,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC;QACVO,EAAE,EAAE;;QAEJ;QACA,OAAO,CAACb,CAAC,GAAGkM,EAAE,IAAI1M,CAAC,KAAKqB,EAAE,EAAE,EAAEb,CAAC,EAAE,EAAE;UACjCsB,CAAC,GAAG9B,CAAC,GAAG0G,IAAI,IAAI9F,EAAE,CAACJ,CAAC,CAAC,IAAI,CAAC,CAAC;UAC3B6L,EAAE,CAAC7L,CAAC,CAAC,GAAGsB,CAAC,GAAGhB,EAAE,GAAG,CAAC;UAClBd,CAAC,GAAG8B,CAAC,GAAGhB,EAAE,GAAG,CAAC;QAChB;QAEAoL,IAAI,GAAGlM,CAAC,IAAIQ,CAAC,GAAGkM,EAAE;;QAEpB;MACA,CAAC,MAAM;QAEL;QACA1M,CAAC,GAAG0G,IAAI,IAAI5F,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;QAE1B,IAAId,CAAC,GAAG,CAAC,EAAE;UACTc,EAAE,GAAG2K,eAAe,CAAC3K,EAAE,EAAEd,CAAC,EAAE0G,IAAI,CAAC;UACjC9F,EAAE,GAAG6K,eAAe,CAAC7K,EAAE,EAAEZ,CAAC,EAAE0G,IAAI,CAAC;UACjCkG,EAAE,GAAG9L,EAAE,CAAC9B,MAAM;UACd0N,EAAE,GAAG9L,EAAE,CAAC5B,MAAM;QAChB;QAEAyN,EAAE,GAAGG,EAAE;QACPN,GAAG,GAAG1L,EAAE,CAACyB,KAAK,CAAC,CAAC,EAAEuK,EAAE,CAAC;QACrBL,IAAI,GAAGD,GAAG,CAACtN,MAAM;;QAEjB;QACA,OAAOuN,IAAI,GAAGK,EAAE,GAAGN,GAAG,CAACC,IAAI,EAAE,CAAC,GAAG,CAAC;QAElCM,EAAE,GAAG/L,EAAE,CAACuB,KAAK,CAAC,CAAC;QACfwK,EAAE,CAAC1E,OAAO,CAAC,CAAC,CAAC;QACbwE,GAAG,GAAG7L,EAAE,CAAC,CAAC,CAAC;QAEX,IAAIA,EAAE,CAAC,CAAC,CAAC,IAAI4F,IAAI,GAAG,CAAC,EAAE,EAAEiG,GAAG;QAE5B,GAAG;UACD3M,CAAC,GAAG,CAAC;;UAEL;UACAK,GAAG,GAAGsL,OAAO,CAAC7K,EAAE,EAAEwL,GAAG,EAAEM,EAAE,EAAEL,IAAI,CAAC;;UAEhC;UACA,IAAIlM,GAAG,GAAG,CAAC,EAAE;YAEX;YACAmM,IAAI,GAAGF,GAAG,CAAC,CAAC,CAAC;YACb,IAAIM,EAAE,IAAIL,IAAI,EAAEC,IAAI,GAAGA,IAAI,GAAG9F,IAAI,IAAI4F,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;;YAElD;YACAtM,CAAC,GAAGwM,IAAI,GAAGG,GAAG,GAAG,CAAC;;YAElB;YACA;YACA;YACA;YACA;YACA;YACA;;YAEA,IAAI3M,CAAC,GAAG,CAAC,EAAE;cACT,IAAIA,CAAC,IAAI0G,IAAI,EAAE1G,CAAC,GAAG0G,IAAI,GAAG,CAAC;;cAE3B;cACAyF,IAAI,GAAGV,eAAe,CAAC3K,EAAE,EAAEd,CAAC,EAAE0G,IAAI,CAAC;cACnC0F,KAAK,GAAGD,IAAI,CAACnN,MAAM;cACnBuN,IAAI,GAAGD,GAAG,CAACtN,MAAM;;cAEjB;cACAqB,GAAG,GAAGsL,OAAO,CAACQ,IAAI,EAAEG,GAAG,EAAEF,KAAK,EAAEG,IAAI,CAAC;;cAErC;cACA,IAAIlM,GAAG,IAAI,CAAC,EAAE;gBACZL,CAAC,EAAE;;gBAEH;gBACAgM,QAAQ,CAACG,IAAI,EAAES,EAAE,GAAGR,KAAK,GAAGS,EAAE,GAAG/L,EAAE,EAAEsL,KAAK,EAAE1F,IAAI,CAAC;cACnD;YACF,CAAC,MAAM;cAEL;cACA;cACA;cACA,IAAI1G,CAAC,IAAI,CAAC,EAAEK,GAAG,GAAGL,CAAC,GAAG,CAAC;cACvBmM,IAAI,GAAGrL,EAAE,CAACuB,KAAK,CAAC,CAAC;YACnB;YAEA+J,KAAK,GAAGD,IAAI,CAACnN,MAAM;YACnB,IAAIoN,KAAK,GAAGG,IAAI,EAAEJ,IAAI,CAAChE,OAAO,CAAC,CAAC,CAAC;;YAEjC;YACA6D,QAAQ,CAACM,GAAG,EAAEH,IAAI,EAAEI,IAAI,EAAE7F,IAAI,CAAC;;YAE/B;YACA,IAAIrG,GAAG,IAAI,CAAC,CAAC,EAAE;cACbkM,IAAI,GAAGD,GAAG,CAACtN,MAAM;;cAEjB;cACAqB,GAAG,GAAGsL,OAAO,CAAC7K,EAAE,EAAEwL,GAAG,EAAEM,EAAE,EAAEL,IAAI,CAAC;;cAEhC;cACA,IAAIlM,GAAG,GAAG,CAAC,EAAE;gBACXL,CAAC,EAAE;;gBAEH;gBACAgM,QAAQ,CAACM,GAAG,EAAEM,EAAE,GAAGL,IAAI,GAAGM,EAAE,GAAG/L,EAAE,EAAEyL,IAAI,EAAE7F,IAAI,CAAC;cAChD;YACF;YAEA6F,IAAI,GAAGD,GAAG,CAACtN,MAAM;UACnB,CAAC,MAAM,IAAIqB,GAAG,KAAK,CAAC,EAAE;YACpBL,CAAC,EAAE;YACHsM,GAAG,GAAG,CAAC,CAAC,CAAC;UACX,CAAC,CAAI;;UAEL;UACAD,EAAE,CAAC7L,CAAC,EAAE,CAAC,GAAGR,CAAC;;UAEX;UACA,IAAIK,GAAG,IAAIiM,GAAG,CAAC,CAAC,CAAC,EAAE;YACjBA,GAAG,CAACC,IAAI,EAAE,CAAC,GAAG3L,EAAE,CAAC6L,EAAE,CAAC,IAAI,CAAC;UAC3B,CAAC,MAAM;YACLH,GAAG,GAAG,CAAC1L,EAAE,CAAC6L,EAAE,CAAC,CAAC;YACdF,IAAI,GAAG,CAAC;UACV;QAEF,CAAC,QAAQ,CAACE,EAAE,EAAE,GAAGC,EAAE,IAAIJ,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,KAAKjL,EAAE,EAAE;QAEjD6K,IAAI,GAAGI,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC;MAC1B;;MAEA;MACA,IAAI,CAACD,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC3E,KAAK,CAAC,CAAC;IACxB;;IAEA;IACA,IAAIuE,OAAO,IAAI,CAAC,EAAE;MAChBpE,CAAC,CAAClI,CAAC,GAAGA,CAAC;MACPhC,OAAO,GAAGuO,IAAI;IAChB,CAAC,MAAM;MAEL;MACA,KAAK1L,CAAC,GAAG,CAAC,EAAER,CAAC,GAAGqM,EAAE,CAAC,CAAC,CAAC,EAAErM,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAEQ,CAAC,EAAE;MAC5CqH,CAAC,CAAClI,CAAC,GAAGa,CAAC,GAAGb,CAAC,GAAGsM,OAAO,GAAG,CAAC;MAEzBxM,QAAQ,CAACoI,CAAC,EAAE/E,EAAE,GAAG3B,EAAE,GAAG0G,CAAC,CAAClI,CAAC,GAAG,CAAC,GAAGwB,EAAE,EAAEC,EAAE,EAAE8K,IAAI,CAAC;IAC/C;IAEA,OAAOrE,CAAC;EACV,CAAC;AACH,CAAC,CAAE,CAAC;;AAGJ;AACA;AACA;AACA;AACC,SAASpI,QAAQA,CAACH,CAAC,EAAE+B,EAAE,EAAED,EAAE,EAAE2L,WAAW,EAAE;EACzC,IAAIC,MAAM;IAAExM,CAAC;IAAEC,CAAC;IAAET,CAAC;IAAEiL,EAAE;IAAEgC,OAAO;IAAElK,CAAC;IAAEnC,EAAE;IAAEsM,GAAG;IAC1CjN,IAAI,GAAGX,CAAC,CAACC,WAAW;;EAEtB;EACA4N,GAAG,EAAE,IAAI9L,EAAE,IAAI,IAAI,EAAE;IACnBT,EAAE,GAAGtB,CAAC,CAACuB,CAAC;;IAER;IACA,IAAI,CAACD,EAAE,EAAE,OAAOtB,CAAC;;IAEjB;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA,KAAK0N,MAAM,GAAG,CAAC,EAAEhN,CAAC,GAAGY,EAAE,CAAC,CAAC,CAAC,EAAEZ,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAEgN,MAAM,EAAE;IACtDxM,CAAC,GAAGa,EAAE,GAAG2L,MAAM;;IAEf;IACA,IAAIxM,CAAC,GAAG,CAAC,EAAE;MACTA,CAAC,IAAI3B,QAAQ;MACb4B,CAAC,GAAGY,EAAE;MACN0B,CAAC,GAAGnC,EAAE,CAACsM,GAAG,GAAG,CAAC,CAAC;;MAEf;MACAjC,EAAE,GAAGlI,CAAC,GAAGzE,OAAO,CAAC,EAAE,EAAE0O,MAAM,GAAGvM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;IAC/C,CAAC,MAAM;MACLyM,GAAG,GAAG9O,IAAI,CAACsB,IAAI,CAAC,CAACc,CAAC,GAAG,CAAC,IAAI3B,QAAQ,CAAC;MACnCmB,CAAC,GAAGY,EAAE,CAAC5B,MAAM;MACb,IAAIkO,GAAG,IAAIlN,CAAC,EAAE;QACZ,IAAI+M,WAAW,EAAE;UAEf;UACA,OAAO/M,CAAC,EAAE,IAAIkN,GAAG,GAAGtM,EAAE,CAAC4G,IAAI,CAAC,CAAC,CAAC;UAC9BzE,CAAC,GAAGkI,EAAE,GAAG,CAAC;UACV+B,MAAM,GAAG,CAAC;UACVxM,CAAC,IAAI3B,QAAQ;UACb4B,CAAC,GAAGD,CAAC,GAAG3B,QAAQ,GAAG,CAAC;QACtB,CAAC,MAAM;UACL,MAAMsO,GAAG;QACX;MACF,CAAC,MAAM;QACLpK,CAAC,GAAG/C,CAAC,GAAGY,EAAE,CAACsM,GAAG,CAAC;;QAEf;QACA,KAAKF,MAAM,GAAG,CAAC,EAAEhN,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAEgN,MAAM,EAAE;;QAE3C;QACAxM,CAAC,IAAI3B,QAAQ;;QAEb;QACA;QACA4B,CAAC,GAAGD,CAAC,GAAG3B,QAAQ,GAAGmO,MAAM;;QAEzB;QACA/B,EAAE,GAAGxK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGsC,CAAC,GAAGzE,OAAO,CAAC,EAAE,EAAE0O,MAAM,GAAGvM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;MAC3D;IACF;;IAEA;IACAsM,WAAW,GAAGA,WAAW,IAAI1L,EAAE,GAAG,CAAC,IACjCT,EAAE,CAACsM,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,CAAC,KAAKzM,CAAC,GAAG,CAAC,GAAGsC,CAAC,GAAGA,CAAC,GAAGzE,OAAO,CAAC,EAAE,EAAE0O,MAAM,GAAGvM,CAAC,GAAG,CAAC,CAAC,CAAC;;IAEzE;IACA;IACA;;IAEAwM,OAAO,GAAG7L,EAAE,GAAG,CAAC,GACZ,CAAC6J,EAAE,IAAI8B,WAAW,MAAM3L,EAAE,IAAI,CAAC,IAAIA,EAAE,KAAK9B,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAC3DyL,EAAE,GAAG,CAAC,IAAIA,EAAE,IAAI,CAAC,KAAK7J,EAAE,IAAI,CAAC,IAAI2L,WAAW,IAAI3L,EAAE,IAAI,CAAC;IAEvD;IACC,CAACZ,CAAC,GAAG,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGsC,CAAC,GAAGzE,OAAO,CAAC,EAAE,EAAE0O,MAAM,GAAGvM,CAAC,CAAC,GAAG,CAAC,GAAGG,EAAE,CAACsM,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,GAAI,CAAC,IACxE9L,EAAE,KAAK9B,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAE9B,IAAI6B,EAAE,GAAG,CAAC,IAAI,CAACT,EAAE,CAAC,CAAC,CAAC,EAAE;MACpBA,EAAE,CAAC5B,MAAM,GAAG,CAAC;MACb,IAAIiO,OAAO,EAAE;QAEX;QACA5L,EAAE,IAAI/B,CAAC,CAACK,CAAC,GAAG,CAAC;;QAEb;QACAiB,EAAE,CAAC,CAAC,CAAC,GAAGtC,OAAO,CAAC,EAAE,EAAE,CAACO,QAAQ,GAAGwC,EAAE,GAAGxC,QAAQ,IAAIA,QAAQ,CAAC;QAC1DS,CAAC,CAACK,CAAC,GAAG,CAAC0B,EAAE,IAAI,CAAC;MAChB,CAAC,MAAM;QAEL;QACAT,EAAE,CAAC,CAAC,CAAC,GAAGtB,CAAC,CAACK,CAAC,GAAG,CAAC;MACjB;MAEA,OAAOL,CAAC;IACV;;IAEA;IACA,IAAIkB,CAAC,IAAI,CAAC,EAAE;MACVI,EAAE,CAAC5B,MAAM,GAAGkO,GAAG;MACflN,CAAC,GAAG,CAAC;MACLkN,GAAG,EAAE;IACP,CAAC,MAAM;MACLtM,EAAE,CAAC5B,MAAM,GAAGkO,GAAG,GAAG,CAAC;MACnBlN,CAAC,GAAG1B,OAAO,CAAC,EAAE,EAAEO,QAAQ,GAAG2B,CAAC,CAAC;;MAE7B;MACA;MACAI,EAAE,CAACsM,GAAG,CAAC,GAAGzM,CAAC,GAAG,CAAC,GAAG,CAACsC,CAAC,GAAGzE,OAAO,CAAC,EAAE,EAAE0O,MAAM,GAAGvM,CAAC,CAAC,GAAGnC,OAAO,CAAC,EAAE,EAAEmC,CAAC,CAAC,GAAG,CAAC,IAAIT,CAAC,GAAG,CAAC;IAC9E;IAEA,IAAIiN,OAAO,EAAE;MACX,SAAS;QAEP;QACA,IAAIC,GAAG,IAAI,CAAC,EAAE;UAEZ;UACA,KAAK1M,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGG,EAAE,CAAC,CAAC,CAAC,EAAEH,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAED,CAAC,EAAE;UAC5CC,CAAC,GAAGG,EAAE,CAAC,CAAC,CAAC,IAAIZ,CAAC;UACd,KAAKA,CAAC,GAAG,CAAC,EAAES,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAET,CAAC,EAAE;;UAEjC;UACA,IAAIQ,CAAC,IAAIR,CAAC,EAAE;YACVV,CAAC,CAACK,CAAC,EAAE;YACL,IAAIiB,EAAE,CAAC,CAAC,CAAC,IAAIhC,IAAI,EAAEgC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;UAC9B;UAEA;QACF,CAAC,MAAM;UACLA,EAAE,CAACsM,GAAG,CAAC,IAAIlN,CAAC;UACZ,IAAIY,EAAE,CAACsM,GAAG,CAAC,IAAItO,IAAI,EAAE;UACrBgC,EAAE,CAACsM,GAAG,EAAE,CAAC,GAAG,CAAC;UACblN,CAAC,GAAG,CAAC;QACP;MACF;IACF;;IAEA;IACA,KAAKQ,CAAC,GAAGI,EAAE,CAAC5B,MAAM,EAAE4B,EAAE,CAAC,EAAEJ,CAAC,CAAC,KAAK,CAAC,GAAGI,EAAE,CAAC6G,GAAG,CAAC,CAAC;EAC9C;EAEA,IAAI5J,QAAQ,EAAE;IAEZ;IACA,IAAIyB,CAAC,CAACK,CAAC,GAAGM,IAAI,CAACxC,IAAI,EAAE;MAEnB;MACA6B,CAAC,CAACuB,CAAC,GAAG,IAAI;MACVvB,CAAC,CAACK,CAAC,GAAGO,GAAG;;MAEX;IACA,CAAC,MAAM,IAAIZ,CAAC,CAACK,CAAC,GAAGM,IAAI,CAACzC,IAAI,EAAE;MAE1B;MACA8B,CAAC,CAACK,CAAC,GAAG,CAAC;MACPL,CAAC,CAACuB,CAAC,GAAG,CAAC,CAAC,CAAC;MACT;IACF,CAAC,CAAC;EACJ;EAEA,OAAOvB,CAAC;AACV;AAGA,SAAS8J,cAAcA,CAAC9J,CAAC,EAAE8N,KAAK,EAAE/L,EAAE,EAAE;EACpC,IAAI,CAAC/B,CAAC,CAAC2C,QAAQ,CAAC,CAAC,EAAE,OAAOoL,iBAAiB,CAAC/N,CAAC,CAAC;EAC9C,IAAIU,CAAC;IACHL,CAAC,GAAGL,CAAC,CAACK,CAAC;IACPwJ,GAAG,GAAGhH,cAAc,CAAC7C,CAAC,CAACuB,CAAC,CAAC;IACzB6C,GAAG,GAAGyF,GAAG,CAACnK,MAAM;EAElB,IAAIoO,KAAK,EAAE;IACT,IAAI/L,EAAE,IAAI,CAACrB,CAAC,GAAGqB,EAAE,GAAGqC,GAAG,IAAI,CAAC,EAAE;MAC5ByF,GAAG,GAAGA,GAAG,CAACvG,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGuG,GAAG,CAAC9G,KAAK,CAAC,CAAC,CAAC,GAAGyI,aAAa,CAAC9K,CAAC,CAAC;IAC7D,CAAC,MAAM,IAAI0D,GAAG,GAAG,CAAC,EAAE;MAClByF,GAAG,GAAGA,GAAG,CAACvG,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGuG,GAAG,CAAC9G,KAAK,CAAC,CAAC,CAAC;IAC1C;IAEA8G,GAAG,GAAGA,GAAG,IAAI7J,CAAC,CAACK,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAGL,CAAC,CAACK,CAAC;EAC1C,CAAC,MAAM,IAAIA,CAAC,GAAG,CAAC,EAAE;IAChBwJ,GAAG,GAAG,IAAI,GAAG2B,aAAa,CAAC,CAACnL,CAAC,GAAG,CAAC,CAAC,GAAGwJ,GAAG;IACxC,IAAI9H,EAAE,IAAI,CAACrB,CAAC,GAAGqB,EAAE,GAAGqC,GAAG,IAAI,CAAC,EAAEyF,GAAG,IAAI2B,aAAa,CAAC9K,CAAC,CAAC;EACvD,CAAC,MAAM,IAAIL,CAAC,IAAI+D,GAAG,EAAE;IACnByF,GAAG,IAAI2B,aAAa,CAACnL,CAAC,GAAG,CAAC,GAAG+D,GAAG,CAAC;IACjC,IAAIrC,EAAE,IAAI,CAACrB,CAAC,GAAGqB,EAAE,GAAG1B,CAAC,GAAG,CAAC,IAAI,CAAC,EAAEwJ,GAAG,GAAGA,GAAG,GAAG,GAAG,GAAG2B,aAAa,CAAC9K,CAAC,CAAC;EACpE,CAAC,MAAM;IACL,IAAI,CAACA,CAAC,GAAGL,CAAC,GAAG,CAAC,IAAI+D,GAAG,EAAEyF,GAAG,GAAGA,GAAG,CAAC9G,KAAK,CAAC,CAAC,EAAErC,CAAC,CAAC,GAAG,GAAG,GAAGmJ,GAAG,CAAC9G,KAAK,CAACrC,CAAC,CAAC;IACjE,IAAIqB,EAAE,IAAI,CAACrB,CAAC,GAAGqB,EAAE,GAAGqC,GAAG,IAAI,CAAC,EAAE;MAC5B,IAAI/D,CAAC,GAAG,CAAC,KAAK+D,GAAG,EAAEyF,GAAG,IAAI,GAAG;MAC7BA,GAAG,IAAI2B,aAAa,CAAC9K,CAAC,CAAC;IACzB;EACF;EAEA,OAAOmJ,GAAG;AACZ;;AAGA;AACA,SAASxB,iBAAiBA,CAACqF,MAAM,EAAErN,CAAC,EAAE;EACpC,IAAIoD,CAAC,GAAGiK,MAAM,CAAC,CAAC,CAAC;;EAEjB;EACA,KAAMrN,CAAC,IAAId,QAAQ,EAAEkE,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAEpD,CAAC,EAAE;EAC1C,OAAOA,CAAC;AACV;AAGA,SAASuH,OAAOA,CAACjH,IAAI,EAAEoB,EAAE,EAAEF,EAAE,EAAE;EAC7B,IAAIE,EAAE,GAAGtC,cAAc,EAAE;IAEvB;IACAlB,QAAQ,GAAG,IAAI;IACf,IAAIsD,EAAE,EAAElB,IAAI,CAAC9C,SAAS,GAAGgE,EAAE;IAC3B,MAAMf,KAAK,CAACpC,sBAAsB,CAAC;EACrC;EACA,OAAOyB,QAAQ,CAAC,IAAIQ,IAAI,CAACjD,IAAI,CAAC,EAAEqE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC;AAC9C;AAGA,SAASwD,KAAKA,CAAC5E,IAAI,EAAEoB,EAAE,EAAED,EAAE,EAAE;EAC3B,IAAIC,EAAE,GAAGpC,YAAY,EAAE,MAAMmB,KAAK,CAACpC,sBAAsB,CAAC;EAC1D,OAAOyB,QAAQ,CAAC,IAAIQ,IAAI,CAAChD,EAAE,CAAC,EAAEoE,EAAE,EAAED,EAAE,EAAE,IAAI,CAAC;AAC7C;AAGA,SAASiH,YAAYA,CAAC2E,MAAM,EAAE;EAC5B,IAAIjK,CAAC,GAAGiK,MAAM,CAAChO,MAAM,GAAG,CAAC;IACvB0E,GAAG,GAAGX,CAAC,GAAGlE,QAAQ,GAAG,CAAC;EAExBkE,CAAC,GAAGiK,MAAM,CAACjK,CAAC,CAAC;;EAEb;EACA,IAAIA,CAAC,EAAE;IAEL;IACA,OAAOA,CAAC,GAAG,EAAE,IAAI,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEW,GAAG,EAAE;;IAElC;IACA,KAAKX,CAAC,GAAGiK,MAAM,CAAC,CAAC,CAAC,EAAEjK,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAEW,GAAG,EAAE;EAC7C;EAEA,OAAOA,GAAG;AACZ;AAGA,SAASoH,aAAaA,CAAC9K,CAAC,EAAE;EACxB,IAAIsN,EAAE,GAAG,EAAE;EACX,OAAOtN,CAAC,EAAE,GAAGsN,EAAE,IAAI,GAAG;EACtB,OAAOA,EAAE;AACX;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASlD,MAAMA,CAACnK,IAAI,EAAEX,CAAC,EAAEqC,CAAC,EAAER,EAAE,EAAE;EAC9B,IAAI4L,WAAW;IACbnL,CAAC,GAAG,IAAI3B,IAAI,CAAC,CAAC,CAAC;IAEf;IACA;IACAD,CAAC,GAAG5B,IAAI,CAACsB,IAAI,CAACyB,EAAE,GAAGtC,QAAQ,GAAG,CAAC,CAAC;EAElChB,QAAQ,GAAG,KAAK;EAEhB,SAAS;IACP,IAAI8D,CAAC,GAAG,CAAC,EAAE;MACTC,CAAC,GAAGA,CAAC,CAACY,KAAK,CAAClD,CAAC,CAAC;MACd,IAAIiO,QAAQ,CAAC3L,CAAC,CAACf,CAAC,EAAEb,CAAC,CAAC,EAAE+M,WAAW,GAAG,IAAI;IAC1C;IAEApL,CAAC,GAAGxD,SAAS,CAACwD,CAAC,GAAG,CAAC,CAAC;IACpB,IAAIA,CAAC,KAAK,CAAC,EAAE;MAEX;MACAA,CAAC,GAAGC,CAAC,CAACf,CAAC,CAAC7B,MAAM,GAAG,CAAC;MAClB,IAAI+N,WAAW,IAAInL,CAAC,CAACf,CAAC,CAACc,CAAC,CAAC,KAAK,CAAC,EAAE,EAAEC,CAAC,CAACf,CAAC,CAACc,CAAC,CAAC;MACzC;IACF;IAEArC,CAAC,GAAGA,CAAC,CAACkD,KAAK,CAAClD,CAAC,CAAC;IACdiO,QAAQ,CAACjO,CAAC,CAACuB,CAAC,EAAEb,CAAC,CAAC;EAClB;EAEAnC,QAAQ,GAAG,IAAI;EAEf,OAAO+D,CAAC;AACV;AAGA,SAAS4L,KAAKA,CAAC7L,CAAC,EAAE;EAChB,OAAOA,CAAC,CAACd,CAAC,CAACc,CAAC,CAACd,CAAC,CAAC7B,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;AAChC;;AAGA;AACA;AACA;AACA,SAASyO,QAAQA,CAACxN,IAAI,EAAEyN,IAAI,EAAE/L,CAAC,EAAE;EAC/B,IAAI3B,CAAC;IAAEO,CAAC;IACNjB,CAAC,GAAG,IAAIW,IAAI,CAACyN,IAAI,CAAC,CAAC,CAAC,CAAC;IACrBlN,CAAC,GAAG,CAAC;EAEP,OAAO,EAAEA,CAAC,GAAGkN,IAAI,CAAC1O,MAAM,GAAG;IACzBuB,CAAC,GAAG,IAAIN,IAAI,CAACyN,IAAI,CAAClN,CAAC,CAAC,CAAC;;IAErB;IACA,IAAI,CAACD,CAAC,CAACf,CAAC,EAAE;MACRF,CAAC,GAAGiB,CAAC;MACL;IACF;IAEAP,CAAC,GAAGV,CAAC,CAACe,GAAG,CAACE,CAAC,CAAC;IAEZ,IAAIP,CAAC,KAAK2B,CAAC,IAAI3B,CAAC,KAAK,CAAC,IAAIV,CAAC,CAACE,CAAC,KAAKmC,CAAC,EAAE;MACnCrC,CAAC,GAAGiB,CAAC;IACP;EACF;EAEA,OAAOjB,CAAC;AACV;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwI,kBAAkBA,CAACxI,CAAC,EAAE+B,EAAE,EAAE;EACjC,IAAIuF,WAAW;IAAEI,KAAK;IAAEvG,CAAC;IAAElC,GAAG;IAAEoP,GAAG;IAAE7L,CAAC;IAAEyD,GAAG;IACzC1D,GAAG,GAAG,CAAC;IACPrB,CAAC,GAAG,CAAC;IACLR,CAAC,GAAG,CAAC;IACLC,IAAI,GAAGX,CAAC,CAACC,WAAW;IACpB6B,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;IAClB+D,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;;EAErB;EACA,IAAI,CAACmC,CAAC,CAACuB,CAAC,IAAI,CAACvB,CAAC,CAACuB,CAAC,CAAC,CAAC,CAAC,IAAIvB,CAAC,CAACK,CAAC,GAAG,EAAE,EAAE;IAE/B,OAAO,IAAIM,IAAI,CAACX,CAAC,CAACuB,CAAC,GACf,CAACvB,CAAC,CAACuB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGvB,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GACjCF,CAAC,CAACE,CAAC,GAAGF,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGF,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACpC;EAEA,IAAI+B,EAAE,IAAI,IAAI,EAAE;IACdxD,QAAQ,GAAG,KAAK;IAChB0H,GAAG,GAAGpE,EAAE;EACV,CAAC,MAAM;IACLoE,GAAG,GAAGlE,EAAE;EACV;EAEAS,CAAC,GAAG,IAAI7B,IAAI,CAAC,OAAO,CAAC;;EAErB;EACA,OAAOX,CAAC,CAACK,CAAC,GAAG,CAAC,CAAC,EAAE;IAEf;IACAL,CAAC,GAAGA,CAAC,CAACkD,KAAK,CAACV,CAAC,CAAC;IACd9B,CAAC,IAAI,CAAC;EACR;;EAEA;EACA;EACAgH,KAAK,GAAG5I,IAAI,CAACqI,GAAG,CAACnI,OAAO,CAAC,CAAC,EAAE0B,CAAC,CAAC,CAAC,GAAG5B,IAAI,CAACpB,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EACvDuI,GAAG,IAAIyB,KAAK;EACZJ,WAAW,GAAGrI,GAAG,GAAGoP,GAAG,GAAG,IAAI1N,IAAI,CAAC,CAAC,CAAC;EACrCA,IAAI,CAAC9C,SAAS,GAAGoI,GAAG;EAEpB,SAAS;IACPhH,GAAG,GAAGkB,QAAQ,CAAClB,GAAG,CAACiE,KAAK,CAAClD,CAAC,CAAC,EAAEiG,GAAG,EAAE,CAAC,CAAC;IACpCqB,WAAW,GAAGA,WAAW,CAACpE,KAAK,CAAC,EAAEhC,CAAC,CAAC;IACpCsB,CAAC,GAAG6L,GAAG,CAAClL,IAAI,CAACC,MAAM,CAACnE,GAAG,EAAEqI,WAAW,EAAErB,GAAG,EAAE,CAAC,CAAC,CAAC;IAE9C,IAAIpD,cAAc,CAACL,CAAC,CAACjB,CAAC,CAAC,CAACwB,KAAK,CAAC,CAAC,EAAEkD,GAAG,CAAC,KAAKpD,cAAc,CAACwL,GAAG,CAAC9M,CAAC,CAAC,CAACwB,KAAK,CAAC,CAAC,EAAEkD,GAAG,CAAC,EAAE;MAC7E9E,CAAC,GAAGT,CAAC;MACL,OAAOS,CAAC,EAAE,EAAEkN,GAAG,GAAGlO,QAAQ,CAACkO,GAAG,CAACnL,KAAK,CAACmL,GAAG,CAAC,EAAEpI,GAAG,EAAE,CAAC,CAAC;;MAElD;MACA;MACA;MACA;MACA;MACA,IAAIlE,EAAE,IAAI,IAAI,EAAE;QAEd,IAAIQ,GAAG,GAAG,CAAC,IAAIsF,mBAAmB,CAACwG,GAAG,CAAC9M,CAAC,EAAE0E,GAAG,GAAGyB,KAAK,EAAE5F,EAAE,EAAES,GAAG,CAAC,EAAE;UAC/D5B,IAAI,CAAC9C,SAAS,GAAGoI,GAAG,IAAI,EAAE;UAC1BqB,WAAW,GAAGrI,GAAG,GAAGuD,CAAC,GAAG,IAAI7B,IAAI,CAAC,CAAC,CAAC;UACnCO,CAAC,GAAG,CAAC;UACLqB,GAAG,EAAE;QACP,CAAC,MAAM;UACL,OAAOpC,QAAQ,CAACkO,GAAG,EAAE1N,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,EAAEC,EAAE,EAAEvD,QAAQ,GAAG,IAAI,CAAC;QAChE;MACF,CAAC,MAAM;QACLoC,IAAI,CAAC9C,SAAS,GAAGgE,EAAE;QACnB,OAAOwM,GAAG;MACZ;IACF;IAEAA,GAAG,GAAG7L,CAAC;EACT;AACF;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmF,gBAAgBA,CAAC1G,CAAC,EAAEc,EAAE,EAAE;EAC/B,IAAIuM,CAAC;IAAEC,EAAE;IAAEjH,WAAW;IAAEjH,CAAC;IAAEmO,SAAS;IAAEjM,GAAG;IAAE8L,GAAG;IAAE7L,CAAC;IAAEyD,GAAG;IAAEwI,EAAE;IAAEjI,EAAE;IAC5DnE,CAAC,GAAG,CAAC;IACLqF,KAAK,GAAG,EAAE;IACV1H,CAAC,GAAGiB,CAAC;IACLK,EAAE,GAAGtB,CAAC,CAACuB,CAAC;IACRZ,IAAI,GAAGX,CAAC,CAACC,WAAW;IACpB6B,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;IAClB+D,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;;EAErB;EACA,IAAImC,CAAC,CAACE,CAAC,GAAG,CAAC,IAAI,CAACoB,EAAE,IAAI,CAACA,EAAE,CAAC,CAAC,CAAC,IAAI,CAACtB,CAAC,CAACK,CAAC,IAAIiB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAIA,EAAE,CAAC5B,MAAM,IAAI,CAAC,EAAE;IACpE,OAAO,IAAIiB,IAAI,CAACW,EAAE,IAAI,CAACA,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGtB,CAAC,CAACE,CAAC,IAAI,CAAC,GAAGU,GAAG,GAAGU,EAAE,GAAG,CAAC,GAAGtB,CAAC,CAAC;EACtE;EAEA,IAAI+B,EAAE,IAAI,IAAI,EAAE;IACdxD,QAAQ,GAAG,KAAK;IAChB0H,GAAG,GAAGpE,EAAE;EACV,CAAC,MAAM;IACLoE,GAAG,GAAGlE,EAAE;EACV;EAEApB,IAAI,CAAC9C,SAAS,GAAGoI,GAAG,IAAIyB,KAAK;EAC7B4G,CAAC,GAAGzL,cAAc,CAACvB,EAAE,CAAC;EACtBiN,EAAE,GAAGD,CAAC,CAAChL,MAAM,CAAC,CAAC,CAAC;EAEhB,IAAIxE,IAAI,CAACiB,GAAG,CAACM,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC,GAAG,MAAM,EAAE;IAE9B;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA,OAAOkO,EAAE,GAAG,CAAC,IAAIA,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,IAAID,CAAC,CAAChL,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACtDtD,CAAC,GAAGA,CAAC,CAACkD,KAAK,CAACjC,CAAC,CAAC;MACdqN,CAAC,GAAGzL,cAAc,CAAC7C,CAAC,CAACuB,CAAC,CAAC;MACvBgN,EAAE,GAAGD,CAAC,CAAChL,MAAM,CAAC,CAAC,CAAC;MAChBjB,CAAC,EAAE;IACL;IAEAhC,CAAC,GAAGL,CAAC,CAACK,CAAC;IAEP,IAAIkO,EAAE,GAAG,CAAC,EAAE;MACVvO,CAAC,GAAG,IAAIW,IAAI,CAAC,IAAI,GAAG2N,CAAC,CAAC;MACtBjO,CAAC,EAAE;IACL,CAAC,MAAM;MACLL,CAAC,GAAG,IAAIW,IAAI,CAAC4N,EAAE,GAAG,GAAG,GAAGD,CAAC,CAACvL,KAAK,CAAC,CAAC,CAAC,CAAC;IACrC;EACF,CAAC,MAAM;IAEL;IACA;IACA;IACAP,CAAC,GAAGoF,OAAO,CAACjH,IAAI,EAAEsF,GAAG,GAAG,CAAC,EAAEpE,EAAE,CAAC,CAACqB,KAAK,CAAC7C,CAAC,GAAG,EAAE,CAAC;IAC5CL,CAAC,GAAG2H,gBAAgB,CAAC,IAAIhH,IAAI,CAAC4N,EAAE,GAAG,GAAG,GAAGD,CAAC,CAACvL,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEkD,GAAG,GAAGyB,KAAK,CAAC,CAACvE,IAAI,CAACX,CAAC,CAAC;IAC1E7B,IAAI,CAAC9C,SAAS,GAAGgE,EAAE;IAEnB,OAAOE,EAAE,IAAI,IAAI,GAAG5B,QAAQ,CAACH,CAAC,EAAE6B,EAAE,EAAEC,EAAE,EAAEvD,QAAQ,GAAG,IAAI,CAAC,GAAGyB,CAAC;EAC9D;;EAEA;EACAyO,EAAE,GAAGzO,CAAC;;EAEN;EACA;EACA;EACAqO,GAAG,GAAGG,SAAS,GAAGxO,CAAC,GAAGoD,MAAM,CAACpD,CAAC,CAAC0E,KAAK,CAAC,CAAC,CAAC,EAAE1E,CAAC,CAACmD,IAAI,CAAC,CAAC,CAAC,EAAE8C,GAAG,EAAE,CAAC,CAAC;EAC3DO,EAAE,GAAGrG,QAAQ,CAACH,CAAC,CAACkD,KAAK,CAAClD,CAAC,CAAC,EAAEiG,GAAG,EAAE,CAAC,CAAC;EACjCqB,WAAW,GAAG,CAAC;EAEf,SAAS;IACPkH,SAAS,GAAGrO,QAAQ,CAACqO,SAAS,CAACtL,KAAK,CAACsD,EAAE,CAAC,EAAEP,GAAG,EAAE,CAAC,CAAC;IACjDzD,CAAC,GAAG6L,GAAG,CAAClL,IAAI,CAACC,MAAM,CAACoL,SAAS,EAAE,IAAI7N,IAAI,CAAC2G,WAAW,CAAC,EAAErB,GAAG,EAAE,CAAC,CAAC,CAAC;IAE9D,IAAIpD,cAAc,CAACL,CAAC,CAACjB,CAAC,CAAC,CAACwB,KAAK,CAAC,CAAC,EAAEkD,GAAG,CAAC,KAAKpD,cAAc,CAACwL,GAAG,CAAC9M,CAAC,CAAC,CAACwB,KAAK,CAAC,CAAC,EAAEkD,GAAG,CAAC,EAAE;MAC7EoI,GAAG,GAAGA,GAAG,CAACnL,KAAK,CAAC,CAAC,CAAC;;MAElB;MACA;MACA,IAAI7C,CAAC,KAAK,CAAC,EAAEgO,GAAG,GAAGA,GAAG,CAAClL,IAAI,CAACyE,OAAO,CAACjH,IAAI,EAAEsF,GAAG,GAAG,CAAC,EAAEpE,EAAE,CAAC,CAACqB,KAAK,CAAC7C,CAAC,GAAG,EAAE,CAAC,CAAC;MACrEgO,GAAG,GAAGjL,MAAM,CAACiL,GAAG,EAAE,IAAI1N,IAAI,CAAC0B,CAAC,CAAC,EAAE4D,GAAG,EAAE,CAAC,CAAC;;MAEtC;MACA;MACA;MACA;MACA;MACA;MACA,IAAIlE,EAAE,IAAI,IAAI,EAAE;QACd,IAAI8F,mBAAmB,CAACwG,GAAG,CAAC9M,CAAC,EAAE0E,GAAG,GAAGyB,KAAK,EAAE5F,EAAE,EAAES,GAAG,CAAC,EAAE;UACpD5B,IAAI,CAAC9C,SAAS,GAAGoI,GAAG,IAAIyB,KAAK;UAC7BlF,CAAC,GAAGgM,SAAS,GAAGxO,CAAC,GAAGoD,MAAM,CAACqL,EAAE,CAAC/J,KAAK,CAAC,CAAC,CAAC,EAAE+J,EAAE,CAACtL,IAAI,CAAC,CAAC,CAAC,EAAE8C,GAAG,EAAE,CAAC,CAAC;UAC3DO,EAAE,GAAGrG,QAAQ,CAACH,CAAC,CAACkD,KAAK,CAAClD,CAAC,CAAC,EAAEiG,GAAG,EAAE,CAAC,CAAC;UACjCqB,WAAW,GAAG/E,GAAG,GAAG,CAAC;QACvB,CAAC,MAAM;UACL,OAAOpC,QAAQ,CAACkO,GAAG,EAAE1N,IAAI,CAAC9C,SAAS,GAAGgE,EAAE,EAAEC,EAAE,EAAEvD,QAAQ,GAAG,IAAI,CAAC;QAChE;MACF,CAAC,MAAM;QACLoC,IAAI,CAAC9C,SAAS,GAAGgE,EAAE;QACnB,OAAOwM,GAAG;MACZ;IACF;IAEAA,GAAG,GAAG7L,CAAC;IACP8E,WAAW,IAAI,CAAC;EAClB;AACF;;AAGA;AACA,SAASyG,iBAAiBA,CAAC/N,CAAC,EAAE;EAC5B;EACA,OAAO0O,MAAM,CAAC1O,CAAC,CAACE,CAAC,GAAGF,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC;AAC9B;;AAGA;AACA;AACA;AACA,SAASyO,YAAYA,CAAC3O,CAAC,EAAE6J,GAAG,EAAE;EAC5B,IAAIxJ,CAAC,EAAEa,CAAC,EAAEkD,GAAG;;EAEb;EACA;EACA,IAAI,CAAC/D,CAAC,GAAGwJ,GAAG,CAAC7G,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE6G,GAAG,GAAGA,GAAG,CAAC+E,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;;EAE3D;EACA,IAAI,CAAC1N,CAAC,GAAG2I,GAAG,CAACgF,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;IAE9B;IACA,IAAIxO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,CAAC;IAChBb,CAAC,IAAI,CAACwJ,GAAG,CAAC9G,KAAK,CAAC7B,CAAC,GAAG,CAAC,CAAC;IACtB2I,GAAG,GAAGA,GAAG,CAACiF,SAAS,CAAC,CAAC,EAAE5N,CAAC,CAAC;EAC3B,CAAC,MAAM,IAAIb,CAAC,GAAG,CAAC,EAAE;IAEhB;IACAA,CAAC,GAAGwJ,GAAG,CAACnK,MAAM;EAChB;;EAEA;EACA,KAAKwB,CAAC,GAAG,CAAC,EAAE2I,GAAG,CAACkF,UAAU,CAAC7N,CAAC,CAAC,KAAK,EAAE,EAAEA,CAAC,EAAE,CAAC;;EAE1C;EACA,KAAKkD,GAAG,GAAGyF,GAAG,CAACnK,MAAM,EAAEmK,GAAG,CAACkF,UAAU,CAAC3K,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,EAAEA,GAAG,CAAC;EAC7DyF,GAAG,GAAGA,GAAG,CAAC9G,KAAK,CAAC7B,CAAC,EAAEkD,GAAG,CAAC;EAEvB,IAAIyF,GAAG,EAAE;IACPzF,GAAG,IAAIlD,CAAC;IACRlB,CAAC,CAACK,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGa,CAAC,GAAG,CAAC;IACnBlB,CAAC,CAACuB,CAAC,GAAG,EAAE;;IAER;;IAEA;IACA;IACAL,CAAC,GAAG,CAACb,CAAC,GAAG,CAAC,IAAId,QAAQ;IACtB,IAAIc,CAAC,GAAG,CAAC,EAAEa,CAAC,IAAI3B,QAAQ;IAExB,IAAI2B,CAAC,GAAGkD,GAAG,EAAE;MACX,IAAIlD,CAAC,EAAElB,CAAC,CAACuB,CAAC,CAAC2G,IAAI,CAAC,CAAC2B,GAAG,CAAC9G,KAAK,CAAC,CAAC,EAAE7B,CAAC,CAAC,CAAC;MACjC,KAAKkD,GAAG,IAAI7E,QAAQ,EAAE2B,CAAC,GAAGkD,GAAG,GAAGpE,CAAC,CAACuB,CAAC,CAAC2G,IAAI,CAAC,CAAC2B,GAAG,CAAC9G,KAAK,CAAC7B,CAAC,EAAEA,CAAC,IAAI3B,QAAQ,CAAC,CAAC;MACtEsK,GAAG,GAAGA,GAAG,CAAC9G,KAAK,CAAC7B,CAAC,CAAC;MAClBA,CAAC,GAAG3B,QAAQ,GAAGsK,GAAG,CAACnK,MAAM;IAC3B,CAAC,MAAM;MACLwB,CAAC,IAAIkD,GAAG;IACV;IAEA,OAAOlD,CAAC,EAAE,GAAG2I,GAAG,IAAI,GAAG;IACvB7J,CAAC,CAACuB,CAAC,CAAC2G,IAAI,CAAC,CAAC2B,GAAG,CAAC;IAEd,IAAItL,QAAQ,EAAE;MAEZ;MACA,IAAIyB,CAAC,CAACK,CAAC,GAAGL,CAAC,CAACC,WAAW,CAAC9B,IAAI,EAAE;QAE5B;QACA6B,CAAC,CAACuB,CAAC,GAAG,IAAI;QACVvB,CAAC,CAACK,CAAC,GAAGO,GAAG;;QAEX;MACA,CAAC,MAAM,IAAIZ,CAAC,CAACK,CAAC,GAAGL,CAAC,CAACC,WAAW,CAAC/B,IAAI,EAAE;QAEnC;QACA8B,CAAC,CAACK,CAAC,GAAG,CAAC;QACPL,CAAC,CAACuB,CAAC,GAAG,CAAC,CAAC,CAAC;QACT;MACF,CAAC,CAAC;IACJ;EACF,CAAC,MAAM;IAEL;IACAvB,CAAC,CAACK,CAAC,GAAG,CAAC;IACPL,CAAC,CAACuB,CAAC,GAAG,CAAC,CAAC,CAAC;EACX;EAEA,OAAOvB,CAAC;AACV;;AAGA;AACA;AACA;AACA,SAASgP,UAAUA,CAAChP,CAAC,EAAE6J,GAAG,EAAE;EAC1B,IAAIzC,IAAI,EAAEzG,IAAI,EAAEsO,OAAO,EAAE/N,CAAC,EAAEgO,OAAO,EAAE9K,GAAG,EAAE+K,CAAC,EAAE7N,EAAE,EAAEyG,EAAE;EAEnD,IAAI8B,GAAG,CAAC7G,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;IACzB6G,GAAG,GAAGA,GAAG,CAAC+E,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC;IACvC,IAAIvP,SAAS,CAAC+P,IAAI,CAACvF,GAAG,CAAC,EAAE,OAAO8E,YAAY,CAAC3O,CAAC,EAAE6J,GAAG,CAAC;EACtD,CAAC,MAAM,IAAIA,GAAG,KAAK,UAAU,IAAIA,GAAG,KAAK,KAAK,EAAE;IAC9C,IAAI,CAAC,CAACA,GAAG,EAAE7J,CAAC,CAACE,CAAC,GAAGU,GAAG;IACpBZ,CAAC,CAACK,CAAC,GAAGO,GAAG;IACTZ,CAAC,CAACuB,CAAC,GAAG,IAAI;IACV,OAAOvB,CAAC;EACV;EAEA,IAAIb,KAAK,CAACiQ,IAAI,CAACvF,GAAG,CAAC,EAAG;IACpBzC,IAAI,GAAG,EAAE;IACTyC,GAAG,GAAGA,GAAG,CAACwF,WAAW,CAAC,CAAC;EACzB,CAAC,MAAM,IAAInQ,QAAQ,CAACkQ,IAAI,CAACvF,GAAG,CAAC,EAAG;IAC9BzC,IAAI,GAAG,CAAC;EACV,CAAC,MAAM,IAAIhI,OAAO,CAACgQ,IAAI,CAACvF,GAAG,CAAC,EAAG;IAC7BzC,IAAI,GAAG,CAAC;EACV,CAAC,MAAM;IACL,MAAMtG,KAAK,CAACrC,eAAe,GAAGoL,GAAG,CAAC;EACpC;;EAEA;EACA3I,CAAC,GAAG2I,GAAG,CAACgF,MAAM,CAAC,IAAI,CAAC;EAEpB,IAAI3N,CAAC,GAAG,CAAC,EAAE;IACTiO,CAAC,GAAG,CAACtF,GAAG,CAAC9G,KAAK,CAAC7B,CAAC,GAAG,CAAC,CAAC;IACrB2I,GAAG,GAAGA,GAAG,CAACiF,SAAS,CAAC,CAAC,EAAE5N,CAAC,CAAC;EAC3B,CAAC,MAAM;IACL2I,GAAG,GAAGA,GAAG,CAAC9G,KAAK,CAAC,CAAC,CAAC;EACpB;;EAEA;EACA;EACA7B,CAAC,GAAG2I,GAAG,CAAC7G,OAAO,CAAC,GAAG,CAAC;EACpBkM,OAAO,GAAGhO,CAAC,IAAI,CAAC;EAChBP,IAAI,GAAGX,CAAC,CAACC,WAAW;EAEpB,IAAIiP,OAAO,EAAE;IACXrF,GAAG,GAAGA,GAAG,CAAC+E,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;IAC1BxK,GAAG,GAAGyF,GAAG,CAACnK,MAAM;IAChBwB,CAAC,GAAGkD,GAAG,GAAGlD,CAAC;;IAEX;IACA+N,OAAO,GAAGnE,MAAM,CAACnK,IAAI,EAAE,IAAIA,IAAI,CAACyG,IAAI,CAAC,EAAElG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;EAClD;EAEAI,EAAE,GAAGsK,WAAW,CAAC/B,GAAG,EAAEzC,IAAI,EAAE9H,IAAI,CAAC;EACjCyI,EAAE,GAAGzG,EAAE,CAAC5B,MAAM,GAAG,CAAC;;EAElB;EACA,KAAKwB,CAAC,GAAG6G,EAAE,EAAEzG,EAAE,CAACJ,CAAC,CAAC,KAAK,CAAC,EAAE,EAAEA,CAAC,EAAEI,EAAE,CAAC6G,GAAG,CAAC,CAAC;EACvC,IAAIjH,CAAC,GAAG,CAAC,EAAE,OAAO,IAAIP,IAAI,CAACX,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC;EACnCF,CAAC,CAACK,CAAC,GAAGgI,iBAAiB,CAAC/G,EAAE,EAAEyG,EAAE,CAAC;EAC/B/H,CAAC,CAACuB,CAAC,GAAGD,EAAE;EACR/C,QAAQ,GAAG,KAAK;;EAEhB;EACA;EACA;EACA;EACA;EACA;EACA,IAAI2Q,OAAO,EAAElP,CAAC,GAAGoD,MAAM,CAACpD,CAAC,EAAEiP,OAAO,EAAE7K,GAAG,GAAG,CAAC,CAAC;;EAE5C;EACA,IAAI+K,CAAC,EAAEnP,CAAC,GAAGA,CAAC,CAACkD,KAAK,CAACpE,IAAI,CAACiB,GAAG,CAACoP,CAAC,CAAC,GAAG,EAAE,GAAGnQ,OAAO,CAAC,CAAC,EAAEmQ,CAAC,CAAC,GAAGG,OAAO,CAACrQ,GAAG,CAAC,CAAC,EAAEkQ,CAAC,CAAC,CAAC;EACxE5Q,QAAQ,GAAG,IAAI;EAEf,OAAOyB,CAAC;AACV;;AAGA;AACA;AACA;AACA;AACA;AACA,SAASiJ,IAAIA,CAACtI,IAAI,EAAEX,CAAC,EAAE;EACrB,IAAIU,CAAC;IACH0D,GAAG,GAAGpE,CAAC,CAACuB,CAAC,CAAC7B,MAAM;EAElB,IAAI0E,GAAG,GAAG,CAAC,EAAE;IACX,OAAOpE,CAAC,CAAC4C,MAAM,CAAC,CAAC,GAAG5C,CAAC,GAAGuE,YAAY,CAAC5D,IAAI,EAAE,CAAC,EAAEX,CAAC,EAAEA,CAAC,CAAC;EACrD;;EAEA;EACA;EACA;;EAEA;EACAU,CAAC,GAAG,GAAG,GAAG5B,IAAI,CAAC+F,IAAI,CAACT,GAAG,CAAC;EACxB1D,CAAC,GAAGA,CAAC,GAAG,EAAE,GAAG,EAAE,GAAGA,CAAC,GAAG,CAAC;EAEvBV,CAAC,GAAGA,CAAC,CAACkD,KAAK,CAAC,CAAC,GAAGoB,OAAO,CAAC,CAAC,EAAE5D,CAAC,CAAC,CAAC;EAC9BV,CAAC,GAAGuE,YAAY,CAAC5D,IAAI,EAAE,CAAC,EAAEX,CAAC,EAAEA,CAAC,CAAC;;EAE/B;EACA,IAAIuP,MAAM;IACRxK,EAAE,GAAG,IAAIpE,IAAI,CAAC,CAAC,CAAC;IAChBqE,GAAG,GAAG,IAAIrE,IAAI,CAAC,EAAE,CAAC;IAClBsE,GAAG,GAAG,IAAItE,IAAI,CAAC,EAAE,CAAC;EACpB,OAAOD,CAAC,EAAE,GAAG;IACX6O,MAAM,GAAGvP,CAAC,CAACkD,KAAK,CAAClD,CAAC,CAAC;IACnBA,CAAC,GAAGA,CAAC,CAACkD,KAAK,CAAC6B,EAAE,CAAC5B,IAAI,CAACoM,MAAM,CAACrM,KAAK,CAAC8B,GAAG,CAAC9B,KAAK,CAACqM,MAAM,CAAC,CAAC7K,KAAK,CAACO,GAAG,CAAC,CAAC,CAAC,CAAC;EAClE;EAEA,OAAOjF,CAAC;AACV;;AAGA;AACA,SAASuE,YAAYA,CAAC5D,IAAI,EAAE0B,CAAC,EAAErC,CAAC,EAAEiB,CAAC,EAAEuO,YAAY,EAAE;EACjD,IAAIrO,CAAC;IAAEqB,CAAC;IAAEiN,CAAC;IAAEjJ,EAAE;IACbtF,CAAC,GAAG,CAAC;IACLW,EAAE,GAAGlB,IAAI,CAAC9C,SAAS;IACnB6C,CAAC,GAAG5B,IAAI,CAACsB,IAAI,CAACyB,EAAE,GAAGtC,QAAQ,CAAC;EAE9BhB,QAAQ,GAAG,KAAK;EAChBiI,EAAE,GAAGxG,CAAC,CAACkD,KAAK,CAAClD,CAAC,CAAC;EACfyP,CAAC,GAAG,IAAI9O,IAAI,CAACM,CAAC,CAAC;EAEf,SAAS;IACPuB,CAAC,GAAGY,MAAM,CAACqM,CAAC,CAACvM,KAAK,CAACsD,EAAE,CAAC,EAAE,IAAI7F,IAAI,CAAC0B,CAAC,EAAE,GAAGA,CAAC,EAAE,CAAC,EAAER,EAAE,EAAE,CAAC,CAAC;IACnD4N,CAAC,GAAGD,YAAY,GAAGvO,CAAC,CAACkC,IAAI,CAACX,CAAC,CAAC,GAAGvB,CAAC,CAACyD,KAAK,CAAClC,CAAC,CAAC;IACzCvB,CAAC,GAAGmC,MAAM,CAACZ,CAAC,CAACU,KAAK,CAACsD,EAAE,CAAC,EAAE,IAAI7F,IAAI,CAAC0B,CAAC,EAAE,GAAGA,CAAC,EAAE,CAAC,EAAER,EAAE,EAAE,CAAC,CAAC;IACnDW,CAAC,GAAGiN,CAAC,CAACtM,IAAI,CAAClC,CAAC,CAAC;IAEb,IAAIuB,CAAC,CAACjB,CAAC,CAACb,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE;MACrB,KAAKS,CAAC,GAAGT,CAAC,EAAE8B,CAAC,CAACjB,CAAC,CAACJ,CAAC,CAAC,KAAKsO,CAAC,CAAClO,CAAC,CAACJ,CAAC,CAAC,IAAIA,CAAC,EAAE,EAAE;MACtC,IAAIA,CAAC,IAAI,CAAC,CAAC,EAAE;IACf;IAEAA,CAAC,GAAGsO,CAAC;IACLA,CAAC,GAAGxO,CAAC;IACLA,CAAC,GAAGuB,CAAC;IACLA,CAAC,GAAGrB,CAAC;IACLD,CAAC,EAAE;EACL;EAEA3C,QAAQ,GAAG,IAAI;EACfiE,CAAC,CAACjB,CAAC,CAAC7B,MAAM,GAAGgB,CAAC,GAAG,CAAC;EAElB,OAAO8B,CAAC;AACV;;AAGA;AACA,SAAS8B,OAAOA,CAACiI,CAAC,EAAElM,CAAC,EAAE;EACrB,IAAIgC,CAAC,GAAGkK,CAAC;EACT,OAAO,EAAElM,CAAC,EAAEgC,CAAC,IAAIkK,CAAC;EAClB,OAAOlK,CAAC;AACV;;AAGA;AACA,SAASL,gBAAgBA,CAACrB,IAAI,EAAEX,CAAC,EAAE;EACjC,IAAIwC,CAAC;IACH8C,KAAK,GAAGtF,CAAC,CAACE,CAAC,GAAG,CAAC;IACfwP,EAAE,GAAGnK,KAAK,CAAC5E,IAAI,EAAEA,IAAI,CAAC9C,SAAS,EAAE,CAAC,CAAC;IACnCwI,MAAM,GAAGqJ,EAAE,CAACxM,KAAK,CAAC,GAAG,CAAC;EAExBlD,CAAC,GAAGA,CAAC,CAACD,GAAG,CAAC,CAAC;EAEX,IAAIC,CAAC,CAAC2F,GAAG,CAACU,MAAM,CAAC,EAAE;IACjB/H,QAAQ,GAAGgH,KAAK,GAAG,CAAC,GAAG,CAAC;IACxB,OAAOtF,CAAC;EACV;EAEAwC,CAAC,GAAGxC,CAAC,CAAC6D,QAAQ,CAAC6L,EAAE,CAAC;EAElB,IAAIlN,CAAC,CAACI,MAAM,CAAC,CAAC,EAAE;IACdtE,QAAQ,GAAGgH,KAAK,GAAG,CAAC,GAAG,CAAC;EAC1B,CAAC,MAAM;IACLtF,CAAC,GAAGA,CAAC,CAAC0E,KAAK,CAAClC,CAAC,CAACU,KAAK,CAACwM,EAAE,CAAC,CAAC;;IAExB;IACA,IAAI1P,CAAC,CAAC2F,GAAG,CAACU,MAAM,CAAC,EAAE;MACjB/H,QAAQ,GAAG4P,KAAK,CAAC1L,CAAC,CAAC,GAAI8C,KAAK,GAAG,CAAC,GAAG,CAAC,GAAKA,KAAK,GAAG,CAAC,GAAG,CAAE;MACvD,OAAOtF,CAAC;IACV;IAEA1B,QAAQ,GAAG4P,KAAK,CAAC1L,CAAC,CAAC,GAAI8C,KAAK,GAAG,CAAC,GAAG,CAAC,GAAKA,KAAK,GAAG,CAAC,GAAG,CAAE;EACzD;EAEA,OAAOtF,CAAC,CAAC0E,KAAK,CAACgL,EAAE,CAAC,CAAC3P,GAAG,CAAC,CAAC;AAC1B;;AAGA;AACA;AACA;AACA;AACA;AACA,SAAS0J,cAAcA,CAACzJ,CAAC,EAAE8L,OAAO,EAAE/J,EAAE,EAAED,EAAE,EAAE;EAC1C,IAAIsF,IAAI;IAAE/G,CAAC;IAAEa,CAAC;IAAER,CAAC;IAAE0D,GAAG;IAAEuJ,OAAO;IAAE9D,GAAG;IAAEvI,EAAE;IAAEL,CAAC;IACzCN,IAAI,GAAGX,CAAC,CAACC,WAAW;IACpB6N,KAAK,GAAG/L,EAAE,KAAK,KAAK,CAAC;EAEvB,IAAI+L,KAAK,EAAE;IACTlE,UAAU,CAAC7H,EAAE,EAAE,CAAC,EAAEvE,UAAU,CAAC;IAC7B,IAAIsE,EAAE,KAAK,KAAK,CAAC,EAAEA,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ,CAAC,KACjC8L,UAAU,CAAC9H,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3B,CAAC,MAAM;IACLC,EAAE,GAAGpB,IAAI,CAAC9C,SAAS;IACnBiE,EAAE,GAAGnB,IAAI,CAAC7C,QAAQ;EACpB;EAEA,IAAI,CAACkC,CAAC,CAAC2C,QAAQ,CAAC,CAAC,EAAE;IACjBkH,GAAG,GAAGkE,iBAAiB,CAAC/N,CAAC,CAAC;EAC5B,CAAC,MAAM;IACL6J,GAAG,GAAGC,cAAc,CAAC9J,CAAC,CAAC;IACvBkB,CAAC,GAAG2I,GAAG,CAAC7G,OAAO,CAAC,GAAG,CAAC;;IAEpB;IACA;IACA;IACA;;IAEA,IAAI8K,KAAK,EAAE;MACT1G,IAAI,GAAG,CAAC;MACR,IAAI0E,OAAO,IAAI,EAAE,EAAE;QACjB/J,EAAE,GAAGA,EAAE,GAAG,CAAC,GAAG,CAAC;MACjB,CAAC,MAAM,IAAI+J,OAAO,IAAI,CAAC,EAAE;QACvB/J,EAAE,GAAGA,EAAE,GAAG,CAAC,GAAG,CAAC;MACjB;IACF,CAAC,MAAM;MACLqF,IAAI,GAAG0E,OAAO;IAChB;;IAEA;IACA;;IAEA;IACA,IAAI5K,CAAC,IAAI,CAAC,EAAE;MACV2I,GAAG,GAAGA,GAAG,CAAC+E,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;MAC1B3N,CAAC,GAAG,IAAIN,IAAI,CAAC,CAAC,CAAC;MACfM,CAAC,CAACZ,CAAC,GAAGwJ,GAAG,CAACnK,MAAM,GAAGwB,CAAC;MACpBD,CAAC,CAACM,CAAC,GAAGqK,WAAW,CAAC9B,cAAc,CAAC7I,CAAC,CAAC,EAAE,EAAE,EAAEmG,IAAI,CAAC;MAC9CnG,CAAC,CAACZ,CAAC,GAAGY,CAAC,CAACM,CAAC,CAAC7B,MAAM;IAClB;IAEA4B,EAAE,GAAGsK,WAAW,CAAC/B,GAAG,EAAE,EAAE,EAAEzC,IAAI,CAAC;IAC/B/G,CAAC,GAAG+D,GAAG,GAAG9C,EAAE,CAAC5B,MAAM;;IAEnB;IACA,OAAO4B,EAAE,CAAC,EAAE8C,GAAG,CAAC,IAAI,CAAC,GAAG9C,EAAE,CAAC6G,GAAG,CAAC,CAAC;IAEhC,IAAI,CAAC7G,EAAE,CAAC,CAAC,CAAC,EAAE;MACVuI,GAAG,GAAGiE,KAAK,GAAG,MAAM,GAAG,GAAG;IAC5B,CAAC,MAAM;MACL,IAAI5M,CAAC,GAAG,CAAC,EAAE;QACTb,CAAC,EAAE;MACL,CAAC,MAAM;QACLL,CAAC,GAAG,IAAIW,IAAI,CAACX,CAAC,CAAC;QACfA,CAAC,CAACuB,CAAC,GAAGD,EAAE;QACRtB,CAAC,CAACK,CAAC,GAAGA,CAAC;QACPL,CAAC,GAAGoD,MAAM,CAACpD,CAAC,EAAEiB,CAAC,EAAEc,EAAE,EAAED,EAAE,EAAE,CAAC,EAAEsF,IAAI,CAAC;QACjC9F,EAAE,GAAGtB,CAAC,CAACuB,CAAC;QACRlB,CAAC,GAAGL,CAAC,CAACK,CAAC;QACPsN,OAAO,GAAGtP,OAAO;MACnB;;MAEA;MACA6C,CAAC,GAAGI,EAAE,CAACS,EAAE,CAAC;MACVrB,CAAC,GAAG0G,IAAI,GAAG,CAAC;MACZuG,OAAO,GAAGA,OAAO,IAAIrM,EAAE,CAACS,EAAE,GAAG,CAAC,CAAC,KAAK,KAAK,CAAC;MAE1C4L,OAAO,GAAG7L,EAAE,GAAG,CAAC,GACZ,CAACZ,CAAC,KAAK,KAAK,CAAC,IAAIyM,OAAO,MAAM7L,EAAE,KAAK,CAAC,IAAIA,EAAE,MAAM9B,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GACnEgB,CAAC,GAAGR,CAAC,IAAIQ,CAAC,KAAKR,CAAC,KAAKoB,EAAE,KAAK,CAAC,IAAI6L,OAAO,IAAI7L,EAAE,KAAK,CAAC,IAAIR,EAAE,CAACS,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,IACtED,EAAE,MAAM9B,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAE7BoB,EAAE,CAAC5B,MAAM,GAAGqC,EAAE;MAEd,IAAI4L,OAAO,EAAE;QAEX;QACA,OAAO,EAAErM,EAAE,CAAC,EAAES,EAAE,CAAC,GAAGqF,IAAI,GAAG,CAAC,GAAG;UAC7B9F,EAAE,CAACS,EAAE,CAAC,GAAG,CAAC;UACV,IAAI,CAACA,EAAE,EAAE;YACP,EAAE1B,CAAC;YACHiB,EAAE,CAACuH,OAAO,CAAC,CAAC,CAAC;UACf;QACF;MACF;;MAEA;MACA,KAAKzE,GAAG,GAAG9C,EAAE,CAAC5B,MAAM,EAAE,CAAC4B,EAAE,CAAC8C,GAAG,GAAG,CAAC,CAAC,EAAE,EAAEA,GAAG,CAAC;;MAE1C;MACA,KAAKlD,CAAC,GAAG,CAAC,EAAE2I,GAAG,GAAG,EAAE,EAAE3I,CAAC,GAAGkD,GAAG,EAAElD,CAAC,EAAE,EAAE2I,GAAG,IAAIpM,QAAQ,CAAC6F,MAAM,CAAChC,EAAE,CAACJ,CAAC,CAAC,CAAC;;MAEjE;MACA,IAAI4M,KAAK,EAAE;QACT,IAAI1J,GAAG,GAAG,CAAC,EAAE;UACX,IAAI0H,OAAO,IAAI,EAAE,IAAIA,OAAO,IAAI,CAAC,EAAE;YACjC5K,CAAC,GAAG4K,OAAO,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC;YACzB,KAAK,EAAE1H,GAAG,EAAEA,GAAG,GAAGlD,CAAC,EAAEkD,GAAG,EAAE,EAAEyF,GAAG,IAAI,GAAG;YACtCvI,EAAE,GAAGsK,WAAW,CAAC/B,GAAG,EAAEzC,IAAI,EAAE0E,OAAO,CAAC;YACpC,KAAK1H,GAAG,GAAG9C,EAAE,CAAC5B,MAAM,EAAE,CAAC4B,EAAE,CAAC8C,GAAG,GAAG,CAAC,CAAC,EAAE,EAAEA,GAAG,CAAC;;YAE1C;YACA,KAAKlD,CAAC,GAAG,CAAC,EAAE2I,GAAG,GAAG,IAAI,EAAE3I,CAAC,GAAGkD,GAAG,EAAElD,CAAC,EAAE,EAAE2I,GAAG,IAAIpM,QAAQ,CAAC6F,MAAM,CAAChC,EAAE,CAACJ,CAAC,CAAC,CAAC;UACrE,CAAC,MAAM;YACL2I,GAAG,GAAGA,GAAG,CAACvG,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGuG,GAAG,CAAC9G,KAAK,CAAC,CAAC,CAAC;UAC1C;QACF;QAEA8G,GAAG,GAAIA,GAAG,IAAIxJ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAGA,CAAC;MACvC,CAAC,MAAM,IAAIA,CAAC,GAAG,CAAC,EAAE;QAChB,OAAO,EAAEA,CAAC,GAAGwJ,GAAG,GAAG,GAAG,GAAGA,GAAG;QAC5BA,GAAG,GAAG,IAAI,GAAGA,GAAG;MAClB,CAAC,MAAM;QACL,IAAI,EAAExJ,CAAC,GAAG+D,GAAG,EAAE,KAAK/D,CAAC,IAAI+D,GAAG,EAAE/D,CAAC,EAAE,GAAIwJ,GAAG,IAAI,GAAG,CAAC,KAC3C,IAAIxJ,CAAC,GAAG+D,GAAG,EAAEyF,GAAG,GAAGA,GAAG,CAAC9G,KAAK,CAAC,CAAC,EAAE1C,CAAC,CAAC,GAAG,GAAG,GAAGwJ,GAAG,CAAC9G,KAAK,CAAC1C,CAAC,CAAC;MAC9D;IACF;IAEAwJ,GAAG,GAAG,CAACiC,OAAO,IAAI,EAAE,GAAG,IAAI,GAAGA,OAAO,IAAI,CAAC,GAAG,IAAI,GAAGA,OAAO,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE,IAAIjC,GAAG;EACrF;EAEA,OAAO7J,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG2J,GAAG,GAAGA,GAAG;AAClC;;AAGA;AACA,SAASoE,QAAQA,CAAClC,GAAG,EAAE3H,GAAG,EAAE;EAC1B,IAAI2H,GAAG,CAACrM,MAAM,GAAG0E,GAAG,EAAE;IACpB2H,GAAG,CAACrM,MAAM,GAAG0E,GAAG;IAChB,OAAO,IAAI;EACb;AACF;;AAGA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,SAASrE,GAAGA,CAACC,CAAC,EAAE;EACd,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAACD,GAAG,CAAC,CAAC;AAC1B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsF,IAAIA,CAACrF,CAAC,EAAE;EACf,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAACqF,IAAI,CAAC,CAAC;AAC3B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,KAAKA,CAAC1F,CAAC,EAAE;EAChB,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAAC0F,KAAK,CAAC,CAAC;AAC5B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiD,GAAGA,CAAC3I,CAAC,EAAEiB,CAAC,EAAE;EACjB,OAAO,IAAI,IAAI,CAACjB,CAAC,CAAC,CAACmD,IAAI,CAAClC,CAAC,CAAC;AAC5B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmF,IAAIA,CAACpG,CAAC,EAAE;EACf,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAACoG,IAAI,CAAC,CAAC;AAC3B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASN,KAAKA,CAAC9F,CAAC,EAAE;EAChB,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAAC8F,KAAK,CAAC,CAAC;AAC5B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASN,IAAIA,CAACxF,CAAC,EAAE;EACf,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAACwF,IAAI,CAAC,CAAC;AAC3B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,KAAKA,CAAChG,CAAC,EAAE;EAChB,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAACgG,KAAK,CAAC,CAAC;AAC5B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2J,KAAKA,CAAC1O,CAAC,EAAEjB,CAAC,EAAE;EACnBiB,CAAC,GAAG,IAAI,IAAI,CAACA,CAAC,CAAC;EACfjB,CAAC,GAAG,IAAI,IAAI,CAACA,CAAC,CAAC;EACf,IAAIsC,CAAC;IACHT,EAAE,GAAG,IAAI,CAAChE,SAAS;IACnBiE,EAAE,GAAG,IAAI,CAAChE,QAAQ;IAClBmI,GAAG,GAAGpE,EAAE,GAAG,CAAC;;EAEd;EACA,IAAI,CAACZ,CAAC,CAACf,CAAC,IAAI,CAACF,CAAC,CAACE,CAAC,EAAE;IAChBoC,CAAC,GAAG,IAAI,IAAI,CAAC1B,GAAG,CAAC;;IAEnB;EACA,CAAC,MAAM,IAAI,CAACK,CAAC,CAACM,CAAC,IAAI,CAACvB,CAAC,CAACuB,CAAC,EAAE;IACvBe,CAAC,GAAGiD,KAAK,CAAC,IAAI,EAAEU,GAAG,EAAE,CAAC,CAAC,CAAC/C,KAAK,CAAClD,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;IACpDoC,CAAC,CAACpC,CAAC,GAAGe,CAAC,CAACf,CAAC;;IAEX;EACA,CAAC,MAAM,IAAI,CAACF,CAAC,CAACuB,CAAC,IAAIN,CAAC,CAAC2B,MAAM,CAAC,CAAC,EAAE;IAC7BN,CAAC,GAAGtC,CAAC,CAACE,CAAC,GAAG,CAAC,GAAGqF,KAAK,CAAC,IAAI,EAAE1D,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;IAC/CQ,CAAC,CAACpC,CAAC,GAAGe,CAAC,CAACf,CAAC;;IAEX;EACA,CAAC,MAAM,IAAI,CAACe,CAAC,CAACM,CAAC,IAAIvB,CAAC,CAAC4C,MAAM,CAAC,CAAC,EAAE;IAC7BN,CAAC,GAAGiD,KAAK,CAAC,IAAI,EAAEU,GAAG,EAAE,CAAC,CAAC,CAAC/C,KAAK,CAAC,GAAG,CAAC;IAClCZ,CAAC,CAACpC,CAAC,GAAGe,CAAC,CAACf,CAAC;;IAEX;EACA,CAAC,MAAM,IAAIF,CAAC,CAACE,CAAC,GAAG,CAAC,EAAE;IAClB,IAAI,CAACrC,SAAS,GAAGoI,GAAG;IACpB,IAAI,CAACnI,QAAQ,GAAG,CAAC;IACjBwE,CAAC,GAAG,IAAI,CAACkD,IAAI,CAACpC,MAAM,CAACnC,CAAC,EAAEjB,CAAC,EAAEiG,GAAG,EAAE,CAAC,CAAC,CAAC;IACnCjG,CAAC,GAAGuF,KAAK,CAAC,IAAI,EAAEU,GAAG,EAAE,CAAC,CAAC;IACvB,IAAI,CAACpI,SAAS,GAAGgE,EAAE;IACnB,IAAI,CAAC/D,QAAQ,GAAGgE,EAAE;IAClBQ,CAAC,GAAGrB,CAAC,CAACf,CAAC,GAAG,CAAC,GAAGoC,CAAC,CAACoC,KAAK,CAAC1E,CAAC,CAAC,GAAGsC,CAAC,CAACa,IAAI,CAACnD,CAAC,CAAC;EACtC,CAAC,MAAM;IACLsC,CAAC,GAAG,IAAI,CAACkD,IAAI,CAACpC,MAAM,CAACnC,CAAC,EAAEjB,CAAC,EAAEiG,GAAG,EAAE,CAAC,CAAC,CAAC;EACrC;EAEA,OAAO3D,CAAC;AACV;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASH,IAAIA,CAACnC,CAAC,EAAE;EACf,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAACmC,IAAI,CAAC,CAAC;AAC3B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS/B,IAAIA,CAACJ,CAAC,EAAE;EACf,OAAOG,QAAQ,CAACH,CAAC,GAAG,IAAI,IAAI,CAACA,CAAC,CAAC,EAAEA,CAAC,CAACK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC9C;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,KAAKA,CAACP,CAAC,EAAEQ,GAAG,EAAEC,GAAG,EAAE;EAC1B,OAAO,IAAI,IAAI,CAACT,CAAC,CAAC,CAACO,KAAK,CAACC,GAAG,EAAEC,GAAG,CAAC;AACpC;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmP,MAAMA,CAACC,GAAG,EAAE;EACnB,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,MAAM/O,KAAK,CAACtC,YAAY,GAAG,iBAAiB,CAAC;EAClF,IAAI0C,CAAC;IAAEiO,CAAC;IAAEW,CAAC;IACTC,WAAW,GAAGF,GAAG,CAACG,QAAQ,KAAK,IAAI;IACnCC,EAAE,GAAG,CACH,WAAW,EAAE,CAAC,EAAEzS,UAAU,EAC1B,UAAU,EAAE,CAAC,EAAE,CAAC,EAChB,UAAU,EAAE,CAACD,SAAS,EAAE,CAAC,EACzB,UAAU,EAAE,CAAC,EAAEA,SAAS,EACxB,MAAM,EAAE,CAAC,EAAEA,SAAS,EACpB,MAAM,EAAE,CAACA,SAAS,EAAE,CAAC,EACrB,QAAQ,EAAE,CAAC,EAAE,CAAC,CACf;EAEH,KAAK2D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+O,EAAE,CAACvQ,MAAM,EAAEwB,CAAC,IAAI,CAAC,EAAE;IACjC,IAAIiO,CAAC,GAAGc,EAAE,CAAC/O,CAAC,CAAC,EAAE6O,WAAW,EAAE,IAAI,CAACZ,CAAC,CAAC,GAAGvR,QAAQ,CAACuR,CAAC,CAAC;IACjD,IAAI,CAACW,CAAC,GAAGD,GAAG,CAACV,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;MAC3B,IAAItQ,SAAS,CAACiR,CAAC,CAAC,KAAKA,CAAC,IAAIA,CAAC,IAAIG,EAAE,CAAC/O,CAAC,GAAG,CAAC,CAAC,IAAI4O,CAAC,IAAIG,EAAE,CAAC/O,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACiO,CAAC,CAAC,GAAGW,CAAC,CAAC,KACnE,MAAMhP,KAAK,CAACrC,eAAe,GAAG0Q,CAAC,GAAG,IAAI,GAAGW,CAAC,CAAC;IAClD;EACF;EAEA,IAAIX,CAAC,GAAG,QAAQ,EAAEY,WAAW,EAAE,IAAI,CAACZ,CAAC,CAAC,GAAGvR,QAAQ,CAACuR,CAAC,CAAC;EACpD,IAAI,CAACW,CAAC,GAAGD,GAAG,CAACV,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;IAC3B,IAAIW,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,EAAE;MACnD,IAAIA,CAAC,EAAE;QACL,IAAI,OAAO1R,MAAM,IAAI,WAAW,IAAIA,MAAM,KACvCA,MAAM,CAAC8R,eAAe,IAAI9R,MAAM,CAAC+R,WAAW,CAAC,EAAE;UAChD,IAAI,CAAChB,CAAC,CAAC,GAAG,IAAI;QAChB,CAAC,MAAM;UACL,MAAMrO,KAAK,CAACnC,iBAAiB,CAAC;QAChC;MACF,CAAC,MAAM;QACL,IAAI,CAACwQ,CAAC,CAAC,GAAG,KAAK;MACjB;IACF,CAAC,MAAM;MACL,MAAMrO,KAAK,CAACrC,eAAe,GAAG0Q,CAAC,GAAG,IAAI,GAAGW,CAAC,CAAC;IAC7C;EACF;EAEA,OAAO,IAAI;AACb;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASlO,GAAGA,CAAC5B,CAAC,EAAE;EACd,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAAC4B,GAAG,CAAC,CAAC;AAC1B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuC,IAAIA,CAACnE,CAAC,EAAE;EACf,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAACmE,IAAI,CAAC,CAAC;AAC3B;;AAGA;AACA;AACA;AACA;AACA;AACA,SAASiM,KAAKA,CAACP,GAAG,EAAE;EAClB,IAAI3O,CAAC,EAAEiO,CAAC,EAAEc,EAAE;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASX,OAAOA,CAACQ,CAAC,EAAE;IAClB,IAAIzP,CAAC;MAAEa,CAAC;MAAEsB,CAAC;MACTxC,CAAC,GAAG,IAAI;;IAEV;IACA,IAAI,EAAEA,CAAC,YAAYsP,OAAO,CAAC,EAAE,OAAO,IAAIA,OAAO,CAACQ,CAAC,CAAC;;IAElD;IACA;IACA9P,CAAC,CAACC,WAAW,GAAGqP,OAAO;IAEvB,IAAIe,iBAAiB,CAACP,CAAC,CAAC,EAAE;MACxB9P,CAAC,CAACE,CAAC,GAAG4P,CAAC,CAAC5P,CAAC;MAET,IAAI3B,QAAQ,EAAE;QACZ,IAAI,CAACuR,CAAC,CAACvO,CAAC,IAAIuO,CAAC,CAACzP,CAAC,GAAGiP,OAAO,CAACnR,IAAI,EAAE;UAE9B;UACA6B,CAAC,CAACK,CAAC,GAAGO,GAAG;UACTZ,CAAC,CAACuB,CAAC,GAAG,IAAI;QACZ,CAAC,MAAM,IAAIuO,CAAC,CAACzP,CAAC,GAAGiP,OAAO,CAACpR,IAAI,EAAE;UAE7B;UACA8B,CAAC,CAACK,CAAC,GAAG,CAAC;UACPL,CAAC,CAACuB,CAAC,GAAG,CAAC,CAAC,CAAC;QACX,CAAC,MAAM;UACLvB,CAAC,CAACK,CAAC,GAAGyP,CAAC,CAACzP,CAAC;UACTL,CAAC,CAACuB,CAAC,GAAGuO,CAAC,CAACvO,CAAC,CAACwB,KAAK,CAAC,CAAC;QACnB;MACF,CAAC,MAAM;QACL/C,CAAC,CAACK,CAAC,GAAGyP,CAAC,CAACzP,CAAC;QACTL,CAAC,CAACuB,CAAC,GAAGuO,CAAC,CAACvO,CAAC,GAAGuO,CAAC,CAACvO,CAAC,CAACwB,KAAK,CAAC,CAAC,GAAG+M,CAAC,CAACvO,CAAC;MAC/B;MAEA;IACF;IAEAiB,CAAC,GAAG,OAAOsN,CAAC;IAEZ,IAAItN,CAAC,KAAK,QAAQ,EAAE;MAClB,IAAIsN,CAAC,KAAK,CAAC,EAAE;QACX9P,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG4P,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;QACxB9P,CAAC,CAACK,CAAC,GAAG,CAAC;QACPL,CAAC,CAACuB,CAAC,GAAG,CAAC,CAAC,CAAC;QACT;MACF;MAEA,IAAIuO,CAAC,GAAG,CAAC,EAAE;QACTA,CAAC,GAAG,CAACA,CAAC;QACN9P,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC;MACV,CAAC,MAAM;QACLF,CAAC,CAACE,CAAC,GAAG,CAAC;MACT;;MAEA;MACA,IAAI4P,CAAC,KAAK,CAAC,CAACA,CAAC,IAAIA,CAAC,GAAG,GAAG,EAAE;QACxB,KAAKzP,CAAC,GAAG,CAAC,EAAEa,CAAC,GAAG4O,CAAC,EAAE5O,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAEb,CAAC,EAAE;QAExC,IAAI9B,QAAQ,EAAE;UACZ,IAAI8B,CAAC,GAAGiP,OAAO,CAACnR,IAAI,EAAE;YACpB6B,CAAC,CAACK,CAAC,GAAGO,GAAG;YACTZ,CAAC,CAACuB,CAAC,GAAG,IAAI;UACZ,CAAC,MAAM,IAAIlB,CAAC,GAAGiP,OAAO,CAACpR,IAAI,EAAE;YAC3B8B,CAAC,CAACK,CAAC,GAAG,CAAC;YACPL,CAAC,CAACuB,CAAC,GAAG,CAAC,CAAC,CAAC;UACX,CAAC,MAAM;YACLvB,CAAC,CAACK,CAAC,GAAGA,CAAC;YACPL,CAAC,CAACuB,CAAC,GAAG,CAACuO,CAAC,CAAC;UACX;QACF,CAAC,MAAM;UACL9P,CAAC,CAACK,CAAC,GAAGA,CAAC;UACPL,CAAC,CAACuB,CAAC,GAAG,CAACuO,CAAC,CAAC;QACX;QAEA;MACF;;MAEA;MACA,IAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;QACf,IAAI,CAACA,CAAC,EAAE9P,CAAC,CAACE,CAAC,GAAGU,GAAG;QACjBZ,CAAC,CAACK,CAAC,GAAGO,GAAG;QACTZ,CAAC,CAACuB,CAAC,GAAG,IAAI;QACV;MACF;MAEA,OAAOoN,YAAY,CAAC3O,CAAC,EAAE8P,CAAC,CAAC7M,QAAQ,CAAC,CAAC,CAAC;IACtC;IAEA,IAAIT,CAAC,KAAK,QAAQ,EAAE;MAClB,IAAI,CAACtB,CAAC,GAAG4O,CAAC,CAACf,UAAU,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE;QAAG;QACnCe,CAAC,GAAGA,CAAC,CAAC/M,KAAK,CAAC,CAAC,CAAC;QACd/C,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC;MACV,CAAC,MAAM;QACL,IAAIgB,CAAC,KAAK,EAAE,EAAE4O,CAAC,GAAGA,CAAC,CAAC/M,KAAK,CAAC,CAAC,CAAC,CAAC,CAAE;QAC/B/C,CAAC,CAACE,CAAC,GAAG,CAAC;MACT;MAEA,OAAOb,SAAS,CAAC+P,IAAI,CAACU,CAAC,CAAC,GAAGnB,YAAY,CAAC3O,CAAC,EAAE8P,CAAC,CAAC,GAAGd,UAAU,CAAChP,CAAC,EAAE8P,CAAC,CAAC;IAClE;IAEA,IAAItN,CAAC,KAAK,QAAQ,EAAE;MAClB,IAAIsN,CAAC,GAAG,CAAC,EAAE;QACTA,CAAC,GAAG,CAACA,CAAC;QACN9P,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC;MACV,CAAC,MAAM;QACLF,CAAC,CAACE,CAAC,GAAG,CAAC;MACT;MAEA,OAAOyO,YAAY,CAAC3O,CAAC,EAAE8P,CAAC,CAAC7M,QAAQ,CAAC,CAAC,CAAC;IACtC;IAEA,MAAMnC,KAAK,CAACrC,eAAe,GAAGqR,CAAC,CAAC;EAClC;EAEAR,OAAO,CAACgB,SAAS,GAAG1Q,CAAC;EAErB0P,OAAO,CAACiB,QAAQ,GAAG,CAAC;EACpBjB,OAAO,CAACkB,UAAU,GAAG,CAAC;EACtBlB,OAAO,CAACmB,UAAU,GAAG,CAAC;EACtBnB,OAAO,CAACoB,WAAW,GAAG,CAAC;EACvBpB,OAAO,CAACqB,aAAa,GAAG,CAAC;EACzBrB,OAAO,CAACsB,eAAe,GAAG,CAAC;EAC3BtB,OAAO,CAACuB,eAAe,GAAG,CAAC;EAC3BvB,OAAO,CAACwB,eAAe,GAAG,CAAC;EAC3BxB,OAAO,CAACyB,gBAAgB,GAAG,CAAC;EAC5BzB,OAAO,CAAC0B,MAAM,GAAG,CAAC;EAElB1B,OAAO,CAACM,MAAM,GAAGN,OAAO,CAAC2B,GAAG,GAAGrB,MAAM;EACrCN,OAAO,CAACc,KAAK,GAAGA,KAAK;EACrBd,OAAO,CAACjQ,SAAS,GAAGgR,iBAAiB;EAErCf,OAAO,CAACvP,GAAG,GAAGA,GAAG;EACjBuP,OAAO,CAACjK,IAAI,GAAGA,IAAI;EACnBiK,OAAO,CAAC5J,KAAK,GAAGA,KAAK,CAAC,CAAQ;EAC9B4J,OAAO,CAAC3G,GAAG,GAAGA,GAAG;EACjB2G,OAAO,CAAClJ,IAAI,GAAGA,IAAI;EACnBkJ,OAAO,CAACxJ,KAAK,GAAGA,KAAK,CAAC,CAAQ;EAC9BwJ,OAAO,CAAC9J,IAAI,GAAGA,IAAI;EACnB8J,OAAO,CAACtJ,KAAK,GAAGA,KAAK,CAAC,CAAQ;EAC9BsJ,OAAO,CAACK,KAAK,GAAGA,KAAK;EACrBL,OAAO,CAACnN,IAAI,GAAGA,IAAI,CAAC,CAAU;EAC9BmN,OAAO,CAAClP,IAAI,GAAGA,IAAI;EACnBkP,OAAO,CAAC/O,KAAK,GAAGA,KAAK;EACrB+O,OAAO,CAAC1N,GAAG,GAAGA,GAAG;EACjB0N,OAAO,CAACnL,IAAI,GAAGA,IAAI,CAAC,CAAU;EAC9BmL,OAAO,CAAC3L,GAAG,GAAGA,GAAG;EACjB2L,OAAO,CAAC7G,GAAG,GAAGA,GAAG;EACjB6G,OAAO,CAACvQ,KAAK,GAAGA,KAAK;EACrBuQ,OAAO,CAAC4B,KAAK,GAAGA,KAAK,CAAC,CAAQ;EAC9B5B,OAAO,CAAC1J,EAAE,GAAGA,EAAE;EACf0J,OAAO,CAACnI,GAAG,GAAGA,GAAG;EACjBmI,OAAO,CAAC6B,KAAK,GAAGA,KAAK,CAAC,CAAQ;EAC9B7B,OAAO,CAAC8B,IAAI,GAAGA,IAAI,CAAC,CAAU;EAC9B9B,OAAO,CAAC7O,GAAG,GAAGA,GAAG;EACjB6O,OAAO,CAAC9O,GAAG,GAAGA,GAAG;EACjB8O,OAAO,CAAChH,GAAG,GAAGA,GAAG;EACjBgH,OAAO,CAAChG,GAAG,GAAGA,GAAG;EACjBgG,OAAO,CAACrQ,GAAG,GAAGA,GAAG;EACjBqQ,OAAO,CAAC+B,MAAM,GAAGA,MAAM;EACvB/B,OAAO,CAACtG,KAAK,GAAGA,KAAK;EACrBsG,OAAO,CAAC9B,IAAI,GAAGA,IAAI,CAAC,CAAU;EAC9B8B,OAAO,CAACpG,GAAG,GAAGA,GAAG;EACjBoG,OAAO,CAAC1K,IAAI,GAAGA,IAAI,CAAC,CAAU;EAC9B0K,OAAO,CAACzK,IAAI,GAAGA,IAAI;EACnByK,OAAO,CAACxH,GAAG,GAAGA,GAAG;EACjBwH,OAAO,CAACjB,GAAG,GAAGA,GAAG;EACjBiB,OAAO,CAACjG,GAAG,GAAGA,GAAG;EACjBiG,OAAO,CAACnK,IAAI,GAAGA,IAAI,CAAC,CAAU;EAC9BmK,OAAO,CAACnE,KAAK,GAAGA,KAAK,CAAC,CAAQ;;EAE9B,IAAI0E,GAAG,KAAK,KAAK,CAAC,EAAEA,GAAG,GAAG,CAAC,CAAC;EAC5B,IAAIA,GAAG,EAAE;IACP,IAAIA,GAAG,CAACG,QAAQ,KAAK,IAAI,EAAE;MACzBC,EAAE,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;MAC1F,KAAK/O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+O,EAAE,CAACvQ,MAAM,GAAG,IAAI,CAACmQ,GAAG,CAACyB,cAAc,CAACnC,CAAC,GAAGc,EAAE,CAAC/O,CAAC,EAAE,CAAC,CAAC,EAAE2O,GAAG,CAACV,CAAC,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC;IACpF;EACF;EAEAG,OAAO,CAACM,MAAM,CAACC,GAAG,CAAC;EAEnB,OAAOP,OAAO;AAChB;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS3L,GAAGA,CAAC3D,CAAC,EAAEiB,CAAC,EAAE;EACjB,OAAO,IAAI,IAAI,CAACjB,CAAC,CAAC,CAAC2D,GAAG,CAAC1C,CAAC,CAAC;AAC3B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwH,GAAGA,CAACzI,CAAC,EAAE;EACd,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAACyI,GAAG,CAAC,CAAC;AAC1B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS1J,KAAKA,CAACiB,CAAC,EAAE;EAChB,OAAOG,QAAQ,CAACH,CAAC,GAAG,IAAI,IAAI,CAACA,CAAC,CAAC,EAAEA,CAAC,CAACK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC9C;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6Q,KAAKA,CAAA,EAAG;EACf,IAAIhQ,CAAC;IAAEmB,CAAC;IACNG,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;EAEjBjE,QAAQ,GAAG,KAAK;EAEhB,KAAK2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqQ,SAAS,CAAC7R,MAAM,GAAG;IACjC2C,CAAC,GAAG,IAAI,IAAI,CAACkP,SAAS,CAACrQ,CAAC,EAAE,CAAC,CAAC;IAC5B,IAAI,CAACmB,CAAC,CAACd,CAAC,EAAE;MACR,IAAIc,CAAC,CAACnC,CAAC,EAAE;QACP3B,QAAQ,GAAG,IAAI;QACf,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;MACxB;MACAiE,CAAC,GAAGH,CAAC;IACP,CAAC,MAAM,IAAIG,CAAC,CAACjB,CAAC,EAAE;MACdiB,CAAC,GAAGA,CAAC,CAACW,IAAI,CAACd,CAAC,CAACa,KAAK,CAACb,CAAC,CAAC,CAAC;IACxB;EACF;EAEA9D,QAAQ,GAAG,IAAI;EAEf,OAAOiE,CAAC,CAACqC,IAAI,CAAC,CAAC;AACjB;;AAGA;AACA;AACA;AACA;AACA;AACA,SAASwL,iBAAiBA,CAACR,GAAG,EAAE;EAC9B,OAAOA,GAAG,YAAYP,OAAO,IAAIO,GAAG,IAAIA,GAAG,CAAChQ,WAAW,KAAKjB,GAAG,IAAI,KAAK;AAC1E;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgH,EAAEA,CAAC5F,CAAC,EAAE;EACb,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAAC4F,EAAE,CAAC,CAAC;AACzB;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuB,GAAGA,CAACnH,CAAC,EAAEiB,CAAC,EAAE;EACjB,OAAO,IAAI,IAAI,CAACjB,CAAC,CAAC,CAACmH,GAAG,CAAClG,CAAC,CAAC;AAC3B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmQ,IAAIA,CAACpR,CAAC,EAAE;EACf,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAACmH,GAAG,CAAC,CAAC,CAAC;AAC3B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgK,KAAKA,CAACnR,CAAC,EAAE;EAChB,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAACmH,GAAG,CAAC,EAAE,CAAC;AAC5B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS1G,GAAGA,CAAA,EAAG;EACb,OAAO0N,QAAQ,CAAC,IAAI,EAAEoD,SAAS,EAAE,CAAC,CAAC,CAAC;AACtC;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS/Q,GAAGA,CAAA,EAAG;EACb,OAAO2N,QAAQ,CAAC,IAAI,EAAEoD,SAAS,EAAE,CAAC,CAAC;AACrC;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASjJ,GAAGA,CAACtI,CAAC,EAAEiB,CAAC,EAAE;EACjB,OAAO,IAAI,IAAI,CAACjB,CAAC,CAAC,CAACsI,GAAG,CAACrH,CAAC,CAAC;AAC3B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqI,GAAGA,CAACtJ,CAAC,EAAEiB,CAAC,EAAE;EACjB,OAAO,IAAI,IAAI,CAACjB,CAAC,CAAC,CAACsJ,GAAG,CAACrI,CAAC,CAAC;AAC3B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAShC,GAAGA,CAACe,CAAC,EAAEiB,CAAC,EAAE;EACjB,OAAO,IAAI,IAAI,CAACjB,CAAC,CAAC,CAACf,GAAG,CAACgC,CAAC,CAAC;AAC3B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoQ,MAAMA,CAACtP,EAAE,EAAE;EAClB,IAAIR,CAAC;IAAElB,CAAC;IAAEK,CAAC;IAAE2B,CAAC;IACZnB,CAAC,GAAG,CAAC;IACLoB,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;IACfqJ,EAAE,GAAG,EAAE;EAET,IAAI5J,EAAE,KAAK,KAAK,CAAC,EAAEA,EAAE,GAAG,IAAI,CAAClE,SAAS,CAAC,KAClC+L,UAAU,CAAC7H,EAAE,EAAE,CAAC,EAAEvE,UAAU,CAAC;EAElCkD,CAAC,GAAG5B,IAAI,CAACsB,IAAI,CAAC2B,EAAE,GAAGxC,QAAQ,CAAC;EAE5B,IAAI,CAAC,IAAI,CAACnB,MAAM,EAAE;IAChB,OAAO8C,CAAC,GAAGR,CAAC,GAAGiL,EAAE,CAACzK,CAAC,EAAE,CAAC,GAAGpC,IAAI,CAACuS,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC;;IAElD;EACA,CAAC,MAAM,IAAIjT,MAAM,CAAC8R,eAAe,EAAE;IACjC3O,CAAC,GAAGnD,MAAM,CAAC8R,eAAe,CAAC,IAAIsB,WAAW,CAAC9Q,CAAC,CAAC,CAAC;IAE9C,OAAOQ,CAAC,GAAGR,CAAC,GAAG;MACb2B,CAAC,GAAGd,CAAC,CAACL,CAAC,CAAC;;MAER;MACA;MACA,IAAImB,CAAC,IAAI,MAAM,EAAE;QACfd,CAAC,CAACL,CAAC,CAAC,GAAG9C,MAAM,CAAC8R,eAAe,CAAC,IAAIsB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC,MAAM;QAEL;QACA;QACA7F,EAAE,CAACzK,CAAC,EAAE,CAAC,GAAGmB,CAAC,GAAG,GAAG;MACnB;IACF;;IAEF;EACA,CAAC,MAAM,IAAIjE,MAAM,CAAC+R,WAAW,EAAE;IAE7B;IACA5O,CAAC,GAAGnD,MAAM,CAAC+R,WAAW,CAACzP,CAAC,IAAI,CAAC,CAAC;IAE9B,OAAOQ,CAAC,GAAGR,CAAC,GAAG;MAEb;MACA2B,CAAC,GAAGd,CAAC,CAACL,CAAC,CAAC,IAAIK,CAAC,CAACL,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAIK,CAAC,CAACL,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAACK,CAAC,CAACL,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,CAAC;;MAEzE;MACA,IAAImB,CAAC,IAAI,MAAM,EAAE;QACfjE,MAAM,CAAC+R,WAAW,CAAC,CAAC,CAAC,CAACsB,IAAI,CAAClQ,CAAC,EAAEL,CAAC,CAAC;MAClC,CAAC,MAAM;QAEL;QACA;QACAyK,EAAE,CAACzD,IAAI,CAAC7F,CAAC,GAAG,GAAG,CAAC;QAChBnB,CAAC,IAAI,CAAC;MACR;IACF;IAEAA,CAAC,GAAGR,CAAC,GAAG,CAAC;EACX,CAAC,MAAM;IACL,MAAMI,KAAK,CAACnC,iBAAiB,CAAC;EAChC;EAEA+B,CAAC,GAAGiL,EAAE,CAAC,EAAEzK,CAAC,CAAC;EACXa,EAAE,IAAIxC,QAAQ;;EAEd;EACA,IAAImB,CAAC,IAAIqB,EAAE,EAAE;IACXM,CAAC,GAAGrD,OAAO,CAAC,EAAE,EAAEO,QAAQ,GAAGwC,EAAE,CAAC;IAC9B4J,EAAE,CAACzK,CAAC,CAAC,GAAG,CAACR,CAAC,GAAG2B,CAAC,GAAG,CAAC,IAAIA,CAAC;EACzB;;EAEA;EACA,OAAOsJ,EAAE,CAACzK,CAAC,CAAC,KAAK,CAAC,EAAEA,CAAC,EAAE,EAAEyK,EAAE,CAACxD,GAAG,CAAC,CAAC;;EAEjC;EACA,IAAIjH,CAAC,GAAG,CAAC,EAAE;IACTb,CAAC,GAAG,CAAC;IACLsL,EAAE,GAAG,CAAC,CAAC,CAAC;EACV,CAAC,MAAM;IACLtL,CAAC,GAAG,CAAC,CAAC;;IAEN;IACA,OAAOsL,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAEtL,CAAC,IAAId,QAAQ,EAAEoM,EAAE,CAACvD,KAAK,CAAC,CAAC;;IAE7C;IACA,KAAK1H,CAAC,GAAG,CAAC,EAAE2B,CAAC,GAAGsJ,EAAE,CAAC,CAAC,CAAC,EAAEtJ,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAE3B,CAAC,EAAE;;IAE5C;IACA,IAAIA,CAAC,GAAGnB,QAAQ,EAAEc,CAAC,IAAId,QAAQ,GAAGmB,CAAC;EACrC;EAEA4B,CAAC,CAACjC,CAAC,GAAGA,CAAC;EACPiC,CAAC,CAACf,CAAC,GAAGoK,EAAE;EAER,OAAOrJ,CAAC;AACV;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0G,KAAKA,CAAChJ,CAAC,EAAE;EAChB,OAAOG,QAAQ,CAACH,CAAC,GAAG,IAAI,IAAI,CAACA,CAAC,CAAC,EAAEA,CAAC,CAACK,CAAC,GAAG,CAAC,EAAE,IAAI,CAACvC,QAAQ,CAAC;AAC1D;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0P,IAAIA,CAACxN,CAAC,EAAE;EACfA,CAAC,GAAG,IAAI,IAAI,CAACA,CAAC,CAAC;EACf,OAAOA,CAAC,CAACuB,CAAC,GAAIvB,CAAC,CAACuB,CAAC,CAAC,CAAC,CAAC,GAAGvB,CAAC,CAACE,CAAC,GAAG,CAAC,GAAGF,CAAC,CAACE,CAAC,GAAIF,CAAC,CAACE,CAAC,IAAIU,GAAG;AACpD;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsI,GAAGA,CAAClJ,CAAC,EAAE;EACd,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAACkJ,GAAG,CAAC,CAAC;AAC1B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAStE,IAAIA,CAAC5E,CAAC,EAAE;EACf,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAAC4E,IAAI,CAAC,CAAC;AAC3B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAAC7E,CAAC,EAAE;EACf,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAAC6E,IAAI,CAAC,CAAC;AAC3B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiD,GAAGA,CAAC9H,CAAC,EAAEiB,CAAC,EAAE;EACjB,OAAO,IAAI,IAAI,CAACjB,CAAC,CAAC,CAAC8H,GAAG,CAAC7G,CAAC,CAAC;AAC3B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoN,GAAGA,CAAA,EAAG;EACb,IAAInN,CAAC,GAAG,CAAC;IACPkN,IAAI,GAAGmD,SAAS;IAChBvR,CAAC,GAAG,IAAI,IAAI,CAACoO,IAAI,CAAClN,CAAC,CAAC,CAAC;EAEvB3C,QAAQ,GAAG,KAAK;EAChB,OAAOyB,CAAC,CAACE,CAAC,IAAI,EAAEgB,CAAC,GAAGkN,IAAI,CAAC1O,MAAM,GAAGM,CAAC,GAAGA,CAAC,CAACmD,IAAI,CAACiL,IAAI,CAAClN,CAAC,CAAC,CAAC;EACrD3C,QAAQ,GAAG,IAAI;EAEf,OAAO4B,QAAQ,CAACH,CAAC,EAAE,IAAI,CAACnC,SAAS,EAAE,IAAI,CAACC,QAAQ,CAAC;AACnD;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuL,GAAGA,CAACrJ,CAAC,EAAE;EACd,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAACqJ,GAAG,CAAC,CAAC;AAC1B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASlE,IAAIA,CAACnF,CAAC,EAAE;EACf,OAAO,IAAI,IAAI,CAACA,CAAC,CAAC,CAACmF,IAAI,CAAC,CAAC;AAC3B;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgG,KAAKA,CAACnL,CAAC,EAAE;EAChB,OAAOG,QAAQ,CAACH,CAAC,GAAG,IAAI,IAAI,CAACA,CAAC,CAAC,EAAEA,CAAC,CAACK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC9C;AAGAT,CAAC,CAAC8R,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAAG/R,CAAC,CAACqD,QAAQ;AACxDrD,CAAC,CAAC8R,MAAM,CAAC7R,WAAW,CAAC,GAAG,SAAS;;AAEjC;AACA,OAAO,IAAIyP,OAAO,GAAG1P,CAAC,CAACK,WAAW,GAAGmQ,KAAK,CAACxS,QAAQ,CAAC;;AAEpD;AACAF,IAAI,GAAG,IAAI4R,OAAO,CAAC5R,IAAI,CAAC;AACxBC,EAAE,GAAG,IAAI2R,OAAO,CAAC3R,EAAE,CAAC;AAEpB,eAAe2R,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}