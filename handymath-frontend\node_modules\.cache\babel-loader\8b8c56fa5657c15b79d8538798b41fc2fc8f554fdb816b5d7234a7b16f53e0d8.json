{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createLog10 } from '../../factoriesAny.js';\nexport var log10Dependencies = {\n  ComplexDependencies,\n  typedDependencies,\n  createLog10\n};", "map": {"version": 3, "names": ["ComplexDependencies", "typedDependencies", "createLog10", "log10Dependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesLog10.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createLog10 } from '../../factoriesAny.js';\nexport var log10Dependencies = {\n  ComplexDependencies,\n  typedDependencies,\n  createLog10\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,WAAW,QAAQ,uBAAuB;AACnD,OAAO,IAAIC,iBAAiB,GAAG;EAC7BH,mBAAmB;EACnBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}