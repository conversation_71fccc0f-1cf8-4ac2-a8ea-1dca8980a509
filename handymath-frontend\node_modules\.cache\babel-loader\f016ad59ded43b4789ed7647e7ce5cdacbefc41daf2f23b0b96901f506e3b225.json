{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { bignumberDependencies } from './dependenciesBignumber.generated.js';\nimport { compareDependencies } from './dependenciesCompare.generated.js';\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { isIntegerDependencies } from './dependenciesIsInteger.generated.js';\nimport { largerDependencies } from './dependenciesLarger.generated.js';\nimport { mapSlicesDependencies } from './dependenciesMapSlices.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { partitionSelectDependencies } from './dependenciesPartitionSelect.generated.js';\nimport { smallerDependencies } from './dependenciesSmaller.generated.js';\nimport { smallerEqDependencies } from './dependenciesSmallerEq.generated.js';\nimport { subtractDependencies } from './dependenciesSubtract.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createQuantileSeqTransform } from '../../factoriesAny.js';\nexport var quantileSeqTransformDependencies = {\n  addDependencies,\n  bignumberDependencies,\n  compareDependencies,\n  divideDependencies,\n  isIntegerDependencies,\n  largerDependencies,\n  mapSlicesDependencies,\n  multiplyDependencies,\n  partitionSelectDependencies,\n  smallerDependencies,\n  smallerEqDependencies,\n  subtractDependencies,\n  typedDependencies,\n  createQuantileSeqTransform\n};", "map": {"version": 3, "names": ["addDependencies", "bignumberDependencies", "compareDependencies", "divideDependencies", "isIntegerDependencies", "largerDependencies", "mapSlicesDependencies", "multiplyDependencies", "partitionSelectDependencies", "smallerDependencies", "smallerEqDependencies", "subtractDependencies", "typedDependencies", "createQuantileSeqTransform", "quantileSeqTransformDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesQuantileSeqTransform.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { bignumberDependencies } from './dependenciesBignumber.generated.js';\nimport { compareDependencies } from './dependenciesCompare.generated.js';\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { isIntegerDependencies } from './dependenciesIsInteger.generated.js';\nimport { largerDependencies } from './dependenciesLarger.generated.js';\nimport { mapSlicesDependencies } from './dependenciesMapSlices.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { partitionSelectDependencies } from './dependenciesPartitionSelect.generated.js';\nimport { smallerDependencies } from './dependenciesSmaller.generated.js';\nimport { smallerEqDependencies } from './dependenciesSmallerEq.generated.js';\nimport { subtractDependencies } from './dependenciesSubtract.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createQuantileSeqTransform } from '../../factoriesAny.js';\nexport var quantileSeqTransformDependencies = {\n  addDependencies,\n  bignumberDependencies,\n  compareDependencies,\n  divideDependencies,\n  isIntegerDependencies,\n  largerDependencies,\n  mapSlicesDependencies,\n  multiplyDependencies,\n  partitionSelectDependencies,\n  smallerDependencies,\n  smallerEqDependencies,\n  subtractDependencies,\n  typedDependencies,\n  createQuantileSeqTransform\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,QAAQ,gCAAgC;AAChE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,0BAA0B,QAAQ,uBAAuB;AAClE,OAAO,IAAIC,gCAAgC,GAAG;EAC5Cd,eAAe;EACfC,qBAAqB;EACrBC,mBAAmB;EACnBC,kBAAkB;EAClBC,qBAAqB;EACrBC,kBAAkB;EAClBC,qBAAqB;EACrBC,oBAAoB;EACpBC,2BAA2B;EAC3BC,mBAAmB;EACnBC,qBAAqB;EACrBC,oBAAoB;EACpBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}