{"ast": null, "code": "export var mapSlicesDocs = {\n  name: 'mapSlices',\n  category: 'Matrix',\n  syntax: ['mapSlices(A, dim, callback)'],\n  description: 'Generate a matrix one dimension less than A by applying callback to ' + 'each slice of A along dimension dim.',\n  examples: ['A = [[1, 2], [3, 4]]', 'mapSlices(A, 1, sum)',\n  // returns [4, 6]\n  'mapSlices(A, 2, prod)' // returns [2, 12]\n  ],\n  seealso: ['map', 'forEach']\n};", "map": {"version": 3, "names": ["mapSlicesDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/mapSlices.js"], "sourcesContent": ["export var mapSlicesDocs = {\n  name: 'mapSlices',\n  category: 'Matrix',\n  syntax: ['mapSlices(A, dim, callback)'],\n  description: 'Generate a matrix one dimension less than A by applying callback to ' + 'each slice of A along dimension dim.',\n  examples: ['A = [[1, 2], [3, 4]]', 'mapSlices(A, 1, sum)',\n  // returns [4, 6]\n  'mapSlices(A, 2, prod)' // returns [2, 12]\n  ],\n  seealso: ['map', 'forEach']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,6BAA6B,CAAC;EACvCC,WAAW,EAAE,sEAAsE,GAAG,sCAAsC;EAC5HC,QAAQ,EAAE,CAAC,sBAAsB,EAAE,sBAAsB;EACzD;EACA,uBAAuB,CAAC;EAAA,CACvB;EACDC,OAAO,EAAE,CAAC,KAAK,EAAE,SAAS;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}