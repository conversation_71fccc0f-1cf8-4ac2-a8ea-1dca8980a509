{"ast": null, "code": "export var bitAndDocs = {\n  name: 'bitAnd',\n  category: 'Bitwise',\n  syntax: ['x & y', 'bitAnd(x, y)'],\n  description: 'Bitwise AND operation. Performs the logical AND operation on each pair of the corresponding bits of the two given values by multiplying them. If both bits in the compared position are 1, the bit in the resulting binary representation is 1, otherwise, the result is 0',\n  examples: ['5 & 3', 'bitAnd(53, 131)', '[1, 12, 31] & 42'],\n  seealso: ['bitNot', 'bitOr', 'bitXor', 'leftShift', 'rightArithShift', 'rightLogShift']\n};", "map": {"version": 3, "names": ["bitAndDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/bitwise/bitAnd.js"], "sourcesContent": ["export var bitAndDocs = {\n  name: 'bitAnd',\n  category: 'Bitwise',\n  syntax: ['x & y', 'bitAnd(x, y)'],\n  description: 'Bitwise AND operation. Performs the logical AND operation on each pair of the corresponding bits of the two given values by multiplying them. If both bits in the compared position are 1, the bit in the resulting binary representation is 1, otherwise, the result is 0',\n  examples: ['5 & 3', 'bitAnd(53, 131)', '[1, 12, 31] & 42'],\n  seealso: ['bitNot', 'bitOr', 'bitXor', 'leftShift', 'rightArithShift', 'rightLogShift']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,OAAO,EAAE,cAAc,CAAC;EACjCC,WAAW,EAAE,4QAA4Q;EACzRC,QAAQ,EAAE,CAAC,OAAO,EAAE,iBAAiB,EAAE,kBAAkB,CAAC;EAC1DC,OAAO,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,iBAAiB,EAAE,eAAe;AACxF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}