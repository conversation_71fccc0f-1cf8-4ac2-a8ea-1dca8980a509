{"ast": null, "code": "export var andDocs = {\n  name: 'and',\n  category: 'Logical',\n  syntax: ['x and y', 'and(x, y)'],\n  description: 'Logical and. Test whether two values are both defined with a nonzero/nonempty value.',\n  examples: ['true and false', 'true and true', '2 and 4'],\n  seealso: ['not', 'or', 'xor']\n};", "map": {"version": 3, "names": ["andDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/logical/and.js"], "sourcesContent": ["export var andDocs = {\n  name: 'and',\n  category: 'Logical',\n  syntax: ['x and y', 'and(x, y)'],\n  description: 'Logical and. Test whether two values are both defined with a nonzero/nonempty value.',\n  examples: ['true and false', 'true and true', '2 and 4'],\n  seealso: ['not', 'or', 'xor']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;EAChCC,WAAW,EAAE,sFAAsF;EACnGC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,eAAe,EAAE,SAAS,CAAC;EACxDC,OAAO,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK;AAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}