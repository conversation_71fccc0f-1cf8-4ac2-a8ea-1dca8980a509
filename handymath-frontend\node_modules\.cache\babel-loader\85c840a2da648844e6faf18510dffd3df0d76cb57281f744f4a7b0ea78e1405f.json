{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { subtractScalarDependencies } from './dependenciesSubtractScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { unaryMinusDependencies } from './dependenciesUnaryMinus.generated.js';\nimport { createSubtract } from '../../factoriesAny.js';\nexport var subtractDependencies = {\n  DenseMatrixDependencies,\n  concatDependencies,\n  equalScalarDependencies,\n  matrixDependencies,\n  subtractScalarDependencies,\n  typedDependencies,\n  unaryMinusDependencies,\n  createSubtract\n};", "map": {"version": 3, "names": ["DenseMatrixDependencies", "concatDependencies", "equalScalarDependencies", "matrixDependencies", "subtractScalarDependencies", "typedDependencies", "unaryMinusDependencies", "createSubtract", "subtractDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSubtract.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { subtractScalarDependencies } from './dependenciesSubtractScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { unaryMinusDependencies } from './dependenciesUnaryMinus.generated.js';\nimport { createSubtract } from '../../factoriesAny.js';\nexport var subtractDependencies = {\n  DenseMatrixDependencies,\n  concatDependencies,\n  equalScalarDependencies,\n  matrixDependencies,\n  subtractScalarDependencies,\n  typedDependencies,\n  unaryMinusDependencies,\n  createSubtract\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,0BAA0B,QAAQ,2CAA2C;AACtF,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,cAAc,QAAQ,uBAAuB;AACtD,OAAO,IAAIC,oBAAoB,GAAG;EAChCR,uBAAuB;EACvBC,kBAAkB;EAClBC,uBAAuB;EACvBC,kBAAkB;EAClBC,0BAA0B;EAC1BC,iBAAiB;EACjBC,sBAAsB;EACtBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}