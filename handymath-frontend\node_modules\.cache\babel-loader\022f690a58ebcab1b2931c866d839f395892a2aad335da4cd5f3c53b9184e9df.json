{"ast": null, "code": "export var rationalizeDocs = {\n  name: 'rationalize',\n  category: 'Algebra',\n  syntax: ['rationalize(expr)', 'rationalize(expr, scope)', 'rationalize(expr, scope, detailed)'],\n  description: 'Transform a rationalizable expression in a rational fraction. If rational fraction is one variable polynomial then converts the numerator and denominator in canonical form, with decreasing exponents, returning the coefficients of numerator.',\n  examples: ['rationalize(\"2x/y - y/(x+1)\")', 'rationalize(\"2x/y - y/(x+1)\", true)'],\n  seealso: ['simplify']\n};", "map": {"version": 3, "names": ["rationalizeDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/rationalize.js"], "sourcesContent": ["export var rationalizeDocs = {\n  name: 'rationalize',\n  category: 'Algebra',\n  syntax: ['rationalize(expr)', 'rationalize(expr, scope)', 'rationalize(expr, scope, detailed)'],\n  description: 'Transform a rationalizable expression in a rational fraction. If rational fraction is one variable polynomial then converts the numerator and denominator in canonical form, with decreasing exponents, returning the coefficients of numerator.',\n  examples: ['rationalize(\"2x/y - y/(x+1)\")', 'rationalize(\"2x/y - y/(x+1)\", true)'],\n  seealso: ['simplify']\n};"], "mappings": "AAAA,OAAO,IAAIA,eAAe,GAAG;EAC3BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,mBAAmB,EAAE,0BAA0B,EAAE,oCAAoC,CAAC;EAC/FC,WAAW,EAAE,kPAAkP;EAC/PC,QAAQ,EAAE,CAAC,+BAA+B,EAAE,qCAAqC,CAAC;EAClFC,OAAO,EAAE,CAAC,UAAU;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}