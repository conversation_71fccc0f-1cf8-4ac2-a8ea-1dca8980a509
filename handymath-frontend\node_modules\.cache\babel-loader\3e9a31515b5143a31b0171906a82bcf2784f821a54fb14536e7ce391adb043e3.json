{"ast": null, "code": "// Reserved keywords not allowed to use in the parser\nexport var keywords = new Set(['end']);", "map": {"version": 3, "names": ["keywords", "Set"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/keywords.js"], "sourcesContent": ["// Reserved keywords not allowed to use in the parser\nexport var keywords = new Set(['end']);"], "mappings": "AAAA;AACA,OAAO,IAAIA,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}