{"ast": null, "code": "export var rightLogShiftDocs = {\n  name: 'rightLogShift',\n  category: 'Bitwise',\n  syntax: ['x >>> y', 'rightLogShift(x, y)'],\n  description: 'Bitwise right logical shift of a value x by y number of bits.',\n  examples: ['8 >>> 1', '4 << 1', '-12 >>> 2'],\n  seealso: ['bitAnd', 'bitNot', 'bitOr', 'bitXor', 'leftShift', 'rightArithShift']\n};", "map": {"version": 3, "names": ["rightLogShiftDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/bitwise/rightLogShift.js"], "sourcesContent": ["export var rightLogShiftDocs = {\n  name: 'rightLogShift',\n  category: 'Bitwise',\n  syntax: ['x >>> y', 'rightLogShift(x, y)'],\n  description: 'Bitwise right logical shift of a value x by y number of bits.',\n  examples: ['8 >>> 1', '4 << 1', '-12 >>> 2'],\n  seealso: ['bitAnd', 'bitNot', 'bitOr', 'bitXor', 'leftShift', 'rightArithShift']\n};"], "mappings": "AAAA,OAAO,IAAIA,iBAAiB,GAAG;EAC7BC,IAAI,EAAE,eAAe;EACrBC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,SAAS,EAAE,qBAAqB,CAAC;EAC1CC,WAAW,EAAE,+DAA+D;EAC5EC,QAAQ,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC;EAC5CC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,iBAAiB;AACjF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}