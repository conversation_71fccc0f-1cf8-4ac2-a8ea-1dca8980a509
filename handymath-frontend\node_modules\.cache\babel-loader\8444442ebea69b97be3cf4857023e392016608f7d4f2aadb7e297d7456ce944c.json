{"ast": null, "code": "export var randomIntDocs = {\n  name: 'randomInt',\n  category: 'Probability',\n  syntax: ['randomInt(max)', 'randomInt(min, max)', 'randomInt(size)', 'randomInt(size, max)', 'randomInt(size, min, max)'],\n  description: 'Return a random integer number',\n  examples: ['randomInt(10, 20)', 'randomInt([2, 3], 10)'],\n  seealso: ['pickRandom', 'random']\n};", "map": {"version": 3, "names": ["randomIntDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/probability/randomInt.js"], "sourcesContent": ["export var randomIntDocs = {\n  name: 'randomInt',\n  category: 'Probability',\n  syntax: ['randomInt(max)', 'randomInt(min, max)', 'randomInt(size)', 'randomInt(size, max)', 'randomInt(size, min, max)'],\n  description: 'Return a random integer number',\n  examples: ['randomInt(10, 20)', 'randomInt([2, 3], 10)'],\n  seealso: ['pickRandom', 'random']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,aAAa;EACvBC,MAAM,EAAE,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,sBAAsB,EAAE,2BAA2B,CAAC;EACzHC,WAAW,EAAE,gCAAgC;EAC7CC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,uBAAuB,CAAC;EACxDC,OAAO,EAAE,CAAC,YAAY,EAAE,QAAQ;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}