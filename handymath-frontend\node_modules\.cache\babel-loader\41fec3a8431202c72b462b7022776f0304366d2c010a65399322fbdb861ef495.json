{"ast": null, "code": "export var leafCountDocs = {\n  name: 'leafCount',\n  category: 'Algebra',\n  syntax: ['leafCount(expr)'],\n  description: 'Computes the number of leaves in the parse tree of the given expression',\n  examples: ['leafCount(\"e^(i*pi)-1\")', 'leafCount(parse(\"{a: 22/7, b: 10^(1/2)}\"))'],\n  seealso: ['simplify']\n};", "map": {"version": 3, "names": ["leafCountDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/leafCount.js"], "sourcesContent": ["export var leafCountDocs = {\n  name: 'leafCount',\n  category: 'Algebra',\n  syntax: ['leafCount(expr)'],\n  description: 'Computes the number of leaves in the parse tree of the given expression',\n  examples: ['leafCount(\"e^(i*pi)-1\")', 'leafCount(parse(\"{a: 22/7, b: 10^(1/2)}\"))'],\n  seealso: ['simplify']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,iBAAiB,CAAC;EAC3BC,WAAW,EAAE,yEAAyE;EACtFC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,4CAA4C,CAAC;EACnFC,OAAO,EAAE,CAAC,UAAU;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}