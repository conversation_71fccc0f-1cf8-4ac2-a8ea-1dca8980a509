{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { absDependencies } from './dependenciesAbs.generated.js';\nimport { mapDependencies } from './dependenciesMap.generated.js';\nimport { medianDependencies } from './dependenciesMedian.generated.js';\nimport { subtractDependencies } from './dependenciesSubtract.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMad } from '../../factoriesAny.js';\nexport var madDependencies = {\n  absDependencies,\n  mapDependencies,\n  medianDependencies,\n  subtractDependencies,\n  typedDependencies,\n  createMad\n};", "map": {"version": 3, "names": ["absDependencies", "mapDependencies", "medianDependencies", "subtractDependencies", "typedDependencies", "createMad", "madDepend<PERSON>"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMad.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { absDependencies } from './dependenciesAbs.generated.js';\nimport { mapDependencies } from './dependenciesMap.generated.js';\nimport { medianDependencies } from './dependenciesMedian.generated.js';\nimport { subtractDependencies } from './dependenciesSubtract.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMad } from '../../factoriesAny.js';\nexport var madDependencies = {\n  absDependencies,\n  mapDependencies,\n  medianDependencies,\n  subtractDependencies,\n  typedDependencies,\n  createMad\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,QAAQ,gCAAgC;AAChE,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,SAAS,QAAQ,uBAAuB;AACjD,OAAO,IAAIC,eAAe,GAAG;EAC3BN,eAAe;EACfC,eAAe;EACfC,kBAAkB;EAClBC,oBAAoB;EACpBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}