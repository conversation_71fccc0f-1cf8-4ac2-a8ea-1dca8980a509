{"ast": null, "code": "import { typeOf } from '../../../utils/is.js';\n\n/**\n * Improve error messages for statistics functions. Errors are typically\n * thrown in an internally used function like larger, causing the error\n * not to mention the function (like max) which is actually used by the user.\n *\n * @param {Error} err\n * @param {String} fnName\n * @param {*} [value]\n * @return {Error}\n */\nexport function improveErrorMessage(err, fnName, value) {\n  // TODO: add information with the index (also needs transform in expression parser)\n  var details;\n  if (String(err).includes('Unexpected type')) {\n    details = arguments.length > 2 ? ' (type: ' + typeOf(value) + ', value: ' + JSON.stringify(value) + ')' : ' (type: ' + err.data.actual + ')';\n    return new TypeError('Cannot calculate ' + fnName + ', unexpected type of argument' + details);\n  }\n  if (String(err).includes('complex numbers')) {\n    details = arguments.length > 2 ? ' (type: ' + typeOf(value) + ', value: ' + JSON.stringify(value) + ')' : '';\n    return new TypeError('Cannot calculate ' + fnName + ', no ordering relation is defined for complex numbers' + details);\n  }\n  return err;\n}", "map": {"version": 3, "names": ["typeOf", "improveErrorMessage", "err", "fnName", "value", "details", "String", "includes", "arguments", "length", "JSON", "stringify", "data", "actual", "TypeError"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/statistics/utils/improveErrorMessage.js"], "sourcesContent": ["import { typeOf } from '../../../utils/is.js';\n\n/**\n * Improve error messages for statistics functions. Errors are typically\n * thrown in an internally used function like larger, causing the error\n * not to mention the function (like max) which is actually used by the user.\n *\n * @param {Error} err\n * @param {String} fnName\n * @param {*} [value]\n * @return {Error}\n */\nexport function improveErrorMessage(err, fnName, value) {\n  // TODO: add information with the index (also needs transform in expression parser)\n  var details;\n  if (String(err).includes('Unexpected type')) {\n    details = arguments.length > 2 ? ' (type: ' + typeOf(value) + ', value: ' + JSON.stringify(value) + ')' : ' (type: ' + err.data.actual + ')';\n    return new TypeError('Cannot calculate ' + fnName + ', unexpected type of argument' + details);\n  }\n  if (String(err).includes('complex numbers')) {\n    details = arguments.length > 2 ? ' (type: ' + typeOf(value) + ', value: ' + JSON.stringify(value) + ')' : '';\n    return new TypeError('Cannot calculate ' + fnName + ', no ordering relation is defined for complex numbers' + details);\n  }\n  return err;\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,sBAAsB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAE;EACtD;EACA,IAAIC,OAAO;EACX,IAAIC,MAAM,CAACJ,GAAG,CAAC,CAACK,QAAQ,CAAC,iBAAiB,CAAC,EAAE;IAC3CF,OAAO,GAAGG,SAAS,CAACC,MAAM,GAAG,CAAC,GAAG,UAAU,GAAGT,MAAM,CAACI,KAAK,CAAC,GAAG,WAAW,GAAGM,IAAI,CAACC,SAAS,CAACP,KAAK,CAAC,GAAG,GAAG,GAAG,UAAU,GAAGF,GAAG,CAACU,IAAI,CAACC,MAAM,GAAG,GAAG;IAC5I,OAAO,IAAIC,SAAS,CAAC,mBAAmB,GAAGX,MAAM,GAAG,+BAA+B,<PERSON><PERSON>,OAAO,CAAC;EAChG;EACA,IAAIC,MAAM,CAACJ,GAAG,CAAC,CAACK,QAAQ,CAAC,iBAAiB,CAAC,EAAE;IAC3CF,OAAO,GAAGG,SAAS,CAACC,MAAM,GAAG,CAAC,GAAG,UAAU,GAAGT,MAAM,CAACI,KAAK,CAAC,GAAG,WAAW,GAAGM,IAAI,CAACC,SAAS,CAACP,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;IAC5G,OAAO,IAAIU,SAAS,CAAC,mBAAmB,GAAGX,MAAM,GAAG,uDAAuD,GAAGE,OAAO,CAAC;EACxH;EACA,OAAOH,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}