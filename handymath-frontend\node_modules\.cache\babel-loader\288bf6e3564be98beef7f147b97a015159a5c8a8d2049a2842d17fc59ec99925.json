{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo13xDD';\nvar dependencies = ['typed'];\nexport var createMatAlgo13xDD = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Iterates over DenseMatrix items and invokes the callback function f(Aij..z, Bij..z).\n   * Callback function invoked MxN times.\n   *\n   * C(i,j,...z) = f(Aij..z, Bij..z)\n   *\n   * @param {Matrix}   a                 The DenseMatrix instance (A)\n   * @param {Matrix}   b                 The DenseMatrix instance (B)\n   * @param {Function} callback          The f(Aij..z,Bij..z) operation to invoke\n   *\n   * @return {Matrix}                    DenseMatrix (C)\n   *\n   * https://github.com/josdejong/mathjs/pull/346#issuecomment-97658658\n   */\n  return function matAlgo13xDD(a, b, callback) {\n    // a arrays\n    var adata = a._data;\n    var asize = a._size;\n    var adt = a._datatype;\n    // b arrays\n    var bdata = b._data;\n    var bsize = b._size;\n    var bdt = b._datatype;\n    // c arrays\n    var csize = [];\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // validate each one of the dimension sizes\n    for (var s = 0; s < asize.length; s++) {\n      // must match\n      if (asize[s] !== bsize[s]) {\n        throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n      }\n      // update dimension in c\n      csize[s] = asize[s];\n    }\n\n    // datatype\n    var dt;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt) {\n      // datatype\n      dt = adt;\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // populate cdata, iterate through dimensions\n    var cdata = csize.length > 0 ? _iterate(cf, 0, csize, csize[0], adata, bdata) : [];\n\n    // c matrix\n    return a.createDenseMatrix({\n      data: cdata,\n      size: csize,\n      datatype: dt\n    });\n  };\n\n  // recursive function\n  function _iterate(f, level, s, n, av, bv) {\n    // initialize array for this level\n    var cv = [];\n    // check we reach the last level\n    if (level === s.length - 1) {\n      // loop arrays in last level\n      for (var i = 0; i < n; i++) {\n        // invoke callback and store value\n        cv[i] = f(av[i], bv[i]);\n      }\n    } else {\n      // iterate current level\n      for (var j = 0; j < n; j++) {\n        // iterate next level\n        cv[j] = _iterate(f, level + 1, s, s[level + 1], av[j], bv[j]);\n      }\n    }\n    return cv;\n  }\n});", "map": {"version": 3, "names": ["factory", "DimensionError", "name", "dependencies", "createMatAlgo13xDD", "_ref", "typed", "matAlgo13xDD", "a", "b", "callback", "adata", "_data", "asize", "_size", "adt", "_datatype", "bdata", "bsize", "bdt", "csize", "length", "s", "RangeError", "dt", "cf", "find", "cdata", "_iterate", "createDenseMatrix", "data", "size", "datatype", "f", "level", "n", "av", "bv", "cv", "i", "j"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/utils/matAlgo13xDD.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo13xDD';\nvar dependencies = ['typed'];\nexport var createMatAlgo13xDD = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Iterates over DenseMatrix items and invokes the callback function f(Aij..z, Bij..z).\n   * Callback function invoked MxN times.\n   *\n   * C(i,j,...z) = f(Aij..z, Bij..z)\n   *\n   * @param {Matrix}   a                 The DenseMatrix instance (A)\n   * @param {Matrix}   b                 The DenseMatrix instance (B)\n   * @param {Function} callback          The f(Aij..z,Bij..z) operation to invoke\n   *\n   * @return {Matrix}                    DenseMatrix (C)\n   *\n   * https://github.com/josdejong/mathjs/pull/346#issuecomment-97658658\n   */\n  return function matAlgo13xDD(a, b, callback) {\n    // a arrays\n    var adata = a._data;\n    var asize = a._size;\n    var adt = a._datatype;\n    // b arrays\n    var bdata = b._data;\n    var bsize = b._size;\n    var bdt = b._datatype;\n    // c arrays\n    var csize = [];\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // validate each one of the dimension sizes\n    for (var s = 0; s < asize.length; s++) {\n      // must match\n      if (asize[s] !== bsize[s]) {\n        throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n      }\n      // update dimension in c\n      csize[s] = asize[s];\n    }\n\n    // datatype\n    var dt;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt) {\n      // datatype\n      dt = adt;\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // populate cdata, iterate through dimensions\n    var cdata = csize.length > 0 ? _iterate(cf, 0, csize, csize[0], adata, bdata) : [];\n\n    // c matrix\n    return a.createDenseMatrix({\n      data: cdata,\n      size: csize,\n      datatype: dt\n    });\n  };\n\n  // recursive function\n  function _iterate(f, level, s, n, av, bv) {\n    // initialize array for this level\n    var cv = [];\n    // check we reach the last level\n    if (level === s.length - 1) {\n      // loop arrays in last level\n      for (var i = 0; i < n; i++) {\n        // invoke callback and store value\n        cv[i] = f(av[i], bv[i]);\n      }\n    } else {\n      // iterate current level\n      for (var j = 0; j < n; j++) {\n        // iterate next level\n        cv[j] = _iterate(f, level + 1, s, s[level + 1], av[j], bv[j]);\n      }\n    }\n    return cv;\n  }\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,IAAIC,IAAI,GAAG,cAAc;AACzB,IAAIC,YAAY,GAAG,CAAC,OAAO,CAAC;AAC5B,OAAO,IAAIC,kBAAkB,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACjF,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASE,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAE;IAC3C;IACA,IAAIC,KAAK,GAAGH,CAAC,CAACI,KAAK;IACnB,IAAIC,KAAK,GAAGL,CAAC,CAACM,KAAK;IACnB,IAAIC,GAAG,GAAGP,CAAC,CAACQ,SAAS;IACrB;IACA,IAAIC,KAAK,GAAGR,CAAC,CAACG,KAAK;IACnB,IAAIM,KAAK,GAAGT,CAAC,CAACK,KAAK;IACnB,IAAIK,GAAG,GAAGV,CAAC,CAACO,SAAS;IACrB;IACA,IAAII,KAAK,GAAG,EAAE;;IAEd;IACA,IAAIP,KAAK,CAACQ,MAAM,KAAKH,KAAK,CAACG,MAAM,EAAE;MACjC,MAAM,IAAIpB,cAAc,CAACY,KAAK,CAACQ,MAAM,EAAEH,KAAK,CAACG,MAAM,CAAC;IACtD;;IAEA;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAACQ,MAAM,EAAEC,CAAC,EAAE,EAAE;MACrC;MACA,IAAIT,KAAK,CAACS,CAAC,CAAC,KAAKJ,KAAK,CAACI,CAAC,CAAC,EAAE;QACzB,MAAM,IAAIC,UAAU,CAAC,gCAAgC,GAAGV,KAAK,GAAG,yBAAyB,GAAGK,KAAK,GAAG,GAAG,CAAC;MAC1G;MACA;MACAE,KAAK,CAACE,CAAC,CAAC,GAAGT,KAAK,CAACS,CAAC,CAAC;IACrB;;IAEA;IACA,IAAIE,EAAE;IACN;IACA,IAAIC,EAAE,GAAGf,QAAQ;;IAEjB;IACA,IAAI,OAAOK,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAKI,GAAG,EAAE;MAC1C;MACAK,EAAE,GAAGT,GAAG;MACR;MACAU,EAAE,GAAGnB,KAAK,CAACoB,IAAI,CAAChB,QAAQ,EAAE,CAACc,EAAE,EAAEA,EAAE,CAAC,CAAC;IACrC;;IAEA;IACA,IAAIG,KAAK,GAAGP,KAAK,CAACC,MAAM,GAAG,CAAC,GAAGO,QAAQ,CAACH,EAAE,EAAE,CAAC,EAAEL,KAAK,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAET,KAAK,EAAEM,KAAK,CAAC,GAAG,EAAE;;IAElF;IACA,OAAOT,CAAC,CAACqB,iBAAiB,CAAC;MACzBC,IAAI,EAAEH,KAAK;MACXI,IAAI,EAAEX,KAAK;MACXY,QAAQ,EAAER;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,SAASI,QAAQA,CAACK,CAAC,EAAEC,KAAK,EAAEZ,CAAC,EAAEa,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAE;IACxC;IACA,IAAIC,EAAE,GAAG,EAAE;IACX;IACA,IAAIJ,KAAK,KAAKZ,CAAC,CAACD,MAAM,GAAG,CAAC,EAAE;MAC1B;MACA,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAAE;QAC1B;QACAD,EAAE,CAACC,CAAC,CAAC,GAAGN,CAAC,CAACG,EAAE,CAACG,CAAC,CAAC,EAAEF,EAAE,CAACE,CAAC,CAAC,CAAC;MACzB;IACF,CAAC,MAAM;MACL;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,EAAEK,CAAC,EAAE,EAAE;QAC1B;QACAF,EAAE,CAACE,CAAC,CAAC,GAAGZ,QAAQ,CAACK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEZ,CAAC,EAAEA,CAAC,CAACY,KAAK,GAAG,CAAC,CAAC,EAAEE,EAAE,CAACI,CAAC,CAAC,EAAEH,EAAE,CAACG,CAAC,CAAC,CAAC;MAC/D;IACF;IACA,OAAOF,EAAE;EACX;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}