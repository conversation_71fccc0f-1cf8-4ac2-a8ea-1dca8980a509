{"ast": null, "code": "export var atanhDocs = {\n  name: 'atanh',\n  category: 'Trigonometry',\n  syntax: ['atanh(x)'],\n  description: 'Calculate the hyperbolic arctangent of a value, defined as `atanh(x) = ln((1 + x)/(1 - x)) / 2`.',\n  examples: ['atanh(0.5)'],\n  seealso: ['acosh', 'asinh']\n};", "map": {"version": 3, "names": ["atanhDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/atanh.js"], "sourcesContent": ["export var atanhDocs = {\n  name: 'atanh',\n  category: 'Trigonometry',\n  syntax: ['atanh(x)'],\n  description: 'Calculate the hyperbolic arctangent of a value, defined as `atanh(x) = ln((1 + x)/(1 - x)) / 2`.',\n  examples: ['atanh(0.5)'],\n  seealso: ['acosh', 'asinh']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,kGAAkG;EAC/GC,QAAQ,EAAE,CAAC,YAAY,CAAC;EACxBC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}