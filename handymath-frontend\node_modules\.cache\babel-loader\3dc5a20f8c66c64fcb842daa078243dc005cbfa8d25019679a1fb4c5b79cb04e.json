{"ast": null, "code": "export var tauDocs = {\n  name: 'tau',\n  category: 'Constants',\n  syntax: ['tau'],\n  description: 'Tau is the ratio constant of a circle\\'s circumference to radius, equal to 2 * pi, approximately 6.2832.',\n  examples: ['tau', '2 * pi'],\n  seealso: ['pi']\n};", "map": {"version": 3, "names": ["tauDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/tau.js"], "sourcesContent": ["export var tauDocs = {\n  name: 'tau',\n  category: 'Constants',\n  syntax: ['tau'],\n  description: 'Tau is the ratio constant of a circle\\'s circumference to radius, equal to 2 * pi, approximately 6.2832.',\n  examples: ['tau', '2 * pi'],\n  seealso: ['pi']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,KAAK,CAAC;EACfC,WAAW,EAAE,0GAA0G;EACvHC,QAAQ,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC3BC,OAAO,EAAE,CAAC,IAAI;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}