{"ast": null, "code": "export var lcmDocs = {\n  name: 'lcm',\n  category: 'Arithmetic',\n  syntax: ['lcm(x, y)'],\n  description: 'Compute the least common multiple.',\n  examples: ['lcm(4, 6)', 'lcm(6, 21)', 'lcm(6, 21, 5)'],\n  seealso: ['gcd']\n};", "map": {"version": 3, "names": ["lcmDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/lcm.js"], "sourcesContent": ["export var lcmDocs = {\n  name: 'lcm',\n  category: 'Arithmetic',\n  syntax: ['lcm(x, y)'],\n  description: 'Compute the least common multiple.',\n  examples: ['lcm(4, 6)', 'lcm(6, 21)', 'lcm(6, 21, 5)'],\n  seealso: ['gcd']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,WAAW,CAAC;EACrBC,WAAW,EAAE,oCAAoC;EACjDC,QAAQ,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,eAAe,CAAC;EACtDC,OAAO,EAAE,CAAC,KAAK;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}