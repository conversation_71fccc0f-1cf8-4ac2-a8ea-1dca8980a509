{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nimport { scatter } from '../../../utils/collection.js';\nvar name = 'matAlgo06xS0S0';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createMatAlgo06xS0S0 = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix A and SparseMatrix B nonzero items and invokes the callback function f(Aij, Bij).\n   * Callback function invoked (Anz U Bnz) times, where Anz and Bnz are the nonzero elements in both matrices.\n   *\n   *\n   *          ┌  f(Aij, Bij)  ; A(i,j) !== 0 && B(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  0            ; otherwise\n   *\n   *\n   * @param {Matrix}   a                 The SparseMatrix instance (A)\n   * @param {Matrix}   b                 The SparseMatrix instance (B)\n   * @param {Function} callback          The f(Aij,Bij) operation to invoke\n   *\n   * @return {Matrix}                    SparseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97620294\n   */\n  return function matAlgo06xS0S0(a, b, callback) {\n    // sparse matrix arrays\n    var avalues = a._values;\n    var asize = a._size;\n    var adt = a._datatype || a._data === undefined ? a._datatype : a.getDataType();\n    // sparse matrix arrays\n    var bvalues = b._values;\n    var bsize = b._size;\n    var bdt = b._datatype || b._data === undefined ? b._datatype : b.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      // datatype\n      dt = adt;\n      // find signature that matches (dt, dt)\n      eq = typed.find(equalScalar, [dt, dt]);\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays\n    var cvalues = avalues && bvalues ? [] : undefined;\n    var cindex = [];\n    var cptr = [];\n\n    // workspaces\n    var x = cvalues ? [] : undefined;\n    // marks indicating we have a value in x for a given column\n    var w = [];\n    // marks indicating value in a given row has been updated\n    var u = [];\n\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // update cptr\n      cptr[j] = cindex.length;\n      // columns mark\n      var mark = j + 1;\n      // scatter the values of A(:,j) into workspace\n      scatter(a, j, w, x, u, mark, cindex, cf);\n      // scatter the values of B(:,j) into workspace\n      scatter(b, j, w, x, u, mark, cindex, cf);\n      // check we need to process values (non pattern matrix)\n      if (x) {\n        // initialize first index in j\n        var k = cptr[j];\n        // loop index in j\n        while (k < cindex.length) {\n          // row\n          var i = cindex[k];\n          // check function was invoked on current row (Aij !=0 && Bij != 0)\n          if (u[i] === mark) {\n            // value @ i\n            var v = x[i];\n            // check for zero value\n            if (!eq(v, zero)) {\n              // push value\n              cvalues.push(v);\n              // increment pointer\n              k++;\n            } else {\n              // remove value @ i, do not increment pointer\n              cindex.splice(k, 1);\n            }\n          } else {\n            // remove value @ i, do not increment pointer\n            cindex.splice(k, 1);\n          }\n        }\n      } else {\n        // initialize first index in j\n        var p = cptr[j];\n        // loop index in j\n        while (p < cindex.length) {\n          // row\n          var r = cindex[p];\n          // check function was invoked on current row (Aij !=0 && Bij != 0)\n          if (u[r] !== mark) {\n            // remove value @ i, do not increment pointer\n            cindex.splice(p, 1);\n          } else {\n            // increment pointer\n            p++;\n          }\n        }\n      }\n    }\n    // update cptr\n    cptr[columns] = cindex.length;\n\n    // return sparse matrix\n    return a.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === a._datatype && bdt === b._datatype ? dt : undefined\n    });\n  };\n});", "map": {"version": 3, "names": ["factory", "DimensionError", "scatter", "name", "dependencies", "createMatAlgo06xS0S0", "_ref", "typed", "equalScalar", "matAlgo06xS0S0", "a", "b", "callback", "avalues", "_values", "asize", "_size", "adt", "_datatype", "_data", "undefined", "getDataType", "bvalues", "bsize", "bdt", "length", "RangeError", "rows", "columns", "dt", "eq", "zero", "cf", "find", "convert", "cvalues", "cindex", "cptr", "x", "w", "u", "j", "mark", "k", "i", "v", "push", "splice", "p", "r", "createSparseMatrix", "values", "index", "ptr", "size", "datatype"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/type/matrix/utils/matAlgo06xS0S0.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nimport { scatter } from '../../../utils/collection.js';\nvar name = 'matAlgo06xS0S0';\nvar dependencies = ['typed', 'equalScalar'];\nexport var createMatAlgo06xS0S0 = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar\n  } = _ref;\n  /**\n   * Iterates over SparseMatrix A and SparseMatrix B nonzero items and invokes the callback function f(Aij, Bij).\n   * Callback function invoked (Anz U Bnz) times, where Anz and Bnz are the nonzero elements in both matrices.\n   *\n   *\n   *          ┌  f(Aij, Bij)  ; A(i,j) !== 0 && B(i,j) !== 0\n   * C(i,j) = ┤\n   *          └  0            ; otherwise\n   *\n   *\n   * @param {Matrix}   a                 The SparseMatrix instance (A)\n   * @param {Matrix}   b                 The SparseMatrix instance (B)\n   * @param {Function} callback          The f(Aij,Bij) operation to invoke\n   *\n   * @return {Matrix}                    SparseMatrix (C)\n   *\n   * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97620294\n   */\n  return function matAlgo06xS0S0(a, b, callback) {\n    // sparse matrix arrays\n    var avalues = a._values;\n    var asize = a._size;\n    var adt = a._datatype || a._data === undefined ? a._datatype : a.getDataType();\n    // sparse matrix arrays\n    var bvalues = b._values;\n    var bsize = b._size;\n    var bdt = b._datatype || b._data === undefined ? b._datatype : b.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    // check rows & columns\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    // equal signature to use\n    var eq = equalScalar;\n    // zero value\n    var zero = 0;\n    // callback signature to use\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      // datatype\n      dt = adt;\n      // find signature that matches (dt, dt)\n      eq = typed.find(equalScalar, [dt, dt]);\n      // convert 0 to the same datatype\n      zero = typed.convert(0, dt);\n      // callback\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays\n    var cvalues = avalues && bvalues ? [] : undefined;\n    var cindex = [];\n    var cptr = [];\n\n    // workspaces\n    var x = cvalues ? [] : undefined;\n    // marks indicating we have a value in x for a given column\n    var w = [];\n    // marks indicating value in a given row has been updated\n    var u = [];\n\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // update cptr\n      cptr[j] = cindex.length;\n      // columns mark\n      var mark = j + 1;\n      // scatter the values of A(:,j) into workspace\n      scatter(a, j, w, x, u, mark, cindex, cf);\n      // scatter the values of B(:,j) into workspace\n      scatter(b, j, w, x, u, mark, cindex, cf);\n      // check we need to process values (non pattern matrix)\n      if (x) {\n        // initialize first index in j\n        var k = cptr[j];\n        // loop index in j\n        while (k < cindex.length) {\n          // row\n          var i = cindex[k];\n          // check function was invoked on current row (Aij !=0 && Bij != 0)\n          if (u[i] === mark) {\n            // value @ i\n            var v = x[i];\n            // check for zero value\n            if (!eq(v, zero)) {\n              // push value\n              cvalues.push(v);\n              // increment pointer\n              k++;\n            } else {\n              // remove value @ i, do not increment pointer\n              cindex.splice(k, 1);\n            }\n          } else {\n            // remove value @ i, do not increment pointer\n            cindex.splice(k, 1);\n          }\n        }\n      } else {\n        // initialize first index in j\n        var p = cptr[j];\n        // loop index in j\n        while (p < cindex.length) {\n          // row\n          var r = cindex[p];\n          // check function was invoked on current row (Aij !=0 && Bij != 0)\n          if (u[r] !== mark) {\n            // remove value @ i, do not increment pointer\n            cindex.splice(p, 1);\n          } else {\n            // increment pointer\n            p++;\n          }\n        }\n      }\n    }\n    // update cptr\n    cptr[columns] = cindex.length;\n\n    // return sparse matrix\n    return a.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === a._datatype && bdt === b._datatype ? dt : undefined\n    });\n  };\n});"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,OAAO,QAAQ,8BAA8B;AACtD,IAAIC,IAAI,GAAG,gBAAgB;AAC3B,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC;AAC3C,OAAO,IAAIC,oBAAoB,GAAG,eAAeL,OAAO,CAACG,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EACnF,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,SAASG,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAE;IAC7C;IACA,IAAIC,OAAO,GAAGH,CAAC,CAACI,OAAO;IACvB,IAAIC,KAAK,GAAGL,CAAC,CAACM,KAAK;IACnB,IAAIC,GAAG,GAAGP,CAAC,CAACQ,SAAS,IAAIR,CAAC,CAACS,KAAK,KAAKC,SAAS,GAAGV,CAAC,CAACQ,SAAS,GAAGR,CAAC,CAACW,WAAW,CAAC,CAAC;IAC9E;IACA,IAAIC,OAAO,GAAGX,CAAC,CAACG,OAAO;IACvB,IAAIS,KAAK,GAAGZ,CAAC,CAACK,KAAK;IACnB,IAAIQ,GAAG,GAAGb,CAAC,CAACO,SAAS,IAAIP,CAAC,CAACQ,KAAK,KAAKC,SAAS,GAAGT,CAAC,CAACO,SAAS,GAAGP,CAAC,CAACU,WAAW,CAAC,CAAC;;IAE9E;IACA,IAAIN,KAAK,CAACU,MAAM,KAAKF,KAAK,CAACE,MAAM,EAAE;MACjC,MAAM,IAAIxB,cAAc,CAACc,KAAK,CAACU,MAAM,EAAEF,KAAK,CAACE,MAAM,CAAC;IACtD;;IAEA;IACA,IAAIV,KAAK,CAAC,CAAC,CAAC,KAAKQ,KAAK,CAAC,CAAC,CAAC,IAAIR,KAAK,CAAC,CAAC,CAAC,KAAKQ,KAAK,CAAC,CAAC,CAAC,EAAE;MAClD,MAAM,IAAIG,UAAU,CAAC,gCAAgC,GAAGX,KAAK,GAAG,yBAAyB,GAAGQ,KAAK,GAAG,GAAG,CAAC;IAC1G;;IAEA;IACA,IAAII,IAAI,GAAGZ,KAAK,CAAC,CAAC,CAAC;IACnB,IAAIa,OAAO,GAAGb,KAAK,CAAC,CAAC,CAAC;;IAEtB;IACA,IAAIc,EAAE;IACN;IACA,IAAIC,EAAE,GAAGtB,WAAW;IACpB;IACA,IAAIuB,IAAI,GAAG,CAAC;IACZ;IACA,IAAIC,EAAE,GAAGpB,QAAQ;;IAEjB;IACA,IAAI,OAAOK,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAKO,GAAG,IAAIP,GAAG,KAAK,OAAO,EAAE;MAC7D;MACAY,EAAE,GAAGZ,GAAG;MACR;MACAa,EAAE,GAAGvB,KAAK,CAAC0B,IAAI,CAACzB,WAAW,EAAE,CAACqB,EAAE,EAAEA,EAAE,CAAC,CAAC;MACtC;MACAE,IAAI,GAAGxB,KAAK,CAAC2B,OAAO,CAAC,CAAC,EAAEL,EAAE,CAAC;MAC3B;MACAG,EAAE,GAAGzB,KAAK,CAAC0B,IAAI,CAACrB,QAAQ,EAAE,CAACiB,EAAE,EAAEA,EAAE,CAAC,CAAC;IACrC;;IAEA;IACA,IAAIM,OAAO,GAAGtB,OAAO,IAAIS,OAAO,GAAG,EAAE,GAAGF,SAAS;IACjD,IAAIgB,MAAM,GAAG,EAAE;IACf,IAAIC,IAAI,GAAG,EAAE;;IAEb;IACA,IAAIC,CAAC,GAAGH,OAAO,GAAG,EAAE,GAAGf,SAAS;IAChC;IACA,IAAImB,CAAC,GAAG,EAAE;IACV;IACA,IAAIC,CAAC,GAAG,EAAE;;IAEV;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,OAAO,EAAEa,CAAC,EAAE,EAAE;MAChC;MACAJ,IAAI,CAACI,CAAC,CAAC,GAAGL,MAAM,CAACX,MAAM;MACvB;MACA,IAAIiB,IAAI,GAAGD,CAAC,GAAG,CAAC;MAChB;MACAvC,OAAO,CAACQ,CAAC,EAAE+B,CAAC,EAAEF,CAAC,EAAED,CAAC,EAAEE,CAAC,EAAEE,IAAI,EAAEN,MAAM,EAAEJ,EAAE,CAAC;MACxC;MACA9B,OAAO,CAACS,CAAC,EAAE8B,CAAC,EAAEF,CAAC,EAAED,CAAC,EAAEE,CAAC,EAAEE,IAAI,EAAEN,MAAM,EAAEJ,EAAE,CAAC;MACxC;MACA,IAAIM,CAAC,EAAE;QACL;QACA,IAAIK,CAAC,GAAGN,IAAI,CAACI,CAAC,CAAC;QACf;QACA,OAAOE,CAAC,GAAGP,MAAM,CAACX,MAAM,EAAE;UACxB;UACA,IAAImB,CAAC,GAAGR,MAAM,CAACO,CAAC,CAAC;UACjB;UACA,IAAIH,CAAC,CAACI,CAAC,CAAC,KAAKF,IAAI,EAAE;YACjB;YACA,IAAIG,CAAC,GAAGP,CAAC,CAACM,CAAC,CAAC;YACZ;YACA,IAAI,CAACd,EAAE,CAACe,CAAC,EAAEd,IAAI,CAAC,EAAE;cAChB;cACAI,OAAO,CAACW,IAAI,CAACD,CAAC,CAAC;cACf;cACAF,CAAC,EAAE;YACL,CAAC,MAAM;cACL;cACAP,MAAM,CAACW,MAAM,CAACJ,CAAC,EAAE,CAAC,CAAC;YACrB;UACF,CAAC,MAAM;YACL;YACAP,MAAM,CAACW,MAAM,CAACJ,CAAC,EAAE,CAAC,CAAC;UACrB;QACF;MACF,CAAC,MAAM;QACL;QACA,IAAIK,CAAC,GAAGX,IAAI,CAACI,CAAC,CAAC;QACf;QACA,OAAOO,CAAC,GAAGZ,MAAM,CAACX,MAAM,EAAE;UACxB;UACA,IAAIwB,CAAC,GAAGb,MAAM,CAACY,CAAC,CAAC;UACjB;UACA,IAAIR,CAAC,CAACS,CAAC,CAAC,KAAKP,IAAI,EAAE;YACjB;YACAN,MAAM,CAACW,MAAM,CAACC,CAAC,EAAE,CAAC,CAAC;UACrB,CAAC,MAAM;YACL;YACAA,CAAC,EAAE;UACL;QACF;MACF;IACF;IACA;IACAX,IAAI,CAACT,OAAO,CAAC,GAAGQ,MAAM,CAACX,MAAM;;IAE7B;IACA,OAAOf,CAAC,CAACwC,kBAAkB,CAAC;MAC1BC,MAAM,EAAEhB,OAAO;MACfiB,KAAK,EAAEhB,MAAM;MACbiB,GAAG,EAAEhB,IAAI;MACTiB,IAAI,EAAE,CAAC3B,IAAI,EAAEC,OAAO,CAAC;MACrB2B,QAAQ,EAAEtC,GAAG,KAAKP,CAAC,CAACQ,SAAS,IAAIM,GAAG,KAAKb,CAAC,CAACO,SAAS,GAAGW,EAAE,GAAGT;IAC9D,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}