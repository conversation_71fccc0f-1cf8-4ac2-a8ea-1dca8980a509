{"ast": null, "code": "export var LN2Docs = {\n  name: 'LN2',\n  category: 'Constants',\n  syntax: ['LN2'],\n  description: 'Returns the natural logarithm of 2, approximately equal to 0.693',\n  examples: ['LN2', 'log(2)'],\n  seealso: []\n};", "map": {"version": 3, "names": ["LN2Docs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/LN2.js"], "sourcesContent": ["export var LN2Docs = {\n  name: 'LN2',\n  category: 'Constants',\n  syntax: ['LN2'],\n  description: 'Returns the natural logarithm of 2, approximately equal to 0.693',\n  examples: ['LN2', 'log(2)'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,KAAK,CAAC;EACfC,WAAW,EAAE,kEAAkE;EAC/EC,QAAQ,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC3BC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}