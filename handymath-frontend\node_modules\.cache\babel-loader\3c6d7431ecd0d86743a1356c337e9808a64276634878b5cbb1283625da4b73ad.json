{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\AboutPage.tsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport SimpleHeader from '../components/SimpleHeader';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AboutPage = () => {\n  const features = [{\n    icon: '🧮',\n    title: 'Résolution d\\'équations',\n    description: 'Résolvez des équations mathématiques complexes avec des étapes détaillées'\n  }, {\n    icon: '📸',\n    title: 'Reconnaissance OCR',\n    description: 'Prenez une photo de votre équation et obtenez la solution instantanément'\n  }, {\n    icon: '📚',\n    title: 'Exercices interactifs',\n    description: 'Pratiquez avec des exercices adaptés à votre niveau'\n  }, {\n    icon: '📊',\n    title: 'Suivi de progression',\n    description: 'Suivez vos progrès et identifiez vos points d\\'amélioration'\n  }, {\n    icon: '🎯',\n    title: 'Apprentissage adaptatif',\n    description: 'Contenu personnalisé selon vos besoins et votre niveau'\n  }, {\n    icon: '🌙',\n    title: 'Mode sombre',\n    description: 'Interface adaptée pour étudier confortablement à toute heure'\n  }];\n  const stats = [{\n    number: '1000+',\n    label: 'Équations résolues'\n  }, {\n    number: '500+',\n    label: 'Utilisateurs actifs'\n  }, {\n    number: '50+',\n    label: 'Exercices disponibles'\n  }, {\n    number: '99%',\n    label: 'Taux de satisfaction'\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(SimpleHeader, {\n      title: \"\\xC0 propos\",\n      showBackButton: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"text-center mb-16\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-6\",\n          children: \"\\uD83E\\uDDEE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-5xl font-bold text-gray-900 dark:text-white mb-6\",\n          children: \"HandyMath\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\",\n          children: \"Votre assistant personnel pour l'apprentissage des math\\xE9matiques. R\\xE9solvez, apprenez et progressez avec notre plateforme innovante.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-16\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900 dark:text-white mb-6\",\n            children: \"Notre Mission\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 dark:text-gray-400 max-w-4xl mx-auto leading-relaxed\",\n            children: \"Rendre les math\\xE9matiques accessibles \\xE0 tous en combinant technologie moderne et p\\xE9dagogie innovante. Nous croyons que chaque \\xE9tudiant peut exceller en math\\xE9matiques avec les bons outils et le bon accompagnement.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-16\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center\",\n          whileHover: {\n            scale: 1.05\n          },\n          transition: {\n            type: \"spring\",\n            stiffness: 300\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2\",\n            children: stat.number\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600 dark:text-gray-400\",\n            children: stat.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"mb-16\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900 dark:text-white text-center mb-12\",\n          children: \"Fonctionnalit\\xE9s Principales\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n            whileHover: {\n              y: -5\n            },\n            transition: {\n              type: \"spring\",\n              stiffness: 300\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-4\",\n              children: feature.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-900 dark:text-white mb-3\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 dark:text-gray-400\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-xl p-8 mb-16\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.5\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900 dark:text-white mb-6\",\n            children: \"Technologie Moderne\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 dark:text-gray-400 mb-8 max-w-3xl mx-auto\",\n            children: \"HandyMath utilise les derni\\xE8res technologies web et d'intelligence artificielle pour offrir une exp\\xE9rience d'apprentissage optimale et personnalis\\xE9e.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap justify-center gap-4\",\n            children: ['React', 'TypeScript', 'Python', 'Django', 'OCR', 'IA'].map((tech, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"px-4 py-2 bg-white dark:bg-gray-800 rounded-full text-sm font-medium text-gray-700 dark:text-gray-300 shadow-md\",\n              children: tech\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"text-center\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.6\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900 dark:text-white mb-6\",\n          children: \"Pr\\xEAt \\xE0 commencer ?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Rejoignez des milliers d'\\xE9tudiants qui am\\xE9liorent leurs comp\\xE9tences en math\\xE9matiques avec HandyMath.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors\",\n            children: \"Cr\\xE9er un compte\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/solver\",\n            className: \"px-8 py-3 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors\",\n            children: \"Essayer le solveur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            className: \"px-8 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 font-medium rounded-lg transition-colors\",\n            children: \"Nous contacter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = AboutPage;\nexport default AboutPage;\nvar _c;\n$RefreshReg$(_c, \"AboutPage\");", "map": {"version": 3, "names": ["React", "motion", "Link", "SimpleHeader", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AboutPage", "features", "icon", "title", "description", "stats", "number", "label", "children", "showBackButton", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "div", "initial", "opacity", "y", "animate", "transition", "delay", "map", "stat", "index", "whileHover", "scale", "type", "stiffness", "feature", "tech", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/pages/AboutPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Link } from 'react-router-dom';\nimport SimpleHeader from '../components/SimpleHeader';\nimport Logo from '../components/Logo';\n\nconst AboutPage: React.FC = () => {\n  const features = [\n    {\n      icon: '🧮',\n      title: 'Résolution d\\'équations',\n      description: 'Résolvez des équations mathématiques complexes avec des étapes détaillées'\n    },\n    {\n      icon: '📸',\n      title: 'Reconnaissance OCR',\n      description: 'Prenez une photo de votre équation et obtenez la solution instantanément'\n    },\n    {\n      icon: '📚',\n      title: 'Exercices interactifs',\n      description: 'Pratiquez avec des exercices adaptés à votre niveau'\n    },\n    {\n      icon: '📊',\n      title: 'Suivi de progression',\n      description: 'Suivez vos progrès et identifiez vos points d\\'amélioration'\n    },\n    {\n      icon: '🎯',\n      title: 'Apprentissage adaptatif',\n      description: 'Contenu personnalisé selon vos besoins et votre niveau'\n    },\n    {\n      icon: '🌙',\n      title: 'Mode sombre',\n      description: 'Interface adaptée pour étudier confortablement à toute heure'\n    }\n  ];\n\n\n\n  const stats = [\n    { number: '1000+', label: 'Équations résolues' },\n    { number: '500+', label: 'Utilisateurs actifs' },\n    { number: '50+', label: 'Exercices disponibles' },\n    { number: '99%', label: 'Taux de satisfaction' }\n  ];\n\n  return (\n    <>\n      <SimpleHeader title=\"À propos\" showBackButton />\n      <div className=\"container mx-auto px-4 py-8\">\n\n        {/* Hero Section */}\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n        >\n          <div className=\"text-6xl mb-6\">🧮</div>\n          <h1 className=\"text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            HandyMath\n          </h1>\n          <p className=\"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\">\n            Votre assistant personnel pour l'apprentissage des mathématiques.\n            Résolvez, apprenez et progressez avec notre plateforme innovante.\n          </p>\n        </motion.div>\n\n        {/* Mission */}\n        <motion.div\n          className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-16\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-6\">\n              Notre Mission\n            </h2>\n            <p className=\"text-lg text-gray-600 dark:text-gray-400 max-w-4xl mx-auto leading-relaxed\">\n              Rendre les mathématiques accessibles à tous en combinant technologie moderne et pédagogie innovante.\n              Nous croyons que chaque étudiant peut exceller en mathématiques avec les bons outils et le bon accompagnement.\n            </p>\n          </div>\n        </motion.div>\n\n        {/* Statistiques */}\n        <motion.div\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-6 mb-16\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.3 }}\n        >\n          {stats.map((stat, index) => (\n            <motion.div\n              key={index}\n              className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center\"\n              whileHover={{ scale: 1.05 }}\n              transition={{ type: \"spring\", stiffness: 300 }}\n            >\n              <div className=\"text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2\">\n                {stat.number}\n              </div>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {stat.label}\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Fonctionnalités */}\n        <motion.div\n          className=\"mb-16\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n        >\n          <h2 className=\"text-3xl font-bold text-gray-900 dark:text-white text-center mb-12\">\n            Fonctionnalités Principales\n          </h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {features.map((feature, index) => (\n              <motion.div\n                key={index}\n                className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\n                whileHover={{ y: -5 }}\n                transition={{ type: \"spring\", stiffness: 300 }}\n              >\n                <div className=\"text-4xl mb-4\">{feature.icon}</div>\n                <h3 className=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">\n                  {feature.title}\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-400\">\n                  {feature.description}\n                </p>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n\n\n        {/* Technologie */}\n        <motion.div\n          className=\"bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-xl p-8 mb-16\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5 }}\n        >\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-6\">\n              Technologie Moderne\n            </h2>\n            <p className=\"text-lg text-gray-600 dark:text-gray-400 mb-8 max-w-3xl mx-auto\">\n              HandyMath utilise les dernières technologies web et d'intelligence artificielle\n              pour offrir une expérience d'apprentissage optimale et personnalisée.\n            </p>\n            <div className=\"flex flex-wrap justify-center gap-4\">\n              {['React', 'TypeScript', 'Python', 'Django', 'OCR', 'IA'].map((tech, index) => (\n                <span\n                  key={index}\n                  className=\"px-4 py-2 bg-white dark:bg-gray-800 rounded-full text-sm font-medium text-gray-700 dark:text-gray-300 shadow-md\"\n                >\n                  {tech}\n                </span>\n              ))}\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Call to Action */}\n        <motion.div\n          className=\"text-center\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.6 }}\n        >\n          <h2 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-6\">\n            Prêt à commencer ?\n          </h2>\n          <p className=\"text-lg text-gray-600 dark:text-gray-400 mb-8\">\n            Rejoignez des milliers d'étudiants qui améliorent leurs compétences en mathématiques avec HandyMath.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              to=\"/register\"\n              className=\"px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors\"\n            >\n              Créer un compte\n            </Link>\n            <Link\n              to=\"/solver\"\n              className=\"px-8 py-3 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors\"\n            >\n              Essayer le solveur\n            </Link>\n            <Link\n              to=\"/contact\"\n              className=\"px-8 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 font-medium rounded-lg transition-colors\"\n            >\n              Nous contacter\n            </Link>\n          </div>\n        </motion.div>\n      </div>\n    </>\n  );\n};\n\nexport default AboutPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,YAAY,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGtD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAChC,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE;EACf,CAAC,CACF;EAID,MAAMC,KAAK,GAAG,CACZ;IAAEC,MAAM,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAqB,CAAC,EAChD;IAAED,MAAM,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAsB,CAAC,EAChD;IAAED,MAAM,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAwB,CAAC,EACjD;IAAED,MAAM,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAuB,CAAC,CACjD;EAED,oBACEV,OAAA,CAAAE,SAAA;IAAAS,QAAA,gBACEX,OAAA,CAACF,YAAY;MAACQ,KAAK,EAAC,aAAU;MAACM,cAAc;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChDhB,OAAA;MAAKiB,SAAS,EAAC,6BAA6B;MAAAN,QAAA,gBAG1CX,OAAA,CAACJ,MAAM,CAACsB,GAAG;QACTD,SAAS,EAAC,mBAAmB;QAC7BE,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAV,QAAA,gBAE9BX,OAAA;UAAKiB,SAAS,EAAC,eAAe;UAAAN,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvChB,OAAA;UAAIiB,SAAS,EAAC,uDAAuD;UAAAN,QAAA,EAAC;QAEtE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhB,OAAA;UAAGiB,SAAS,EAAC,4DAA4D;UAAAN,QAAA,EAAC;QAG1E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGbhB,OAAA,CAACJ,MAAM,CAACsB,GAAG;QACTD,SAAS,EAAC,0DAA0D;QACpEE,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAb,QAAA,eAE3BX,OAAA;UAAKiB,SAAS,EAAC,aAAa;UAAAN,QAAA,gBAC1BX,OAAA;YAAIiB,SAAS,EAAC,uDAAuD;YAAAN,QAAA,EAAC;UAEtE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhB,OAAA;YAAGiB,SAAS,EAAC,4EAA4E;YAAAN,QAAA,EAAC;UAG1F;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbhB,OAAA,CAACJ,MAAM,CAACsB,GAAG;QACTD,SAAS,EAAC,6CAA6C;QACvDE,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAb,QAAA,EAE1BH,KAAK,CAACiB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrB3B,OAAA,CAACJ,MAAM,CAACsB,GAAG;UAETD,SAAS,EAAC,gEAAgE;UAC1EW,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BN,UAAU,EAAE;YAAEO,IAAI,EAAE,QAAQ;YAAEC,SAAS,EAAE;UAAI,CAAE;UAAApB,QAAA,gBAE/CX,OAAA;YAAKiB,SAAS,EAAC,gEAAgE;YAAAN,QAAA,EAC5Ee,IAAI,CAACjB;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNhB,OAAA;YAAKiB,SAAS,EAAC,0CAA0C;YAAAN,QAAA,EACtDe,IAAI,CAAChB;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA,GAVDW,KAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWA,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAGbhB,OAAA,CAACJ,MAAM,CAACsB,GAAG;QACTD,SAAS,EAAC,OAAO;QACjBE,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAb,QAAA,gBAE3BX,OAAA;UAAIiB,SAAS,EAAC,oEAAoE;UAAAN,QAAA,EAAC;QAEnF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhB,OAAA;UAAKiB,SAAS,EAAC,sDAAsD;UAAAN,QAAA,EAClEP,QAAQ,CAACqB,GAAG,CAAC,CAACO,OAAO,EAAEL,KAAK,kBAC3B3B,OAAA,CAACJ,MAAM,CAACsB,GAAG;YAETD,SAAS,EAAC,oDAAoD;YAC9DW,UAAU,EAAE;cAAEP,CAAC,EAAE,CAAC;YAAE,CAAE;YACtBE,UAAU,EAAE;cAAEO,IAAI,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAI,CAAE;YAAApB,QAAA,gBAE/CX,OAAA;cAAKiB,SAAS,EAAC,eAAe;cAAAN,QAAA,EAAEqB,OAAO,CAAC3B;YAAI;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDhB,OAAA;cAAIiB,SAAS,EAAC,sDAAsD;cAAAN,QAAA,EACjEqB,OAAO,CAAC1B;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLhB,OAAA;cAAGiB,SAAS,EAAC,kCAAkC;cAAAN,QAAA,EAC5CqB,OAAO,CAACzB;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA,GAXCW,KAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAKbhB,OAAA,CAACJ,MAAM,CAACsB,GAAG;QACTD,SAAS,EAAC,+GAA+G;QACzHE,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAb,QAAA,eAE3BX,OAAA;UAAKiB,SAAS,EAAC,aAAa;UAAAN,QAAA,gBAC1BX,OAAA;YAAIiB,SAAS,EAAC,uDAAuD;YAAAN,QAAA,EAAC;UAEtE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhB,OAAA;YAAGiB,SAAS,EAAC,iEAAiE;YAAAN,QAAA,EAAC;UAG/E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJhB,OAAA;YAAKiB,SAAS,EAAC,qCAAqC;YAAAN,QAAA,EACjD,CAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAACc,GAAG,CAAC,CAACQ,IAAI,EAAEN,KAAK,kBACxE3B,OAAA;cAEEiB,SAAS,EAAC,iHAAiH;cAAAN,QAAA,EAE1HsB;YAAI,GAHAN,KAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIN,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbhB,OAAA,CAACJ,MAAM,CAACsB,GAAG;QACTD,SAAS,EAAC,aAAa;QACvBE,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAb,QAAA,gBAE3BX,OAAA;UAAIiB,SAAS,EAAC,uDAAuD;UAAAN,QAAA,EAAC;QAEtE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhB,OAAA;UAAGiB,SAAS,EAAC,+CAA+C;UAAAN,QAAA,EAAC;QAE7D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJhB,OAAA;UAAKiB,SAAS,EAAC,gDAAgD;UAAAN,QAAA,gBAC7DX,OAAA,CAACH,IAAI;YACHqC,EAAE,EAAC,WAAW;YACdjB,SAAS,EAAC,mGAAmG;YAAAN,QAAA,EAC9G;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPhB,OAAA,CAACH,IAAI;YACHqC,EAAE,EAAC,SAAS;YACZjB,SAAS,EAAC,6FAA6F;YAAAN,QAAA,EACxG;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPhB,OAAA,CAACH,IAAI;YACHqC,EAAE,EAAC,UAAU;YACbjB,SAAS,EAAC,yKAAyK;YAAAN,QAAA,EACpL;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACmB,EAAA,GA3MIhC,SAAmB;AA6MzB,eAAeA,SAAS;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}