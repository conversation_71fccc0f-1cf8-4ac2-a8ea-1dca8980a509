{"ast": null, "code": "export var bitXorDocs = {\n  name: 'bitXor',\n  category: 'Bitwise',\n  syntax: ['bitXor(x, y)'],\n  description: 'Bitwise XOR operation, exclusive OR. Performs the logical exclusive OR operation on each pair of corresponding bits of the two given values. The result in each position is 1 if only the first bit is 1 or only the second bit is 1, but will be 0 if both are 0 or both are 1.',\n  examples: ['bitOr(1, 2)', 'bitXor([2, 3, 4], 4)'],\n  seealso: ['bitAnd', 'bitNot', 'bitOr', 'leftShift', 'rightArithShift', 'rightLogShift']\n};", "map": {"version": 3, "names": ["bitXorDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/bitwise/bitXor.js"], "sourcesContent": ["export var bitXorDocs = {\n  name: 'bitXor',\n  category: 'Bitwise',\n  syntax: ['bitXor(x, y)'],\n  description: 'Bitwise XOR operation, exclusive OR. Performs the logical exclusive OR operation on each pair of corresponding bits of the two given values. The result in each position is 1 if only the first bit is 1 or only the second bit is 1, but will be 0 if both are 0 or both are 1.',\n  examples: ['bitOr(1, 2)', 'bitXor([2, 3, 4], 4)'],\n  seealso: ['bitAnd', 'bitNot', 'bitOr', 'leftShift', 'rightArithShift', 'rightLogShift']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,CAAC,cAAc,CAAC;EACxBC,WAAW,EAAE,kRAAkR;EAC/RC,QAAQ,EAAE,CAAC,aAAa,EAAE,sBAAsB,CAAC;EACjDC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAE,eAAe;AACxF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}