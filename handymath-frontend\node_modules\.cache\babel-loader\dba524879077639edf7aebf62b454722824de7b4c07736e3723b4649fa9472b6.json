{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { factorialDependencies } from './dependenciesFactorial.generated.js';\nimport { isIntegerDependencies } from './dependenciesIsInteger.generated.js';\nimport { isPositiveDependencies } from './dependenciesIsPositive.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMultinomial } from '../../factoriesAny.js';\nexport var multinomialDependencies = {\n  addDependencies,\n  divideDependencies,\n  factorialDependencies,\n  isIntegerDependencies,\n  isPositiveDependencies,\n  multiplyDependencies,\n  typedDependencies,\n  createMultinomial\n};", "map": {"version": 3, "names": ["addDependencies", "divideDependencies", "factorialDependencies", "isIntegerDependencies", "isPositiveDependencies", "multiplyDependencies", "typedDependencies", "createMultinomial", "multinomialDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMultinomial.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { factorialDependencies } from './dependenciesFactorial.generated.js';\nimport { isIntegerDependencies } from './dependenciesIsInteger.generated.js';\nimport { isPositiveDependencies } from './dependenciesIsPositive.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMultinomial } from '../../factoriesAny.js';\nexport var multinomialDependencies = {\n  addDependencies,\n  divideDependencies,\n  factorialDependencies,\n  isIntegerDependencies,\n  isPositiveDependencies,\n  multiplyDependencies,\n  typedDependencies,\n  createMultinomial\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAe,QAAQ,gCAAgC;AAChE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,OAAO,IAAIC,uBAAuB,GAAG;EACnCR,eAAe;EACfC,kBAAkB;EAClBC,qBAAqB;EACrBC,qBAAqB;EACrBC,sBAAsB;EACtBC,oBAAoB;EACpBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}