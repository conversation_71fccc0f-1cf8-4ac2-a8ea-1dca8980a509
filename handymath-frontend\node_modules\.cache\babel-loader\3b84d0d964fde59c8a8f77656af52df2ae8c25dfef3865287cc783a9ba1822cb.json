{"ast": null, "code": "import { memoize } from '../function.js';\n\n/**\n * Calculate BigNumber e\n * @param {function} BigNumber   BigNumber constructor\n * @returns {BigNumber} Returns e\n */\nexport var createBigNumberE = memoize(function (BigNumber) {\n  return new BigNumber(1).exp();\n}, {\n  hasher\n});\n\n/**\n * Calculate BigNumber golden ratio, phi = (1+sqrt(5))/2\n * @param {function} BigNumber   BigNumber constructor\n * @returns {BigNumber} Returns phi\n */\nexport var createBigNumberPhi = memoize(function (BigNumber) {\n  return new BigNumber(1).plus(new BigNumber(5).sqrt()).div(2);\n}, {\n  hasher\n});\n\n/**\n * Calculate BigNumber pi.\n * @param {function} BigNumber   BigNumber constructor\n * @returns {BigNumber} Returns pi\n */\nexport var createBigNumberPi = memoize(function (BigNumber) {\n  return BigNumber.acos(-1);\n}, {\n  hasher\n});\n\n/**\n * Calculate BigNumber tau, tau = 2 * pi\n * @param {function} BigNumber   BigNumber constructor\n * @returns {BigNumber} Returns tau\n */\nexport var createBigNumberTau = memoize(function (BigNumber) {\n  return createBigNumberPi(BigNumber).times(2);\n}, {\n  hasher\n});\n\n/**\n * Create a hash for a BigNumber constructor function. The created has is\n * the configured precision\n * @param {Array} args         Supposed to contain a single entry with\n *                             a BigNumber constructor\n * @return {number} precision\n * @private\n */\nfunction hasher(args) {\n  return args[0].precision;\n}", "map": {"version": 3, "names": ["memoize", "createBigNumberE", "BigNumber", "exp", "hasher", "createBigNumberPhi", "plus", "sqrt", "div", "createBigNumberPi", "acos", "createBigNumberTau", "times", "args", "precision"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/utils/bignumber/constants.js"], "sourcesContent": ["import { memoize } from '../function.js';\n\n/**\n * Calculate BigNumber e\n * @param {function} BigNumber   BigNumber constructor\n * @returns {BigNumber} Returns e\n */\nexport var createBigNumberE = memoize(function (BigNumber) {\n  return new BigNumber(1).exp();\n}, {\n  hasher\n});\n\n/**\n * Calculate BigNumber golden ratio, phi = (1+sqrt(5))/2\n * @param {function} BigNumber   BigNumber constructor\n * @returns {BigNumber} Returns phi\n */\nexport var createBigNumberPhi = memoize(function (BigNumber) {\n  return new BigNumber(1).plus(new BigNumber(5).sqrt()).div(2);\n}, {\n  hasher\n});\n\n/**\n * Calculate BigNumber pi.\n * @param {function} BigNumber   BigNumber constructor\n * @returns {BigNumber} Returns pi\n */\nexport var createBigNumberPi = memoize(function (BigNumber) {\n  return BigNumber.acos(-1);\n}, {\n  hasher\n});\n\n/**\n * Calculate BigNumber tau, tau = 2 * pi\n * @param {function} BigNumber   BigNumber constructor\n * @returns {BigNumber} Returns tau\n */\nexport var createBigNumberTau = memoize(function (BigNumber) {\n  return createBigNumberPi(BigNumber).times(2);\n}, {\n  hasher\n});\n\n/**\n * Create a hash for a BigNumber constructor function. The created has is\n * the configured precision\n * @param {Array} args         Supposed to contain a single entry with\n *                             a BigNumber constructor\n * @return {number} precision\n * @private\n */\nfunction hasher(args) {\n  return args[0].precision;\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,gBAAgB;;AAExC;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,gBAAgB,GAAGD,OAAO,CAAC,UAAUE,SAAS,EAAE;EACzD,OAAO,IAAIA,SAAS,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC;AAC/B,CAAC,EAAE;EACDC;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,kBAAkB,GAAGL,OAAO,CAAC,UAAUE,SAAS,EAAE;EAC3D,OAAO,IAAIA,SAAS,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC,IAAIJ,SAAS,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC;AAC9D,CAAC,EAAE;EACDJ;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIK,iBAAiB,GAAGT,OAAO,CAAC,UAAUE,SAAS,EAAE;EAC1D,OAAOA,SAAS,CAACQ,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3B,CAAC,EAAE;EACDN;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIO,kBAAkB,GAAGX,OAAO,CAAC,UAAUE,SAAS,EAAE;EAC3D,OAAOO,iBAAiB,CAACP,SAAS,CAAC,CAACU,KAAK,CAAC,CAAC,CAAC;AAC9C,CAAC,EAAE;EACDR;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACS,IAAI,EAAE;EACpB,OAAOA,IAAI,CAAC,CAAC,CAAC,CAACC,SAAS;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}