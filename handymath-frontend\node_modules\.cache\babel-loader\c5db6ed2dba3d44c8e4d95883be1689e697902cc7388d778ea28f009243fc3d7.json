{"ast": null, "code": "export var typeOfDocs = {\n  name: 'typeOf',\n  category: 'Utils',\n  syntax: ['typeOf(x)'],\n  description: 'Get the type of a variable.',\n  examples: ['typeOf(3.5)', 'typeOf(2 - 4i)', 'typeOf(45 deg)', 'typeOf(\"hello world\")'],\n  seealso: ['getMatrixDataType']\n};", "map": {"version": 3, "names": ["typeOfDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/typeOf.js"], "sourcesContent": ["export var typeOfDocs = {\n  name: 'typeOf',\n  category: 'Utils',\n  syntax: ['typeOf(x)'],\n  description: 'Get the type of a variable.',\n  examples: ['typeOf(3.5)', 'typeOf(2 - 4i)', 'typeOf(45 deg)', 'typeOf(\"hello world\")'],\n  seealso: ['getMatrixDataType']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,WAAW,CAAC;EACrBC,WAAW,EAAE,6BAA6B;EAC1CC,QAAQ,EAAE,CAAC,aAAa,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,uBAAuB,CAAC;EACtFC,OAAO,EAAE,CAAC,mBAAmB;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}