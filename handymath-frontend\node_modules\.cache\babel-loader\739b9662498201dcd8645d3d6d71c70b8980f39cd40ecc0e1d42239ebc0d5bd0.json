{"ast": null, "code": "export var mapDocs = {\n  name: 'map',\n  category: 'Matrix',\n  syntax: ['map(x, callback)', 'map(x, y, ..., callback)'],\n  description: 'Create a new matrix or array with the results of the callback function executed on each entry of the matrix/array or the matrices/arrays.',\n  examples: ['map([1, 2, 3], square)', 'map([1, 2], [3, 4], f(a,b) = a + b)'],\n  seealso: ['filter', 'forEach']\n};", "map": {"version": 3, "names": ["mapDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/map.js"], "sourcesContent": ["export var mapDocs = {\n  name: 'map',\n  category: 'Matrix',\n  syntax: ['map(x, callback)', 'map(x, y, ..., callback)'],\n  description: 'Create a new matrix or array with the results of the callback function executed on each entry of the matrix/array or the matrices/arrays.',\n  examples: ['map([1, 2, 3], square)', 'map([1, 2], [3, 4], f(a,b) = a + b)'],\n  seealso: ['filter', 'forEach']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,kBAAkB,EAAE,0BAA0B,CAAC;EACxDC,WAAW,EAAE,2IAA2I;EACxJC,QAAQ,EAAE,CAAC,wBAAwB,EAAE,qCAAqC,CAAC;EAC3EC,OAAO,EAAE,CAAC,QAAQ,EAAE,SAAS;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}