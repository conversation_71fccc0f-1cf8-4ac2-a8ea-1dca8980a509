{"ast": null, "code": "export var matrixFromFunctionDocs = {\n  name: 'matrixFromFunction',\n  category: 'Matrix',\n  syntax: ['matrixFromFunction(size, fn)', 'matrixFromFunction(size, fn, format)', 'matrixFromFunction(size, fn, format, datatype)', 'matrixFromFunction(size, format, fn)', 'matrixFromFunction(size, format, datatype, fn)'],\n  description: 'Create a matrix by evaluating a generating function at each index.',\n  examples: ['f(I) = I[1] - I[2]', 'matrixFromFunction([3,3], f)', 'g(I) = I[1] - I[2] == 1 ? 4 : 0', 'matrixFromFunction([100, 100], \"sparse\", g)', 'matrixFromFunction([5], random)'],\n  seealso: ['matrix', 'matrixFromRows', 'matrixFromColumns', 'zeros']\n};", "map": {"version": 3, "names": ["matrixFromFunctionDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/matrixFromFunction.js"], "sourcesContent": ["export var matrixFromFunctionDocs = {\n  name: 'matrixFromFunction',\n  category: 'Matrix',\n  syntax: ['matrixFromFunction(size, fn)', 'matrixFromFunction(size, fn, format)', 'matrixFromFunction(size, fn, format, datatype)', 'matrixFromFunction(size, format, fn)', 'matrixFromFunction(size, format, datatype, fn)'],\n  description: 'Create a matrix by evaluating a generating function at each index.',\n  examples: ['f(I) = I[1] - I[2]', 'matrixFromFunction([3,3], f)', 'g(I) = I[1] - I[2] == 1 ? 4 : 0', 'matrixFromFunction([100, 100], \"sparse\", g)', 'matrixFromFunction([5], random)'],\n  seealso: ['matrix', 'matrixFromRows', 'matrixFromColumns', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,sBAAsB,GAAG;EAClCC,IAAI,EAAE,oBAAoB;EAC1BC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,8BAA8B,EAAE,sCAAsC,EAAE,gDAAgD,EAAE,sCAAsC,EAAE,gDAAgD,CAAC;EAC5NC,WAAW,EAAE,oEAAoE;EACjFC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,8BAA8B,EAAE,iCAAiC,EAAE,6CAA6C,EAAE,iCAAiC,CAAC;EACrLC,OAAO,EAAE,CAAC,QAAQ,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,OAAO;AACpE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}