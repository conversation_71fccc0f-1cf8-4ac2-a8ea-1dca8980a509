{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\components\\\\StudentNavbar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport Logo from './Logo';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentNavbar = () => {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const {\n    theme,\n    toggleTheme\n  } = useTheme();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  // Navigation items pour les étudiants\n  const navigationItems = [{\n    name: 'Dashboard',\n    path: '/etudiant/dashboard',\n    description: 'Tableau de bord'\n  }, {\n    name: 'Résolveur',\n    path: '/solver',\n    description: 'Résoudre des équations'\n  }, {\n    name: 'Exercices',\n    path: '/exercises',\n    description: 'Faire des exercices'\n  }, {\n    name: 'Cours',\n    path: '/courses',\n    description: 'Consulter les cours'\n  }, {\n    name: 'Progression',\n    path: '/progress',\n    description: 'Voir ma progression'\n  }];\n  const isActive = path => {\n    return location.pathname === path || location.pathname.startsWith(path + '/');\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(Logo, {\n            variant: \"student\",\n            linkTo: \"/\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:block\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-500 dark:text-gray-400\",\n              children: \"Espace \\xC9tudiant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center space-x-1\",\n          children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.path,\n            className: `px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${isActive(item.path) ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300' : 'text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700'}`,\n            title: item.description,\n            children: item.name\n          }, item.path, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: toggleTheme,\n            className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors\",\n            title: theme === 'dark' ? 'Mode clair' : 'Mode sombre',\n            children: theme === 'dark' ? /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-semibold\",\n                children: user === null || user === void 0 ? void 0 : user.username.charAt(0).toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-gray-900 dark:text-white\",\n                  children: (user === null || user === void 0 ? void 0 : user.prenom) || (user === null || user === void 0 ? void 0 : user.username)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-blue-600 dark:text-blue-400\",\n                  children: \"\\xC9tudiant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"p-1 text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"py-1\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/profile\",\n                    className: \"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                    children: \"Mon profil\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/settings\",\n                    className: \"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                    children: \"Param\\xE8tres\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                    className: \"my-1 border-gray-200 dark:border-gray-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleLogout,\n                    className: \"block w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors\",\n                    children: \"D\\xE9connexion\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n            className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 6h16M4 12h16M4 18h16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:hidden border-t border-gray-200 dark:border-gray-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-2 pt-2 pb-3 space-y-1\",\n          children: [navigationItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.path,\n            onClick: () => setIsMobileMenuOpen(false),\n            className: `block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${isActive(item.path) ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300' : 'text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700'}`,\n            children: item.name\n          }, item.path, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 17\n          }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-t border-gray-200 dark:border-gray-700 pt-3 mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/profile\",\n              onClick: () => setIsMobileMenuOpen(false),\n              className: \"block px-3 py-2 text-base font-medium text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n              children: \"Mon profil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/settings\",\n              onClick: () => setIsMobileMenuOpen(false),\n              className: \"block px-3 py-2 text-base font-medium text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n              children: \"Param\\xE8tres\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setIsMobileMenuOpen(false);\n                handleLogout();\n              },\n              className: \"block w-full text-left px-3 py-2 text-base font-medium text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors\",\n              children: \"D\\xE9connexion\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentNavbar, \"mqNeklrjssChoYLNAXV6Lg6vJL0=\", false, function () {\n  return [useLocation, useNavigate, useAuth, useTheme];\n});\n_c = StudentNavbar;\nexport default StudentNavbar;\nvar _c;\n$RefreshReg$(_c, \"StudentNavbar\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "useNavigate", "useAuth", "useTheme", "Logo", "jsxDEV", "_jsxDEV", "StudentNavbar", "_s", "location", "navigate", "user", "logout", "theme", "toggleTheme", "isMobileMenuOpen", "setIsMobileMenuOpen", "handleLogout", "navigationItems", "name", "path", "description", "isActive", "pathname", "startsWith", "className", "children", "variant", "linkTo", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "to", "title", "onClick", "fill", "viewBox", "fillRule", "d", "clipRule", "username", "char<PERSON>t", "toUpperCase", "prenom", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/components/StudentNavbar.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport Logo from './Logo';\n\nconst StudentNavbar: React.FC = () => {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { user, logout } = useAuth();\n  const { theme, toggleTheme } = useTheme();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  // Navigation items pour les étudiants\n  const navigationItems = [\n    {\n      name: 'Dashboard',\n      path: '/etudiant/dashboard',\n      description: 'Tableau de bord'\n    },\n    {\n      name: 'Résolveur',\n      path: '/solver',\n      description: 'Résoudre des équations'\n    },\n    {\n      name: 'Exercices',\n      path: '/exercises',\n      description: '<PERSON>e des exercices'\n    },\n    {\n      name: 'Cours',\n      path: '/courses',\n      description: 'Consulter les cours'\n    },\n    {\n      name: 'Progression',\n      path: '/progress',\n      description: 'Voir ma progression'\n    }\n  ];\n\n  const isActive = (path: string) => {\n    return location.pathname === path || location.pathname.startsWith(path + '/');\n  };\n\n  return (\n    <nav className=\"bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center h-16\">\n          \n          {/* Logo et titre */}\n          <div className=\"flex items-center space-x-4\">\n            <Logo variant=\"student\" linkTo=\"/\" />\n            <div className=\"hidden md:block\">\n              <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                Espace Étudiant\n              </span>\n            </div>\n          </div>\n\n          {/* Navigation principale - Desktop */}\n          <div className=\"hidden md:flex items-center space-x-1\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.path}\n                to={item.path}\n                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${\n                  isActive(item.path)\n                    ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'\n                    : 'text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n                title={item.description}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n\n          {/* Actions utilisateur - Desktop */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            \n            {/* Bouton thème */}\n            <button\n              onClick={toggleTheme}\n              className=\"p-2 text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors\"\n              title={theme === 'dark' ? 'Mode clair' : 'Mode sombre'}\n            >\n              {theme === 'dark' ? (\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z\" clipRule=\"evenodd\" />\n                </svg>\n              ) : (\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z\" />\n                </svg>\n              )}\n            </button>\n\n            {/* Profil utilisateur */}\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-semibold\">\n                  {user?.username.charAt(0).toUpperCase()}\n                </div>\n                <div className=\"text-sm\">\n                  <div className=\"font-medium text-gray-900 dark:text-white\">\n                    {user?.prenom || user?.username}\n                  </div>\n                  <div className=\"text-xs text-blue-600 dark:text-blue-400\">\n                    Étudiant\n                  </div>\n                </div>\n              </div>\n\n              {/* Menu déroulant profil */}\n              <div className=\"relative group\">\n                <button className=\"p-1 text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors\">\n                  <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                  </svg>\n                </button>\n                \n                {/* Dropdown menu */}\n                <div className=\"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                  <div className=\"py-1\">\n                    <Link\n                      to=\"/profile\"\n                      className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                    >\n                      Mon profil\n                    </Link>\n                    <Link\n                      to=\"/settings\"\n                      className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                    >\n                      Paramètres\n                    </Link>\n                    <hr className=\"my-1 border-gray-200 dark:border-gray-600\" />\n                    <button\n                      onClick={handleLogout}\n                      className=\"block w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors\"\n                    >\n                      Déconnexion\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Bouton menu mobile */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"p-2 text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                {isMobileMenuOpen ? (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                ) : (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                )}\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Menu mobile */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden border-t border-gray-200 dark:border-gray-700\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.path}\n                  to={item.path}\n                  onClick={() => setIsMobileMenuOpen(false)}\n                  className={`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${\n                    isActive(item.path)\n                      ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'\n                      : 'text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700'\n                  }`}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              \n              {/* Actions mobiles */}\n              <div className=\"border-t border-gray-200 dark:border-gray-700 pt-3 mt-3\">\n                <Link\n                  to=\"/profile\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                  className=\"block px-3 py-2 text-base font-medium text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                >\n                  Mon profil\n                </Link>\n                <Link\n                  to=\"/settings\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                  className=\"block px-3 py-2 text-base font-medium text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                >\n                  Paramètres\n                </Link>\n                <button\n                  onClick={() => {\n                    setIsMobileMenuOpen(false);\n                    handleLogout();\n                  }}\n                  className=\"block w-full text-left px-3 py-2 text-base font-medium text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors\"\n                >\n                  Déconnexion\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default StudentNavbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,IAAI,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU,IAAI;IAAEC;EAAO,CAAC,GAAGV,OAAO,CAAC,CAAC;EAClC,MAAM;IAAEW,KAAK;IAAEC;EAAY,CAAC,GAAGX,QAAQ,CAAC,CAAC;EACzC,MAAM,CAACY,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMmB,YAAY,GAAGA,CAAA,KAAM;IACzBL,MAAM,CAAC,CAAC;IACRF,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;;EAED;EACA,MAAMQ,eAAe,GAAG,CACtB;IACEC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,qBAAqB;IAC3BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,SAAS;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,QAAQ,GAAIF,IAAY,IAAK;IACjC,OAAOX,QAAQ,CAACc,QAAQ,KAAKH,IAAI,IAAIX,QAAQ,CAACc,QAAQ,CAACC,UAAU,CAACJ,IAAI,GAAG,GAAG,CAAC;EAC/E,CAAC;EAED,oBACEd,OAAA;IAAKmB,SAAS,EAAC,mFAAmF;IAAAC,QAAA,eAChGpB,OAAA;MAAKmB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCpB,OAAA;QAAKmB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAGrDpB,OAAA;UAAKmB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CpB,OAAA,CAACF,IAAI;YAACuB,OAAO,EAAC,SAAS;YAACC,MAAM,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrC1B,OAAA;YAAKmB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BpB,OAAA;cAAMmB,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAE3D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1B,OAAA;UAAKmB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDR,eAAe,CAACe,GAAG,CAAEC,IAAI,iBACxB5B,OAAA,CAACP,IAAI;YAEHoC,EAAE,EAAED,IAAI,CAACd,IAAK;YACdK,SAAS,EAAE,2EACTH,QAAQ,CAACY,IAAI,CAACd,IAAI,CAAC,GACf,2EAA2E,GAC3E,8HAA8H,EACjI;YACHgB,KAAK,EAAEF,IAAI,CAACb,WAAY;YAAAK,QAAA,EAEvBQ,IAAI,CAACf;UAAI,GATLe,IAAI,CAACd,IAAI;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN1B,OAAA;UAAKmB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAGpDpB,OAAA;YACE+B,OAAO,EAAEvB,WAAY;YACrBW,SAAS,EAAC,2GAA2G;YACrHW,KAAK,EAAEvB,KAAK,KAAK,MAAM,GAAG,YAAY,GAAG,aAAc;YAAAa,QAAA,EAEtDb,KAAK,KAAK,MAAM,gBACfP,OAAA;cAAKmB,SAAS,EAAC,SAAS;cAACa,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAb,QAAA,eAC9DpB,OAAA;gBAAMkC,QAAQ,EAAC,SAAS;gBAACC,CAAC,EAAC,sfAAsf;gBAACC,QAAQ,EAAC;cAAS;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpiB,CAAC,gBAEN1B,OAAA;cAAKmB,SAAS,EAAC,SAAS;cAACa,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAb,QAAA,eAC9DpB,OAAA;gBAAMmC,CAAC,EAAC;cAAmE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAGT1B,OAAA;YAAKmB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CpB,OAAA;cAAKmB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CpB,OAAA;gBAAKmB,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,EAChHf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACN1B,OAAA;gBAAKmB,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACtBpB,OAAA;kBAAKmB,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EACvD,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,MAAM,MAAInC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,QAAQ;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACN1B,OAAA;kBAAKmB,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAE1D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN1B,OAAA;cAAKmB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BpB,OAAA;gBAAQmB,SAAS,EAAC,2GAA2G;gBAAAC,QAAA,eAC3HpB,OAAA;kBAAKmB,SAAS,EAAC,SAAS;kBAACa,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAb,QAAA,eAC9DpB,OAAA;oBAAMkC,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,oHAAoH;oBAACC,QAAQ,EAAC;kBAAS;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eAGT1B,OAAA;gBAAKmB,SAAS,EAAC,wNAAwN;gBAAAC,QAAA,eACrOpB,OAAA;kBAAKmB,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBpB,OAAA,CAACP,IAAI;oBACHoC,EAAE,EAAC,UAAU;oBACbV,SAAS,EAAC,qHAAqH;oBAAAC,QAAA,EAChI;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP1B,OAAA,CAACP,IAAI;oBACHoC,EAAE,EAAC,WAAW;oBACdV,SAAS,EAAC,qHAAqH;oBAAAC,QAAA,EAChI;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP1B,OAAA;oBAAImB,SAAS,EAAC;kBAA2C;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5D1B,OAAA;oBACE+B,OAAO,EAAEpB,YAAa;oBACtBQ,SAAS,EAAC,oIAAoI;oBAAAC,QAAA,EAC/I;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1B,OAAA;UAAKmB,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBpB,OAAA;YACE+B,OAAO,EAAEA,CAAA,KAAMrB,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;YACtDU,SAAS,EAAC,2GAA2G;YAAAC,QAAA,eAErHpB,OAAA;cAAKmB,SAAS,EAAC,SAAS;cAACa,IAAI,EAAC,MAAM;cAACS,MAAM,EAAC,cAAc;cAACR,OAAO,EAAC,WAAW;cAAAb,QAAA,EAC3EX,gBAAgB,gBACfT,OAAA;gBAAM0C,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACT,CAAC,EAAC;cAAsB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE9F1B,OAAA;gBAAM0C,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACT,CAAC,EAAC;cAAyB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACjG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLjB,gBAAgB,iBACfT,OAAA;QAAKmB,SAAS,EAAC,yDAAyD;QAAAC,QAAA,eACtEpB,OAAA;UAAKmB,SAAS,EAAC,0BAA0B;UAAAC,QAAA,GACtCR,eAAe,CAACe,GAAG,CAAEC,IAAI,iBACxB5B,OAAA,CAACP,IAAI;YAEHoC,EAAE,EAAED,IAAI,CAACd,IAAK;YACdiB,OAAO,EAAEA,CAAA,KAAMrB,mBAAmB,CAAC,KAAK,CAAE;YAC1CS,SAAS,EAAE,mFACTH,QAAQ,CAACY,IAAI,CAACd,IAAI,CAAC,GACf,2EAA2E,GAC3E,8HAA8H,EACjI;YAAAM,QAAA,EAEFQ,IAAI,CAACf;UAAI,GATLe,IAAI,CAACd,IAAI;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUV,CACP,CAAC,eAGF1B,OAAA;YAAKmB,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACtEpB,OAAA,CAACP,IAAI;cACHoC,EAAE,EAAC,UAAU;cACbE,OAAO,EAAEA,CAAA,KAAMrB,mBAAmB,CAAC,KAAK,CAAE;cAC1CS,SAAS,EAAC,sLAAsL;cAAAC,QAAA,EACjM;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP1B,OAAA,CAACP,IAAI;cACHoC,EAAE,EAAC,WAAW;cACdE,OAAO,EAAEA,CAAA,KAAMrB,mBAAmB,CAAC,KAAK,CAAE;cAC1CS,SAAS,EAAC,sLAAsL;cAAAC,QAAA,EACjM;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP1B,OAAA;cACE+B,OAAO,EAAEA,CAAA,KAAM;gBACbrB,mBAAmB,CAAC,KAAK,CAAC;gBAC1BC,YAAY,CAAC,CAAC;cAChB,CAAE;cACFQ,SAAS,EAAC,kJAAkJ;cAAAC,QAAA,EAC7J;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxB,EAAA,CA1NID,aAAuB;EAAA,QACVP,WAAW,EACXC,WAAW,EACHC,OAAO,EACDC,QAAQ;AAAA;AAAAgD,EAAA,GAJnC5C,aAAuB;AA4N7B,eAAeA,aAAa;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}