#!/usr/bin/env python3
"""
Script pour créer un utilisateur de test et obtenir ses tokens
"""

import os
import sys
import django
import requests
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'handymath_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

def create_test_user():
    """Créer un utilisateur de test"""
    print("🚀 Création d'un utilisateur de test\n")
    
    # <PERSON><PERSON><PERSON> ou récupérer un utilisateur de test
    user, created = User.objects.get_or_create(
        username='lamiae',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Lamiae',
            'last_name': 'Test',
            'role': 'student'
        }
    )
    
    if created:
        user.set_password('password123')
        user.save()
        print(f"✅ Utilisateur créé: {user.username}")
    else:
        print(f"📚 Utilisateur existant: {user.username}")
        # Mettre à jour le mot de passe au cas où
        user.set_password('password123')
        user.save()
        print(f"🔄 Mot de passe mis à jour")
    
    # Générer les tokens JWT
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    refresh_token = str(refresh)
    
    print(f"\n🔑 Tokens générés:")
    print(f"Access Token: {access_token}")
    print(f"Refresh Token: {refresh_token}")
    
    # Tester la connexion via l'API
    print(f"\n🧪 Test de connexion via l'API:")
    try:
        response = requests.post('http://localhost:8000/api/token/', {
            'username': 'lamiae',
            'password': 'password123'
        })
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Connexion réussie!")
            print(f"Access Token: {data['access'][:50]}...")
            print(f"Refresh Token: {data['refresh'][:50]}...")
            
            # Sauvegarder dans un fichier pour utilisation dans le frontend
            tokens = {
                'access': data['access'],
                'refresh': data['refresh'],
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'nom': user.last_name,
                    'prenom': user.first_name,
                    'role': user.role
                }
            }
            
            with open('test_tokens.json', 'w') as f:
                json.dump(tokens, f, indent=2)
            
            print(f"\n💾 Tokens sauvegardés dans test_tokens.json")
            print(f"\n📋 Pour utiliser dans le frontend:")
            print(f"localStorage.setItem('authToken', '{data['access']}');")
            print(f"localStorage.setItem('refreshToken', '{data['refresh']}');")
            print(f"localStorage.setItem('user', '{json.dumps(tokens['user'])}');")
            
        else:
            print(f"❌ Erreur de connexion: {response.status_code}")
            print(f"Réponse: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Erreur de connexion - Le serveur Django n'est pas accessible")
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")

if __name__ == "__main__":
    create_test_user()
