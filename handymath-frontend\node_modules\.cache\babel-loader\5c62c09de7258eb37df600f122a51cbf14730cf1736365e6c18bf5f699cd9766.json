{"ast": null, "code": "// A Javascript implementaion of the \"xor128\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function (global, module, define) {\n  function XorGen(seed) {\n    var me = this,\n      strseed = '';\n    me.x = 0;\n    me.y = 0;\n    me.z = 0;\n    me.w = 0;\n\n    // Set up generator function.\n    me.next = function () {\n      var t = me.x ^ me.x << 11;\n      me.x = me.y;\n      me.y = me.z;\n      me.z = me.w;\n      return me.w ^= me.w >>> 19 ^ t ^ t >>> 8;\n    };\n    if (seed === (seed | 0)) {\n      // Integer seed.\n      me.x = seed;\n    } else {\n      // String seed.\n      strseed += seed;\n    }\n\n    // Mix in string seed, then discard an initial batch of 64 values.\n    for (var k = 0; k < strseed.length + 64; k++) {\n      me.x ^= strseed.charCodeAt(k) | 0;\n      me.next();\n    }\n  }\n  function copy(f, t) {\n    t.x = f.x;\n    t.y = f.y;\n    t.z = f.z;\n    t.w = f.w;\n    return t;\n  }\n  function impl(seed, opts) {\n    var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function () {\n        return (xg.next() >>> 0) / 0x100000000;\n      };\n    prng.double = function () {\n      do {\n        var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n      } while (result === 0);\n      return result;\n    };\n    prng.int32 = xg.next;\n    prng.quick = prng;\n    if (state) {\n      if (typeof state == 'object') copy(state, xg);\n      prng.state = function () {\n        return copy(xg, {});\n      };\n    }\n    return prng;\n  }\n  if (module && module.exports) {\n    module.exports = impl;\n  } else if (define && define.amd) {\n    define(function () {\n      return impl;\n    });\n  } else {\n    this.xor128 = impl;\n  }\n})(this, typeof module == 'object' && module,\n// present in node.js\ntypeof define == 'function' && define // present with an AMD loader\n);", "map": {"version": 3, "names": ["global", "module", "define", "XorGen", "seed", "me", "strseed", "x", "y", "z", "w", "next", "t", "k", "length", "charCodeAt", "copy", "f", "impl", "opts", "xg", "state", "prng", "double", "top", "bot", "result", "int32", "quick", "exports", "amd", "xor128"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/seedrandom/lib/xor128.js"], "sourcesContent": ["// A Javascript implementaion of the \"xor128\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n\n  // Set up generator function.\n  me.next = function() {\n    var t = me.x ^ (me.x << 11);\n    me.x = me.y;\n    me.y = me.z;\n    me.z = me.w;\n    return me.w ^= (me.w >>> 19) ^ t ^ (t >>> 8);\n  };\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor128 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n"], "mappings": "AAAA;AACA;;AAEA,CAAC,UAASA,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAElC,SAASC,MAAMA,CAACC,IAAI,EAAE;IACpB,IAAIC,EAAE,GAAG,IAAI;MAAEC,OAAO,GAAG,EAAE;IAE3BD,EAAE,CAACE,CAAC,GAAG,CAAC;IACRF,EAAE,CAACG,CAAC,GAAG,CAAC;IACRH,EAAE,CAACI,CAAC,GAAG,CAAC;IACRJ,EAAE,CAACK,CAAC,GAAG,CAAC;;IAER;IACAL,EAAE,CAACM,IAAI,GAAG,YAAW;MACnB,IAAIC,CAAC,GAAGP,EAAE,CAACE,CAAC,GAAIF,EAAE,CAACE,CAAC,IAAI,EAAG;MAC3BF,EAAE,CAACE,CAAC,GAAGF,EAAE,CAACG,CAAC;MACXH,EAAE,CAACG,CAAC,GAAGH,EAAE,CAACI,CAAC;MACXJ,EAAE,CAACI,CAAC,GAAGJ,EAAE,CAACK,CAAC;MACX,OAAOL,EAAE,CAACK,CAAC,IAAKL,EAAE,CAACK,CAAC,KAAK,EAAE,GAAIE,CAAC,GAAIA,CAAC,KAAK,CAAE;IAC9C,CAAC;IAED,IAAIR,IAAI,MAAMA,IAAI,GAAG,CAAC,CAAC,EAAE;MACvB;MACAC,EAAE,CAACE,CAAC,GAAGH,IAAI;IACb,CAAC,MAAM;MACL;MACAE,OAAO,IAAIF,IAAI;IACjB;;IAEA;IACA,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,OAAO,CAACQ,MAAM,GAAG,EAAE,EAAED,CAAC,EAAE,EAAE;MAC5CR,EAAE,CAACE,CAAC,IAAID,OAAO,CAACS,UAAU,CAACF,CAAC,CAAC,GAAG,CAAC;MACjCR,EAAE,CAACM,IAAI,CAAC,CAAC;IACX;EACF;EAEA,SAASK,IAAIA,CAACC,CAAC,EAAEL,CAAC,EAAE;IAClBA,CAAC,CAACL,CAAC,GAAGU,CAAC,CAACV,CAAC;IACTK,CAAC,CAACJ,CAAC,GAAGS,CAAC,CAACT,CAAC;IACTI,CAAC,CAACH,CAAC,GAAGQ,CAAC,CAACR,CAAC;IACTG,CAAC,CAACF,CAAC,GAAGO,CAAC,CAACP,CAAC;IACT,OAAOE,CAAC;EACV;EAEA,SAASM,IAAIA,CAACd,IAAI,EAAEe,IAAI,EAAE;IACxB,IAAIC,EAAE,GAAG,IAAIjB,MAAM,CAACC,IAAI,CAAC;MACrBiB,KAAK,GAAGF,IAAI,IAAIA,IAAI,CAACE,KAAK;MAC1BC,IAAI,GAAG,SAAAA,CAAA,EAAW;QAAE,OAAO,CAACF,EAAE,CAACT,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,WAAW;MAAE,CAAC;IACjEW,IAAI,CAACC,MAAM,GAAG,YAAW;MACvB,GAAG;QACD,IAAIC,GAAG,GAAGJ,EAAE,CAACT,IAAI,CAAC,CAAC,KAAK,EAAE;UACtBc,GAAG,GAAG,CAACL,EAAE,CAACT,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,WAAW;UACrCe,MAAM,GAAG,CAACF,GAAG,GAAGC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;MACtC,CAAC,QAAQC,MAAM,KAAK,CAAC;MACrB,OAAOA,MAAM;IACf,CAAC;IACDJ,IAAI,CAACK,KAAK,GAAGP,EAAE,CAACT,IAAI;IACpBW,IAAI,CAACM,KAAK,GAAGN,IAAI;IACjB,IAAID,KAAK,EAAE;MACT,IAAI,OAAOA,KAAM,IAAI,QAAQ,EAAEL,IAAI,CAACK,KAAK,EAAED,EAAE,CAAC;MAC9CE,IAAI,CAACD,KAAK,GAAG,YAAW;QAAE,OAAOL,IAAI,CAACI,EAAE,EAAE,CAAC,CAAC,CAAC;MAAE,CAAC;IAClD;IACA,OAAOE,IAAI;EACb;EAEA,IAAIrB,MAAM,IAAIA,MAAM,CAAC4B,OAAO,EAAE;IAC5B5B,MAAM,CAAC4B,OAAO,GAAGX,IAAI;EACvB,CAAC,MAAM,IAAIhB,MAAM,IAAIA,MAAM,CAAC4B,GAAG,EAAE;IAC/B5B,MAAM,CAAC,YAAW;MAAE,OAAOgB,IAAI;IAAE,CAAC,CAAC;EACrC,CAAC,MAAM;IACL,IAAI,CAACa,MAAM,GAAGb,IAAI;EACpB;AAEA,CAAC,EACC,IAAI,EACH,OAAOjB,MAAM,IAAK,QAAQ,IAAIA,MAAM;AAAK;AACzC,OAAOC,MAAM,IAAK,UAAU,IAAIA,MAAM,CAAG;AAC5C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}