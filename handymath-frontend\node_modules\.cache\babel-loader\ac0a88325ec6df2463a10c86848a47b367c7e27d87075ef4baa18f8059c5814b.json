{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { dotDependencies } from './dependenciesDot.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMultiply } from '../../factoriesAny.js';\nexport var multiplyDependencies = {\n  addScalarDependencies,\n  dotDependencies,\n  equalScalarDependencies,\n  matrixDependencies,\n  multiplyScalarDependencies,\n  typedDependencies,\n  createMultiply\n};", "map": {"version": 3, "names": ["addScalarDependencies", "dotDependencies", "equalScalarDependencies", "matrixDependencies", "multiplyScalarDependencies", "typedDependencies", "createMultiply", "multiplyDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMultiply.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { dotDependencies } from './dependenciesDot.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMultiply } from '../../factoriesAny.js';\nexport var multiplyDependencies = {\n  addScalarDependencies,\n  dotDependencies,\n  equalScalarDependencies,\n  matrixDependencies,\n  multiplyScalarDependencies,\n  typedDependencies,\n  createMultiply\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,uBAAuB,QAAQ,wCAAwC;AAChF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,0BAA0B,QAAQ,2CAA2C;AACtF,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,cAAc,QAAQ,uBAAuB;AACtD,OAAO,IAAIC,oBAAoB,GAAG;EAChCP,qBAAqB;EACrBC,eAAe;EACfC,uBAAuB;EACvBC,kBAAkB;EAClBC,0BAA0B;EAC1BC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}