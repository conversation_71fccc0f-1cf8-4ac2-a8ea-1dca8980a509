{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { factory } from '../../../utils/factory.js';\nimport { csLeaf } from './csLeaf.js';\nvar name = 'csCounts';\nvar dependencies = ['transpose'];\nexport var createCsCounts = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    transpose\n  } = _ref;\n  /**\n   * Computes the column counts using the upper triangular part of A.\n   * It transposes A internally, none of the input parameters are modified.\n   *\n   * @param {Matrix} a           The sparse matrix A\n   *\n   * @param {Matrix} ata         Count the columns of A'A instead\n   *\n   * @return                     An array of size n of the column counts or null on error\n   */\n  return function (a, parent, post, ata) {\n    // check inputs\n    if (!a || !parent || !post) {\n      return null;\n    }\n    // a matrix arrays\n    var asize = a._size;\n    // rows and columns\n    var m = asize[0];\n    var n = asize[1];\n    // variables\n    var i, j, k, J, p, p0, p1;\n\n    // workspace size\n    var s = 4 * n + (ata ? n + m + 1 : 0);\n    // allocate workspace\n    var w = []; // (s)\n    var ancestor = 0; // first n entries\n    var maxfirst = n; // next n entries\n    var prevleaf = 2 * n; // next n entries\n    var first = 3 * n; // next n entries\n    var head = 4 * n; // next n + 1 entries (used when ata is true)\n    var next = 5 * n + 1; // last entries in workspace\n    // clear workspace w[0..s-1]\n    for (k = 0; k < s; k++) {\n      w[k] = -1;\n    }\n\n    // allocate result\n    var colcount = []; // (n)\n\n    // AT = A'\n    var at = transpose(a);\n    // at arrays\n    var tindex = at._index;\n    var tptr = at._ptr;\n\n    // find w[first + j]\n    for (k = 0; k < n; k++) {\n      j = post[k];\n      // colcount[j]=1 if j is a leaf\n      colcount[j] = w[first + j] === -1 ? 1 : 0;\n      for (; j !== -1 && w[first + j] === -1; j = parent[j]) {\n        w[first + j] = k;\n      }\n    }\n\n    // initialize ata if needed\n    if (ata) {\n      // invert post\n      for (k = 0; k < n; k++) {\n        w[post[k]] = k;\n      }\n      // loop rows (columns in AT)\n      for (i = 0; i < m; i++) {\n        // values in column i of AT\n        for (k = n, p0 = tptr[i], p1 = tptr[i + 1], p = p0; p < p1; p++) {\n          k = Math.min(k, w[tindex[p]]);\n        }\n        // place row i in linked list k\n        w[next + i] = w[head + k];\n        w[head + k] = i;\n      }\n    }\n\n    // each node in its own set\n    for (i = 0; i < n; i++) {\n      w[ancestor + i] = i;\n    }\n    for (k = 0; k < n; k++) {\n      // j is the kth node in postordered etree\n      j = post[k];\n      // check j is not a root\n      if (parent[j] !== -1) {\n        colcount[parent[j]]--;\n      }\n\n      // J=j for LL'=A case\n      for (J = ata ? w[head + k] : j; J !== -1; J = ata ? w[next + J] : -1) {\n        for (p = tptr[J]; p < tptr[J + 1]; p++) {\n          i = tindex[p];\n          var r = csLeaf(i, j, w, first, maxfirst, prevleaf, ancestor);\n          // check A(i,j) is in skeleton\n          if (r.jleaf >= 1) {\n            colcount[j]++;\n          }\n          // check account for overlap in q\n          if (r.jleaf === 2) {\n            colcount[r.q]--;\n          }\n        }\n      }\n      if (parent[j] !== -1) {\n        w[ancestor + j] = parent[j];\n      }\n    }\n    // sum up colcount's of each child\n    for (j = 0; j < n; j++) {\n      if (parent[j] !== -1) {\n        colcount[parent[j]] += colcount[j];\n      }\n    }\n    return colcount;\n  };\n});", "map": {"version": 3, "names": ["factory", "csLeaf", "name", "dependencies", "createCsCounts", "_ref", "transpose", "a", "parent", "post", "ata", "asize", "_size", "m", "n", "i", "j", "k", "J", "p", "p0", "p1", "s", "w", "ancestor", "maxfirst", "prevleaf", "first", "head", "next", "colcount", "at", "tindex", "_index", "tptr", "_ptr", "Math", "min", "r", "j<PERSON>", "q"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/function/algebra/sparse/csCounts.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { factory } from '../../../utils/factory.js';\nimport { csLeaf } from './csLeaf.js';\nvar name = 'csCounts';\nvar dependencies = ['transpose'];\nexport var createCsCounts = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    transpose\n  } = _ref;\n  /**\n   * Computes the column counts using the upper triangular part of A.\n   * It transposes A internally, none of the input parameters are modified.\n   *\n   * @param {Matrix} a           The sparse matrix A\n   *\n   * @param {Matrix} ata         Count the columns of A'A instead\n   *\n   * @return                     An array of size n of the column counts or null on error\n   */\n  return function (a, parent, post, ata) {\n    // check inputs\n    if (!a || !parent || !post) {\n      return null;\n    }\n    // a matrix arrays\n    var asize = a._size;\n    // rows and columns\n    var m = asize[0];\n    var n = asize[1];\n    // variables\n    var i, j, k, J, p, p0, p1;\n\n    // workspace size\n    var s = 4 * n + (ata ? n + m + 1 : 0);\n    // allocate workspace\n    var w = []; // (s)\n    var ancestor = 0; // first n entries\n    var maxfirst = n; // next n entries\n    var prevleaf = 2 * n; // next n entries\n    var first = 3 * n; // next n entries\n    var head = 4 * n; // next n + 1 entries (used when ata is true)\n    var next = 5 * n + 1; // last entries in workspace\n    // clear workspace w[0..s-1]\n    for (k = 0; k < s; k++) {\n      w[k] = -1;\n    }\n\n    // allocate result\n    var colcount = []; // (n)\n\n    // AT = A'\n    var at = transpose(a);\n    // at arrays\n    var tindex = at._index;\n    var tptr = at._ptr;\n\n    // find w[first + j]\n    for (k = 0; k < n; k++) {\n      j = post[k];\n      // colcount[j]=1 if j is a leaf\n      colcount[j] = w[first + j] === -1 ? 1 : 0;\n      for (; j !== -1 && w[first + j] === -1; j = parent[j]) {\n        w[first + j] = k;\n      }\n    }\n\n    // initialize ata if needed\n    if (ata) {\n      // invert post\n      for (k = 0; k < n; k++) {\n        w[post[k]] = k;\n      }\n      // loop rows (columns in AT)\n      for (i = 0; i < m; i++) {\n        // values in column i of AT\n        for (k = n, p0 = tptr[i], p1 = tptr[i + 1], p = p0; p < p1; p++) {\n          k = Math.min(k, w[tindex[p]]);\n        }\n        // place row i in linked list k\n        w[next + i] = w[head + k];\n        w[head + k] = i;\n      }\n    }\n\n    // each node in its own set\n    for (i = 0; i < n; i++) {\n      w[ancestor + i] = i;\n    }\n    for (k = 0; k < n; k++) {\n      // j is the kth node in postordered etree\n      j = post[k];\n      // check j is not a root\n      if (parent[j] !== -1) {\n        colcount[parent[j]]--;\n      }\n\n      // J=j for LL'=A case\n      for (J = ata ? w[head + k] : j; J !== -1; J = ata ? w[next + J] : -1) {\n        for (p = tptr[J]; p < tptr[J + 1]; p++) {\n          i = tindex[p];\n          var r = csLeaf(i, j, w, first, maxfirst, prevleaf, ancestor);\n          // check A(i,j) is in skeleton\n          if (r.jleaf >= 1) {\n            colcount[j]++;\n          }\n          // check account for overlap in q\n          if (r.jleaf === 2) {\n            colcount[r.q]--;\n          }\n        }\n      }\n      if (parent[j] !== -1) {\n        w[ancestor + j] = parent[j];\n      }\n    }\n    // sum up colcount's of each child\n    for (j = 0; j < n; j++) {\n      if (parent[j] !== -1) {\n        colcount[parent[j]] += colcount[j];\n      }\n    }\n    return colcount;\n  };\n});"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,MAAM,QAAQ,aAAa;AACpC,IAAIC,IAAI,GAAG,UAAU;AACrB,IAAIC,YAAY,GAAG,CAAC,WAAW,CAAC;AAChC,OAAO,IAAIC,cAAc,GAAG,eAAeJ,OAAO,CAACE,IAAI,EAAEC,YAAY,EAAEE,IAAI,IAAI;EAC7E,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO,UAAUE,CAAC,EAAEC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAE;IACrC;IACA,IAAI,CAACH,CAAC,IAAI,CAACC,MAAM,IAAI,CAACC,IAAI,EAAE;MAC1B,OAAO,IAAI;IACb;IACA;IACA,IAAIE,KAAK,GAAGJ,CAAC,CAACK,KAAK;IACnB;IACA,IAAIC,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC;IAChB,IAAIG,CAAC,GAAGH,KAAK,CAAC,CAAC,CAAC;IAChB;IACA,IAAII,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE;;IAEzB;IACA,IAAIC,CAAC,GAAG,CAAC,GAAGR,CAAC,IAAIJ,GAAG,GAAGI,CAAC,GAAGD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACrC;IACA,IAAIU,CAAC,GAAG,EAAE,CAAC,CAAC;IACZ,IAAIC,QAAQ,GAAG,CAAC,CAAC,CAAC;IAClB,IAAIC,QAAQ,GAAGX,CAAC,CAAC,CAAC;IAClB,IAAIY,QAAQ,GAAG,CAAC,GAAGZ,CAAC,CAAC,CAAC;IACtB,IAAIa,KAAK,GAAG,CAAC,GAAGb,CAAC,CAAC,CAAC;IACnB,IAAIc,IAAI,GAAG,CAAC,GAAGd,CAAC,CAAC,CAAC;IAClB,IAAIe,IAAI,GAAG,CAAC,GAAGf,CAAC,GAAG,CAAC,CAAC,CAAC;IACtB;IACA,KAAKG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,EAAEL,CAAC,EAAE,EAAE;MACtBM,CAAC,CAACN,CAAC,CAAC,GAAG,CAAC,CAAC;IACX;;IAEA;IACA,IAAIa,QAAQ,GAAG,EAAE,CAAC,CAAC;;IAEnB;IACA,IAAIC,EAAE,GAAGzB,SAAS,CAACC,CAAC,CAAC;IACrB;IACA,IAAIyB,MAAM,GAAGD,EAAE,CAACE,MAAM;IACtB,IAAIC,IAAI,GAAGH,EAAE,CAACI,IAAI;;IAElB;IACA,KAAKlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MACtBD,CAAC,GAAGP,IAAI,CAACQ,CAAC,CAAC;MACX;MACAa,QAAQ,CAACd,CAAC,CAAC,GAAGO,CAAC,CAACI,KAAK,GAAGX,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;MACzC,OAAOA,CAAC,KAAK,CAAC,CAAC,IAAIO,CAAC,CAACI,KAAK,GAAGX,CAAC,CAAC,KAAK,CAAC,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACQ,CAAC,CAAC,EAAE;QACrDO,CAAC,CAACI,KAAK,GAAGX,CAAC,CAAC,GAAGC,CAAC;MAClB;IACF;;IAEA;IACA,IAAIP,GAAG,EAAE;MACP;MACA,KAAKO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;QACtBM,CAAC,CAACd,IAAI,CAACQ,CAAC,CAAC,CAAC,GAAGA,CAAC;MAChB;MACA;MACA,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAEE,CAAC,EAAE,EAAE;QACtB;QACA,KAAKE,CAAC,GAAGH,CAAC,EAAEM,EAAE,GAAGc,IAAI,CAACnB,CAAC,CAAC,EAAEM,EAAE,GAAGa,IAAI,CAACnB,CAAC,GAAG,CAAC,CAAC,EAAEI,CAAC,GAAGC,EAAE,EAAED,CAAC,GAAGE,EAAE,EAAEF,CAAC,EAAE,EAAE;UAC/DF,CAAC,GAAGmB,IAAI,CAACC,GAAG,CAACpB,CAAC,EAAEM,CAAC,CAACS,MAAM,CAACb,CAAC,CAAC,CAAC,CAAC;QAC/B;QACA;QACAI,CAAC,CAACM,IAAI,GAAGd,CAAC,CAAC,GAAGQ,CAAC,CAACK,IAAI,GAAGX,CAAC,CAAC;QACzBM,CAAC,CAACK,IAAI,GAAGX,CAAC,CAAC,GAAGF,CAAC;MACjB;IACF;;IAEA;IACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,EAAEC,CAAC,EAAE,EAAE;MACtBQ,CAAC,CAACC,QAAQ,GAAGT,CAAC,CAAC,GAAGA,CAAC;IACrB;IACA,KAAKE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MACtB;MACAD,CAAC,GAAGP,IAAI,CAACQ,CAAC,CAAC;MACX;MACA,IAAIT,MAAM,CAACQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACpBc,QAAQ,CAACtB,MAAM,CAACQ,CAAC,CAAC,CAAC,EAAE;MACvB;;MAEA;MACA,KAAKE,CAAC,GAAGR,GAAG,GAAGa,CAAC,CAACK,IAAI,GAAGX,CAAC,CAAC,GAAGD,CAAC,EAAEE,CAAC,KAAK,CAAC,CAAC,EAAEA,CAAC,GAAGR,GAAG,GAAGa,CAAC,CAACM,IAAI,GAAGX,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;QACpE,KAAKC,CAAC,GAAGe,IAAI,CAAChB,CAAC,CAAC,EAAEC,CAAC,GAAGe,IAAI,CAAChB,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,EAAE,EAAE;UACtCJ,CAAC,GAAGiB,MAAM,CAACb,CAAC,CAAC;UACb,IAAImB,CAAC,GAAGrC,MAAM,CAACc,CAAC,EAAEC,CAAC,EAAEO,CAAC,EAAEI,KAAK,EAAEF,QAAQ,EAAEC,QAAQ,EAAEF,QAAQ,CAAC;UAC5D;UACA,IAAIc,CAAC,CAACC,KAAK,IAAI,CAAC,EAAE;YAChBT,QAAQ,CAACd,CAAC,CAAC,EAAE;UACf;UACA;UACA,IAAIsB,CAAC,CAACC,KAAK,KAAK,CAAC,EAAE;YACjBT,QAAQ,CAACQ,CAAC,CAACE,CAAC,CAAC,EAAE;UACjB;QACF;MACF;MACA,IAAIhC,MAAM,CAACQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACpBO,CAAC,CAACC,QAAQ,GAAGR,CAAC,CAAC,GAAGR,MAAM,CAACQ,CAAC,CAAC;MAC7B;IACF;IACA;IACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAEE,CAAC,EAAE,EAAE;MACtB,IAAIR,MAAM,CAACQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACpBc,QAAQ,CAACtB,MAAM,CAACQ,CAAC,CAAC,CAAC,IAAIc,QAAQ,CAACd,CAAC,CAAC;MACpC;IACF;IACA,OAAOc,QAAQ;EACjB,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}