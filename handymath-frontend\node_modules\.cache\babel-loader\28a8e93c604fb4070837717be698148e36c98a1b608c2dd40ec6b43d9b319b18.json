{"ast": null, "code": "// A library of seedable RNGs implemented in Javascript.\n//\n// Usage:\n//\n// var seedrandom = require('seedrandom');\n// var random = seedrandom(1); // or any seed.\n// var x = random();       // 0 <= x < 1.  Every bit is random.\n// var x = random.quick(); // 0 <= x < 1.  32 bits of randomness.\n\n// alea, a 53-bit multiply-with-carry generator by <PERSON>.\n// Period: ~2^116\n// Reported to pass all BigCrush tests.\nvar alea = require('./lib/alea');\n\n// xor128, a pure xor-shift generator by <PERSON>.\n// Period: 2^128-1.\n// Reported to fail: MatrixRank and LinearComp.\nvar xor128 = require('./lib/xor128');\n\n// xorwow, <PERSON>'s 160-bit xor-shift combined plus weyl.\n// Period: 2^192-2^32\n// Reported to fail: CollisionOver, SimpPoker, and LinearComp.\nvar xorwow = require('./lib/xorwow');\n\n// xorshift7, by <PERSON> and <PERSON>, takes\n// a different approach: it adds robustness by allowing more shifts\n// than Marsaglia's original three.  It is a 7-shift generator\n// with 256 bits, that passes BigCrush with no systmatic failures.\n// Period 2^256-1.\n// No systematic BigCrush failures reported.\nvar xorshift7 = require('./lib/xorshift7');\n\n// xor4096, by Richard Brent, is a 4096-bit xor-shift with a\n// very long period that also adds a Weyl generator. It also passes\n// BigCrush with no systematic failures.  Its long period may\n// be useful if you have many generators and need to avoid\n// collisions.\n// Period: 2^4128-2^32.\n// No systematic BigCrush failures reported.\nvar xor4096 = require('./lib/xor4096');\n\n// Tyche-i, by Samuel Neves and Filipe Araujo, is a bit-shifting random\n// number generator derived from ChaCha, a modern stream cipher.\n// https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n// Period: ~2^127\n// No systematic BigCrush failures reported.\nvar tychei = require('./lib/tychei');\n\n// The original ARC4-based prng included in this library.\n// Period: ~2^1600\nvar sr = require('./seedrandom');\nsr.alea = alea;\nsr.xor128 = xor128;\nsr.xorwow = xorwow;\nsr.xorshift7 = xorshift7;\nsr.xor4096 = xor4096;\nsr.tychei = tychei;\nmodule.exports = sr;", "map": {"version": 3, "names": ["alea", "require", "xor128", "xorwow", "xorshift7", "xor4096", "tychei", "sr", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/seedrandom/index.js"], "sourcesContent": ["// A library of seedable RNGs implemented in Javascript.\n//\n// Usage:\n//\n// var seedrandom = require('seedrandom');\n// var random = seedrandom(1); // or any seed.\n// var x = random();       // 0 <= x < 1.  Every bit is random.\n// var x = random.quick(); // 0 <= x < 1.  32 bits of randomness.\n\n// alea, a 53-bit multiply-with-carry generator by <PERSON>.\n// Period: ~2^116\n// Reported to pass all BigCrush tests.\nvar alea = require('./lib/alea');\n\n// xor128, a pure xor-shift generator by <PERSON>.\n// Period: 2^128-1.\n// Reported to fail: MatrixRank and LinearComp.\nvar xor128 = require('./lib/xor128');\n\n// xorwow, <PERSON>'s 160-bit xor-shift combined plus weyl.\n// Period: 2^192-2^32\n// Reported to fail: CollisionOver, SimpPoker, and LinearComp.\nvar xorwow = require('./lib/xorwow');\n\n// xorshift7, by <PERSON> and <PERSON>, takes\n// a different approach: it adds robustness by allowing more shifts\n// than Marsaglia's original three.  It is a 7-shift generator\n// with 256 bits, that passes BigCrush with no systmatic failures.\n// Period 2^256-1.\n// No systematic BigCrush failures reported.\nvar xorshift7 = require('./lib/xorshift7');\n\n// xor4096, by Richard Brent, is a 4096-bit xor-shift with a\n// very long period that also adds a Weyl generator. It also passes\n// BigCrush with no systematic failures.  Its long period may\n// be useful if you have many generators and need to avoid\n// collisions.\n// Period: 2^4128-2^32.\n// No systematic BigCrush failures reported.\nvar xor4096 = require('./lib/xor4096');\n\n// Tyche-i, by Samuel Neves and Filipe Araujo, is a bit-shifting random\n// number generator derived from ChaCha, a modern stream cipher.\n// https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n// Period: ~2^127\n// No systematic BigCrush failures reported.\nvar tychei = require('./lib/tychei');\n\n// The original ARC4-based prng included in this library.\n// Period: ~2^1600\nvar sr = require('./seedrandom');\n\nsr.alea = alea;\nsr.xor128 = xor128;\nsr.xorwow = xorwow;\nsr.xorshift7 = xorshift7;\nsr.xor4096 = xor4096;\nsr.tychei = tychei;\n\nmodule.exports = sr;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAIA,IAAI,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEhC;AACA;AACA;AACA,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAc,CAAC;;AAEpC;AACA;AACA;AACA,IAAIE,MAAM,GAAGF,OAAO,CAAC,cAAc,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA,IAAIG,SAAS,GAAGH,OAAO,CAAC,iBAAiB,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAII,OAAO,GAAGJ,OAAO,CAAC,eAAe,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA,IAAIK,MAAM,GAAGL,OAAO,CAAC,cAAc,CAAC;;AAEpC;AACA;AACA,IAAIM,EAAE,GAAGN,OAAO,CAAC,cAAc,CAAC;AAEhCM,EAAE,CAACP,IAAI,GAAGA,IAAI;AACdO,EAAE,CAACL,MAAM,GAAGA,MAAM;AAClBK,EAAE,CAACJ,MAAM,GAAGA,MAAM;AAClBI,EAAE,CAACH,SAAS,GAAGA,SAAS;AACxBG,EAAE,CAACF,OAAO,GAAGA,OAAO;AACpBE,EAAE,CAACD,MAAM,GAAGA,MAAM;AAElBE,MAAM,CAACC,OAAO,GAAGF,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}