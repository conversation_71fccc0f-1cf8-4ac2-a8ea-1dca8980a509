{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { isNaNDependencies } from './dependenciesIsNaN.generated.js';\nimport { isNumericDependencies } from './dependenciesIsNumeric.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMode } from '../../factoriesAny.js';\nexport var modeDependencies = {\n  isNaNDependencies,\n  isNumericDependencies,\n  typedDependencies,\n  createMode\n};", "map": {"version": 3, "names": ["isNaNDependencies", "isNumericDependencies", "typedDependencies", "createMode", "modeDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMode.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { isNaNDependencies } from './dependenciesIsNaN.generated.js';\nimport { isNumericDependencies } from './dependenciesIsNumeric.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMode } from '../../factoriesAny.js';\nexport var modeDependencies = {\n  isNaNDependencies,\n  isNumericDependencies,\n  typedDependencies,\n  createMode\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAO,IAAIC,gBAAgB,GAAG;EAC5BJ,iBAAiB;EACjBC,qBAAqB;EACrBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}