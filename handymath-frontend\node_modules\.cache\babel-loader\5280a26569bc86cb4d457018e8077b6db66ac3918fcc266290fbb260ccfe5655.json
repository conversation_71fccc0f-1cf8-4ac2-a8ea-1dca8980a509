{"ast": null, "code": "export var meanDocs = {\n  name: 'mean',\n  category: 'Statistics',\n  syntax: ['mean(a, b, c, ...)', 'mean(A)', 'mean(A, dimension)'],\n  description: 'Compute the arithmetic mean of a list of values.',\n  examples: ['mean(2, 3, 4, 1)', 'mean([2, 3, 4, 1])', 'mean([2, 5; 4, 3])', 'mean([2, 5; 4, 3], 1)', 'mean([2, 5; 4, 3], 2)', 'mean([1.0, 2.7, 3.2, 4.0])'],\n  seealso: ['max', 'median', 'min', 'prod', 'std', 'sum', 'variance']\n};", "map": {"version": 3, "names": ["meanDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/statistics/mean.js"], "sourcesContent": ["export var meanDocs = {\n  name: 'mean',\n  category: 'Statistics',\n  syntax: ['mean(a, b, c, ...)', 'mean(A)', 'mean(A, dimension)'],\n  description: 'Compute the arithmetic mean of a list of values.',\n  examples: ['mean(2, 3, 4, 1)', 'mean([2, 3, 4, 1])', 'mean([2, 5; 4, 3])', 'mean([2, 5; 4, 3], 1)', 'mean([2, 5; 4, 3], 2)', 'mean([1.0, 2.7, 3.2, 4.0])'],\n  seealso: ['max', 'median', 'min', 'prod', 'std', 'sum', 'variance']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,oBAAoB,EAAE,SAAS,EAAE,oBAAoB,CAAC;EAC/DC,WAAW,EAAE,kDAAkD;EAC/DC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,uBAAuB,EAAE,uBAAuB,EAAE,4BAA4B,CAAC;EAC1JC,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU;AACpE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}