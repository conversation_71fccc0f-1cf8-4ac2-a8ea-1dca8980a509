{"ast": null, "code": "export var deepEqualDocs = {\n  name: 'deepEqual',\n  category: 'Relational',\n  syntax: ['deepEqual(x, y)'],\n  description: 'Check equality of two matrices element wise. Returns true if the size of both matrices is equal and when and each of the elements are equal.',\n  examples: ['deepEqual([1,3,4], [1,3,4])', 'deepEqual([1,3,4], [1,3])'],\n  seealso: ['equal', 'unequal', 'smaller', 'larger', 'smallerEq', 'largerEq', 'compare']\n};", "map": {"version": 3, "names": ["deepEqualDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/relational/deepEqual.js"], "sourcesContent": ["export var deepEqualDocs = {\n  name: 'deepEqual',\n  category: 'Relational',\n  syntax: ['deepEqual(x, y)'],\n  description: 'Check equality of two matrices element wise. Returns true if the size of both matrices is equal and when and each of the elements are equal.',\n  examples: ['deepEqual([1,3,4], [1,3,4])', 'deepEqual([1,3,4], [1,3])'],\n  seealso: ['equal', 'unequal', 'smaller', 'larger', 'smallerEq', 'largerEq', 'compare']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,iBAAiB,CAAC;EAC3BC,WAAW,EAAE,8IAA8I;EAC3JC,QAAQ,EAAE,CAAC,6BAA6B,EAAE,2BAA2B,CAAC;EACtEC,OAAO,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS;AACvF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}