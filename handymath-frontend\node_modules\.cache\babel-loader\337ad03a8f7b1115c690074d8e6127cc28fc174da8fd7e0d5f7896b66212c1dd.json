{"ast": null, "code": "export var isNumericDocs = {\n  name: 'isNumeric',\n  category: 'Utils',\n  syntax: ['isNumeric(x)'],\n  description: 'Test whether a value is a numeric value. ' + 'Returns true when the input is a number, BigNumber, Fraction, or boolean.',\n  examples: ['isNumeric(2)', 'isNumeric(\"2\")', 'hasNumericValue(\"2\")', 'isNumeric(0)', 'isNumeric(bignumber(500))', 'isNumeric(fraction(0.125))', 'isNumeric(2 + 3i)', 'isNumeric([2.3, \"foo\", false])'],\n  seealso: ['isInteger', 'isZero', 'isNegative', 'isPositive', 'isNaN', 'hasNumericValue']\n};", "map": {"version": 3, "names": ["isNumericDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/isNumeric.js"], "sourcesContent": ["export var isNumericDocs = {\n  name: 'isNumeric',\n  category: 'Utils',\n  syntax: ['isNumeric(x)'],\n  description: 'Test whether a value is a numeric value. ' + 'Returns true when the input is a number, BigNumber, Fraction, or boolean.',\n  examples: ['isNumeric(2)', 'isNumeric(\"2\")', 'hasNumericValue(\"2\")', 'isNumeric(0)', 'isNumeric(bignumber(500))', 'isNumeric(fraction(0.125))', 'isNumeric(2 + 3i)', 'isNumeric([2.3, \"foo\", false])'],\n  seealso: ['isInteger', 'isZero', 'isNegative', 'isPositive', 'isNaN', 'hasNumericValue']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC,cAAc,CAAC;EACxBC,WAAW,EAAE,2CAA2C,GAAG,2EAA2E;EACtIC,QAAQ,EAAE,CAAC,cAAc,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,cAAc,EAAE,2BAA2B,EAAE,4BAA4B,EAAE,mBAAmB,EAAE,gCAAgC,CAAC;EACtMC,OAAO,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,iBAAiB;AACzF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}