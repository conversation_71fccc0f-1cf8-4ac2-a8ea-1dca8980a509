{"ast": null, "code": "export var setMultiplicityDocs = {\n  name: 'setMultiplicity',\n  category: 'Set',\n  syntax: ['setMultiplicity(element, set)'],\n  description: 'Count the multiplicity of an element in a multiset. A multi-dimension array will be converted to a single-dimension array before the operation.',\n  examples: ['setMultiplicity(1, [1, 2, 2, 4])', 'setMultiplicity(2, [1, 2, 2, 4])'],\n  seealso: ['setDistinct', 'setSize']\n};", "map": {"version": 3, "names": ["setMultiplicityDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/set/setMultiplicity.js"], "sourcesContent": ["export var setMultiplicityDocs = {\n  name: 'setMultiplicity',\n  category: 'Set',\n  syntax: ['setMultiplicity(element, set)'],\n  description: 'Count the multiplicity of an element in a multiset. A multi-dimension array will be converted to a single-dimension array before the operation.',\n  examples: ['setMultiplicity(1, [1, 2, 2, 4])', 'setMultiplicity(2, [1, 2, 2, 4])'],\n  seealso: ['setDistinct', 'setSize']\n};"], "mappings": "AAAA,OAAO,IAAIA,mBAAmB,GAAG;EAC/BC,IAAI,EAAE,iBAAiB;EACvBC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,CAAC,+BAA+B,CAAC;EACzCC,WAAW,EAAE,iJAAiJ;EAC9JC,QAAQ,EAAE,CAAC,kCAAkC,EAAE,kCAAkC,CAAC;EAClFC,OAAO,EAAE,CAAC,aAAa,EAAE,SAAS;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}