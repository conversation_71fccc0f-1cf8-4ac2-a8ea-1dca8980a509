{"ast": null, "code": "\"use strict\";\n\n// Map the characters to escape to their escaped values. The list is derived\n// from http://www.cespedes.org/blog/85/how-to-escape-latex-special-characters\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nvar defaultEscapes = {\n  \"{\": \"\\\\{\",\n  \"}\": \"\\\\}\",\n  \"\\\\\": \"\\\\textbackslash{}\",\n  \"#\": \"\\\\#\",\n  $: \"\\\\$\",\n  \"%\": \"\\\\%\",\n  \"&\": \"\\\\&\",\n  \"^\": \"\\\\textasciicircum{}\",\n  _: \"\\\\_\",\n  \"~\": \"\\\\textasciitilde{}\"\n};\nvar formatEscapes = {\n  \"\\u2013\": \"\\\\--\",\n  \"\\u2014\": \"\\\\---\",\n  \" \": \"~\",\n  \"\\t\": \"\\\\qquad{}\",\n  \"\\r\\n\": \"\\\\newline{}\",\n  \"\\n\": \"\\\\newline{}\"\n};\nvar defaultEscapeMapFn = function defaultEscapeMapFn(defaultEscapes, formatEscapes) {\n  return _extends({}, defaultEscapes, formatEscapes);\n};\n\n/**\n * Escape a string to be used in LaTeX documents.\n * @param {string} str the string to be escaped.\n * @param {boolean} params.preserveFormatting whether formatting escapes should\n *  be performed (default: false).\n * @param {function} params.escapeMapFn the function to modify the escape maps.\n * @return {string} the escaped string, ready to be used in LaTeX.\n */\nmodule.exports = function (str) {\n  var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    _ref$preserveFormatti = _ref.preserveFormatting,\n    preserveFormatting = _ref$preserveFormatti === undefined ? false : _ref$preserveFormatti,\n    _ref$escapeMapFn = _ref.escapeMapFn,\n    escapeMapFn = _ref$escapeMapFn === undefined ? defaultEscapeMapFn : _ref$escapeMapFn;\n  var runningStr = String(str);\n  var result = \"\";\n  var escapes = escapeMapFn(_extends({}, defaultEscapes), preserveFormatting ? _extends({}, formatEscapes) : {});\n  var escapeKeys = Object.keys(escapes); // as it is reused later on\n\n  // Algorithm: Go through the string character by character, if it matches\n  // with one of the special characters then we'll replace it with the escaped\n  // version.\n\n  var _loop = function _loop() {\n    var specialCharFound = false;\n    escapeKeys.forEach(function (key, index) {\n      if (specialCharFound) {\n        return;\n      }\n      if (runningStr.length >= key.length && runningStr.slice(0, key.length) === key) {\n        result += escapes[escapeKeys[index]];\n        runningStr = runningStr.slice(key.length, runningStr.length);\n        specialCharFound = true;\n      }\n    });\n    if (!specialCharFound) {\n      result += runningStr.slice(0, 1);\n      runningStr = runningStr.slice(1, runningStr.length);\n    }\n  };\n  while (runningStr) {\n    _loop();\n  }\n  return result;\n};", "map": {"version": 3, "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "defaultEscapes", "$", "_", "formatEscapes", "defaultEscapeMapFn", "module", "exports", "str", "_ref", "undefined", "_ref$preserveFormatti", "preserveFormatting", "_ref$escapeMapFn", "escapeMapFn", "runningStr", "String", "result", "escapes", "escape<PERSON>eys", "keys", "_loop", "specialCharFound", "for<PERSON>ach", "index", "slice"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/escape-latex/dist/index.js"], "sourcesContent": ["\"use strict\";\n\n// Map the characters to escape to their escaped values. The list is derived\n// from http://www.cespedes.org/blog/85/how-to-escape-latex-special-characters\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar defaultEscapes = {\n  \"{\": \"\\\\{\",\n  \"}\": \"\\\\}\",\n  \"\\\\\": \"\\\\textbackslash{}\",\n  \"#\": \"\\\\#\",\n  $: \"\\\\$\",\n  \"%\": \"\\\\%\",\n  \"&\": \"\\\\&\",\n  \"^\": \"\\\\textasciicircum{}\",\n  _: \"\\\\_\",\n  \"~\": \"\\\\textasciitilde{}\"\n};\nvar formatEscapes = {\n  \"\\u2013\": \"\\\\--\",\n  \"\\u2014\": \"\\\\---\",\n  \" \": \"~\",\n  \"\\t\": \"\\\\qquad{}\",\n  \"\\r\\n\": \"\\\\newline{}\",\n  \"\\n\": \"\\\\newline{}\"\n};\n\nvar defaultEscapeMapFn = function defaultEscapeMapFn(defaultEscapes, formatEscapes) {\n  return _extends({}, defaultEscapes, formatEscapes);\n};\n\n/**\n * Escape a string to be used in LaTeX documents.\n * @param {string} str the string to be escaped.\n * @param {boolean} params.preserveFormatting whether formatting escapes should\n *  be performed (default: false).\n * @param {function} params.escapeMapFn the function to modify the escape maps.\n * @return {string} the escaped string, ready to be used in LaTeX.\n */\nmodule.exports = function (str) {\n  var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n      _ref$preserveFormatti = _ref.preserveFormatting,\n      preserveFormatting = _ref$preserveFormatti === undefined ? false : _ref$preserveFormatti,\n      _ref$escapeMapFn = _ref.escapeMapFn,\n      escapeMapFn = _ref$escapeMapFn === undefined ? defaultEscapeMapFn : _ref$escapeMapFn;\n\n  var runningStr = String(str);\n  var result = \"\";\n\n  var escapes = escapeMapFn(_extends({}, defaultEscapes), preserveFormatting ? _extends({}, formatEscapes) : {});\n  var escapeKeys = Object.keys(escapes); // as it is reused later on\n\n  // Algorithm: Go through the string character by character, if it matches\n  // with one of the special characters then we'll replace it with the escaped\n  // version.\n\n  var _loop = function _loop() {\n    var specialCharFound = false;\n    escapeKeys.forEach(function (key, index) {\n      if (specialCharFound) {\n        return;\n      }\n      if (runningStr.length >= key.length && runningStr.slice(0, key.length) === key) {\n        result += escapes[escapeKeys[index]];\n        runningStr = runningStr.slice(key.length, runningStr.length);\n        specialCharFound = true;\n      }\n    });\n    if (!specialCharFound) {\n      result += runningStr.slice(0, 1);\n      runningStr = runningStr.slice(1, runningStr.length);\n    }\n  };\n\n  while (runningStr) {\n    _loop();\n  }\n  return result;\n};"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AAEA,IAAIA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAAUC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MAAE,IAAIN,MAAM,CAACQ,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;QAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE,CAAC;AAEhQ,IAAIS,cAAc,GAAG;EACnB,GAAG,EAAE,KAAK;EACV,GAAG,EAAE,KAAK;EACV,IAAI,EAAE,mBAAmB;EACzB,GAAG,EAAE,KAAK;EACVC,CAAC,EAAE,KAAK;EACR,GAAG,EAAE,KAAK;EACV,GAAG,EAAE,KAAK;EACV,GAAG,EAAE,qBAAqB;EAC1BC,CAAC,EAAE,KAAK;EACR,GAAG,EAAE;AACP,CAAC;AACD,IAAIC,aAAa,GAAG;EAClB,QAAQ,EAAE,MAAM;EAChB,QAAQ,EAAE,OAAO;EACjB,GAAG,EAAE,GAAG;EACR,IAAI,EAAE,WAAW;EACjB,MAAM,EAAE,aAAa;EACrB,IAAI,EAAE;AACR,CAAC;AAED,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACJ,cAAc,EAAEG,aAAa,EAAE;EAClF,OAAOf,QAAQ,CAAC,CAAC,CAAC,EAAEY,cAAc,EAAEG,aAAa,CAAC;AACpD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAE,MAAM,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAE;EAC9B,IAAIC,IAAI,GAAGf,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKgB,SAAS,GAAGhB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC7EiB,qBAAqB,GAAGF,IAAI,CAACG,kBAAkB;IAC/CA,kBAAkB,GAAGD,qBAAqB,KAAKD,SAAS,GAAG,KAAK,GAAGC,qBAAqB;IACxFE,gBAAgB,GAAGJ,IAAI,CAACK,WAAW;IACnCA,WAAW,GAAGD,gBAAgB,KAAKH,SAAS,GAAGL,kBAAkB,GAAGQ,gBAAgB;EAExF,IAAIE,UAAU,GAAGC,MAAM,CAACR,GAAG,CAAC;EAC5B,IAAIS,MAAM,GAAG,EAAE;EAEf,IAAIC,OAAO,GAAGJ,WAAW,CAACzB,QAAQ,CAAC,CAAC,CAAC,EAAEY,cAAc,CAAC,EAAEW,kBAAkB,GAAGvB,QAAQ,CAAC,CAAC,CAAC,EAAEe,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;EAC9G,IAAIe,UAAU,GAAG7B,MAAM,CAAC8B,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC;;EAEvC;EACA;EACA;;EAEA,IAAIG,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC3B,IAAIC,gBAAgB,GAAG,KAAK;IAC5BH,UAAU,CAACI,OAAO,CAAC,UAAU1B,GAAG,EAAE2B,KAAK,EAAE;MACvC,IAAIF,gBAAgB,EAAE;QACpB;MACF;MACA,IAAIP,UAAU,CAACpB,MAAM,IAAIE,GAAG,CAACF,MAAM,IAAIoB,UAAU,CAACU,KAAK,CAAC,CAAC,EAAE5B,GAAG,CAACF,MAAM,CAAC,KAAKE,GAAG,EAAE;QAC9EoB,MAAM,IAAIC,OAAO,CAACC,UAAU,CAACK,KAAK,CAAC,CAAC;QACpCT,UAAU,GAAGA,UAAU,CAACU,KAAK,CAAC5B,GAAG,CAACF,MAAM,EAAEoB,UAAU,CAACpB,MAAM,CAAC;QAC5D2B,gBAAgB,GAAG,IAAI;MACzB;IACF,CAAC,CAAC;IACF,IAAI,CAACA,gBAAgB,EAAE;MACrBL,MAAM,IAAIF,UAAU,CAACU,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAChCV,UAAU,GAAGA,UAAU,CAACU,KAAK,CAAC,CAAC,EAAEV,UAAU,CAACpB,MAAM,CAAC;IACrD;EACF,CAAC;EAED,OAAOoB,UAAU,EAAE;IACjBM,KAAK,CAAC,CAAC;EACT;EACA,OAAOJ,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}