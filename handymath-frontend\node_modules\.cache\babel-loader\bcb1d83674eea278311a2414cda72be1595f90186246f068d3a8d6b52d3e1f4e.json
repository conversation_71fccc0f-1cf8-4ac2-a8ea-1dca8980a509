{"ast": null, "code": "export var fftDocs = {\n  name: 'fft',\n  category: 'Matrix',\n  syntax: ['fft(x)'],\n  description: 'Calculate N-dimensional Fourier transform',\n  examples: ['fft([[1, 0], [1, 0]])'],\n  seealso: ['ifft']\n};", "map": {"version": 3, "names": ["fftDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/fft.js"], "sourcesContent": ["export var fftDocs = {\n  name: 'fft',\n  category: 'Matrix',\n  syntax: ['fft(x)'],\n  description: 'Calculate N-dimensional Fourier transform',\n  examples: ['fft([[1, 0], [1, 0]])'],\n  seealso: ['ifft']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,QAAQ,CAAC;EAClBC,WAAW,EAAE,2CAA2C;EACxDC,QAAQ,EAAE,CAAC,uBAAuB,CAAC;EACnCC,OAAO,EAAE,CAAC,MAAM;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}