{"ast": null, "code": "export var iDocs = {\n  name: 'i',\n  category: 'Constants',\n  syntax: ['i'],\n  description: 'Imaginary unit, defined as i*i=-1. A complex number is described as a + b*i, where a is the real part, and b is the imaginary part.',\n  examples: ['i', 'i * i', 'sqrt(-1)'],\n  seealso: []\n};", "map": {"version": 3, "names": ["iDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/i.js"], "sourcesContent": ["export var iDocs = {\n  name: 'i',\n  category: 'Constants',\n  syntax: ['i'],\n  description: 'Imaginary unit, defined as i*i=-1. A complex number is described as a + b*i, where a is the real part, and b is the imaginary part.',\n  examples: ['i', 'i * i', 'sqrt(-1)'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,KAAK,GAAG;EACjBC,IAAI,EAAE,GAAG;EACTC,QAAQ,EAAE,WAAW;EACrBC,MAAM,EAAE,CAAC,GAAG,CAAC;EACbC,WAAW,EAAE,qIAAqI;EAClJC,QAAQ,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,UAAU,CAAC;EACpCC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}