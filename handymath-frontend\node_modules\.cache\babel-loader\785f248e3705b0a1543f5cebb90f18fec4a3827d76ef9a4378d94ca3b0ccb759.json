{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createKlitzing } from '../../factoriesAny.js';\nexport var klitzingDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createKlitzing\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createKlitzing", "klitzingDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesKlitzing.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createKlitzing } from '../../factoriesAny.js';\nexport var klitzingDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createKlitzing\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,2CAA2C;AACjF,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,cAAc,QAAQ,uBAAuB;AACtD,OAAO,IAAIC,oBAAoB,GAAG;EAChCH,qBAAqB;EACrBC,gBAAgB;EAChBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}