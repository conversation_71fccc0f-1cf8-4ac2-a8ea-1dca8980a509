{"ast": null, "code": "export var stdDocs = {\n  name: 'std',\n  category: 'Statistics',\n  syntax: ['std(a, b, c, ...)', 'std(A)', 'std(A, dimension)', 'std(A, normalization)', 'std(A, dimension, normalization)'],\n  description: 'Compute the standard deviation of all values, defined as std(A) = sqrt(variance(A)). Optional parameter normalization can be \"unbiased\" (default), \"uncorrected\", or \"biased\".',\n  examples: ['std(2, 4, 6)', 'std([2, 4, 6, 8])', 'std([2, 4, 6, 8], \"uncorrected\")', 'std([2, 4, 6, 8], \"biased\")', 'std([1, 2, 3; 4, 5, 6])'],\n  seealso: ['max', 'mean', 'min', 'median', 'prod', 'sum', 'variance']\n};", "map": {"version": 3, "names": ["stdDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/statistics/std.js"], "sourcesContent": ["export var stdDocs = {\n  name: 'std',\n  category: 'Statistics',\n  syntax: ['std(a, b, c, ...)', 'std(A)', 'std(A, dimension)', 'std(A, normalization)', 'std(A, dimension, normalization)'],\n  description: 'Compute the standard deviation of all values, defined as std(A) = sqrt(variance(A)). Optional parameter normalization can be \"unbiased\" (default), \"uncorrected\", or \"biased\".',\n  examples: ['std(2, 4, 6)', 'std([2, 4, 6, 8])', 'std([2, 4, 6, 8], \"uncorrected\")', 'std([2, 4, 6, 8], \"biased\")', 'std([1, 2, 3; 4, 5, 6])'],\n  seealso: ['max', 'mean', 'min', 'median', 'prod', 'sum', 'variance']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,kCAAkC,CAAC;EACzHC,WAAW,EAAE,gLAAgL;EAC7LC,QAAQ,EAAE,CAAC,cAAc,EAAE,mBAAmB,EAAE,kCAAkC,EAAE,6BAA6B,EAAE,yBAAyB,CAAC;EAC7IC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU;AACrE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}