{"ast": null, "code": "import { isInteger } from '../../utils/number.js';\nvar n1 = 'number';\nvar n2 = 'number, number';\nexport function bitAndNumber(x, y) {\n  if (!isInteger(x) || !isInteger(y)) {\n    throw new Error('Integers expected in function bitAnd');\n  }\n  return x & y;\n}\nbitAndNumber.signature = n2;\nexport function bitNotNumber(x) {\n  if (!isInteger(x)) {\n    throw new Error('Integer expected in function bitNot');\n  }\n  return ~x;\n}\nbitNotNumber.signature = n1;\nexport function bitOrNumber(x, y) {\n  if (!isInteger(x) || !isInteger(y)) {\n    throw new Error('Integers expected in function bitOr');\n  }\n  return x | y;\n}\nbitOrNumber.signature = n2;\nexport function bitXorNumber(x, y) {\n  if (!isInteger(x) || !isInteger(y)) {\n    throw new Error('Integers expected in function bitXor');\n  }\n  return x ^ y;\n}\nbitXorNumber.signature = n2;\nexport function leftShiftNumber(x, y) {\n  if (!isInteger(x) || !isInteger(y)) {\n    throw new Error('Integers expected in function leftShift');\n  }\n  return x << y;\n}\nleftShiftNumber.signature = n2;\nexport function rightArithShiftNumber(x, y) {\n  if (!isInteger(x) || !isInteger(y)) {\n    throw new Error('Integers expected in function rightArithShift');\n  }\n  return x >> y;\n}\nrightArithShiftNumber.signature = n2;\nexport function rightLogShiftNumber(x, y) {\n  if (!isInteger(x) || !isInteger(y)) {\n    throw new Error('Integers expected in function rightLogShift');\n  }\n  return x >>> y;\n}\nrightLogShiftNumber.signature = n2;", "map": {"version": 3, "names": ["isInteger", "n1", "n2", "bitAndNumber", "x", "y", "Error", "signature", "bitNotNumber", "bitOrNumber", "bitXorNumber", "leftShiftNumber", "rightArithShiftNumber", "rightLogShiftNumber"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/plain/number/bitwise.js"], "sourcesContent": ["import { isInteger } from '../../utils/number.js';\nvar n1 = 'number';\nvar n2 = 'number, number';\nexport function bitAndNumber(x, y) {\n  if (!isInteger(x) || !isInteger(y)) {\n    throw new Error('Integers expected in function bitAnd');\n  }\n  return x & y;\n}\nbitAndNumber.signature = n2;\nexport function bitNotNumber(x) {\n  if (!isInteger(x)) {\n    throw new Error('Integer expected in function bitNot');\n  }\n  return ~x;\n}\nbitNotNumber.signature = n1;\nexport function bitOrNumber(x, y) {\n  if (!isInteger(x) || !isInteger(y)) {\n    throw new Error('Integers expected in function bitOr');\n  }\n  return x | y;\n}\nbitOrNumber.signature = n2;\nexport function bitXorNumber(x, y) {\n  if (!isInteger(x) || !isInteger(y)) {\n    throw new Error('Integers expected in function bitXor');\n  }\n  return x ^ y;\n}\nbitXorNumber.signature = n2;\nexport function leftShiftNumber(x, y) {\n  if (!isInteger(x) || !isInteger(y)) {\n    throw new Error('Integers expected in function leftShift');\n  }\n  return x << y;\n}\nleftShiftNumber.signature = n2;\nexport function rightArithShiftNumber(x, y) {\n  if (!isInteger(x) || !isInteger(y)) {\n    throw new Error('Integers expected in function rightArithShift');\n  }\n  return x >> y;\n}\nrightArithShiftNumber.signature = n2;\nexport function rightLogShiftNumber(x, y) {\n  if (!isInteger(x) || !isInteger(y)) {\n    throw new Error('Integers expected in function rightLogShift');\n  }\n  return x >>> y;\n}\nrightLogShiftNumber.signature = n2;"], "mappings": "AAAA,SAASA,SAAS,QAAQ,uBAAuB;AACjD,IAAIC,EAAE,GAAG,QAAQ;AACjB,IAAIC,EAAE,GAAG,gBAAgB;AACzB,OAAO,SAASC,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjC,IAAI,CAACL,SAAS,CAACI,CAAC,CAAC,IAAI,CAACJ,SAAS,CAACK,CAAC,CAAC,EAAE;IAClC,MAAM,IAAIC,KAAK,CAAC,sCAAsC,CAAC;EACzD;EACA,OAAOF,CAAC,GAAGC,CAAC;AACd;AACAF,YAAY,CAACI,SAAS,GAAGL,EAAE;AAC3B,OAAO,SAASM,YAAYA,CAACJ,CAAC,EAAE;EAC9B,IAAI,CAACJ,SAAS,CAACI,CAAC,CAAC,EAAE;IACjB,MAAM,IAAIE,KAAK,CAAC,qCAAqC,CAAC;EACxD;EACA,OAAO,CAACF,CAAC;AACX;AACAI,YAAY,CAACD,SAAS,GAAGN,EAAE;AAC3B,OAAO,SAASQ,WAAWA,CAACL,CAAC,EAAEC,CAAC,EAAE;EAChC,IAAI,CAACL,SAAS,CAACI,CAAC,CAAC,IAAI,CAACJ,SAAS,CAACK,CAAC,CAAC,EAAE;IAClC,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;EACxD;EACA,OAAOF,CAAC,GAAGC,CAAC;AACd;AACAI,WAAW,CAACF,SAAS,GAAGL,EAAE;AAC1B,OAAO,SAASQ,YAAYA,CAACN,CAAC,EAAEC,CAAC,EAAE;EACjC,IAAI,CAACL,SAAS,CAACI,CAAC,CAAC,IAAI,CAACJ,SAAS,CAACK,CAAC,CAAC,EAAE;IAClC,MAAM,IAAIC,KAAK,CAAC,sCAAsC,CAAC;EACzD;EACA,OAAOF,CAAC,GAAGC,CAAC;AACd;AACAK,YAAY,CAACH,SAAS,GAAGL,EAAE;AAC3B,OAAO,SAASS,eAAeA,CAACP,CAAC,EAAEC,CAAC,EAAE;EACpC,IAAI,CAACL,SAAS,CAACI,CAAC,CAAC,IAAI,CAACJ,SAAS,CAACK,CAAC,CAAC,EAAE;IAClC,MAAM,IAAIC,KAAK,CAAC,yCAAyC,CAAC;EAC5D;EACA,OAAOF,CAAC,IAAIC,CAAC;AACf;AACAM,eAAe,CAACJ,SAAS,GAAGL,EAAE;AAC9B,OAAO,SAASU,qBAAqBA,CAACR,CAAC,EAAEC,CAAC,EAAE;EAC1C,IAAI,CAACL,SAAS,CAACI,CAAC,CAAC,IAAI,CAACJ,SAAS,CAACK,CAAC,CAAC,EAAE;IAClC,MAAM,IAAIC,KAAK,CAAC,+CAA+C,CAAC;EAClE;EACA,OAAOF,CAAC,IAAIC,CAAC;AACf;AACAO,qBAAqB,CAACL,SAAS,GAAGL,EAAE;AACpC,OAAO,SAASW,mBAAmBA,CAACT,CAAC,EAAEC,CAAC,EAAE;EACxC,IAAI,CAACL,SAAS,CAACI,CAAC,CAAC,IAAI,CAACJ,SAAS,CAACK,CAAC,CAAC,EAAE;IAClC,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOF,CAAC,KAAKC,CAAC;AAChB;AACAQ,mBAAmB,CAACN,SAAS,GAAGL,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}