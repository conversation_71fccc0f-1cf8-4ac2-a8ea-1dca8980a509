{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { numericDependencies } from './dependenciesNumeric.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createUnaryPlus } from '../../factoriesAny.js';\nexport var unaryPlusDependencies = {\n  numericDependencies,\n  typedDependencies,\n  createUnaryPlus\n};", "map": {"version": 3, "names": ["numericDependencies", "typedDependencies", "createUnaryPlus", "unaryPlusDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesUnaryPlus.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { numericDependencies } from './dependenciesNumeric.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createUnaryPlus } from '../../factoriesAny.js';\nexport var unaryPlusDependencies = {\n  numericDependencies,\n  typedDependencies,\n  createUnaryPlus\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,eAAe,QAAQ,uBAAuB;AACvD,OAAO,IAAIC,qBAAqB,GAAG;EACjCH,mBAAmB;EACnBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}