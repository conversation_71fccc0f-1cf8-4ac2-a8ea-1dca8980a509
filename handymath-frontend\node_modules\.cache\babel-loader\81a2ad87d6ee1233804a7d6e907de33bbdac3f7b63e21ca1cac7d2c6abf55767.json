{"ast": null, "code": "export var gcdDocs = {\n  name: 'gcd',\n  category: 'Arithmetic',\n  syntax: ['gcd(a, b)', 'gcd(a, b, c, ...)'],\n  description: 'Compute the greatest common divisor.',\n  examples: ['gcd(8, 12)', 'gcd(-4, 6)', 'gcd(25, 15, -10)'],\n  seealso: ['lcm', 'xgcd']\n};", "map": {"version": 3, "names": ["gcdDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/gcd.js"], "sourcesContent": ["export var gcdDocs = {\n  name: 'gcd',\n  category: 'Arithmetic',\n  syntax: ['gcd(a, b)', 'gcd(a, b, c, ...)'],\n  description: 'Compute the greatest common divisor.',\n  examples: ['gcd(8, 12)', 'gcd(-4, 6)', 'gcd(25, 15, -10)'],\n  seealso: ['lcm', 'xgcd']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,CAAC,WAAW,EAAE,mBAAmB,CAAC;EAC1CC,WAAW,EAAE,sCAAsC;EACnDC,QAAQ,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,kBAAkB,CAAC;EAC1DC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}