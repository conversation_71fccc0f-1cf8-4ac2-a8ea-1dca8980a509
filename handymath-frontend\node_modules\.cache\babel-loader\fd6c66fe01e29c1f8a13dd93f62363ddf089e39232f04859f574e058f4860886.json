{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { log2Dependencies } from './dependenciesLog2.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createRandomInt } from '../../factoriesAny.js';\nexport var randomIntDependencies = {\n  log2Dependencies,\n  typedDependencies,\n  createRandomInt\n};", "map": {"version": 3, "names": ["log2Dependencies", "typedDependencies", "createRandomInt", "randomIntDependencies"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesRandomInt.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { log2Dependencies } from './dependenciesLog2.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createRandomInt } from '../../factoriesAny.js';\nexport var randomIntDependencies = {\n  log2Dependencies,\n  typedDependencies,\n  createRandomInt\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,eAAe,QAAQ,uBAAuB;AACvD,OAAO,IAAIC,qBAAqB,GAAG;EACjCH,gBAAgB;EAChBC,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}