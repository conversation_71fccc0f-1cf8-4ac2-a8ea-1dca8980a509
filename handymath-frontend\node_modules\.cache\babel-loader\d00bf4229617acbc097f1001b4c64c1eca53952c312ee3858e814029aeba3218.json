{"ast": null, "code": "export var sizeDocs = {\n  name: 'size',\n  category: 'Matrix',\n  syntax: ['size(x)'],\n  description: 'Calculate the size of a matrix.',\n  examples: ['size(2.3)', 'size(\"hello world\")', 'a = [1, 2; 3, 4; 5, 6]', 'size(a)', 'size(1:6)'],\n  seealso: ['concat', 'count', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};", "map": {"version": 3, "names": ["sizeDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/size.js"], "sourcesContent": ["export var sizeDocs = {\n  name: 'size',\n  category: 'Matrix',\n  syntax: ['size(x)'],\n  description: 'Calculate the size of a matrix.',\n  examples: ['size(2.3)', 'size(\"hello world\")', 'a = [1, 2; 3, 4; 5, 6]', 'size(a)', 'size(1:6)'],\n  seealso: ['concat', 'count', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,CAAC,SAAS,CAAC;EACnBC,WAAW,EAAE,iCAAiC;EAC9CC,QAAQ,EAAE,CAAC,WAAW,EAAE,qBAAqB,EAAE,wBAAwB,EAAE,SAAS,EAAE,WAAW,CAAC;EAChGC,OAAO,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO;AACpI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}