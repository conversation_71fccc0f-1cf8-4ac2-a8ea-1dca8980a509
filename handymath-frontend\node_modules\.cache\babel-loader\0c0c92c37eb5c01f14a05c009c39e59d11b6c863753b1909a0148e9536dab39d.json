{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\HandyMath2\\\\handymath-frontend\\\\src\\\\pages\\\\CourseDetailPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CourseDetailPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useAuth();\n  const {\n    addNotification\n  } = useNotifications();\n  const [course, setCourse] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [enrolling, setEnrolling] = useState(false);\n  useEffect(() => {\n    if (user && id) {\n      fetchCourse();\n    }\n  }, [user, id]);\n  const fetchCourse = async () => {\n    if (!id) return;\n    try {\n      setLoading(true);\n      console.log('🔍 Tentative de récupération du cours ID:', id);\n      console.log('👤 Utilisateur connecté:', user);\n      console.log('🔑 Token dans localStorage:', localStorage.getItem('authToken') ? 'Présent' : 'Absent');\n      const response = await api.get(`/api/courses/${id}/`);\n      console.log('✅ Réponse API reçue:', response.data);\n      setCourse(response.data);\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$response4, _error$response5;\n      console.error('❌ Erreur lors de la récupération du cours:', error);\n      console.error('📊 Détails de l\\'erreur:', {\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n        data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data,\n        message: error.message\n      });\n      if (((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.status) === 404) {\n        console.log('🚫 Erreur 404 - Cours non trouvé');\n      } else if (((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status) === 401) {\n        console.log('🔐 Erreur 401 - Non autorisé');\n      }\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de charger les détails du cours'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEnroll = async () => {\n    if (!course) return;\n    try {\n      setEnrolling(true);\n      await api.post(`/api/courses/${course.id}/enroll/`);\n      setCourse({\n        ...course,\n        is_enrolled: true\n      });\n      addNotification({\n        type: 'success',\n        title: 'Inscription réussie',\n        message: `Vous êtes maintenant inscrit au cours \"${course.title}\"`\n      });\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur d\\'inscription',\n        message: 'Impossible de s\\'inscrire au cours'\n      });\n    } finally {\n      setEnrolling(false);\n    }\n  };\n  const getLevelColor = level => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n  const formatDuration = minutes => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Acc\\xE8s restreint\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Vous devez \\xEAtre connect\\xE9 pour acc\\xE9der \\xE0 ce cours.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Chargement du cours...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this);\n  }\n  if (!course) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold mb-4\",\n          children: \"Cours non trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400 mb-8\",\n          children: \"Le cours demand\\xE9 n'existe pas ou n'est plus disponible.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/courses'),\n          className: \"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\",\n          children: \"Retour aux cours\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => navigate('/courses'),\n      className: \"mb-6 flex items-center text-primary-600 hover:text-primary-700 transition-colors\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"mr-2\",\n        children: \"\\u2190\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), \"Retour aux cours\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start justify-between mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-4xl mr-4\",\n              children: course.thumbnail\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                children: course.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-block px-3 py-1 rounded-full text-sm font-medium mt-2 ${getLevelColor(course.level)}`,\n                children: course.level_display\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-700 dark:text-gray-300 mb-6\",\n          children: course.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Chapitres:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 21\n              }, this), \" \", course.chapters.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Le\\xE7ons:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 21\n              }, this), \" \", course.chapters.reduce((total, chapter) => total + chapter.lessons.length, 0)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2\",\n              children: \"\\u23F1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Dur\\xE9e:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 21\n              }, this), \" \", formatDuration(course.estimated_duration)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Progression:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 21\n              }, this), \" \", course.progress_percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), course.is_enrolled && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-sm mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"Progression du cours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [course.progress_percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-primary-600 h-3 rounded-full transition-all duration-300\",\n              style: {\n                width: `${course.progress_percentage}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), !course.is_enrolled && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleEnroll,\n            disabled: enrolling,\n            className: \"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-medium py-3 px-8 rounded-lg transition-colors\",\n            children: enrolling ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 21\n              }, this), \"Inscription...\"]\n            }, void 0, true) : 'S\\'inscrire au cours'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), course.is_enrolled && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: course.chapters.map((chapter, chapterIndex) => /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\",\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: (chapterIndex + 1) * 0.1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n              children: [\"Chapitre \", chapter.order, \": \", chapter.title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600 dark:text-gray-400\",\n              children: [chapter.progress_percentage, \"% termin\\xE9\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 17\n          }, this), chapter.description && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-700 dark:text-gray-300 mb-4\",\n            children: chapter.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-green-500 h-2 rounded-full transition-all duration-300\",\n                style: {\n                  width: `${chapter.progress_percentage}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: chapter.lessons.map(lesson => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center justify-between p-3 rounded-lg border ${lesson.is_completed ? 'bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700' : lesson.is_accessible ? 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600' : 'bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 opacity-60'} transition-colors`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xl mr-3\",\n                  children: lesson.is_completed ? '✅' : lesson.is_accessible ? '📖' : '🔒'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-gray-900 dark:text-white\",\n                    children: lesson.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center text-sm text-gray-600 dark:text-gray-400\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"mr-3\",\n                      children: lesson.type_display\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 337,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"mr-3\",\n                      children: [\"\\u23F1\", lesson.estimated_duration, \"min\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 29\n                    }, this), lesson.time_spent > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [Math.floor(lesson.time_spent / 60), \"min pass\\xE9es\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: lesson.is_accessible ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => navigate(`/lessons/${lesson.id}`),\n                    className: \"bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors\",\n                    children: lesson.is_completed ? 'Revoir' : 'Commencer'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 29\n                  }, this), lesson.exercise_id && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => navigate(`/exercises/${lesson.exercise_id}`),\n                    className: \"bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors\",\n                    children: \"Exercice\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 31\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-500 dark:text-gray-400\",\n                  children: \"Terminez les le\\xE7ons pr\\xE9c\\xE9dentes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 23\n              }, this)]\n            }, lesson.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 17\n          }, this)]\n        }, chapter.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 198,\n    columnNumber: 5\n  }, this);\n};\n_s(CourseDetailPage, \"h17A3oKL5abROQuvfWaCa9lWwHM=\", false, function () {\n  return [useParams, useNavigate, useAuth, useNotifications];\n});\n_c = CourseDetailPage;\nexport default CourseDetailPage;\nvar _c;\n$RefreshReg$(_c, \"CourseDetailPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "motion", "useAuth", "useNotifications", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CourseDetailPage", "_s", "id", "navigate", "user", "addNotification", "course", "setCourse", "loading", "setLoading", "enrolling", "setEnrolling", "fetchCourse", "console", "log", "localStorage", "getItem", "response", "get", "data", "error", "_error$response", "_error$response2", "_error$response3", "_error$response4", "_error$response5", "status", "statusText", "message", "type", "title", "handleEnroll", "post", "is_enrolled", "getLevelColor", "level", "formatDuration", "minutes", "hours", "Math", "floor", "mins", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "div", "initial", "opacity", "y", "animate", "thumbnail", "level_display", "description", "chapters", "length", "reduce", "total", "chapter", "lessons", "estimated_duration", "progress_percentage", "style", "width", "disabled", "map", "chapterIndex", "transition", "delay", "order", "lesson", "is_completed", "is_accessible", "type_display", "time_spent", "exercise_id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/src/pages/CourseDetailPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotifications } from '../components/NotificationSystem';\nimport api from '../services/api';\n\ninterface Lesson {\n  id: number;\n  title: string;\n  lesson_type: string;\n  type_display: string;\n  order: number;\n  estimated_duration: number;\n  is_accessible: boolean;\n  is_completed: boolean;\n  time_spent: number;\n  exercise_id?: number;\n}\n\ninterface Chapter {\n  id: number;\n  title: string;\n  description: string;\n  order: number;\n  progress_percentage: number;\n  lessons: Lesson[];\n}\n\ninterface Course {\n  id: number;\n  title: string;\n  description: string;\n  level: string;\n  level_display: string;\n  thumbnail: string;\n  estimated_duration: number;\n  progress_percentage: number;\n  is_enrolled: boolean;\n  enrollment_date?: string;\n  chapters: Chapter[];\n  prerequisites: Array<{\n    id: number;\n    title: string;\n    progress: number;\n  }>;\n  created_at: string;\n}\n\nconst CourseDetailPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const { user } = useAuth();\n  const { addNotification } = useNotifications();\n  const [course, setCourse] = useState<Course | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [enrolling, setEnrolling] = useState(false);\n\n  useEffect(() => {\n    if (user && id) {\n      fetchCourse();\n    }\n  }, [user, id]);\n\n  const fetchCourse = async () => {\n    if (!id) return;\n\n    try {\n      setLoading(true);\n      console.log('🔍 Tentative de récupération du cours ID:', id);\n      console.log('👤 Utilisateur connecté:', user);\n      console.log('🔑 Token dans localStorage:', localStorage.getItem('authToken') ? 'Présent' : 'Absent');\n\n      const response = await api.get(`/api/courses/${id}/`);\n      console.log('✅ Réponse API reçue:', response.data);\n      setCourse(response.data);\n    } catch (error: any) {\n      console.error('❌ Erreur lors de la récupération du cours:', error);\n      console.error('📊 Détails de l\\'erreur:', {\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        message: error.message\n      });\n\n      if (error.response?.status === 404) {\n        console.log('🚫 Erreur 404 - Cours non trouvé');\n      } else if (error.response?.status === 401) {\n        console.log('🔐 Erreur 401 - Non autorisé');\n      }\n\n      addNotification({\n        type: 'error',\n        title: 'Erreur',\n        message: 'Impossible de charger les détails du cours'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleEnroll = async () => {\n    if (!course) return;\n\n    try {\n      setEnrolling(true);\n      await api.post(`/api/courses/${course.id}/enroll/`);\n      setCourse({ ...course, is_enrolled: true });\n      addNotification({\n        type: 'success',\n        title: 'Inscription réussie',\n        message: `Vous êtes maintenant inscrit au cours \"${course.title}\"`\n      });\n    } catch (error) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      addNotification({\n        type: 'error',\n        title: 'Erreur d\\'inscription',\n        message: 'Impossible de s\\'inscrire au cours'\n      });\n    } finally {\n      setEnrolling(false);\n    }\n  };\n\n  const getLevelColor = (level: string) => {\n    switch (level) {\n      case 'beginner':\n        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n      case 'intermediate':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n      case 'advanced':\n        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';\n    }\n  };\n\n  const formatDuration = (minutes: number) => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    if (hours > 0) {\n      return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;\n    }\n    return `${mins}min`;\n  };\n\n  if (!user) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Accès restreint</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            Vous devez être connecté pour accéder à ce cours.\n          </p>\n          <a\n            href=\"/login\"\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Se connecter\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Chargement du cours...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!course) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-4\">Cours non trouvé</h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n            Le cours demandé n'existe pas ou n'est plus disponible.\n          </p>\n          <button\n            onClick={() => navigate('/courses')}\n            className=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors\"\n          >\n            Retour aux cours\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <button\n        onClick={() => navigate('/courses')}\n        className=\"mb-6 flex items-center text-primary-600 hover:text-primary-700 transition-colors\"\n      >\n        <span className=\"mr-2\">←</span>\n        Retour aux cours\n      </button>\n\n      <div className=\"max-w-4xl mx-auto\">\n        <motion.div\n          className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n        >\n          <div className=\"flex items-start justify-between mb-4\">\n            <div className=\"flex items-center\">\n              <span className=\"text-4xl mr-4\">{course.thumbnail}</span>\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                  {course.title}\n                </h1>\n                <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium mt-2 ${getLevelColor(course.level)}`}>\n                  {course.level_display}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          <p className=\"text-gray-700 dark:text-gray-300 mb-6\">\n            {course.description}\n          </p>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm mb-6\">\n            <div className=\"flex items-center\">\n              <span><strong>Chapitres:</strong> {course.chapters.length}</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span><strong>Leçons:</strong> {course.chapters.reduce((total, chapter) => total + chapter.lessons.length, 0)}</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span className=\"mr-2\">⏱</span>\n              <span><strong>Durée:</strong> {formatDuration(course.estimated_duration)}</span>\n            </div>\n            <div className=\"flex items-center\">\n              <span><strong>Progression:</strong> {course.progress_percentage}%</span>\n            </div>\n          </div>\n\n          {course.is_enrolled && (\n            <div className=\"mb-6\">\n              <div className=\"flex justify-between text-sm mb-2\">\n                <span className=\"font-medium\">Progression du cours</span>\n                <span>{course.progress_percentage}%</span>\n              </div>\n              <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3\">\n                <div\n                  className=\"bg-primary-600 h-3 rounded-full transition-all duration-300\"\n                  style={{ width: `${course.progress_percentage}%` }}\n                />\n              </div>\n            </div>\n          )}\n\n          {!course.is_enrolled && (\n            <div className=\"text-center\">\n              <button\n                onClick={handleEnroll}\n                disabled={enrolling}\n                className=\"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white font-medium py-3 px-8 rounded-lg transition-colors\"\n              >\n                {enrolling ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white inline-block mr-2\"></div>\n                    Inscription...\n                  </>\n                ) : (\n                  'S\\'inscrire au cours'\n                )}\n              </button>\n            </div>\n          )}\n        </motion.div>\n\n        {course.is_enrolled && (\n          <div className=\"space-y-6\">\n            {course.chapters.map((chapter, chapterIndex) => (\n              <motion.div\n                key={chapter.id}\n                className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: (chapterIndex + 1) * 0.1 }}\n              >\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                    Chapitre {chapter.order}: {chapter.title}\n                  </h2>\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {chapter.progress_percentage}% terminé\n                  </span>\n                </div>\n\n                {chapter.description && (\n                  <p className=\"text-gray-700 dark:text-gray-300 mb-4\">\n                    {chapter.description}\n                  </p>\n                )}\n\n                <div className=\"mb-4\">\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div\n                      className=\"bg-green-500 h-2 rounded-full transition-all duration-300\"\n                      style={{ width: `${chapter.progress_percentage}%` }}\n                    />\n                  </div>\n                </div>\n\n                <div className=\"space-y-3\">\n                  {chapter.lessons.map((lesson) => (\n                    <div\n                      key={lesson.id}\n                      className={`flex items-center justify-between p-3 rounded-lg border ${\n                        lesson.is_completed\n                          ? 'bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700'\n                          : lesson.is_accessible\n                          ? 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'\n                          : 'bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 opacity-60'\n                      } transition-colors`}\n                    >\n                      <div className=\"flex items-center\">\n                        <span className=\"text-xl mr-3\">\n                          {lesson.is_completed ? '✅' : lesson.is_accessible ? '📖' : '🔒'}\n                        </span>\n                        <div>\n                          <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                            {lesson.title}\n                          </h4>\n                          <div className=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                            <span className=\"mr-3\">{lesson.type_display}</span>\n                            <span className=\"mr-3\">⏱{lesson.estimated_duration}min</span>\n                            {lesson.time_spent > 0 && (\n                              <span>{Math.floor(lesson.time_spent / 60)}min passées</span>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center space-x-2\">\n                        {lesson.is_accessible ? (\n                          <>\n                            <button\n                              onClick={() => navigate(`/lessons/${lesson.id}`)}\n                              className=\"bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors\"\n                            >\n                              {lesson.is_completed ? 'Revoir' : 'Commencer'}\n                            </button>\n                            {lesson.exercise_id && (\n                              <button\n                                onClick={() => navigate(`/exercises/${lesson.exercise_id}`)}\n                                className=\"bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors\"\n                              >\n                                Exercice\n                              </button>\n                            )}\n                          </>\n                        ) : (\n                          <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                            Terminez les leçons précédentes\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CourseDetailPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA4ClC,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAG,CAAC,GAAGZ,SAAS,CAAiB,CAAC;EAC1C,MAAMa,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEa;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEY;EAAgB,CAAC,GAAGX,gBAAgB,CAAC,CAAC;EAC9C,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAgB,IAAI,CAAC;EACzD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACd,IAAIe,IAAI,IAAIF,EAAE,EAAE;MACdU,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACR,IAAI,EAAEF,EAAE,CAAC,CAAC;EAEd,MAAMU,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACV,EAAE,EAAE;IAET,IAAI;MACFO,UAAU,CAAC,IAAI,CAAC;MAChBI,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEZ,EAAE,CAAC;MAC5DW,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEV,IAAI,CAAC;MAC7CS,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,GAAG,SAAS,GAAG,QAAQ,CAAC;MAEpG,MAAMC,QAAQ,GAAG,MAAMtB,GAAG,CAACuB,GAAG,CAAC,gBAAgBhB,EAAE,GAAG,CAAC;MACrDW,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEG,QAAQ,CAACE,IAAI,CAAC;MAClDZ,SAAS,CAACU,QAAQ,CAACE,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACnBZ,OAAO,CAACO,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClEP,OAAO,CAACO,KAAK,CAAC,0BAA0B,EAAE;QACxCM,MAAM,GAAAL,eAAA,GAAED,KAAK,CAACH,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBK,MAAM;QAC9BC,UAAU,GAAAL,gBAAA,GAAEF,KAAK,CAACH,QAAQ,cAAAK,gBAAA,uBAAdA,gBAAA,CAAgBK,UAAU;QACtCR,IAAI,GAAAI,gBAAA,GAAEH,KAAK,CAACH,QAAQ,cAAAM,gBAAA,uBAAdA,gBAAA,CAAgBJ,IAAI;QAC1BS,OAAO,EAAER,KAAK,CAACQ;MACjB,CAAC,CAAC;MAEF,IAAI,EAAAJ,gBAAA,GAAAJ,KAAK,CAACH,QAAQ,cAAAO,gBAAA,uBAAdA,gBAAA,CAAgBE,MAAM,MAAK,GAAG,EAAE;QAClCb,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MACjD,CAAC,MAAM,IAAI,EAAAW,gBAAA,GAAAL,KAAK,CAACH,QAAQ,cAAAQ,gBAAA,uBAAdA,gBAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QACzCb,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC7C;MAEAT,eAAe,CAAC;QACdwB,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,QAAQ;QACfF,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACzB,MAAM,EAAE;IAEb,IAAI;MACFK,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMhB,GAAG,CAACqC,IAAI,CAAC,gBAAgB1B,MAAM,CAACJ,EAAE,UAAU,CAAC;MACnDK,SAAS,CAAC;QAAE,GAAGD,MAAM;QAAE2B,WAAW,EAAE;MAAK,CAAC,CAAC;MAC3C5B,eAAe,CAAC;QACdwB,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,qBAAqB;QAC5BF,OAAO,EAAE,0CAA0CtB,MAAM,CAACwB,KAAK;MACjE,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDf,eAAe,CAAC;QACdwB,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,uBAAuB;QAC9BF,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,SAAS;MACRjB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMuB,aAAa,GAAIC,KAAa,IAAK;IACvC,QAAQA,KAAK;MACX,KAAK,UAAU;QACb,OAAO,mEAAmE;MAC5E,KAAK,cAAc;QACjB,OAAO,uEAAuE;MAChF,KAAK,UAAU;QACb,OAAO,2DAA2D;MACpE;QACE,OAAO,+DAA+D;IAC1E;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,OAAe,IAAK;IAC1C,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,IAAIG,IAAI,GAAG,CAAC,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE,EAAE;IACpD;IACA,OAAO,GAAGA,IAAI,KAAK;EACrB,CAAC;EAED,IAAI,CAACrC,IAAI,EAAE;IACT,oBACEP,OAAA;MAAK6C,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C9C,OAAA;QAAK6C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9C,OAAA;UAAI6C,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5DlD,OAAA;UAAG6C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJlD,OAAA;UACEmD,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIvC,OAAO,EAAE;IACX,oBACEX,OAAA;MAAK6C,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C9C,OAAA;QAAK6C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC9C,OAAA;UAAK6C,SAAS,EAAC;QAAgF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtGlD,OAAA;UAAG6C,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACzC,MAAM,EAAE;IACX,oBACET,OAAA;MAAK6C,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C9C,OAAA;QAAK6C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9C,OAAA;UAAI6C,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DlD,OAAA;UAAG6C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJlD,OAAA;UACEoD,OAAO,EAAEA,CAAA,KAAM9C,QAAQ,CAAC,UAAU,CAAE;UACpCuC,SAAS,EAAC,mGAAmG;UAAAC,QAAA,EAC9G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElD,OAAA;IAAK6C,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1C9C,OAAA;MACEoD,OAAO,EAAEA,CAAA,KAAM9C,QAAQ,CAAC,UAAU,CAAE;MACpCuC,SAAS,EAAC,kFAAkF;MAAAC,QAAA,gBAE5F9C,OAAA;QAAM6C,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,oBAEjC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAETlD,OAAA;MAAK6C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC9C,OAAA,CAACL,MAAM,CAAC0D,GAAG;QACTR,SAAS,EAAC,yDAAyD;QACnES,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAV,QAAA,gBAE9B9C,OAAA;UAAK6C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpD9C,OAAA;YAAK6C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC9C,OAAA;cAAM6C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAErC,MAAM,CAACiD;YAAS;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzDlD,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAI6C,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,EAC7DrC,MAAM,CAACwB;cAAK;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACLlD,OAAA;gBAAM6C,SAAS,EAAE,gEAAgER,aAAa,CAAC5B,MAAM,CAAC6B,KAAK,CAAC,EAAG;gBAAAQ,QAAA,EAC5GrC,MAAM,CAACkD;cAAa;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA;UAAG6C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACjDrC,MAAM,CAACmD;QAAW;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eAEJlD,OAAA;UAAK6C,SAAS,EAAC,oDAAoD;UAAAC,QAAA,gBACjE9C,OAAA;YAAK6C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChC9C,OAAA;cAAA8C,QAAA,gBAAM9C,OAAA;gBAAA8C,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACzC,MAAM,CAACoD,QAAQ,CAACC,MAAM;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACNlD,OAAA;YAAK6C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChC9C,OAAA;cAAA8C,QAAA,gBAAM9C,OAAA;gBAAA8C,QAAA,EAAQ;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACzC,MAAM,CAACoD,QAAQ,CAACE,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAKD,KAAK,GAAGC,OAAO,CAACC,OAAO,CAACJ,MAAM,EAAE,CAAC,CAAC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClH,CAAC,eACNlD,OAAA;YAAK6C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC9C,OAAA;cAAM6C,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/BlD,OAAA;cAAA8C,QAAA,gBAAM9C,OAAA;gBAAA8C,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACX,cAAc,CAAC9B,MAAM,CAAC0D,kBAAkB,CAAC;YAAA;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eACNlD,OAAA;YAAK6C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChC9C,OAAA;cAAA8C,QAAA,gBAAM9C,OAAA;gBAAA8C,QAAA,EAAQ;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACzC,MAAM,CAAC2D,mBAAmB,EAAC,GAAC;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELzC,MAAM,CAAC2B,WAAW,iBACjBpC,OAAA;UAAK6C,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB9C,OAAA;YAAK6C,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9C,OAAA;cAAM6C,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzDlD,OAAA;cAAA8C,QAAA,GAAOrC,MAAM,CAAC2D,mBAAmB,EAAC,GAAC;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNlD,OAAA;YAAK6C,SAAS,EAAC,sDAAsD;YAAAC,QAAA,eACnE9C,OAAA;cACE6C,SAAS,EAAC,6DAA6D;cACvEwB,KAAK,EAAE;gBAAEC,KAAK,EAAE,GAAG7D,MAAM,CAAC2D,mBAAmB;cAAI;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA,CAACzC,MAAM,CAAC2B,WAAW,iBAClBpC,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B9C,OAAA;YACEoD,OAAO,EAAElB,YAAa;YACtBqC,QAAQ,EAAE1D,SAAU;YACpBgC,SAAS,EAAC,2HAA2H;YAAAC,QAAA,EAEpIjC,SAAS,gBACRb,OAAA,CAAAE,SAAA;cAAA4C,QAAA,gBACE9C,OAAA;gBAAK6C,SAAS,EAAC;cAA6E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,kBAErG;YAAA,eAAE,CAAC,GAEH;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,EAEZzC,MAAM,CAAC2B,WAAW,iBACjBpC,OAAA;QAAK6C,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBrC,MAAM,CAACoD,QAAQ,CAACW,GAAG,CAAC,CAACP,OAAO,EAAEQ,YAAY,kBACzCzE,OAAA,CAACL,MAAM,CAAC0D,GAAG;UAETR,SAAS,EAAC,oDAAoD;UAC9DS,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BkB,UAAU,EAAE;YAAEC,KAAK,EAAE,CAACF,YAAY,GAAG,CAAC,IAAI;UAAI,CAAE;UAAA3B,QAAA,gBAEhD9C,OAAA;YAAK6C,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD9C,OAAA;cAAI6C,SAAS,EAAC,qDAAqD;cAAAC,QAAA,GAAC,WACzD,EAACmB,OAAO,CAACW,KAAK,EAAC,IAAE,EAACX,OAAO,CAAChC,KAAK;YAAA;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACLlD,OAAA;cAAM6C,SAAS,EAAC,0CAA0C;cAAAC,QAAA,GACvDmB,OAAO,CAACG,mBAAmB,EAAC,cAC/B;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAELe,OAAO,CAACL,WAAW,iBAClB5D,OAAA;YAAG6C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACjDmB,OAAO,CAACL;UAAW;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CACJ,eAEDlD,OAAA;YAAK6C,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB9C,OAAA;cAAK6C,SAAS,EAAC,sDAAsD;cAAAC,QAAA,eACnE9C,OAAA;gBACE6C,SAAS,EAAC,2DAA2D;gBACrEwB,KAAK,EAAE;kBAAEC,KAAK,EAAE,GAAGL,OAAO,CAACG,mBAAmB;gBAAI;cAAE;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlD,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBmB,OAAO,CAACC,OAAO,CAACM,GAAG,CAAEK,MAAM,iBAC1B7E,OAAA;cAEE6C,SAAS,EAAE,2DACTgC,MAAM,CAACC,YAAY,GACf,sEAAsE,GACtED,MAAM,CAACE,aAAa,GACpB,2GAA2G,GAC3G,8EAA8E,oBAC/D;cAAAjC,QAAA,gBAErB9C,OAAA;gBAAK6C,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC9C,OAAA;kBAAM6C,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAC3B+B,MAAM,CAACC,YAAY,GAAG,GAAG,GAAGD,MAAM,CAACE,aAAa,GAAG,IAAI,GAAG;gBAAI;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACPlD,OAAA;kBAAA8C,QAAA,gBACE9C,OAAA;oBAAI6C,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EACtD+B,MAAM,CAAC5C;kBAAK;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACLlD,OAAA;oBAAK6C,SAAS,EAAC,4DAA4D;oBAAAC,QAAA,gBACzE9C,OAAA;sBAAM6C,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAAE+B,MAAM,CAACG;oBAAY;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACnDlD,OAAA;sBAAM6C,SAAS,EAAC,MAAM;sBAAAC,QAAA,GAAC,QAAC,EAAC+B,MAAM,CAACV,kBAAkB,EAAC,KAAG;oBAAA;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAC5D2B,MAAM,CAACI,UAAU,GAAG,CAAC,iBACpBjF,OAAA;sBAAA8C,QAAA,GAAOJ,IAAI,CAACC,KAAK,CAACkC,MAAM,CAACI,UAAU,GAAG,EAAE,CAAC,EAAC,gBAAW;oBAAA;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAC5D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlD,OAAA;gBAAK6C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EACzC+B,MAAM,CAACE,aAAa,gBACnB/E,OAAA,CAAAE,SAAA;kBAAA4C,QAAA,gBACE9C,OAAA;oBACEoD,OAAO,EAAEA,CAAA,KAAM9C,QAAQ,CAAC,YAAYuE,MAAM,CAACxE,EAAE,EAAE,CAAE;oBACjDwC,SAAS,EAAC,2GAA2G;oBAAAC,QAAA,EAEpH+B,MAAM,CAACC,YAAY,GAAG,QAAQ,GAAG;kBAAW;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,EACR2B,MAAM,CAACK,WAAW,iBACjBlF,OAAA;oBACEoD,OAAO,EAAEA,CAAA,KAAM9C,QAAQ,CAAC,cAAcuE,MAAM,CAACK,WAAW,EAAE,CAAE;oBAC5DrC,SAAS,EAAC,uGAAuG;oBAAAC,QAAA,EAClH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA,eACD,CAAC,gBAEHlD,OAAA;kBAAM6C,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACP;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GAlDD2B,MAAM,CAACxE,EAAE;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDX,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,GAtFDe,OAAO,CAAC5D,EAAE;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuFL,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9C,EAAA,CA1UID,gBAA0B;EAAA,QACfV,SAAS,EACPC,WAAW,EACXE,OAAO,EACIC,gBAAgB;AAAA;AAAAsF,EAAA,GAJxChF,gBAA0B;AA4UhC,eAAeA,gBAAgB;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}