{"ast": null, "code": "export var acothDocs = {\n  name: 'acoth',\n  category: 'Trigonometry',\n  syntax: ['acoth(x)'],\n  description: 'Calculate the inverse hyperbolic tangent of a value, defined as `acoth(x) = (ln((x+1)/x) + ln(x/(x-1))) / 2`.',\n  examples: ['acoth(2)', 'acoth(0.5)'],\n  seealso: ['acsch', 'asech']\n};", "map": {"version": 3, "names": ["acothDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/HandyMath2/handymath-frontend/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/acoth.js"], "sourcesContent": ["export var acothDocs = {\n  name: 'acoth',\n  category: 'Trigonometry',\n  syntax: ['acoth(x)'],\n  description: 'Calculate the inverse hyperbolic tangent of a value, defined as `acoth(x) = (ln((x+1)/x) + ln(x/(x-1))) / 2`.',\n  examples: ['acoth(2)', 'acoth(0.5)'],\n  seealso: ['acsch', 'asech']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,CAAC,UAAU,CAAC;EACpBC,WAAW,EAAE,+GAA+G;EAC5HC,QAAQ,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;EACpCC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}